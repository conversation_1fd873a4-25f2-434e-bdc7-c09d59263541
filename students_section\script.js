// Supabase Initialization - using variables already defined in config.js
let supabaseUrl, supabase<PERSON>nonKey;

// Safely access the global variables
if (typeof SUPABASE_URL !== 'undefined') {
    supabaseUrl = SUPABASE_URL;
} else {
    supabaseUrl = 'https://jalwmdtsmfalyfvvseoi.supabase.co';
    console.warn('SUPABASE_URL not found in global scope, using fallback');
}

if (typeof SUPABASE_ANON_KEY !== 'undefined') {
    supabaseAnonKey = SUPABASE_ANON_KEY;
} else {
    supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImphbHdtZHRzbWZhbHlmdnZzZW9pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxNDAxNDksImV4cCI6MjA2MzcxNjE0OX0.OBMKqeQmMwG1vY5HUMkt9rQqzm3yrnZUqO9PPrtaJEM';
    console.warn('SUPABASE_ANON_KEY not found in global scope, using fallback');
}

const { createClient } = supabase;
const _supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('Supabase Initialized:', _supabase);

// --- DOM Elements ---
const studentForm = document.getElementById('student-form');
const formMessage = document.getElementById('form-message');
const addStudentBtn = document.getElementById('add-student-btn');
const addStudentSection = document.getElementById('add-student-section');
const cancelEditBtn = document.getElementById('cancel-edit-btn');
const closeFormBtn = document.getElementById('close-form-btn');
const studentsTableBody = document.getElementById('students-tbody');
const listMessage = document.getElementById('list-message');
const searchInput = document.getElementById('search-input');
const searchBtn = document.getElementById('search-btn');
const printReportBtn = document.getElementById('print-report-btn');
const studentIdField = document.getElementById('student_id');
const formTitle = document.getElementById('form-title');
const totalStudentsEl = document.getElementById('total-students');
const activeStudentsEl = document.getElementById('active-students');
const totalSchoolsEl = document.getElementById('total-schools');
const studentsCountBadge = document.getElementById('students-count');
const paginationControls = document.getElementById('pagination-controls');

// --- State ---
let currentStudents = [];
let editMode = false;
let stats = {
    totalStudents: 0,
    activeStudents: 0,
    totalSchools: 0
};
// Hardcoded stages
const stagesData = [
    { id: 1, name: 'الابتدائية' },
    { id: 2, name: 'متوسط' },
    { id: 3, name: 'ثانوية' },
    { id: 4, name: 'جامعة' }
];
// Hardcoded classes based on stage ID
const classesData = {
    1: [ // الابتدائية
        { id: '1', name: 'الصف الأول الابتدائي' },
        { id: '2', name: 'الصف الثاني الابتدائي' },
        { id: '3', name: 'الصف الثالث الابتدائي' },
        { id: '4', name: 'الصف الرابع الابتدائي' },
        { id: '5', name: 'الصف الخامس الابتدائي' },
        { id: '6', name: 'الصف السادس الابتدائي' }
    ],
    2: [ // متوسط
        { id: '7', name: 'الصف الأول المتوسط' }, // Using unique IDs across stages might be better
        { id: '8', name: 'الصف الثاني المتوسط' },
        { id: '9', name: 'الصف الثالث المتوسط' }
    ],
    3: [ // ثانوية
        { id: '10', name: 'الصف الأول الثانوي' },
        { id: '11', name: 'الصف الثاني الثانوي' },
        { id: '12', name: 'الصف الثالث الثانوي' }
    ],
    4: [ // جامعة
        { id: '13', name: 'سنة أولى' },
        { id: '14', name: 'سنة ثانية' },
        { id: '15', name: 'سنة ثالثة' },
        { id: '16', name: 'سنة رابعة' },
        { id: '17', name: 'سنة خامسة' },
        { id: '18', name: 'أخرى' } // Or specific years/levels
    ]
};
// Pagination variables
let currentPage = 1;
const studentsPerPage = 12; // 12 students per page as requested
let totalPages = 1;

// --- Functions ---

// Function to display messages (form or list)
const showMessage = (element, message, type = 'info') => {
    element.textContent = message;
    element.className = `message ${type}`;
    element.style.display = 'block';

    // Auto hide success messages after 5 seconds
    if (type === 'success') {
        setTimeout(() => {
            element.style.display = 'none';
        }, 5000);
    }
};

// Function to render pagination controls
const renderPaginationControls = () => {
    paginationControls.innerHTML = '';

    // Don't show pagination if no students or only one page
    if (currentStudents.length <= studentsPerPage) {
        return;
    }

    totalPages = Math.ceil(currentStudents.length / studentsPerPage);

    // Previous button
    const prevButton = document.createElement('button');
    prevButton.innerHTML = '<i class="fas fa-angle-right"></i>';
    prevButton.className = currentPage === 1 ? 'disabled' : '';
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', () => {
        if (currentPage > 1) {
            goToPage(currentPage - 1);
        }
    });
    paginationControls.appendChild(prevButton);

    // Page buttons
    const maxPageButtons = 5; // Show at most 5 page buttons
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);

    // Adjust startPage if we're at the end
    if (endPage - startPage + 1 < maxPageButtons) {
        startPage = Math.max(1, endPage - maxPageButtons + 1);
    }

    // First page button (if not already showing)
    if (startPage > 1) {
        const firstPageBtn = document.createElement('button');
        firstPageBtn.textContent = '1';
        firstPageBtn.addEventListener('click', () => goToPage(1));
        paginationControls.appendChild(firstPageBtn);

        // Ellipsis if there's a gap
        if (startPage > 2) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'pagination-ellipsis';
            paginationControls.appendChild(ellipsis);
        }
    }

    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.textContent = i.toString();
        pageBtn.className = i === currentPage ? 'active' : '';
        pageBtn.addEventListener('click', () => goToPage(i));
        paginationControls.appendChild(pageBtn);
    }

    // Last page button (if not already showing)
    if (endPage < totalPages) {
        // Ellipsis if there's a gap
        if (endPage < totalPages - 1) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'pagination-ellipsis';
            paginationControls.appendChild(ellipsis);
        }

        const lastPageBtn = document.createElement('button');
        lastPageBtn.textContent = totalPages.toString();
        lastPageBtn.addEventListener('click', () => goToPage(totalPages));
        paginationControls.appendChild(lastPageBtn);
    }

    // Next button
    const nextButton = document.createElement('button');
    nextButton.innerHTML = '<i class="fas fa-angle-left"></i>';
    nextButton.className = currentPage === totalPages ? 'disabled' : '';
    nextButton.disabled = currentPage === totalPages;
    nextButton.addEventListener('click', () => {
        if (currentPage < totalPages) {
            goToPage(currentPage + 1);
        }
    });
    paginationControls.appendChild(nextButton);
};

// Function to navigate to a specific page
const goToPage = (page) => {
    currentPage = page;
    renderStudentsTable(currentStudents);
};

// Function to get current page students
const getCurrentPageStudents = () => {
    const startIndex = (currentPage - 1) * studentsPerPage;
    const endIndex = startIndex + studentsPerPage;
    return currentStudents.slice(startIndex, endIndex);
};

// Function to render the students table
const renderStudentsTable = (students) => {
    studentsTableBody.innerHTML = '';
    listMessage.style.display = 'none';

    if (!students || students.length === 0) {
        // Update colspan to match the new number of columns (7)
        studentsTableBody.innerHTML = '<tr><td colspan="7" class="loading-message">لا يوجد طلاب لعرضهم.</td></tr>';
        studentsCountBadge.textContent = '0';
        paginationControls.innerHTML = ''; // No pagination needed
        return;
    }

    // Update total count badge
    studentsCountBadge.textContent = students.length;

    // Get only students for the current page
    const pageStudents = getCurrentPageStudents();

    pageStudents.forEach(student => {
        // Log the student data for debugging
        console.log('Student data with relations:', student); // Log the full student object

        // Determine status display
        let statusClass = student.is_active ? 'status-active' : 'status-inactive';
        let statusText = student.is_active ? 'نشط' : 'غير نشط';

        // Get related names safely
        const schoolName = student.schools?.name || 'غير محدد'; // Use optional chaining and fallback
        const neighborhoodName = student.neighborhoods?.name || 'غير محدد'; // Use optional chaining and fallback

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${student.name || 'غير محدد'}</td>
            <td>${student.parent_phone || 'غير محدد'}</td>
            <td>${schoolName}</td> <!-- Use related school name -->
            <td>${neighborhoodName}</td> <!-- Use related neighborhood name -->
            <td><span class="${statusClass}">${statusText}</span></td>
            <td>
                ${student.location_url ?
                    `<a href="${student.location_url}" target="_blank" class="action-btn location-btn" title="عرض الموقع"><i class="fas fa-map-marker-alt"></i> عرض</a>` :
                    'لا يوجد'}
            </td>
            <td>
                <button class="action-btn edit-btn" data-id="${student.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                <button class="action-btn delete-btn" data-id="${student.id}" title="حذف"><i class="fas fa-trash-alt"></i></button>
            </td>
        `;

        row.querySelector('.edit-btn').addEventListener('click', () => handleEditStudent(student));
        row.querySelector('.delete-btn').addEventListener('click', () => handleDeleteStudent(student.id, student.name));

        studentsTableBody.appendChild(row);
    });

    // Render pagination controls
    renderPaginationControls();
};

// Function to update dashboard stats
const updateDashboardStats = async () => {
    try {
        // Get total students count
        const { count: totalCount, error: totalError } = await _supabase
            .from('students')
            .select('*', { count: 'exact', head: true });

        // Get active students count
        const { count: activeCount, error: activeError } = await _supabase
            .from('students')
            .select('*', { count: 'exact', head: true })
            .eq('is_active', true);

        // For schools count
        const { data: schools, error: schoolsError } = await _supabase
            .from('schools')
            .select('id')
            .order('id');

        if (!totalError && !activeError && !schoolsError) {
            stats.totalStudents = totalCount || 0;
            stats.activeStudents = activeCount || 0;
            stats.totalSchools = schools?.length || 0;

            // Update dashboard
            totalStudentsEl.textContent = stats.totalStudents;
            activeStudentsEl.textContent = stats.activeStudents;
            totalSchoolsEl.textContent = stats.totalSchools;
        } else {
            console.error('Error fetching stats data:', totalError, activeError, schoolsError);
        }
    } catch (error) {
        console.error('Error updating stats:', error);
    }
};

// --- School & Neighborhood Management Functions ---

// Function to fetch educational stages
const fetchStages = async () => {
    try {
        const { data, error } = await _supabase
            .from('stages')
            .select('id, name')
            .order('name', { ascending: true });

        if (error) {
            console.error('Error fetching stages:', error);
            return [];
        }

        return data || [];
    } catch (error) {
        console.error('Error in fetchStages:', error);
        return [];
    }
};

// Function to fetch schools, optionally filtered by stage_id (now stage)
const fetchSchools = async (stageId = null) => {
    try {
        // Fetch schools - Simplified select statement
        let query = _supabase.from('schools')
            .select(`
                id,
                name,
                stage
            `); // Simplified: Removed stages(name)

        // Apply filter by stage ONLY if a specific stageId is provided
        if (stageId) {
            // Convert stageId to stage name for filtering
            const selectedStage = stagesData.find(s => s.id == stageId);
            if (selectedStage) {
                query = query.eq('stage', selectedStage.name);
            }
        }

        // Order by name
        query = query.order('name', { ascending: true });

        const { data, error } = await query;

        if (error) {
            console.error('Error fetching schools:', error); // Log the specific error
            return [];
        }

        console.log('Fetched schools data:', data); // Log fetched data for debugging
        return data || [];
    } catch (error) {
        console.error('Error in fetchSchools function:', error); // Log any JS error during fetch
        return [];
    }
};

// Function to fetch neighborhoods
const fetchNeighborhoods = async () => {
    try {
        const { data, error } = await _supabase
            .from('neighborhoods')
            .select('id, name')
            .order('name', { ascending: true });

        if (error) {
            console.error('Error fetching neighborhoods:', error);
            return [];
        }

        return data || [];
    } catch (error) {
        console.error('Error in fetchNeighborhoods:', error);
        return [];
    }
};

// Function to add a new school
const addNewSchool = async (schoolName, stageId) => {
    if (!schoolName || !stageId) {
        return { success: false, message: 'يرجى إدخال اسم المدرسة واختيار المرحلة الدراسية' };
    }

    try {
        // Convert stageId to stage name
        const selectedStage = stagesData.find(s => s.id == stageId);
        if (!selectedStage) {
            return { success: false, message: 'المرحلة الدراسية المختارة غير صحيحة' };
        }

        const { data, error } = await _supabase
            .from('schools')
            .insert([{
                name: schoolName,
                stage: selectedStage.name // Use stage name instead of ID
            }])
            .select();

        if (error) {
            console.error('Error adding school:', error);
            return {
                success: false,
                message: `خطأ في إضافة المدرسة: ${error.message}`
            };
        }

        return {
            success: true,
            message: 'تم إضافة المدرسة بنجاح',
            school: data[0]
        };
    } catch (error) {
        console.error('Error in addNewSchool:', error);
        return {
            success: false,
            message: `حدث خطأ غير متوقع: ${error.message}`
        };
    }
};

// Function to add a new neighborhood
const addNewNeighborhood = async (neighborhoodName) => {
    if (!neighborhoodName) {
        return { success: false, message: 'يرجى إدخال اسم الحي' };
    }

    try {
        const { data, error } = await _supabase
            .from('neighborhoods')
            .insert([{ name: neighborhoodName }])
            .select();

        if (error) {
            console.error('Error adding neighborhood:', error);
            return {
                success: false,
                message: `خطأ في إضافة الحي: ${error.message}`
            };
        }

        return {
            success: true,
            message: 'تم إضافة الحي بنجاح',
            neighborhood: data[0]
        };
    } catch (error) {
        console.error('Error in addNewNeighborhood:', error);
        return {
            success: false,
            message: `حدث خطأ غير متوقع: ${error.message}`
        };
    }
};

// Function to populate the stage dropdown (using hardcoded data)
const populateStageDropdown = () => { // No longer async
    const stageDropdown = document.getElementById('stage');
    if (!stageDropdown) return;

    try {
        // Clear previous options except the default one
        stageDropdown.innerHTML = '<option value="" disabled selected>اختر المرحلة</option>';

        // Add options for each hardcoded stage
        stagesData.forEach(stage => {
            const option = document.createElement('option');
            option.value = stage.id; // Use the assigned ID
            option.textContent = stage.name;
            stageDropdown.appendChild(option);
        });

        console.log('Hardcoded stages loaded:', stagesData.length);
    } catch (error) {
        console.error('Error in populateStageDropdown:', error);
    }
};

// Function to update the School/University label based on Stage selection
const updateSchoolLabel = () => {
    const stageDropdown = document.getElementById('stage');
    const schoolLabel = document.getElementById('school-label'); // Target the label by ID
    const schoolDropdown = document.getElementById('school_id'); // Target the dropdown itself
    if (!stageDropdown || !schoolLabel || !schoolDropdown) return;

    const selectedStageId = stageDropdown.value;
    // Find the stage object from our hardcoded data
    const selectedStage = stagesData.find(s => s.id == selectedStageId); // Use == for potential type difference

    let labelText = 'المدرسة';
    let defaultOptionText = 'اختر المدرسة';
    let addOptionText = '✚ إضافة مدرسة جديدة';

    if (selectedStage && selectedStage.name === 'جامعة') {
        labelText = 'الجامعة';
        defaultOptionText = 'اختر الجامعة';
        addOptionText = '✚ إضافة جامعة جديدة';
    }

    schoolLabel.innerHTML = `${labelText} <span class="required">*</span>`;

    // Update the default option text in the school/university dropdown
    const defaultOption = schoolDropdown.querySelector('option[value=""]');
    if (defaultOption) {
        defaultOption.textContent = defaultOptionText;
    }

    // Update the "add new" option text
    const addNewOption = schoolDropdown.querySelector('option[value="add_new"]');
    if (addNewOption) {
        addNewOption.textContent = addOptionText;
    }

    // Populate the class dropdown based on the selected stage
    populateClassDropdown(selectedStageId);

    // Optionally, repopulate schools based on the selected stage if needed
    // populateSchoolDropdown(selectedStageId); // Uncomment if filtering is desired
};

// Function to populate the class dropdown based on selected stage
const populateClassDropdown = (stageId) => {
    const classDropdown = document.getElementById('class_id');
    if (!classDropdown) return;

    const classes = classesData[stageId] || []; // Get classes for the stage, or empty array

    // Clear previous options and set default
    classDropdown.innerHTML = '<option value="" disabled selected>اختر الصف</option>';

    if (classes.length > 0) {
        classes.forEach(cls => {
            const option = document.createElement('option');
            option.value = cls.id; // Use the defined ID
            option.textContent = cls.name;
            classDropdown.appendChild(option);
        });
        classDropdown.disabled = false; // Enable dropdown
    } else {
        // Disable if no stage selected or no classes for the stage
        classDropdown.disabled = true;
        classDropdown.innerHTML = '<option value="" disabled selected>اختر المرحلة أولاً</option>';
    }
    console.log(`Populated ${classes.length} classes for stage ID: ${stageId}`);
};

// Function to populate the school dropdown, filtered by stage if provided
const populateSchoolDropdown = async (stageId = null) => {
    const schoolDropdown = document.getElementById('school_id');
    if (!schoolDropdown) return;

    // Show loading indicator
    schoolDropdown.innerHTML = '<option value="" disabled selected>جاري تحميل المدارس...</option>';
    schoolDropdown.disabled = true; // Disable while loading

    // Fetch schools - if stageId is null, it fetches all schools
    const schools = await fetchSchools(stageId);

    // Check if schools array is valid
    if (!Array.isArray(schools)) {
        console.error('populateSchoolDropdown received invalid data:', schools);
        schoolDropdown.innerHTML = '<option value="" disabled selected>خطأ في تحميل المدارس</option>';
        schoolDropdown.disabled = false;
        return;
    }

    // Clear previous options and add default one
    // Determine the default text based on the current stage selection
    const stageDropdown = document.getElementById('stage');
    const selectedStage = stagesData.find(s => s.id == stageDropdown?.value); // Added safe navigation
    const defaultOptionText = (selectedStage && selectedStage.name === 'جامعة') ? 'اختر الجامعة' : 'اختر المدرسة';
    schoolDropdown.innerHTML = `<option value="" disabled selected>${defaultOptionText}</option>`;


    // Add option for adding a new school/university
    const addOptionText = (selectedStage && selectedStage.name === 'جامعة') ? '✚ إضافة جامعة جديدة' : '✚ إضافة مدرسة جديدة';
    const newOption = document.createElement('option');
    newOption.value = "add_new";
    newOption.textContent = addOptionText;
    newOption.className = "add-new-option";
    schoolDropdown.appendChild(newOption);

    // Add options for each fetched school
    schools.forEach(school => {
        if (school && school.id && school.name) { // Check if school object is valid
            const option = document.createElement('option');
            option.value = school.id;
            // Display only the school name
            option.textContent = school.name; // Simplified: Just show the name
            schoolDropdown.appendChild(option);
        } else {
            console.warn('Skipping invalid school data:', school);
        }
    });

    schoolDropdown.disabled = false; // Re-enable dropdown
    console.log(`Populated ${schools.length} schools/universities in dropdown for stage ID: ${stageId || 'all'}`);
};

// Function to populate the neighborhood dropdown
const populateNeighborhoodDropdown = async () => {
    const neighborhoodDropdown = document.getElementById('neighborhood_id');
    if (!neighborhoodDropdown) return;

    // Show loading indicator
    neighborhoodDropdown.innerHTML = '<option value="" disabled selected>جاري تحميل الأحياء...</option>';

    const neighborhoods = await fetchNeighborhoods();

    // Clear previous options and add default one
    neighborhoodDropdown.innerHTML = '<option value="" disabled selected>اختر الحي</option>';

    // Add option for adding a new neighborhood
    const newOption = document.createElement('option');
    newOption.value = "add_new";
    newOption.textContent = "✚ إضافة حي جديد";
    newOption.className = "add-new-option";
    neighborhoodDropdown.appendChild(newOption);

    // Add options for each neighborhood
    neighborhoods.forEach(neighborhood => {
        const option = document.createElement('option');
        option.value = neighborhood.id;
        option.textContent = neighborhood.name;
        neighborhoodDropdown.appendChild(option);
    });

    console.log(`Populated ${neighborhoods.length} neighborhoods in dropdown`);
};

// Function to show the new school form
const showNewSchoolForm = () => {
    // Create overlay for the mini-form
    const overlay = document.createElement('div');
    overlay.className = 'mini-form-overlay';

    // Create the form container
    const formContainer = document.createElement('div');
    formContainer.className = 'mini-form';

    formContainer.innerHTML = `
        <h3>إضافة مدرسة جديدة</h3>
        <div class="mini-form-group">
            <label for="new_school_name">اسم المدرسة:</label>
            <input type="text" id="new_school_name" placeholder="أدخل اسم المدرسة" required>
        </div>
        <div class="mini-form-group">
            <label for="new_school_stage">المرحلة الدراسية:</label>
            <select id="new_school_stage" required>
                <option value="" disabled selected>اختر المرحلة</option>
                <!-- Will be populated dynamically -->
            </select>
        </div>
        <div class="mini-form-actions">
            <button type="button" id="save_new_school_btn" class="mini-form-save">حفظ</button>
            <button type="button" id="cancel_new_school_btn" class="mini-form-cancel">إلغاء</button>
        </div>
    `;

    // Append the form to the overlay, and overlay to the body
    overlay.appendChild(formContainer);
    document.body.appendChild(overlay);

    // Populate the stage dropdown in the mini form
    const stageSelect = document.getElementById('new_school_stage');
    const mainStageSelect = document.getElementById('stage');

    // Copy options from the main stage dropdown
    if (mainStageSelect && stageSelect) {
        // Clone options from the main dropdown
        Array.from(mainStageSelect.options).forEach(option => {
            if (option.value !== "") { // Skip the default/placeholder option
                const newOption = document.createElement('option');
                newOption.value = option.value;
                newOption.textContent = option.textContent;
                stageSelect.appendChild(newOption);
            }
        });

        // Set the currently selected stage as default
        if (mainStageSelect.value) {
            stageSelect.value = mainStageSelect.value;
        }
    }

    // Event handlers for the mini form
    document.getElementById('save_new_school_btn').addEventListener('click', async () => {
        const schoolName = document.getElementById('new_school_name').value;
        const stageId = document.getElementById('new_school_stage').value;

        if (!schoolName || !stageId) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        const result = await addNewSchool(schoolName, stageId);

        if (result.success) {
            // Remove the overlay
            document.body.removeChild(overlay);

            // Re-populate schools dropdown and select the new school
            await populateSchoolDropdown(stageId);

            // Select the new school in the dropdown
            const schoolDropdown = document.getElementById('school_id');
            if (schoolDropdown && result.school) {
                schoolDropdown.value = result.school.id;
            }

            // Show success message
            alert(result.message);
        } else {
            alert(result.message);
        }
    });

    document.getElementById('cancel_new_school_btn').addEventListener('click', () => {
        document.body.removeChild(overlay);

        // Reset the school dropdown selection
        const schoolDropdown = document.getElementById('school_id');
        if (schoolDropdown) {
            schoolDropdown.selectedIndex = 0;
        }
    });

    // Also close when clicking outside the form
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            document.body.removeChild(overlay);
            // Reset the school dropdown selection
            const schoolDropdown = document.getElementById('school_id');
            if (schoolDropdown) {
                schoolDropdown.selectedIndex = 0;
            }
        }
    });

    // Focus on the school name input
    setTimeout(() => {
        document.getElementById('new_school_name').focus();
    }, 100);
};

// Function to show the new neighborhood form
const showNewNeighborhoodForm = () => {
    // Create overlay for the mini-form
    const overlay = document.createElement('div');
    overlay.className = 'mini-form-overlay';

    // Create the form container
    const formContainer = document.createElement('div');
    formContainer.className = 'mini-form';

    formContainer.innerHTML = `
        <h3>إضافة حي جديد</h3>
        <div class="mini-form-group">
            <label for="new_neighborhood_name">اسم الحي:</label>
            <input type="text" id="new_neighborhood_name" placeholder="أدخل اسم الحي" required>
        </div>
        <div class="mini-form-actions">
            <button type="button" id="save_new_neighborhood_btn" class="mini-form-save">حفظ</button>
            <button type="button" id="cancel_new_neighborhood_btn" class="mini-form-cancel">إلغاء</button>
        </div>
    `;

    // Append the form to the overlay, and overlay to the body
    overlay.appendChild(formContainer);
    document.body.appendChild(overlay);

    // Event handlers for the mini form
    document.getElementById('save_new_neighborhood_btn').addEventListener('click', async () => {
        const neighborhoodName = document.getElementById('new_neighborhood_name').value;

        if (!neighborhoodName) {
            alert('يرجى إدخال اسم الحي');
            return;
        }

        const result = await addNewNeighborhood(neighborhoodName);

        if (result.success) {
            // Remove the overlay
            document.body.removeChild(overlay);

            // Re-populate neighborhoods dropdown and select the new neighborhood
            await populateNeighborhoodDropdown();

            // Select the new neighborhood in the dropdown
            const neighborhoodDropdown = document.getElementById('neighborhood_id');
            if (neighborhoodDropdown && result.neighborhood) {
                neighborhoodDropdown.value = result.neighborhood.id;
            }

            // Show success message
            alert(result.message);
        } else {
            alert(result.message);
        }
    });

    document.getElementById('cancel_new_neighborhood_btn').addEventListener('click', () => {
        document.body.removeChild(overlay);

        // Reset the neighborhood dropdown selection
        const neighborhoodDropdown = document.getElementById('neighborhood_id');
        if (neighborhoodDropdown) {
            neighborhoodDropdown.selectedIndex = 0;
        }
    });

    // Also close when clicking outside the form
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            document.body.removeChild(overlay);
            // Reset the neighborhood dropdown selection
            const neighborhoodDropdown = document.getElementById('neighborhood_id');
            if (neighborhoodDropdown) {
                neighborhoodDropdown.selectedIndex = 0;
            }
        }
    });

    // Focus on the neighborhood name input
    setTimeout(() => {
        document.getElementById('new_neighborhood_name').focus();
    }, 100);
};

// Function to set up event listeners for school and neighborhood dropdowns
const setupDynamicDropdowns = () => {
    // Add event listener that filters schools based on stage selection
    const stageSelect = document.getElementById('stage');
    if (stageSelect) {
        stageSelect.addEventListener('change', async (e) => {
            const stageId = e.target.value;
            console.log('Stage changed to:', stageId);

            // Update school label and populate schools for selected stage
            updateSchoolLabel();
            await populateSchoolDropdown(stageId || null);

            // Update class dropdown based on stage
            populateClassDropdown(stageId);
        });
    }

    // Add event listener to school dropdown for "add new"
    const schoolDropdown = document.getElementById('school_id');
    if (schoolDropdown) {
        schoolDropdown.addEventListener('change', (e) => {
            if (e.target.value === 'add_new') {
                // Ensure a stage is selected before allowing adding a new school
                // Although filtering is removed, we still need the stage for adding a new school
                const selectedStageId = document.getElementById('stage').value;
                if (!selectedStageId) {
                    alert('يرجى اختيار المرحلة الدراسية أولاً قبل إضافة مدرسة جديدة.');
                    // Reset the school dropdown selection
                    schoolDropdown.selectedIndex = 0;
                    return;
                }
                showNewSchoolForm();
            }
        });
    }

    // Add event listener to neighborhood dropdown for "add new"
    const neighborhoodDropdown = document.getElementById('neighborhood_id');
    if (neighborhoodDropdown) {
        neighborhoodDropdown.addEventListener('change', (e) => {
            if (e.target.value === 'add_new') {
                showNewNeighborhoodForm();
            }
        });
    }
};

// Function to fetch students from Supabase
const fetchStudents = async (searchTerm = '') => {
    // Update colspan in the loading message
    studentsTableBody.innerHTML = '<tr><td colspan="7" class="loading-message">جاري التحميل...</td></tr>';
    paginationControls.innerHTML = ''; // Clear pagination while loading

    try {
        // Modify the query to fetch related school and neighborhood names
        let query = _supabase
            .from('students')
            .select(`
                *,
                schools ( name ),
                neighborhoods ( name )
            `); // Fetch all student columns AND the name from related tables

        // Add search condition if searchTerm is provided
        if (searchTerm) {
            // Update search to use 'name' column from students table
            query = query.or(`name.ilike.%${searchTerm}%,parent_phone.ilike.%${searchTerm}%`);
            // Note: Searching related tables requires more complex logic or database functions/views
        }

        // Order by most recently added first
        query = query.order('created_at', { ascending: false });

        const { data, error } = await query;

        if (error) {
            console.error('Supabase Fetch Error:', error);
            showMessage(listMessage, `خطأ في جلب البيانات: ${error.message}`, 'error');
            studentsTableBody.innerHTML = '<tr><td colspan="7" class="loading-message">خطأ في تحميل البيانات.</td></tr>';
            paginationControls.innerHTML = '';
        } else {
            console.log('Fetched students with relations:', data); // Log fetched data
            currentStudents = data;
            currentPage = 1; // Reset to first page
            renderStudentsTable(currentStudents);

            if (data.length === 0 && searchTerm) {
                showMessage(listMessage, `لا توجد نتائج للبحث عن "${searchTerm}".`, 'info');
            }

            // Update dashboard stats
            updateDashboardStats();
        }
    } catch (error) {
        console.error('JavaScript Fetch Error:', error);
        showMessage(listMessage, `حدث خطأ غير متوقع: ${error.message}`, 'error');
        studentsTableBody.innerHTML = '<tr><td colspan="7" class="loading-message">خطأ غير متوقع.</td></tr>';
        paginationControls.innerHTML = '';
    }
};

// Function to handle form submission (Add or Update)
const handleFormSubmit = async (event) => {
    event.preventDefault();
    showMessage(formMessage, 'جاري حفظ البيانات...', 'info');
    const submitBtn = studentForm.querySelector('.submit-btn');
    submitBtn.disabled = true;

    const formData = new FormData(studentForm);
    // Convert FormData to object, handle checkbox explicitly
    const studentData = {};
    formData.forEach((value, key) => {
        studentData[key] = value;
    });
    // Handle checkbox: if not checked, it won't be in formData
    studentData.is_active = formData.has('is_active');

    const studentId = studentData.student_id;

    // Basic validation
    if (!studentData.student_name || !studentData.stage || !studentData.school_id || !studentData.class_id || !studentData.neighborhood_id) {
        showMessage(formMessage, 'الرجاء ملء جميع الحقول المطلوبة (*).', 'error');
        submitBtn.disabled = false;
        return;
    }

    // ** File Upload Handling (Placeholder) **
    // const housePhotoFile = formData.get('house_photo');
    // let photoUrl = null;
    // if (housePhotoFile && housePhotoFile.size > 0) {
    //     // Implement Supabase Storage upload logic here
    //     // const filePath = `public/house_photos/${Date.now()}_${housePhotoFile.name}`;
    //     // const { data: uploadData, error: uploadError } = await _supabase.storage
    //     //     .from('your-bucket-name') // Replace with your bucket name
    //     //     .upload(filePath, housePhotoFile);
    //     // if (uploadError) { /* Handle error */ }
    //     // else { photoUrl = uploadData.path; }
    //     console.warn("File upload not implemented yet.");
    // }

    // Prepare data for Supabase
    const dataToUpsert = {
        name: studentData.student_name,
        parent_phone: studentData.parent_phone || null,
        student_phone: studentData.student_phone || null,
        is_active: studentData.is_active, // Use boolean from checkbox
        stage: studentData.stage, // Changed from stage_id
        school_id: studentData.school_id,
        grade: studentData.class_id, // <--- استخدم اسم العمود الصحيح هنا
        neighborhood_id: studentData.neighborhood_id,
        location_url: studentData.location_url || null,
        // house_photo_url: photoUrl, // Add this if implementing file upload
    };

    // Remove fields that might not exist in the 'students' table directly
    // delete dataToUpsert.stage; // If stage_id is used
    // delete dataToUpsert.school; // If school_id is used
    // delete dataToUpsert.class; // If class_id is used
    // delete dataToUpsert.neighborhood; // If neighborhood_id is used

    try {
        let result;
        if (editMode && studentId) {
            // Update existing student
            result = await _supabase
                .from('students')
                .update(dataToUpsert)
                .eq('id', studentId) // Assuming primary key is 'id'
                .select();
        } else {
            // Insert new student
            // delete dataToUpsert.student_id; // student_id is hidden input, not needed here
            result = await _supabase
                .from('students')
                .insert([dataToUpsert])
                .select();
        }

        const { data, error } = result;

        if (error) {
            console.error('Supabase Save Error:', error);
            showMessage(formMessage, `خطأ في الحفظ: ${error.message}`, 'error');
        } else {
            console.log('Supabase Save Success:', data);
            showMessage(formMessage, `تم ${editMode ? 'تحديث' : 'إضافة'} الطالب بنجاح!`, 'success');
            setTimeout(() => {
                toggleModal(false);
            }, 1500);
            fetchStudents(); // Refresh list
        }
    } catch (error) {
        console.error('JavaScript Save Error:', error);
        showMessage(formMessage, `حدث خطأ غير متوقع: ${error.message}`, 'error');
    } finally {
        submitBtn.disabled = false;
    }
};

// Function to reset the form and UI elements
const resetForm = () => {
    editMode = false;
    if (studentForm) { // Check if form exists
       studentForm.reset();
    }
    if (studentIdField) studentIdField.value = '';
    if (formMessage) formMessage.style.display = 'none';
    if (formTitle) formTitle.textContent = 'إضافة طالب جديد';
    const submitBtn = studentForm?.querySelector('.submit-btn');
    if (submitBtn) submitBtn.textContent = 'حفظ';
    // Reset checkbox state if needed
    const activeCheckbox = document.getElementById('is_active');
    if (activeCheckbox) activeCheckbox.checked = true;
    // Reset school label
    const schoolLabel = document.getElementById('school-label');
    if (schoolLabel) {
        schoolLabel.innerHTML = 'المدرسة <span class="required">*</span>';
    }
    // Reset dropdowns to default selection
    const stageDropdown = document.getElementById('stage');
    if (stageDropdown) stageDropdown.selectedIndex = 0;
    const schoolDropdown = document.getElementById('school_id');
    if (schoolDropdown) schoolDropdown.selectedIndex = 0;
    const classDropdown = document.getElementById('class_id');
    if (classDropdown) {
        classDropdown.innerHTML = '<option value="" disabled selected>اختر المرحلة أولاً</option>'; // Reset class dropdown
        classDropdown.disabled = true; // Disable it
    }
    const neighborhoodDropdown = document.getElementById('neighborhood_id');
    if (neighborhoodDropdown) neighborhoodDropdown.selectedIndex = 0;
};

// Function to populate form for editing
const handleEditStudent = (student) => {
    if (!student) return;
    toggleModal(true); // Open modal first
    editMode = true;

    formTitle.textContent = 'تعديل بيانات الطالب';
    studentForm.querySelector('.submit-btn').textContent = 'تحديث';

    // Populate form fields
    studentIdField.value = student.id || ''; // Assuming PK is 'id'
    document.getElementById('student_name').value = student.name || '';
    document.getElementById('parent_phone').value = student.parent_phone || '';
    document.getElementById('student_phone').value = student.student_phone || '';
    document.getElementById('is_active').checked = student.is_active === true;

    // Select dropdown values - needs IDs from the student object
    const stageValue = student.stage || ''; // Changed from stage_id
    document.getElementById('stage').value = stageValue;

    // Manually trigger the change event on stage dropdown AFTER setting its value
    // This ensures the school label AND class dropdown update correctly
    const stageChangeEvent = new Event('change');
    document.getElementById('stage').dispatchEvent(stageChangeEvent);

    // Populate class dropdown based on the stage (already done by the event trigger)
    // Now set the class value AFTER the dropdown is populated
    // Use setTimeout to ensure the dropdown population finishes
    setTimeout(() => {
        // استخدم اسم العمود الصحيح هنا أيضاً عند جلب البيانات للتعديل
        document.getElementById('class_id').value = student.grade || ''; // <--- استخدم اسم العمود الصحيح هنا
    }, 0); // Small delay might be needed if population is slow, but 0 often works

    document.getElementById('school_id').value = student.school_id || '';
    document.getElementById('neighborhood_id').value = student.neighborhood_id || '';

    document.getElementById('location_url').value = student.location_url || '';
    // Handle file input display if needed (e.g., show current image link)
};

// Function to handle student deletion
const handleDeleteStudent = async (studentId, studentName) => {
    const confirmDelete = confirm(`هل أنت متأكد من حذف الطالب "${studentName || 'هذا الطالب'}"؟ لا يمكن التراجع عن هذا الإجراء.`);

    if (confirmDelete) {
        try {
            const { error } = await _supabase
                .from('students')
                .delete()
                .eq('id', studentId); // Assuming PK is 'id'

            if (error) {
                console.error('Supabase Delete Error:', error);
                showMessage(listMessage, `خطأ في الحذف: ${error.message}`, 'error');
            } else {
                console.log('Student deleted:', studentId);
                showMessage(listMessage, `تم حذف الطالب بنجاح.`, 'success');
                fetchStudents();
            }
        } catch (error) {
            console.error('JavaScript Delete Error:', error);
            showMessage(listMessage, `حدث خطأ غير متوقع أثناء الحذف: ${error.message}`, 'error');
        }
    }
};

// Function to handle search
const handleSearch = () => {
    const searchTerm = searchInput.value.trim();
    fetchStudents(searchTerm);
};

// Function to handle printing report - NOW shows options modal
const handlePrintReport = () => {
    showPrintOptionsModal();
};

// Function to show the print options modal
const showPrintOptionsModal = async () => {
    // Create overlay
    const overlay = document.createElement('div');
    overlay.className = 'mini-form-overlay print-options-overlay'; // Reuse overlay style, add specific class

    // Create modal container
    const modal = document.createElement('div');
    modal.className = 'mini-form print-options-modal'; // Reuse mini-form style, add specific class

    modal.innerHTML = `
        <h3>خيارات طباعة التقرير</h3>
        <div class="mini-form-group">
            <label>اختر نوع التقرير:</label>
            <div class="radio-group">
                <label>
                    <input type="radio" name="reportType" value="all" checked> كل الطلاب (مرتب أبجدياً)
                </label>
                <label>
                    <input type="radio" name="reportType" value="school"> حسب المدرسة
                </label>
                <label>
                    <input type="radio" name="reportType" value="neighborhood"> حسب الحي
                </label>
            </div>
        </div>
        <div class="mini-form-group" id="school-select-group" style="display: none;">
            <label for="report_school_select">اختر المدرسة:</label>
            <select id="report_school_select">
                <option value="" disabled selected>جاري التحميل...</option>
            </select>
        </div>
        <div class="mini-form-group" id="neighborhood-select-group" style="display: none;">
            <label for="report_neighborhood_select">اختر الحي:</label>
            <select id="report_neighborhood_select">
                <option value="" disabled selected>جاري التحميل...</option>
            </select>
        </div>
        <div id="print-options-message" class="message" style="display: none;"></div>
        <div class="mini-form-actions">
            <button type="button" id="generate_report_btn" class="mini-form-save">إنشاء التقرير</button>
            <button type="button" id="cancel_print_options_btn" class="mini-form-cancel">إلغاء</button>
        </div>
    `;

    overlay.appendChild(modal);
    document.body.appendChild(overlay);

    const schoolSelectGroup = document.getElementById('school-select-group');
    const neighborhoodSelectGroup = document.getElementById('neighborhood-select-group');
    const schoolSelect = document.getElementById('report_school_select');
    const neighborhoodSelect = document.getElementById('report_neighborhood_select');
    const messageEl = document.getElementById('print-options-message');

    // --- Populate Dropdowns ---
    const populateModalDropdowns = async () => {
        // Populate Schools
        const schools = await fetchSchools(); // Fetch all schools
        schoolSelect.innerHTML = '<option value="" disabled selected>اختر المدرسة</option>';
        if (schools && schools.length > 0) {
            schools.sort((a, b) => a.name.localeCompare(b.name)).forEach(school => {
                const option = document.createElement('option');
                option.value = school.id;
                option.textContent = school.name;
                schoolSelect.appendChild(option);
            });
        } else {
            schoolSelect.innerHTML = '<option value="" disabled selected>لا توجد مدارس</option>';
        }

        // Populate Neighborhoods
        const neighborhoods = await fetchNeighborhoods(); // Fetch all neighborhoods
        neighborhoodSelect.innerHTML = '<option value="" disabled selected>اختر الحي</option>';
        if (neighborhoods && neighborhoods.length > 0) {
            neighborhoods.sort((a, b) => a.name.localeCompare(b.name)).forEach(neighborhood => {
                const option = document.createElement('option');
                option.value = neighborhood.id;
                option.textContent = neighborhood.name;
                neighborhoodSelect.appendChild(option);
            });
        } else {
            neighborhoodSelect.innerHTML = '<option value="" disabled selected>لا توجد أحياء</option>';
        }
    };

    populateModalDropdowns(); // Call population

    // --- Event Listeners for Radio Buttons ---
    const reportTypeRadios = modal.querySelectorAll('input[name="reportType"]');
    reportTypeRadios.forEach(radio => {
        radio.addEventListener('change', (e) => {
            const type = e.target.value;
            schoolSelectGroup.style.display = type === 'school' ? 'block' : 'none';
            neighborhoodSelectGroup.style.display = type === 'neighborhood' ? 'block' : 'none';
            messageEl.style.display = 'none'; // Hide message on change
        });
    });

    // --- Event Listener for Generate Button ---
    document.getElementById('generate_report_btn').addEventListener('click', () => {
        const selectedType = modal.querySelector('input[name="reportType"]:checked').value;
        let filterId = null;
        let filterName = null; // To display in report title

        messageEl.style.display = 'none'; // Reset message

        if (selectedType === 'school') {
            filterId = schoolSelect.value;
            if (!filterId) {
                showMessage(messageEl, 'يرجى اختيار مدرسة.', 'error');
                return;
            }
            filterName = schoolSelect.options[schoolSelect.selectedIndex].text;
        } else if (selectedType === 'neighborhood') {
            filterId = neighborhoodSelect.value;
            if (!filterId) {
                showMessage(messageEl, 'يرجى اختيار حي.', 'error');
                return;
            }
            filterName = neighborhoodSelect.options[neighborhoodSelect.selectedIndex].text;
        }

        generateAndPrintReport(selectedType, filterId, filterName);
        document.body.removeChild(overlay); // Close modal on success
    });

    // --- Event Listener for Cancel Button ---
    document.getElementById('cancel_print_options_btn').addEventListener('click', () => {
        document.body.removeChild(overlay);
    });

    // Close on overlay click
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            document.body.removeChild(overlay);
        }
    });
};

// Function to generate the report content based on filters and sorting
const generateAndPrintReport = (type, filterId = null, filterName = null) => {
    let studentsToPrint = [...currentStudents]; // Copy current students
    let reportTitle = 'تقرير الطلاب الشامل';

    // --- Filter Data ---
    if (type === 'school' && filterId) {
        studentsToPrint = studentsToPrint.filter(student => student.school_id == filterId);
        reportTitle = `تقرير طلاب مدرسة: ${filterName}`;
    } else if (type === 'neighborhood' && filterId) {
        studentsToPrint = studentsToPrint.filter(student => student.neighborhood_id == filterId);
        reportTitle = `تقرير طلاب حي: ${filterName}`;
    }

    // --- Sort Data ---
    studentsToPrint.sort((a, b) => a.name.localeCompare(b.name)); // Sort alphabetically by name

    // --- Generate Report HTML ---
    const printWindow = window.open('', '_blank', 'height=600,width=800');
    let reportContent = `
        <html dir="rtl">
        <head>
            <title>${reportTitle}</title>
            <style>
                body { font-family: 'Tajawal', sans-serif; padding: 20px; direction: rtl; }
                h1 { text-align: center; margin-bottom: 10px; color: #333; }
                .print-meta { display: flex; justify-content: space-between; margin-bottom: 20px; font-size: 0.9em; color: #555; }
                table { width: 100%; border-collapse: collapse; margin-top: 15px; }
                th, td { padding: 10px; text-align: right; border: 1px solid #ddd; }
                th { background-color: #f2f2f2; font-weight: bold; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                .status-active { color: #2ecc71; font-weight: bold; }
                .status-inactive { color: #e74c3c; font-weight: bold; }
                @media print {
                    .no-print { display: none; }
                    body { padding: 5px; } /* Reduce padding for print */
                    h1 { font-size: 1.5em; }
                }
            </style>
        </head>
        <body>
            <div class="print-meta">
                <span>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</span>
                <span>عدد الطلاب في التقرير: ${studentsToPrint.length}</span>
            </div>
            <h1>${reportTitle}</h1>
            ${studentsToPrint.length === 0 ? '<p style="text-align: center; color: #888;">لا يوجد طلاب لعرضهم حسب المعايير المحددة.</p>' : `
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الطالب</th>
                        <th>رقم ولي الأمر</th>
                        <th>المرحلة</th>
                        <th>الصف</th>
                        <th>المدرسة</th>
                        <th>الحي</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
            `}
    `;

    // Add student rows
    studentsToPrint.forEach((student, index) => {
        // Find stage name from hardcoded data
        const stageObj = stagesData.find(s => s.id == student.stage);
        const stageName = stageObj ? stageObj.name : 'غير محدد';

        // Find class name from hardcoded data
        const classObj = stageObj && classesData[stageObj.id] ? classesData[stageObj.id].find(c => c.id == student.grade) : null;
        const className = classObj ? classObj.name : student.grade || 'غير محدد'; // Fallback to grade ID if name not found

        // Get related names safely from the fetched data
        const schoolName = student.schools?.name || 'غير محدد'; // Use related school name
        const neighborhoodName = student.neighborhoods?.name || 'غير محدد'; // Use related neighborhood name

        let statusClass = student.is_active ? 'status-active' : 'status-inactive';
        let statusText = student.is_active ? 'نشط' : 'غير نشط';

        reportContent += `
            <tr>
                <td>${index + 1}</td>
                <td>${student.name || 'غير محدد'}</td>
                <td>${student.parent_phone || 'غير محدد'}</td>
                <td>${stageName}</td>
                <td>${className}</td>
                <td>${schoolName}</td>
                <td>${neighborhoodName}</td>
                <td><span class="${statusClass}">${statusText}</span></td>
            </tr>
        `;
    });

    // Complete the HTML if there were students
    if (studentsToPrint.length > 0) {
        reportContent += `
                </tbody>
            </table>
        `;
    }

    reportContent += `
            <div class="no-print" style="margin-top: 20px; text-align: center;">
                <button onclick="window.print()">طباعة</button>
                <button onclick="window.close()">إغلاق</button>
            </div>
        </body>
        </html>
    `;

    // Write to the new window and initiate print
    printWindow.document.write(reportContent);
    printWindow.document.close();
    // Optional: Focus and print automatically
    // printWindow.focus();
    // setTimeout(() => { printWindow.print(); }, 500); // Delay print slightly
};


// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOM loaded, initializing application...');

    // Add necessary styles
    addStatusStyles();
    addMiniFormStyles();

    // Set up event listeners (including the new stage listener)
    setupEventListeners();

    // Populate dropdowns - Populate schools initially without filter (shows all schools)
    populateStageDropdown(); // Use the modified function (no await needed)
    await populateSchoolDropdown(); // Call without stageId to load all schools initially
    await populateNeighborhoodDropdown();

    // Disable class dropdown initially
    const classDropdown = document.getElementById('class_id');
    if (classDropdown) {
        classDropdown.innerHTML = '<option value="" disabled selected>اختر المرحلة أولاً</option>';
        classDropdown.disabled = true;
    }

    // Set up event listeners for dynamic behavior (excluding stage->school filtering)
    setupDynamicDropdowns();

    // Fetch students
    fetchStudents();
});

// Function to handle modal toggling
function toggleModal(show = true) {
    console.log('Modal toggled:', show ? 'show' : 'hide');

    if (show) {
        // Make sure the form is reset before showing the modal
        resetForm();

        // Show the modal with animation
        addStudentSection.classList.add('show');

        // Focus on first input field
        setTimeout(() => {
            const nameInput = document.getElementById('student_name');
            if (nameInput) nameInput.focus();
        }, 300); // slight delay to allow animation to complete

        // Disable scrolling on the body
        document.body.style.overflow = 'hidden';
    } else {
        // Hide modal
        addStudentSection.classList.remove('show');

        // Re-enable scrolling
        document.body.style.overflow = '';
    }
}

// Function to add CSS for the mini forms AND print options modal
const addMiniFormStyles = () => {
    const style = document.createElement('style');
    style.textContent = `
        /* ... existing mini-form-overlay and mini-form styles ... */
        .mini-form-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000; /* Ensure it's above other content */
        }

        .mini-form {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            width: 100%;
            max-width: 450px; /* Slightly wider for options */
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            direction: rtl;
        }

        .mini-form h3 {
            margin-top: 0;
            margin-bottom: 20px; /* More space */
            color: var(--primary-dark);
            font-size: 1.2rem;
            text-align: center;
        }

        .mini-form-group {
            margin-bottom: 15px;
        }

        .mini-form-group label {
            display: block;
            margin-bottom: 8px; /* More space */
            font-weight: 500;
        }

        .mini-form-group input,
        .mini-form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 0.95rem;
        }

        /* Styles specific to print options modal */
        .print-options-modal .radio-group {
            display: flex;
            flex-direction: column;
            gap: 10px; /* Space between radio buttons */
            margin-top: 5px;
        }
        .print-options-modal .radio-group label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-weight: normal; /* Normal weight for radio labels */
            margin-bottom: 0;
        }
        .print-options-modal .radio-group input[type="radio"] {
            width: auto; /* Default radio size */
            margin-left: 8px; /* Space between radio and text */
            accent-color: var(--primary-color);
        }

        .mini-form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 25px; /* More space before actions */
        }

        .mini-form-actions button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }

        .mini-form-save {
            background-color: var(--success-color);
            color: white;
        }

        .mini-form-cancel {
            background-color: var(--light-color);
            color: var(--text-dark);
        }

        .add-new-option {
            color: var(--primary-dark);
            font-weight: bold;
            background-color: #f0f8ff;
        }

        /* Message style within modal */
        .print-options-modal .message {
             margin-top: 0;
             margin-bottom: 15px;
        }
    `;
    document.head.appendChild(style);
};

// --- Add CSS for status indicators and pagination ---
const addStatusStyles = () => {
    const style = document.createElement('style');
    style.textContent = `
        .status-active { color: #2ecc71; font-weight: bold; }
        .status-inactive { color: #e74c3c; font-weight: bold; }
        .status-pending { color: #f39c12; font-weight: bold; }

        /* Additional pagination styles */
        .pagination-ellipsis {
            padding: 8px 5px;
            color: var(--text-muted);
        }
        .pagination button.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .pagination button.active {
            background-color: var(--primary-color);
            color: var(--text-light);
            font-weight: bold;
        }

        /* Style for location button */
        .location-btn {
            color: var(--primary-color);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border: 1px solid var(--primary-color);
            border-radius: 4px;
        }
        .location-btn:hover {
            background-color: rgba(52, 152, 219, 0.1);
            text-decoration: none;
        }
        .location-btn i {
             margin-left: 4px; /* RTL */
        }
    `;
    document.head.appendChild(style);
};

// --- Event Listeners ---

// Setup event listeners
const setupEventListeners = () => {
    if (studentForm) {
        studentForm.addEventListener('submit', handleFormSubmit);
    }

    // Add listener for stage change to update school label
    const stageSelect = document.getElementById('stage');
    if (stageSelect) {
        stageSelect.addEventListener('change', updateSchoolLabel);
    }

    if (addStudentBtn) {
        addStudentBtn.addEventListener('click', () => {
            editMode = false;
            toggleModal(true);
        });
    }

    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', () => {
            toggleModal(false);
        });
    }

    if (closeFormBtn) {
        closeFormBtn.addEventListener('click', () => {
            toggleModal(false);
        });
    }

    if (addStudentSection) {
        addStudentSection.addEventListener('click', (event) => {
            if (event.target === addStudentSection) {
                toggleModal(false);
            }
        });
    }

    if (searchBtn) {
        searchBtn.addEventListener('click', handleSearch);
    }

    if (searchInput) {
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
    }

    if (printReportBtn) {
        printReportBtn.addEventListener('click', handlePrintReport);
    }

    // Escape key listener for modal
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && addStudentSection && addStudentSection.classList.contains('show')) {
            toggleModal(false);
        }
    });
};

// ===== إدارة الشاشات المنبثقة الجديدة =====

// DOM Elements for new modals
const schoolsCard = document.getElementById('schools-card');
const neighborhoodsCard = document.getElementById('neighborhoods-card');
const schoolsModal = document.getElementById('schools-modal');
const neighborhoodsModal = document.getElementById('neighborhoods-modal');
const closeSchoolsModal = document.getElementById('close-schools-modal');
const closeNeighborhoodsModal = document.getElementById('close-neighborhoods-modal');
const totalNeighborhoodsEl = document.getElementById('total-neighborhoods');

// School management elements
const schoolForm = document.getElementById('school-form');
const schoolsTableBody = document.getElementById('schools-tbody');
const cancelSchoolEdit = document.getElementById('cancel-school-edit');

// Neighborhood management elements
const neighborhoodForm = document.getElementById('neighborhood-form');
const neighborhoodsTableBody = document.getElementById('neighborhoods-tbody');
const cancelNeighborhoodEdit = document.getElementById('cancel-neighborhood-edit');

// State for editing
let editingSchoolId = null;
let editingNeighborhoodId = null;

// Function to show modal
const showModal = (modal) => {
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';
};

// Function to hide modal
const hideModal = (modal) => {
    modal.classList.remove('show');
    document.body.style.overflow = 'auto';
};

// Function to load schools for management
const loadSchoolsForManagement = async () => {
    try {
        const schools = await fetchSchools();
        renderSchoolsTable(schools);
    } catch (error) {
        console.error('Error loading schools for management:', error);
    }
};

// Function to render schools table
const renderSchoolsTable = (schools) => {
    schoolsTableBody.innerHTML = '';

    if (!schools || schools.length === 0) {
        schoolsTableBody.innerHTML = '<tr><td colspan="3" class="loading-message">لا توجد مدارس مسجلة</td></tr>';
        return;
    }

    schools.forEach(school => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${school.name}</td>
            <td>${school.stage}</td>
            <td>
                <button class="manage-action-btn edit-btn" data-id="${school.id}" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="manage-action-btn delete-btn" data-id="${school.id}" title="حذف">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </td>
        `;

        // Add event listeners
        row.querySelector('.edit-btn').addEventListener('click', () => editSchool(school));
        row.querySelector('.delete-btn').addEventListener('click', () => deleteSchool(school.id, school.name));

        schoolsTableBody.appendChild(row);
    });
};

// Function to load neighborhoods for management
const loadNeighborhoodsForManagement = async () => {
    try {
        const neighborhoods = await fetchNeighborhoods();
        renderNeighborhoodsTable(neighborhoods);

        // Update neighborhoods count
        totalNeighborhoodsEl.textContent = neighborhoods.length;
    } catch (error) {
        console.error('Error loading neighborhoods for management:', error);
    }
};

// Function to render neighborhoods table
const renderNeighborhoodsTable = (neighborhoods) => {
    neighborhoodsTableBody.innerHTML = '';

    if (!neighborhoods || neighborhoods.length === 0) {
        neighborhoodsTableBody.innerHTML = '<tr><td colspan="2" class="loading-message">لا توجد أحياء مسجلة</td></tr>';
        return;
    }

    neighborhoods.forEach(neighborhood => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${neighborhood.name}</td>
            <td>
                <button class="manage-action-btn edit-btn" data-id="${neighborhood.id}" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="manage-action-btn delete-btn" data-id="${neighborhood.id}" title="حذف">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </td>
        `;

        // Add event listeners
        row.querySelector('.edit-btn').addEventListener('click', () => editNeighborhood(neighborhood));
        row.querySelector('.delete-btn').addEventListener('click', () => deleteNeighborhood(neighborhood.id, neighborhood.name));

        neighborhoodsTableBody.appendChild(row);
    });
};

// Function to edit school
const editSchool = (school) => {
    editingSchoolId = school.id;
    document.getElementById('school_edit_id').value = school.id;
    document.getElementById('school_name').value = school.name;

    // Convert stage name back to stage ID for the dropdown
    const stageOption = stagesData.find(s => s.name === school.stage);
    if (stageOption) {
        document.getElementById('school_stage').value = stageOption.id;
    }

    // Change button text
    const submitBtn = schoolForm.querySelector('.submit-btn');
    submitBtn.innerHTML = '<i class="fas fa-save"></i> تحديث';

    // Show cancel button
    cancelSchoolEdit.style.display = 'inline-block';
};

// Function to edit neighborhood
const editNeighborhood = (neighborhood) => {
    editingNeighborhoodId = neighborhood.id;
    document.getElementById('neighborhood_edit_id').value = neighborhood.id;
    document.getElementById('neighborhood_name').value = neighborhood.name;

    // Change button text
    const submitBtn = neighborhoodForm.querySelector('.submit-btn');
    submitBtn.innerHTML = '<i class="fas fa-save"></i> تحديث';

    // Show cancel button
    cancelNeighborhoodEdit.style.display = 'inline-block';
};

// Function to reset school form
const resetSchoolForm = () => {
    editingSchoolId = null;
    schoolForm.reset();
    document.getElementById('school_edit_id').value = '';

    // Reset button text
    const submitBtn = schoolForm.querySelector('.submit-btn');
    submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ';

    // Hide cancel button
    cancelSchoolEdit.style.display = 'none';
};

// Function to reset neighborhood form
const resetNeighborhoodForm = () => {
    editingNeighborhoodId = null;
    neighborhoodForm.reset();
    document.getElementById('neighborhood_edit_id').value = '';

    // Reset button text
    const submitBtn = neighborhoodForm.querySelector('.submit-btn');
    submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ';

    // Hide cancel button
    cancelNeighborhoodEdit.style.display = 'none';
};

// Function to delete school
const deleteSchool = async (schoolId, schoolName) => {
    if (!confirm(`هل أنت متأكد من حذف المدرسة "${schoolName}"؟`)) {
        return;
    }

    try {
        const { error } = await _supabase
            .from('schools')
            .delete()
            .eq('id', schoolId);

        if (error) {
            alert(`خطأ في حذف المدرسة: ${error.message}`);
            return;
        }

        alert('تم حذف المدرسة بنجاح');
        await loadSchoolsForManagement();
        await updateDashboardStats();
        await populateSchoolDropdown(); // Refresh dropdown
    } catch (error) {
        console.error('Error deleting school:', error);
        alert('حدث خطأ أثناء حذف المدرسة');
    }
};

// Function to delete neighborhood
const deleteNeighborhood = async (neighborhoodId, neighborhoodName) => {
    if (!confirm(`هل أنت متأكد من حذف الحي "${neighborhoodName}"؟`)) {
        return;
    }

    try {
        const { error } = await _supabase
            .from('neighborhoods')
            .delete()
            .eq('id', neighborhoodId);

        if (error) {
            alert(`خطأ في حذف الحي: ${error.message}`);
            return;
        }

        alert('تم حذف الحي بنجاح');
        await loadNeighborhoodsForManagement();
        await populateNeighborhoodDropdown(); // Refresh dropdown
    } catch (error) {
        console.error('Error deleting neighborhood:', error);
        alert('حدث خطأ أثناء حذف الحي');
    }
};

// Event Listeners for new functionality

// Schools card click
schoolsCard?.addEventListener('click', () => {
    showModal(schoolsModal);
    loadSchoolsForManagement();
});

// Neighborhoods card click
neighborhoodsCard?.addEventListener('click', () => {
    showModal(neighborhoodsModal);
    loadNeighborhoodsForManagement();
});

// Close modals
closeSchoolsModal?.addEventListener('click', () => {
    hideModal(schoolsModal);
    resetSchoolForm();
});

closeNeighborhoodsModal?.addEventListener('click', () => {
    hideModal(neighborhoodsModal);
    resetNeighborhoodForm();
});

// Cancel edit buttons
cancelSchoolEdit?.addEventListener('click', resetSchoolForm);
cancelNeighborhoodEdit?.addEventListener('click', resetNeighborhoodForm);

// School form submission
schoolForm?.addEventListener('submit', async (e) => {
    e.preventDefault();

    const schoolName = document.getElementById('school_name').value.trim();
    const schoolStage = document.getElementById('school_stage').value;

    if (!schoolName || !schoolStage) {
        alert('يرجى إدخال جميع البيانات المطلوبة');
        return;
    }

    try {
        if (editingSchoolId) {
            // Update existing school
            const { error } = await _supabase
                .from('schools')
                .update({
                    name: schoolName,
                    stage: schoolStage
                })
                .eq('id', editingSchoolId);

            if (error) {
                alert(`خطأ في تحديث المدرسة: ${error.message}`);
                return;
            }

            alert('تم تحديث المدرسة بنجاح');
        } else {
            // Add new school
            const result = await addNewSchool(schoolName, schoolStage);

            if (!result.success) {
                alert(result.message);
                return;
            }

            alert(result.message);
        }

        resetSchoolForm();
        await loadSchoolsForManagement();
        await updateDashboardStats();
        await populateSchoolDropdown(); // Refresh dropdown
    } catch (error) {
        console.error('Error saving school:', error);
        alert('حدث خطأ أثناء حفظ المدرسة');
    }
});

// Neighborhood form submission
neighborhoodForm?.addEventListener('submit', async (e) => {
    e.preventDefault();

    const neighborhoodName = document.getElementById('neighborhood_name').value.trim();

    if (!neighborhoodName) {
        alert('يرجى إدخال اسم الحي');
        return;
    }

    try {
        if (editingNeighborhoodId) {
            // Update existing neighborhood
            const { error } = await _supabase
                .from('neighborhoods')
                .update({ name: neighborhoodName })
                .eq('id', editingNeighborhoodId);

            if (error) {
                alert(`خطأ في تحديث الحي: ${error.message}`);
                return;
            }

            alert('تم تحديث الحي بنجاح');
        } else {
            // Add new neighborhood
            const result = await addNewNeighborhood(neighborhoodName);

            if (!result.success) {
                alert(result.message);
                return;
            }

            alert(result.message);
        }

        resetNeighborhoodForm();
        await loadNeighborhoodsForManagement();
        await populateNeighborhoodDropdown(); // Refresh dropdown
    } catch (error) {
        console.error('Error saving neighborhood:', error);
        alert('حدث خطأ أثناء حفظ الحي');
    }
});

// Close modals when clicking outside
schoolsModal?.addEventListener('click', (e) => {
    if (e.target === schoolsModal) {
        hideModal(schoolsModal);
        resetSchoolForm();
    }
});

neighborhoodsModal?.addEventListener('click', (e) => {
    if (e.target === neighborhoodsModal) {
        hideModal(neighborhoodsModal);
        resetNeighborhoodForm();
    }
});

// ===== نظام عرض الكروت المبسط =====

// Global variables for cards view
let allStudents = [];
let currentViewMode = 'table';

// DOM elements for view switching (will be initialized after DOM loads)
let tableView = null;
let cardsView = null;
let cardsGrid = null;
let cardsTitle = null;
let cardsCount = null;

// Form elements and state (these are already defined at the top of the file)
// let editMode = false; - already defined
// let addStudentSection = null; - already defined as DOM element
// let studentForm = null; - already defined as DOM element
// let formTitle = null; - already defined as DOM element
// let formMessage = null; - already defined as DOM element
// let studentIdField = null; - already defined as DOM element

// ===== وظائف الشاشة المنبثقة لخيارات الكروت =====

// Function to show cards options modal
const showCardsOptionsModal = () => {
    const modal = document.getElementById('cards-options-modal');
    if (modal) {
        modal.classList.add('show');
        updateCardsOptionsStats();
    }
};

// Function to hide cards options modal
const hideCardsOptionsModal = () => {
    const modal = document.getElementById('cards-options-modal');
    if (modal) {
        modal.classList.remove('show');
    }
};

// Function to update stats in cards options
const updateCardsOptionsStats = () => {
    if (!allStudents || !allStudents.length) return;

    // Count schools
    const schools = [...new Set(allStudents.map(s => s.school_name).filter(Boolean))];
    const schoolsStats = document.getElementById('schools-stats');
    if (schoolsStats) {
        schoolsStats.querySelector('.stat-number').textContent = schools.length;
    }

    // Count neighborhoods
    const neighborhoods = [...new Set(allStudents.map(s => s.neighborhood_name).filter(Boolean))];
    const neighborhoodsStats = document.getElementById('neighborhoods-stats');
    if (neighborhoodsStats) {
        neighborhoodsStats.querySelector('.stat-number').textContent = neighborhoods.length;
    }

    // Count sibling groups
    const siblingsGroups = findSiblingGroups(allStudents);
    const siblingsStats = document.getElementById('siblings-stats');
    if (siblingsStats) {
        siblingsStats.querySelector('.stat-number').textContent = Object.keys(siblingsGroups).length;
    }

    // Count total students
    const tableStats = document.getElementById('table-stats');
    if (tableStats) {
        tableStats.querySelector('.stat-number').textContent = allStudents.length;
    }
};

// Function to find sibling groups
const findSiblingGroups = (students) => {
    const phoneGroups = {};

    students.forEach(student => {
        if (student.parent_phone) {
            const phone = student.parent_phone.trim();
            if (!phoneGroups[phone]) {
                phoneGroups[phone] = [];
            }
            phoneGroups[phone].push(student);
        }
    });

    // Only keep groups with more than 1 student
    Object.keys(phoneGroups).forEach(phone => {
        if (phoneGroups[phone].length <= 1) {
            delete phoneGroups[phone];
        }
    });

    return phoneGroups;
};

// ===== وظائف العرض المبسطة =====

// Function to render students based on view mode
const renderStudents = (viewMode = 'table') => {
    if (!allStudents || !allStudents.length) return;

    currentViewMode = viewMode;

    if (viewMode === 'table') {
        renderTableView(allStudents);
    } else {
        renderCardsView(allStudents, viewMode);
    }
};

// Function to switch between table and cards view
const switchView = (viewMode) => {
    currentViewMode = viewMode;

    // Make sure DOM elements are available
    if (!tableView || !cardsView) {
        console.error('View elements not initialized');
        return;
    }

    if (viewMode === 'table') {
        tableView.style.display = 'block';
        cardsView.style.display = 'none';
        renderTableView(allStudents || []);
    } else {
        tableView.style.display = 'none';
        cardsView.style.display = 'block';
        renderCardsView(allStudents || [], viewMode);
    }

    // Hide the cards options modal
    hideCardsOptionsModal();
};

// Function to render table view
const renderTableView = (students) => {
    const tbody = document.getElementById('students-tbody');
    const studentsCount = document.getElementById('students-count');

    if (!tbody) {
        console.error('Table tbody not found');
        return;
    }

    // Clear existing content
    tbody.innerHTML = '';

    if (!students || students.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="loading-message">لا توجد نتائج تطابق الفلاتر المحددة</td></tr>';
        if (studentsCount) studentsCount.textContent = '0';
        return;
    }

    // Render students in table format
    students.forEach(student => {
        const row = createTableRow(student);
        tbody.appendChild(row);
    });

    if (studentsCount) studentsCount.textContent = students.length;
};

// Function to create table row
const createTableRow = (student) => {
    const row = document.createElement('tr');
    row.className = 'student-row';

    row.innerHTML = `
        <td>${student.student_name}</td>
        <td>${student.parent_phone || 'غير محدد'}</td>
        <td>${student.school_name || 'غير محدد'}</td>
        <td>${student.neighborhood_name || 'غير محدد'}</td>
        <td>
            <span class="status-badge ${student.is_active ? 'active' : 'inactive'}">
                ${student.is_active ? 'نشط' : 'غير نشط'}
            </span>
        </td>
        <td>
            ${student.location_url ?
                `<a href="${student.location_url}" target="_blank" class="location-link">
                    <i class="fas fa-map-marker-alt"></i>
                </a>` :
                '<span class="no-location">لا يوجد</span>'
            }
        </td>
        <td>
            <div class="action-buttons">
                <button class="action-btn edit-btn" data-id="${student.id}" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" data-id="${student.id}" title="حذف">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </div>
        </td>
    `;

    // Add event listeners for action buttons
    row.querySelector('.edit-btn').addEventListener('click', () => editStudent(student.id));
    row.querySelector('.delete-btn').addEventListener('click', () => deleteStudent(student.id, student.student_name));

    return row;
};

// Function to update filter results info
const updateFilterResultsInfo = (filteredCount, totalCount) => {
    let infoElement = document.querySelector('.filter-results-info');

    if (!infoElement) {
        infoElement = document.createElement('div');
        infoElement.className = 'filter-results-info';
        const tableCard = document.querySelector('.table-card .card-body') || document.querySelector('.cards-container');
        if (tableCard) {
            tableCard.insertBefore(infoElement, tableCard.firstChild);
        }
    }

    if (filteredCount === totalCount) {
        infoElement.style.display = 'none';
    } else {
        infoElement.style.display = 'block';
        infoElement.innerHTML = `
            عرض <span class="highlight">${filteredCount}</span> من أصل
            <span class="highlight">${totalCount}</span> طالب
        `;
    }
};

// Function to render cards view
const renderCardsView = (students, viewMode) => {
    if (!cardsGrid) {
        console.error('Cards grid not found');
        return;
    }

    // Clear existing content
    cardsGrid.innerHTML = '';

    if (!students || students.length === 0) {
        cardsGrid.innerHTML = `
            <div class="empty-cards-state">
                <i class="fas fa-inbox"></i>
                <h3>لا توجد نتائج</h3>
                <p>لا توجد نتائج تطابق الفلاتر المحددة</p>
            </div>
        `;
        if (cardsCount) cardsCount.textContent = '0';
        return;
    }

    // Group students based on view mode
    const groups = groupStudents(students, viewMode);

    // Update title and count
    updateCardsHeader(viewMode, Object.keys(groups).length);

    // Render cards
    Object.entries(groups).forEach(([groupKey, groupStudents]) => {
        const card = createGroupCard(groupKey, groupStudents, viewMode);
        cardsGrid.appendChild(card);
    });

    if (cardsCount) cardsCount.textContent = Object.keys(groups).length;
};

// Function to group students based on view mode
const groupStudents = (students, viewMode) => {
    const groups = {};

    students.forEach(student => {
        let groupKey;

        switch (viewMode) {
            case 'cards-school':
                groupKey = student.school_name || 'مدرسة غير محددة';
                break;
            case 'cards-neighborhood':
                groupKey = student.neighborhood_name || 'حي غير محدد';
                break;
            case 'cards-stage':
                groupKey = student.stage || 'مرحلة غير محددة';
                break;
            case 'cards-siblings':
                groupKey = student.parent_phone || 'رقم غير محدد';
                break;
            default:
                groupKey = 'مجموعة عامة';
        }

        if (!groups[groupKey]) {
            groups[groupKey] = [];
        }
        groups[groupKey].push(student);
    });

    // For siblings view, only keep groups with more than 1 student
    if (viewMode === 'cards-siblings') {
        Object.keys(groups).forEach(key => {
            if (groups[key].length <= 1) {
                delete groups[key];
            }
        });
    }

    return groups;
};

// Function to extract family name from student name
const extractFamilyName = (studentName) => {
    if (!studentName) return 'غير محدد';

    const nameParts = studentName.trim().split(' ');
    // Return the second part (family name) if exists, otherwise return the first part
    return nameParts.length > 1 ? nameParts[1] : nameParts[0];
};

// Function to update cards header
const updateCardsHeader = (viewMode, groupCount) => {
    if (!cardsTitle) return;

    let title;

    switch (viewMode) {
        case 'cards-school':
            title = 'كروت المدارس';
            break;
        case 'cards-neighborhood':
            title = 'كروت الأحياء';
            break;
        case 'cards-stage':
            title = 'كروت المراحل';
            break;
        case 'cards-siblings':
            title = 'كروت الأخوان';
            break;
        default:
            title = 'عرض الكروت';
    }

    cardsTitle.textContent = title;
};

// Function to create group card
const createGroupCard = (groupKey, students, viewMode) => {
    const card = document.createElement('div');
    card.className = 'group-card';

    // Determine card type for styling
    let cardType = '';
    switch (viewMode) {
        case 'cards-school':
            cardType = 'school-card';
            break;
        case 'cards-neighborhood':
            cardType = 'neighborhood-card';
            break;
        case 'cards-stage':
            cardType = 'stage-card';
            break;
        case 'cards-siblings':
            cardType = 'siblings-card';
            break;
    }

    // Get group info
    const groupInfo = getGroupInfo(students, viewMode);

    // For siblings cards, create special header with family name
    let cardTitle = groupKey;
    let cardSubtitle = '';

    if (viewMode === 'cards-siblings' && students.length > 0) {
        // Extract family name from any student in the group
        const familyName = extractFamilyName(students[0].student_name || students[0].name);
        cardTitle = groupKey; // Phone number
        cardSubtitle = `أبناء ${familyName}`;
    }

    card.innerHTML = `
        <div class="group-card-header ${cardType}">
            <div class="header-content">
                <h3 class="group-card-title">${cardTitle}</h3>
                ${cardSubtitle ? `<div class="group-card-subtitle">${cardSubtitle}</div>` : ''}
            </div>
            <span class="group-card-count">${students.length} طالب</span>
        </div>
        <div class="group-card-body">
            ${groupInfo ? `<div class="group-info">${groupInfo}</div>` : ''}
            <div class="card-students-list">
                ${students.map(student => createStudentCardItem(student)).join('')}
            </div>
        </div>
        <div class="group-card-footer">
            <button class="add-student-to-group-btn" data-group="${groupKey}" data-view-mode="${viewMode}">
                <i class="fas fa-plus"></i>
                إضافة طالب جديد
            </button>
        </div>
    `;

    // Add event listeners for student actions
    card.querySelectorAll('.mini-action-btn.edit').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const studentId = e.target.closest('.mini-action-btn').dataset.studentId;
            editStudent(studentId);
        });
    });

    card.querySelectorAll('.mini-action-btn.delete').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const studentId = e.target.closest('.mini-action-btn').dataset.studentId;
            const studentName = e.target.closest('.mini-action-btn').dataset.studentName;
            deleteStudent(studentId, studentName);
        });
    });

    // Add event listener for add student button
    card.querySelector('.add-student-to-group-btn').addEventListener('click', (e) => {
        const groupKey = e.target.dataset.group;
        const viewMode = e.target.dataset.viewMode;
        openAddStudentWithGroupInfo(groupKey, viewMode, students[0]);
    });

    return card;
};

// Function to get group info for cards
const getGroupInfo = (students, viewMode) => {
    if (students.length === 0) return '';

    const firstStudent = students[0];
    let info = '';

    switch (viewMode) {
        case 'cards-school':
            info = `
                <div class="info-item">
                    <span class="info-label">المرحلة:</span>
                    <span>${firstStudent.stage || 'غير محددة'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">عدد الطلاب:</span>
                    <span>${students.length}</span>
                </div>
            `;
            break;
        case 'cards-neighborhood':
            info = `
                <div class="info-item">
                    <span class="info-label">عدد الطلاب:</span>
                    <span>${students.length}</span>
                </div>
            `;
            break;
        case 'cards-stage':
            const schools = [...new Set(students.map(s => s.school_name).filter(Boolean))];
            info = `
                <div class="info-item">
                    <span class="info-label">عدد المدارس:</span>
                    <span>${schools.length}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">عدد الطلاب:</span>
                    <span>${students.length}</span>
                </div>
            `;
            break;
        case 'cards-siblings':
            info = `
                <div class="info-item">
                    <span class="info-label">رقم ولي الأمر:</span>
                    <span>${firstStudent.parent_phone}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">عدد الأخوان:</span>
                    <span>${students.length}</span>
                </div>
            `;
            break;
    }

    return info;
};

// Function to create student card item
const createStudentCardItem = (student) => {
    return `
        <div class="card-student-item">
            <div class="student-info">
                <div class="student-name">${student.student_name}</div>
                <div class="student-details">
                    ${student.school_name || 'مدرسة غير محددة'} •
                    ${student.neighborhood_name || 'حي غير محدد'}
                    ${student.is_active ? '' : ' • غير نشط'}
                </div>
            </div>
            <div class="student-actions">
                <button class="mini-action-btn edit" data-student-id="${student.id}" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="mini-action-btn delete" data-student-id="${student.id}" data-student-name="${student.student_name}" title="حذف">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </div>
        </div>
    `;
};

// Function to open add student form with pre-filled group info
const openAddStudentWithGroupInfo = (groupKey, viewMode, sampleStudent) => {
    // Open the add student form
    toggleModal(true);

    // Clear form first
    document.getElementById('student-form').reset();
    document.getElementById('student_id').value = '';

    // Pre-fill based on view mode and group
    setTimeout(() => {
        switch (viewMode) {
            case 'cards-school':
                if (sampleStudent.school_id) {
                    document.getElementById('school_id').value = sampleStudent.school_id;
                }
                if (sampleStudent.stage) {
                    const stageOption = stagesData.find(s => s.name === sampleStudent.stage);
                    if (stageOption) {
                        document.getElementById('stage').value = stageOption.id;
                        // Trigger change event to update school dropdown
                        document.getElementById('stage').dispatchEvent(new Event('change'));
                    }
                }
                break;

            case 'cards-neighborhood':
                if (sampleStudent.neighborhood_id) {
                    document.getElementById('neighborhood_id').value = sampleStudent.neighborhood_id;
                }
                break;

            case 'cards-stage':
                if (sampleStudent.stage) {
                    const stageOption = stagesData.find(s => s.name === sampleStudent.stage);
                    if (stageOption) {
                        document.getElementById('stage').value = stageOption.id;
                        // Trigger change event to update school dropdown
                        document.getElementById('stage').dispatchEvent(new Event('change'));
                    }
                }
                break;

            case 'cards-siblings':
                if (sampleStudent.parent_phone) {
                    document.getElementById('parent_phone').value = sampleStudent.parent_phone;
                }
                break;
        }
    }, 100);
};

// Function updateFilterResultsInfo is already defined above, removing duplicate

// ===== Event Listeners للشاشة المنبثقة =====

// Function to setup cards options modal event listeners
const setupCardsOptionsEventListeners = () => {
    // View cards button
    const viewCardsBtn = document.getElementById('view-cards-btn');
    if (viewCardsBtn) {
        viewCardsBtn.addEventListener('click', showCardsOptionsModal);
    }

    // Close modal button
    const closeModalBtn = document.getElementById('close-cards-options-modal');
    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', hideCardsOptionsModal);
    }

    // Card options
    const cardOptions = document.querySelectorAll('.card-option');
    cardOptions.forEach(option => {
        option.addEventListener('click', () => {
            const viewMode = option.dataset.view;
            switchView(viewMode);
        });
    });

    // Close modal when clicking outside
    const modal = document.getElementById('cards-options-modal');
    if (modal) {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                hideCardsOptionsModal();
            }
        });
    }

    // Close modal with Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            hideCardsOptionsModal();
        }
    });
};

// Removed old filter event listeners - no longer needed

// Function to fetch students data only (without updating UI)
const fetchStudentsData = async (searchTerm = '') => {
    try {
        // Modify the query to fetch related school and neighborhood names
        let query = _supabase
            .from('students')
            .select(`
                *,
                schools ( name ),
                neighborhoods ( name )
            `); // Fetch all student columns AND the name from related tables

        // Add search condition if searchTerm is provided
        if (searchTerm) {
            // Update search to use 'name' column from students table
            query = query.or(`name.ilike.%${searchTerm}%,parent_phone.ilike.%${searchTerm}%`);
        }

        // Order by most recently added first
        query = query.order('created_at', { ascending: false });

        const { data, error } = await query;

        if (error) {
            console.error('Supabase Fetch Error:', error);
            return [];
        }

        // Process the data to add computed fields
        const processedData = data.map(student => ({
            ...student,
            student_name: student.name, // Add alias for consistency
            school_name: student.schools?.name || 'غير محدد',
            neighborhood_name: student.neighborhoods?.name || 'غير محدد'
        }));

        console.log('Fetched students data:', processedData);
        return processedData;
    } catch (error) {
        console.error('JavaScript Fetch Error:', error);
        return [];
    }
};

// Update the loadStudents function to work with cards view
const loadStudentsWithCardsView = async () => {
    try {
        const students = await fetchStudentsData();

        // Make sure students is an array
        if (!students || !Array.isArray(students)) {
            console.warn('fetchStudentsData returned invalid data:', students);
            allStudents = [];
        } else {
            allStudents = students;
        }

        // Render in current view mode
        renderStudents(currentViewMode);

        console.log(`Loaded ${allStudents.length} students with cards view support`);
    } catch (error) {
        console.error('Error loading students with cards view:', error);
        allStudents = [];
    }
};

// Function to initialize basic event listeners
const initializeEventListeners = () => {
    // Add student button
    const addStudentBtn = document.getElementById('add-student-btn');
    if (addStudentBtn) {
        addStudentBtn.addEventListener('click', () => {
            editMode = false; // Make sure we're in add mode
            toggleModal(true);
        });
    }

    // Form submission
    const studentForm = document.getElementById('student-form');
    if (studentForm) {
        studentForm.addEventListener('submit', handleFormSubmit);
    }

    // Close form button
    const closeFormBtn = document.getElementById('close-form-btn');
    if (closeFormBtn) {
        closeFormBtn.addEventListener('click', () => {
            toggleModal(false);
        });
    }

    // Cancel edit button
    const cancelEditBtn = document.getElementById('cancel-edit-btn');
    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', () => {
            toggleModal(false);
        });
    }

    console.log('Basic event listeners initialized');
};

// Function resetForm is already defined above, removing duplicate

// Function to edit student (wrapper for handleEditStudent)
const editStudent = async (studentId) => {
    try {
        // Find student in current data
        let student = allStudents.find(s => s.id == studentId);

        if (!student) {
            // If not found in filtered data, fetch from database
            const { data, error } = await _supabase
                .from('students')
                .select(`
                    *,
                    schools(name),
                    neighborhoods(name)
                `)
                .eq('id', studentId)
                .single();

            if (error) {
                console.error('Error fetching student for edit:', error);
                alert('خطأ في جلب بيانات الطالب');
                return;
            }

            student = data;
        }

        handleEditStudent(student);
    } catch (error) {
        console.error('Error in editStudent:', error);
        alert('حدث خطأ أثناء تحضير بيانات التعديل');
    }
};

// Function to delete student (wrapper for handleDeleteStudent)
const deleteStudent = (studentId, studentName) => {
    handleDeleteStudent(studentId, studentName);
};

// Initialize the application with new features
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOM Content Loaded - Initializing Students Management System');

    // لا تضيف أي كود متعلق بالـ sidebar هنا!
    // الـ sidebar يتم التعامل معه بواسطة shared_components/sidebar.js

    // Initialize DOM elements for view switching
    tableView = document.getElementById('table-view');
    cardsView = document.getElementById('cards-view');
    cardsGrid = document.getElementById('cards-grid');
    cardsTitle = document.getElementById('cards-title');
    cardsCount = document.getElementById('cards-count');

    // Form elements are already initialized at the top of the file
    // No need to reinitialize them here

    // Check if elements are found
    if (!tableView || !cardsView || !cardsGrid) {
        console.error('Required DOM elements not found');
        return;
    }

    // Initialize form dropdowns
    populateStageDropdown(); // Now synchronous
    await populateSchoolDropdown();
    await populateNeighborhoodDropdown();

    // Setup dynamic dropdowns (stage filtering)
    setupDynamicDropdowns();

    // Setup cards options modal event listeners
    setupCardsOptionsEventListeners();

    // Load initial data with cards view support
    await loadStudentsWithCardsView();
    await updateDashboardStats();

    // Load neighborhoods count for the new card
    await loadNeighborhoodsForManagement();

    // Initialize basic event listeners
    initializeEventListeners();

    console.log('Students Management System with cards view initialized successfully');
});
