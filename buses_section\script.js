// Supabase Initialization - using variables already defined in config.js
let supabaseUrl, supabaseAnonKey;

// Safely access the global variables
if (typeof SUPABASE_URL !== 'undefined') {
    supabaseUrl = SUPABASE_URL;
} else {
    supabaseUrl = 'YOUR_FALLBACK_SUPABASE_URL'; // Add fallback or handle error
    console.error('SUPABASE_URL not found in global scope.');
}

if (typeof SUPABASE_ANON_KEY !== 'undefined') {
    supabaseAnonKey = SUPABASE_ANON_KEY;
} else {
    supabaseAnonKey = 'YOUR_FALLBACK_SUPABASE_ANON_KEY'; // Add fallback or handle error
    console.error('SUPABASE_ANON_KEY not found in global scope.');
}

const { createClient } = supabase;
const _supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('Supabase Initialized for Buses:', _supabase);

// --- DOM Elements ---
const busForm = document.getElementById('bus-form');
const formMessage = document.getElementById('form-message');
const addBusBtn = document.getElementById('add-bus-btn');
const addBusSection = document.getElementById('add-bus-section');
const cancelEditBtn = document.getElementById('cancel-edit-btn');
const closeFormBtn = document.getElementById('close-form-btn');
const busesTableBody = document.getElementById('buses-tbody');
const listMessage = document.getElementById('list-message');
const searchInput = document.getElementById('search-input');
const searchBtn = document.getElementById('search-btn');
const printReportBtn = document.getElementById('print-report-btn');
const busIdField = document.getElementById('bus_id'); // Hidden input for bus ID (PK)
const formTitle = document.getElementById('form-title');
const totalBusesEl = document.getElementById('total-buses');
const activeBusesEl = document.getElementById('active-buses');
const needsMaintenanceEl = document.getElementById('needs-maintenance'); // Example stat
const busesCountBadge = document.getElementById('buses-count');
const paginationControls = document.getElementById('pagination-controls');

// --- State ---
let currentBuses = [];
let editMode = false;
let stats = {
    totalBuses: 0,
    activeBuses: 0,
    needsMaintenance: 0 // Example stat
};

// Pagination variables
let currentPage = 1;
const busesPerPage = 12; // Number of buses per page
let totalPages = 1;

// --- Functions ---

// Function to display messages (form or list)
const showMessage = (element, message, type = 'info') => {
    if (!element) return; // Guard clause
    element.textContent = message;
    element.className = `message ${type} show`; // Ensure 'show' class is added

    // Auto hide success messages after 5 seconds
    if (type === 'success') {
        setTimeout(() => {
            element.style.display = 'none'; // Hide element directly
            element.classList.remove('show');
        }, 5000);
    } else {
         element.style.display = 'block'; // Ensure non-success messages are visible
    }
};

// Function to render pagination controls
const renderPaginationControls = () => {
    if (!paginationControls) return;
    paginationControls.innerHTML = '';

    if (!currentBuses || currentBuses.length <= busesPerPage) {
        return;
    }

    totalPages = Math.ceil(currentBuses.length / busesPerPage);

    // Previous button
    const prevButton = document.createElement('button');
    prevButton.innerHTML = '<i class="fas fa-angle-right"></i>';
    prevButton.className = currentPage === 1 ? 'disabled' : '';
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', () => {
        if (currentPage > 1) {
            goToPage(currentPage - 1);
        }
    });
    paginationControls.appendChild(prevButton);

    // Page buttons logic (simplified for brevity)
    const maxPageButtons = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
    let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);

    if (endPage - startPage + 1 < maxPageButtons) {
        startPage = Math.max(1, endPage - maxPageButtons + 1);
    }

    if (startPage > 1) {
        const firstPageBtn = document.createElement('button');
        firstPageBtn.textContent = '1';
        firstPageBtn.addEventListener('click', () => goToPage(1));
        paginationControls.appendChild(firstPageBtn);
        if (startPage > 2) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'pagination-ellipsis';
            paginationControls.appendChild(ellipsis);
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.textContent = i.toString();
        pageBtn.className = i === currentPage ? 'active' : '';
        pageBtn.addEventListener('click', () => goToPage(i));
        paginationControls.appendChild(pageBtn);
    }

     if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
             const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'pagination-ellipsis';
            paginationControls.appendChild(ellipsis);
        }
        const lastPageBtn = document.createElement('button');
        lastPageBtn.textContent = totalPages.toString();
        lastPageBtn.addEventListener('click', () => goToPage(totalPages));
        paginationControls.appendChild(lastPageBtn);
    }

    // Next button
    const nextButton = document.createElement('button');
    nextButton.innerHTML = '<i class="fas fa-angle-left"></i>';
    nextButton.className = currentPage === totalPages ? 'disabled' : '';
    nextButton.disabled = currentPage === totalPages;
    nextButton.addEventListener('click', () => {
        if (currentPage < totalPages) {
            goToPage(currentPage + 1);
        }
    });
    paginationControls.appendChild(nextButton);
};

// Function to navigate to a specific page
const goToPage = (page) => {
    currentPage = page;
    renderBusesTable(currentBuses); // Re-render table for the new page
};

// Function to get current page buses
const getCurrentPageBuses = () => {
    const startIndex = (currentPage - 1) * busesPerPage;
    const endIndex = startIndex + busesPerPage;
    return currentBuses.slice(startIndex, endIndex);
};

// Function to get status class based on status text
const getStatusClass = (status) => {
    switch (status?.toLowerCase()) {
        case 'نشط':
            return 'status-active';
        case 'صيانة':
            return 'status-maintenance';
        case 'متوقف':
            return 'status-stopped';
        default:
            return 'status-inactive'; // Default or for unknown status
    }
};

// Function to render the buses table
const renderBusesTable = (buses) => {
    if (!busesTableBody) return;
    busesTableBody.innerHTML = '';
    if (listMessage) listMessage.style.display = 'none';

    if (!buses || buses.length === 0) {
        // Adjusted colspan to 7
        busesTableBody.innerHTML = '<tr><td colspan="7" class="loading-message">لا يوجد حافلات لعرضها.</td></tr>';
        if (busesCountBadge) busesCountBadge.textContent = '0';
        if (paginationControls) paginationControls.innerHTML = ''; // No pagination needed
        return;
    }

    // Update total count badge
    if (busesCountBadge) busesCountBadge.textContent = buses.length;

    // Get only buses for the current page
    const pageBuses = getCurrentPageBuses();

    pageBuses.forEach(bus => {
        // Combine plate letters and numbers
        const plateNumber = `${bus.plate_letters || ''} ${bus.plate_numbers || ''}`.trim();
        const statusClass = getStatusClass(bus.status);
        const statusText = bus.status || 'غير محدد';

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${bus.bus_number || 'غير محدد'}</td>
            <td>${bus.bus_type || 'غير محدد'}</td>
            <td>${bus.model || 'غير محدد'}</td>
            <td>${plateNumber || 'غير محدد'}</td>
            <td><span class="${statusClass}">${statusText}</span></td>
            <td>${bus.notes || ''}</td>
            <td>
                <button class="action-btn edit-btn" data-id="${bus.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                <button class="action-btn delete-btn" data-id="${bus.id}" title="حذف"><i class="fas fa-trash-alt"></i></button>
            </td>
        `;

        // Add event listeners for edit/delete buttons
        const editBtn = row.querySelector('.edit-btn');
        const deleteBtn = row.querySelector('.delete-btn');
        if (editBtn) {
            editBtn.addEventListener('click', () => handleEditBus(bus));
        }
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => handleDeleteBus(bus.id, bus.bus_number, plateNumber));
        }

        busesTableBody.appendChild(row);
    });

    // Render pagination controls
    renderPaginationControls();
};

// Function to update dashboard stats
const updateDashboardStats = async () => {
    try {
        // Get total buses count
        const { count: totalCount, error: totalError } = await _supabase
            .from('buses')
            .select('*', { count: 'exact', head: true });

        // Get active buses count (assuming 'نشط' means active)
        const { count: activeCount, error: activeError } = await _supabase
            .from('buses')
            .select('*', { count: 'exact', head: true })
            .eq('status', 'نشط'); // Adjust based on your actual active status value

        // Get count of buses needing maintenance (example)
        const { count: maintenanceCount, error: maintenanceError } = await _supabase
            .from('buses')
            .select('*', { count: 'exact', head: true })
            .eq('status', 'صيانة'); // Adjust based on your maintenance status value

        if (!totalError && !activeError && !maintenanceError) {
            stats.totalBuses = totalCount || 0;
            stats.activeBuses = activeCount || 0;
            stats.needsMaintenance = maintenanceCount || 0;

            // Update dashboard elements
            if (totalBusesEl) totalBusesEl.textContent = stats.totalBuses;
            if (activeBusesEl) activeBusesEl.textContent = stats.activeBuses;
            if (needsMaintenanceEl) needsMaintenanceEl.textContent = stats.needsMaintenance;
        } else {
            console.error('Error fetching stats data:', totalError, activeError, maintenanceError);
        }
    } catch (error) {
        console.error('Error updating stats:', error);
    }
};

// Function to fetch buses from Supabase
const fetchBuses = async (searchTerm = '') => {
    if (!busesTableBody) return;
    // Adjusted colspan to 7
    busesTableBody.innerHTML = '<tr><td colspan="7" class="loading-message">جاري التحميل...</td></tr>';
    if (paginationControls) paginationControls.innerHTML = ''; // Clear pagination while loading

    try {
        let query = _supabase
            .from('buses')
            .select('*'); // Select all columns from buses table

        // Add search condition if searchTerm is provided
        if (searchTerm) {
            // Search by bus number, plate letters, or plate numbers
            query = query.or(`bus_number.ilike.%${searchTerm}%,plate_letters.ilike.%${searchTerm}%,plate_numbers.ilike.%${searchTerm}%`);
        }

        // Order by bus number
        query = query.order('bus_number', { ascending: true });

        const { data, error } = await query;

        if (error) {
            console.error('Supabase Fetch Error:', error);
            showMessage(listMessage, `خطأ في جلب البيانات: ${error.message}`, 'error');
            // Adjusted colspan to 7
            busesTableBody.innerHTML = '<tr><td colspan="7" class="loading-message">خطأ في تحميل البيانات.</td></tr>';
        } else {
            console.log('Fetched buses:', data);
            currentBuses = data || [];
            currentPage = 1; // Reset to first page
            renderBusesTable(currentBuses); // Render the table with fetched data

            if (data.length === 0 && searchTerm) {
                showMessage(listMessage, `لا توجد نتائج للبحث عن "${searchTerm}".`, 'info');
            } else if (data.length === 0) {
                 showMessage(listMessage, 'لا يوجد حافلات مسجلة حالياً.', 'info');
            }

            // Update dashboard stats after fetching
            updateDashboardStats();
        }
    } catch (error) {
        console.error('JavaScript Fetch Error:', error);
        showMessage(listMessage, `حدث خطأ غير متوقع: ${error.message}`, 'error');
        // Adjusted colspan to 7
        busesTableBody.innerHTML = '<tr><td colspan="7" class="loading-message">خطأ غير متوقع.</td></tr>';
    }
};

// Function to handle form submission (Add or Update)
const handleFormSubmit = async (event) => {
    event.preventDefault();
    if (!busForm || !formMessage) return;

    showMessage(formMessage, 'جاري حفظ البيانات...', 'info');
    const submitBtn = busForm.querySelector('.submit-btn');
    if (submitBtn) submitBtn.disabled = true;

    const formData = new FormData(busForm);
    const busData = {};
    formData.forEach((value, key) => {
        // Convert empty strings for number fields to null
        if (key === 'model' && value === '') {
            busData[key] = null;
        } else {
            busData[key] = value.trim(); // Trim whitespace
        }
    });

    const busId = busData.bus_id; // Get the hidden ID (PK)

    // Basic validation
    if (!busData.bus_number || !busData.status || !busData.plate_letters || !busData.plate_numbers) {
        showMessage(formMessage, 'الرجاء ملء جميع الحقول المطلوبة (*).', 'error');
        if (submitBtn) submitBtn.disabled = false;
        return;
    }

    // Prepare data for Supabase (match column names from your image)
    const dataToUpsert = {
        bus_number: busData.bus_number,
        bus_type: busData.bus_type || null,
        model: busData.model, // Already handled null conversion
        status: busData.status,
        plate_letters: busData.plate_letters,
        plate_numbers: busData.plate_numbers,
        notes: busData.notes || null,
    };

    try {
        let result;
        if (editMode && busId) {
            // Update existing bus
            result = await _supabase
                .from('buses')
                .update(dataToUpsert)
                .eq('id', busId) // Use 'id' or 'uuid' based on your primary key column name
                .select();
        } else {
            // Insert new bus
            result = await _supabase
                .from('buses')
                .insert([dataToUpsert])
                .select();
        }

        const { data, error } = result;

        if (error) {
            console.error('Supabase Save Error:', error);
            let userMessage = `خطأ في الحفظ: ${error.message}`;
            if (error.code === '23505') { // Unique constraint violation
                 userMessage = 'خطأ: رقم الحافلة أو رقم اللوحة موجود مسبقاً.';
            }
            showMessage(formMessage, userMessage, 'error');
        } else {
            console.log('Supabase Save Success:', data);
            showMessage(formMessage, `تم ${editMode ? 'تحديث' : 'إضافة'} الحافلة بنجاح!`, 'success');
            setTimeout(() => {
                toggleModal(false);
            }, 1500);
            fetchBuses(); // Refresh list
        }
    } catch (error) {
        console.error('JavaScript Save Error:', error);
        showMessage(formMessage, `حدث خطأ غير متوقع: ${error.message}`, 'error');
    } finally {
        if (submitBtn) submitBtn.disabled = false;
    }
};

// Function to reset the form and UI elements
const resetForm = () => {
    editMode = false;
    if (busForm) busForm.reset();
    if (busIdField) busIdField.value = ''; // Clear hidden ID field
    if (formMessage) formMessage.style.display = 'none';
    if (formTitle) formTitle.textContent = 'إضافة حافلة جديدة';
    const submitBtn = busForm?.querySelector('.submit-btn');
    if (submitBtn) submitBtn.textContent = 'حفظ';
    // Set default status if needed
    const statusSelect = document.getElementById('status');
    if (statusSelect) statusSelect.value = 'نشط';
};

// Function to populate form for editing
const handleEditBus = (bus) => {
    if (!bus || !busForm) return;
    toggleModal(true); // Open modal first
    editMode = true;

    if (formTitle) formTitle.textContent = 'تعديل بيانات الحافلة';
    const submitBtn = busForm.querySelector('.submit-btn');
    if (submitBtn) submitBtn.textContent = 'تحديث';

    // Populate form fields
    if (busIdField) busIdField.value = bus.id || ''; // Set the hidden ID field
    document.getElementById('bus_number').value = bus.bus_number || '';
    document.getElementById('bus_type').value = bus.bus_type || '';
    document.getElementById('model').value = bus.model || '';
    document.getElementById('status').value = bus.status || 'نشط';
    document.getElementById('plate_letters').value = bus.plate_letters || '';
    document.getElementById('plate_numbers').value = bus.plate_numbers || '';
    document.getElementById('notes').value = bus.notes || '';

    // Ensure the modal is fully rendered before focusing
    setTimeout(() => {
        document.getElementById('bus_number')?.focus();
    }, 100);
};

// Function to handle bus deletion
const handleDeleteBus = async (busId, busNumber, plateNumber) => {
    const confirmDelete = confirm(`هل أنت متأكد من حذف الحافلة رقم "${busNumber || plateNumber || 'هذه الحافلة'}"؟ لا يمكن التراجع عن هذا الإجراء.`);

    if (confirmDelete) {
        try {
            const { error } = await _supabase
                .from('buses')
                .delete()
                .eq('id', busId); // Use 'id' or 'uuid' based on your PK

            if (error) {
                console.error('Supabase Delete Error:', error);
                 // Check for foreign key constraint violation (e.g., if drivers are assigned to this bus)
                let userMessage = `خطأ في الحذف: ${error.message}`;
                if (error.code === '23503') {
                    userMessage = 'خطأ: لا يمكن حذف الحافلة لوجود سائقين أو بيانات أخرى مرتبطة بها.';
                }
                showMessage(listMessage, userMessage, 'error');
            } else {
                console.log('Bus deleted:', busId);
                showMessage(listMessage, `تم حذف الحافلة بنجاح.`, 'success');
                fetchBuses(); // Refresh the list
            }
        } catch (error) {
            console.error('JavaScript Delete Error:', error);
            showMessage(listMessage, `حدث خطأ غير متوقع أثناء الحذف: ${error.message}`, 'error');
        }
    }
};

// Function to handle search
const handleSearch = () => {
    if (!searchInput) return;
    const searchTerm = searchInput.value.trim();
    fetchBuses(searchTerm);
};

// Function to handle printing report
const handlePrintReport = () => {
    const busesToPrint = [...currentBuses];
    let reportTitle = 'تقرير الحافلات';

    // Sort by bus number
    busesToPrint.sort((a, b) => (a.bus_number || '').localeCompare(b.bus_number || '', undefined, { numeric: true }));

    const printWindow = window.open('', '_blank', 'height=600,width=800');
    let reportContent = `
        <html dir="rtl">
        <head>
            <title>${reportTitle}</title>
            <style>
                body { font-family: 'Tajawal', sans-serif; padding: 20px; direction: rtl; }
                h1 { text-align: center; margin-bottom: 10px; color: #333; }
                .print-meta { display: flex; justify-content: space-between; margin-bottom: 20px; font-size: 0.9em; color: #555; }
                table { width: 100%; border-collapse: collapse; margin-top: 15px; }
                th, td { padding: 8px; text-align: right; border: 1px solid #ddd; font-size: 0.9em; }
                th { background-color: #f2f2f2; font-weight: bold; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                .status-active { color: #2ecc71; font-weight: bold; }
                .status-maintenance { color: #f39c12; font-weight: bold; }
                .status-stopped { color: #e74c3c; font-weight: bold; }
                .status-inactive { color: #7f8c8d; font-weight: bold; }
                @media print {
                    .no-print { display: none; }
                    body { padding: 5px; }
                    h1 { font-size: 1.5em; }
                }
            </style>
        </head>
        <body>
            <div class="print-meta">
                <span>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</span>
                <span>عدد الحافلات في التقرير: ${busesToPrint.length}</span>
            </div>
            <h1>${reportTitle}</h1>
            ${busesToPrint.length === 0 ? '<p style="text-align: center; color: #888;">لا يوجد حافلات لعرضها.</p>' : `
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>رقم الحافلة</th>
                        <th>النوع</th>
                        <th>الموديل</th>
                        <th>رقم اللوحة</th>
                        <th>الحالة</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
            `}
    `;

    busesToPrint.forEach((bus, index) => {
        const plateNumber = `${bus.plate_letters || ''} ${bus.plate_numbers || ''}`.trim();
        const statusClass = getStatusClass(bus.status);
        const statusText = bus.status || 'غير محدد';

        reportContent += `
            <tr>
                <td>${index + 1}</td>
                <td>${bus.bus_number || ''}</td>
                <td>${bus.bus_type || ''}</td>
                <td>${bus.model || ''}</td>
                <td>${plateNumber || ''}</td>
                <td><span class="${statusClass}">${statusText}</span></td>
                <td>${bus.notes || ''}</td>
            </tr>
        `;
    });

    if (busesToPrint.length > 0) {
        reportContent += `
                </tbody>
            </table>
        `;
    }

    reportContent += `
            <div class="no-print" style="margin-top: 20px; text-align: center;">
                <button onclick="window.print()">طباعة</button>
                <button onclick="window.close()">إغلاق</button>
            </div>
        </body>
        </html>
    `;

    printWindow.document.write(reportContent);
    printWindow.document.close();
};


// Function to handle modal toggling
function toggleModal(show = true) {
    if (!addBusSection) return;
    console.log('Bus Modal toggled:', show ? 'show' : 'hide');

    if (show) {
        resetForm(); // Reset form before showing
        addBusSection.classList.add('show');
        setTimeout(() => {
            document.getElementById('bus_number')?.focus();
        }, 300);
        document.body.style.overflow = 'hidden';
    } else {
        addBusSection.classList.remove('show');
        document.body.style.overflow = '';
    }
}

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOM loaded, initializing buses application...');

    // Setup event listeners
    setupEventListeners();

    // Fetch initial buses data
    fetchBuses();
});


// --- Event Listeners ---
const setupEventListeners = () => {
    if (busForm) {
        busForm.addEventListener('submit', handleFormSubmit);
    }

    if (addBusBtn) {
        addBusBtn.addEventListener('click', () => {
            editMode = false;
            toggleModal(true);
        });
    }

    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', () => {
            toggleModal(false);
        });
    }

    if (closeFormBtn) {
        closeFormBtn.addEventListener('click', () => {
            toggleModal(false);
        });
    }

    // Close modal on background click
    if (addBusSection) {
        addBusSection.addEventListener('click', (event) => {
            if (event.target === addBusSection) {
                toggleModal(false);
            }
        });
    }

    if (searchBtn) {
        searchBtn.addEventListener('click', handleSearch);
    }

    if (searchInput) {
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
    }

    if (printReportBtn) {
        printReportBtn.addEventListener('click', handlePrintReport);
    }

    // Escape key listener for modal
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && addBusSection && addBusSection.classList.contains('show')) {
            toggleModal(false);
        }
    });
};
