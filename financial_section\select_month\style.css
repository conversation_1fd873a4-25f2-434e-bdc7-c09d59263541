@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --light-color: #ecf0f1;
    --body-bg: #f5f7fa;
    --card-bg: #ffffff;
    --border-color: #e1e8ed;
    --text-dark: #34495e;
    --text-muted: #7f8c8d;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --border-radius: 8px;
    --card-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--body-bg);
    color: var(--text-dark);
    line-height: 1.6;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: 100vh;
    padding: 20px;
}

.container {
    width: 100%;
    max-width: 700px;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: #fff;
    padding: 25px 30px;
    text-align: center;
}

header h1 {
    font-size: 1.8rem;
    margin-bottom: 8px;
}
header h1 i {
    margin-left: 10px;
}

header p {
    opacity: 0.9;
    font-size: 1rem;
}

main {
    padding: 30px;
}

.selection-card, .actions-card {
    background-color: #fff;
    padding: 25px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
}

.actions-card h2 {
    text-align: center;
    margin-bottom: 20px;
    color: var(--primary-dark);
    font-size: 1.4rem;
}
.actions-card h2 i {
    margin-left: 8px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    background-color: #fff;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%2334495e' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: left 15px center;
    padding-left: 40px; /* Space for icon */
}

.form-group select:disabled {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-btn {
    padding: 12px 25px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #fff;
    transition: all 0.3s ease;
    min-width: 180px;
}
.action-btn i {
    font-size: 1.1rem;
}

.action-btn.primary { background-color: var(--success-color); }
.action-btn.primary:hover { background-color: #27ae60; }

.action-btn.secondary { background-color: var(--secondary-color); }
.action-btn.secondary:hover { background-color: #34495e; }

.month-status {
    text-align: center;
    margin-bottom: 15px;
    font-weight: bold;
    padding: 8px;
    border-radius: 4px;
}
.month-status.closed {
    color: var(--danger-color);
    background-color: rgba(231, 76, 60, 0.1);
}
.month-status.open {
    color: var(--success-color);
    background-color: rgba(46, 204, 113, 0.1);
}


footer {
    text-align: center;
    padding: 20px;
    color: var(--text-muted);
    border-top: 1px solid var(--border-color);
    margin-top: 20px; /* Ensure footer is pushed down */
}

/* Message Styles */
.message {
    margin-top: 15px;
    padding: 12px;
    border-radius: var(--border-radius);
    text-align: center;
    font-weight: 500;
    display: none; /* Hide by default */
}
.message.show { display: block; }
.message.info { background-color: rgba(52, 152, 219, 0.1); color: var(--primary-color); border: 1px solid rgba(52, 152, 219, 0.3); }
.message.error { background-color: rgba(231, 76, 60, 0.1); color: var(--danger-color); border: 1px solid rgba(231, 76, 60, 0.3); }
.message.warning { background-color: rgba(243, 156, 18, 0.1); color: var(--warning-color); border: 1px solid rgba(243, 156, 18, 0.3); }

@media (max-width: 600px) {
    header h1 { font-size: 1.5rem; }
    main { padding: 20px; }
    .selection-card, .actions-card { padding: 20px; }
    .action-buttons { flex-direction: column; }
    .action-btn { width: 100%; }
}
