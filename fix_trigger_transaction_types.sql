-- إصلاح مشكلة أنواع المعاملات في التريجر العام
-- المشكلة: التريجر يستخدم قيم عربية لكن النوع المُعرف يستخدم قيم إنجليزية

-- الخطوة 1: إصلاح دالة generic_payment_trigger_handler
CREATE OR REPLACE FUNCTION public.generic_payment_trigger_handler()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_description text;
    v_source_table_name text := TG_TABLE_NAME;
    v_original_record_id text; --  معرف السجل الأصلي (سواء كان NEW أو OLD)
    v_bank_id uuid;
    v_budget_month_id uuid;
    v_transaction_date date;
    v_amount numeric;
    v_paid_amount numeric; --  لجدول enterprise_subscriptions

    -- إصلاح: استخدام القيم الإنجليزية المُعرفة في النوع
    v_income_transaction_type public.transaction_type := 'deposit';
    v_expense_transaction_type public.transaction_type := 'withdrawal';
    v_current_transaction_type public.transaction_type;
    v_reversed_transaction_type public.transaction_type;

    v_log_entry_id uuid;
    v_old_log_entry_id uuid;
    v_is_new_transaction boolean := false;
    v_is_old_transaction_reversal boolean := false;

BEGIN
    -- تحديد الحقول المشتركة بناءً على الجدول
    IF v_source_table_name = 'student_payments' THEN
        v_current_transaction_type := v_income_transaction_type;
        IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := NEW.id::text;
            v_bank_id := NEW.bank_id;
            v_budget_month_id := NEW.budget_month_id;
            v_transaction_date := NEW.payment_date;
            v_amount := NEW.amount;
            v_description := 'Student payment: ' || NEW.student_id::text;
            v_is_new_transaction := true;
        END IF;
        IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
            --  إذا كان UPDATE، سنعكس القديم ثم نضيف الجديد
            --  إذا كان DELETE، سنعكس القديم فقط
            v_original_record_id := OLD.id::text; --  نستخدمه لتحديد السجل الأصلي للعكس
            v_bank_id := OLD.bank_id;
            v_budget_month_id := OLD.budget_month_id;
            v_transaction_date := OLD.payment_date;
            v_amount := OLD.amount;
            v_description := 'Reversal for student payment: ' || OLD.student_id::text;
            v_is_old_transaction_reversal := true;
        END IF;

    ELSIF v_source_table_name = 'enterprise_group_payments' THEN
        v_current_transaction_type := v_income_transaction_type;
        IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := NEW.id::text;
            v_bank_id := NEW.payment_bank_id;
            v_budget_month_id := (SELECT es.budget_month_id FROM public.enterprise_subscriptions es WHERE es.id = NEW.enterprise_subscription_id);
            v_transaction_date := NEW.payment_date;
            v_amount := NEW.payment_amount;
            v_description := 'Enterprise group payment: ' || NEW.enterprise_group_id::text;
            v_is_new_transaction := true;
        END IF;
        IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := OLD.id::text;
            v_bank_id := OLD.payment_bank_id;
            v_budget_month_id := (SELECT es.budget_month_id FROM public.enterprise_subscriptions es WHERE es.id = OLD.enterprise_subscription_id);
            v_transaction_date := OLD.payment_date;
            v_amount := OLD.payment_amount;
            v_description := 'Reversal for enterprise group payment: ' || OLD.enterprise_group_id::text;
            v_is_old_transaction_reversal := true;
        END IF;

    ELSIF v_source_table_name = 'enterprise_subscriptions' THEN
        --  هذا خاص، نتعامل فقط مع تغيير paid_amount أو إدخال بدفعة أولية
        --  نفترض أن الدفعات هنا هي إيرادات
        v_current_transaction_type := v_income_transaction_type;
        IF TG_OP = 'INSERT' THEN
            IF NEW.paid_amount > 0 AND NEW.payment_bank_id IS NOT NULL AND NEW.payment_date IS NOT NULL THEN
                v_original_record_id := NEW.id::text;
                v_bank_id := NEW.payment_bank_id;
                v_budget_month_id := NEW.budget_month_id;
                v_transaction_date := NEW.payment_date;
                v_amount := NEW.paid_amount;
                v_description := 'Initial payment for enterprise subscription: ' || NEW.enterprise_id::text;
                v_is_new_transaction := true;
            END IF;
        ELSIF TG_OP = 'UPDATE' THEN
            --  نعكس الدفعة القديمة إذا تغيرت وتأثرت بها البنود المالية
            IF OLD.paid_amount > 0 AND OLD.payment_bank_id IS NOT NULL AND OLD.payment_date IS NOT NULL AND
               (OLD.paid_amount IS DISTINCT FROM NEW.paid_amount OR
                OLD.payment_bank_id IS DISTINCT FROM NEW.payment_bank_id OR
                OLD.payment_date IS DISTINCT FROM NEW.payment_date)
            THEN
                v_original_record_id := OLD.id::text; --  للعكس
                v_bank_id := OLD.payment_bank_id;
                v_budget_month_id := OLD.budget_month_id;
                v_transaction_date := OLD.payment_date;
                v_amount := OLD.paid_amount;
                v_description := 'Reversal for enterprise subscription payment update: ' || OLD.enterprise_id::text;
                v_is_old_transaction_reversal := true;
            END IF;
            --  نضيف الدفعة الجديدة إذا كانت موجودة ومختلفة
            IF NEW.paid_amount > 0 AND NEW.payment_bank_id IS NOT NULL AND NEW.payment_date IS NOT NULL THEN
                v_original_record_id := NEW.id::text; --  للجديد
                v_bank_id := NEW.payment_bank_id;
                v_budget_month_id := NEW.budget_month_id;
                v_transaction_date := NEW.payment_date;
                v_amount := NEW.paid_amount;
                v_description := 'Updated payment for enterprise subscription: ' || NEW.enterprise_id::text;
                v_is_new_transaction := true; --  سيتم التعامل معها لاحقًا
            END IF;
        END IF;
        --  لا يوجد DELETE مباشر هنا يؤثر على paid_amount عادة، الحذف يكون للسجل كله

    ELSIF v_source_table_name = 'nathriyat_transactions' THEN
        v_current_transaction_type := v_expense_transaction_type;
        IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := NEW.id::text;
            v_bank_id := NEW.bank_id;
            v_budget_month_id := NEW.budget_month_id;
            v_transaction_date := NEW.transaction_date;
            v_amount := NEW.amount;
            v_description := 'Nathriyat transaction: ' || COALESCE(NEW.nathriyat_type_id::text, 'unknown');
            v_is_new_transaction := true;
        END IF;
        IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := OLD.id::text;
            v_bank_id := OLD.bank_id;
            v_budget_month_id := OLD.budget_month_id;
            v_transaction_date := OLD.transaction_date;
            v_amount := OLD.amount;
            v_description := 'Reversal for nathriyat transaction: ' || COALESCE(OLD.nathriyat_type_id::text, 'unknown');
            v_is_old_transaction_reversal := true;
        END IF;

    ELSIF v_source_table_name = 'bus_expenses' THEN
        v_current_transaction_type := v_expense_transaction_type;
        IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := NEW.id::text;
            v_bank_id := NEW.bank_id; --  فقط إذا كان المصروف من البنك
            v_budget_month_id := NEW.budget_month_id;
            v_transaction_date := NEW.expense_date;
            -- إصلاح: استخدام total_with_tax بدلاً من amount إذا كان موجوداً
            v_amount := COALESCE(NEW.total_with_tax, NEW.amount, 0);
            v_description := 'Bus expense: ' || COALESCE(NEW.expense_type, 'unknown');
            v_is_new_transaction := true;
        END IF;
        IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := OLD.id::text;
            v_bank_id := OLD.bank_id;
            v_budget_month_id := OLD.budget_month_id;
            v_transaction_date := OLD.expense_date;
            v_amount := COALESCE(OLD.total_with_tax, OLD.amount, 0);
            v_description := 'Reversal for bus expense: ' || COALESCE(OLD.expense_type, 'unknown');
            v_is_old_transaction_reversal := true;
        END IF;

    ELSIF v_source_table_name = 'driver_expenses' THEN
        --  فقط إذا كان هناك bank_paid_amount و bank_id
        v_current_transaction_type := v_expense_transaction_type;
        IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
            IF NEW.bank_id IS NOT NULL AND NEW.bank_paid_amount > 0 THEN
                v_original_record_id := NEW.id::text; --  هنا ID من driver_expenses هو bigserial
                v_bank_id := NEW.bank_id;
                v_budget_month_id := NEW.budget_month_id;
                v_transaction_date := NEW.expense_date;
                v_amount := NEW.bank_paid_amount;
                v_description := 'Driver expense (bank paid): ' || COALESCE(NEW.expense_type, 'unknown');
                v_is_new_transaction := true;
            END IF;
        END IF;
        IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
            IF OLD.bank_id IS NOT NULL AND OLD.bank_paid_amount > 0 THEN
                v_original_record_id := OLD.id::text;
                v_bank_id := OLD.bank_id;
                v_budget_month_id := OLD.budget_month_id;
                v_transaction_date := OLD.expense_date;
                v_amount := OLD.bank_paid_amount;
                v_description := 'Reversal for driver expense (bank paid): ' || COALESCE(OLD.expense_type, 'unknown');
                v_is_old_transaction_reversal := true;
            END IF;
        END IF;
    ELSE
        -- جدول غير مدعوم بواسطة هذا التريجر العام
        RAISE NOTICE 'generic_payment_trigger_handler: Table % not supported.', v_source_table_name;
        RETURN COALESCE(NEW, OLD);
    END IF;

    -- تحديد نوع المعاملة العكسية
    IF v_current_transaction_type = v_income_transaction_type THEN
        v_reversed_transaction_type := v_expense_transaction_type;
    ELSE
        v_reversed_transaction_type := v_income_transaction_type;
    END IF;

    -- التعامل مع العمليات
    -- 1. اعكس المعاملة القديمة إذا كانت UPDATE أو DELETE
    IF v_is_old_transaction_reversal AND v_amount > 0 AND v_bank_id IS NOT NULL THEN --  فقط إذا كانت هناك معاملة بنكية لعكسها
        --  ابحث عن السجل الأصلي في اللوغ لعكسه
        SELECT id INTO v_old_log_entry_id
        FROM public.financial_transactions_log ftl
        WHERE ftl.source_table = v_source_table_name
          AND ftl.source_record_id = v_original_record_id --  هذا هو ID السجل في الجدول المصدر
          AND ftl.is_reversal = false --  ابحث عن المعاملة الأصلية غير المعكوسة
        ORDER BY ftl.created_at DESC LIMIT 1; --  خذ الأحدث إذا كان هناك عدة (نادر)

        IF v_old_log_entry_id IS NOT NULL THEN
            PERFORM public.log_financial_transaction(
                p_transaction_date := v_transaction_date, --  أو تاريخ اليوم للعكس؟
                p_amount := v_amount,
                p_transaction_type := v_reversed_transaction_type, --  النوع المعكوس
                p_description := v_description,
                p_source_table := v_source_table_name,
                p_source_record_id := v_original_record_id,
                p_bank_id := v_bank_id,
                p_budget_month_id := v_budget_month_id,
                p_is_reversal := true,
                p_original_log_entry_id := v_old_log_entry_id
            );
            RAISE NOTICE 'Created reversal transaction for % record %', v_source_table_name, v_original_record_id;
        ELSE
             RAISE NOTICE 'generic_payment_trigger_handler: Could not find original log entry to reverse for table %, record id %', v_source_table_name, v_original_record_id;
        END IF;
    END IF;

    -- 2. أضف المعاملة الجديدة إذا كانت INSERT أو UPDATE
    IF v_is_new_transaction AND v_amount > 0 AND v_bank_id IS NOT NULL THEN
         --  في حالة UPDATE لـ enterprise_subscriptions، قد يكون v_amount هو نفسه القديم إذا لم يتغير paid_amount
         --  لذا، نحتاج إلى التأكد من أننا لا نسجل نفس الشيء مرتين إذا لم يكن هناك تغيير حقيقي.
         --  هذا يتم التعامل معه جزئيًا بمنطق v_is_old_transaction_reversal أعلاه.
        IF v_source_table_name = 'enterprise_subscriptions' AND TG_OP = 'UPDATE' THEN
            IF OLD.paid_amount IS DISTINCT FROM NEW.paid_amount OR
               OLD.payment_bank_id IS DISTINCT FROM NEW.payment_bank_id OR
               OLD.payment_date IS DISTINCT FROM NEW.payment_date THEN
                --  فقط إذا كان هناك تغيير فعلي
                PERFORM public.log_financial_transaction(
                    p_transaction_date := v_transaction_date,
                    p_amount := v_amount,
                    p_transaction_type := v_current_transaction_type,
                    p_description := v_description,
                    p_source_table := v_source_table_name,
                    p_source_record_id := v_original_record_id,
                    p_bank_id := v_bank_id,
                    p_budget_month_id := v_budget_month_id
                );
                RAISE NOTICE 'Created updated transaction for % record %', v_source_table_name, v_original_record_id;
            END IF;
        ELSE
            PERFORM public.log_financial_transaction(
                p_transaction_date := v_transaction_date,
                p_amount := v_amount,
                p_transaction_type := v_current_transaction_type,
                p_description := v_description,
                p_source_table := v_source_table_name,
                p_source_record_id := v_original_record_id,
                p_bank_id := v_bank_id,
                p_budget_month_id := v_budget_month_id
            );
            RAISE NOTICE 'Created new transaction for % record %', v_source_table_name, v_original_record_id;
        END IF;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$;

-- الخطوة 2: التأكد من وجود دالة log_financial_transaction
-- (إذا لم تكن موجودة، سيتم إنشاؤها)
CREATE OR REPLACE FUNCTION public.log_financial_transaction(
    p_transaction_date date,
    p_amount numeric,
    p_transaction_type public.transaction_type,
    p_description text,
    p_source_table text,
    p_source_record_id text,
    p_bank_id uuid,
    p_budget_month_id uuid,
    p_is_reversal boolean DEFAULT false,
    p_original_log_entry_id uuid DEFAULT NULL
)
RETURNS uuid
LANGUAGE plpgsql
AS $$
DECLARE
    v_log_id uuid;
BEGIN
    INSERT INTO public.financial_transactions_log (
        transaction_date, amount, transaction_type, description,
        source_table, source_record_id, bank_id, budget_month_id,
        is_reversal, original_log_entry_id
    )
    VALUES (
        p_transaction_date, p_amount, p_transaction_type, p_description,
        p_source_table, p_source_record_id, p_bank_id, p_budget_month_id,
        p_is_reversal, p_original_log_entry_id
    )
    RETURNING id INTO v_log_id;
    
    RETURN v_log_id;
END;
$$;

-- الخطوة 3: إعادة إنشاء التريجرات للتأكد من تطبيق التحديثات
DROP TRIGGER IF EXISTS enterprise_group_payments_financial_log_trigger ON public.enterprise_group_payments;
DROP TRIGGER IF EXISTS enterprise_subscriptions_financial_log_trigger ON public.enterprise_subscriptions;
DROP TRIGGER IF EXISTS nathriyat_transactions_financial_log_trigger ON public.nathriyat_transactions;
DROP TRIGGER IF EXISTS bus_expenses_financial_log_trigger ON public.bus_expenses;
DROP TRIGGER IF EXISTS driver_expenses_financial_log_trigger ON public.driver_expenses;

-- إعادة إنشاء التريجرات
CREATE TRIGGER enterprise_group_payments_financial_log_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.enterprise_group_payments
FOR EACH ROW EXECUTE FUNCTION public.generic_payment_trigger_handler();

CREATE TRIGGER enterprise_subscriptions_financial_log_trigger
AFTER INSERT OR UPDATE OF paid_amount, payment_bank_id, payment_date ON public.enterprise_subscriptions
FOR EACH ROW EXECUTE FUNCTION public.generic_payment_trigger_handler();

CREATE TRIGGER nathriyat_transactions_financial_log_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.nathriyat_transactions
FOR EACH ROW EXECUTE FUNCTION public.generic_payment_trigger_handler();

CREATE TRIGGER bus_expenses_financial_log_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.bus_expenses
FOR EACH ROW EXECUTE FUNCTION public.generic_payment_trigger_handler();

CREATE TRIGGER driver_expenses_financial_log_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.driver_expenses
FOR EACH ROW EXECUTE FUNCTION public.generic_payment_trigger_handler();

-- الخطوة 4: اختبار التريجر
SELECT 'تم إصلاح التريجر العام بنجاح!' as status;

-- عرض الجداول المدعومة
SELECT 'الجداول المدعومة بالتريجر العام:' as info
UNION ALL
SELECT '- student_payments (إيرادات)' as info
UNION ALL
SELECT '- enterprise_group_payments (إيرادات)' as info
UNION ALL
SELECT '- enterprise_subscriptions (إيرادات)' as info
UNION ALL
SELECT '- nathriyat_transactions (مصروفات)' as info
UNION ALL
SELECT '- bus_expenses (مصروفات)' as info
UNION ALL
SELECT '- driver_expenses (مصروفات بنكية فقط)' as info;
