:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #7f8c8d;
    --success-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #dee2e6;
    --card-bg: #ffffff;
    --body-bg: #f4f7f6;
    --shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
}

body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--body-bg);
    color: var(--dark-color);
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

/* Top Navigation Bar */
.top-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-toggle-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.sidebar-toggle-btn:hover {
    background-color: rgba(255,255,255,0.1);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 600;
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.navbar-user {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logout-btn {
    background: rgba(255,255,255,0.1);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: rgba(255,255,255,0.2);
}

.page-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    margin-top: 70px; /* Account for fixed navbar */
}

/* Layout for page with sidebar */
.page-container.with-sidebar {
    display: flex;
    gap: 20px; /* Space between sidebar and main content */
}

/* Sidebar Styles */
.sidebar {
    width: 280px; /* Adjust width as needed */
    flex-shrink: 0; /* Prevent sidebar from shrinking */
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    height: fit-content; /* Adjust height based on content */
    max-height: calc(100vh - 100px); /* Example max height, adjust based on navbar/footer */
    overflow-y: auto; /* Add scroll if content exceeds max height */
    padding-bottom: 15px;
}

.sidebar-header {
    padding: 1rem 1.2rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--light-color); /* Light background for header */
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--primary-dark);
    display: flex;
    align-items: center;
    gap: 8px;
}

.sidebar-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-list li {
    padding: 12px 18px;
    border-bottom: 1px solid var(--border-color-light, #eee); /* Lighter border */
    cursor: default; /* Default cursor, change if interactive */
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.95rem;
    color: var(--secondary-color);
}

.sidebar-list li:last-child {
    border-bottom: none;
}

.sidebar-list li:hover {
    background-color: var(--hover-bg, #f8f9fa); /* Subtle hover effect */
}

.sidebar-list li i { /* Style for potential icons */
    color: var(--primary-color);
    width: 16px; /* Fixed width for alignment */
    text-align: center;
}

.sidebar-list .loading-item {
    font-style: italic;
    color: var(--text-muted);
    justify-content: center;
    cursor: default;
}
.sidebar-list .loading-item:hover {
    background-color: transparent;
}
.sidebar-list .empty-item {
    font-style: italic;
    color: var(--text-muted);
    text-align: center;
    padding: 20px;
    cursor: default;
}
.sidebar-list .empty-item:hover {
    background-color: transparent;
}

/* Adjust main content area when sidebar is present */
.page-container.with-sidebar .main-content {
    flex-grow: 1; /* Allow main content to take remaining space */
    /* No specific margin needed if using flex gap */
}

/* Responsive adjustments if needed */
@media (max-width: 992px) {
    .page-container.with-sidebar {
        flex-direction: column;
    }
    .sidebar {
        width: 100%; /* Full width on smaller screens */
        max-height: 300px; /* Limit height */
        margin-bottom: 20px;
    }
}

/* Header */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 1.5rem 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow);
    display: flex;
    justify-content: space-between;
    align-items: center; /* Vertically align items */
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
    flex-wrap: wrap; /* Allow wrapping on smaller screens */
    gap: 15px; /* Add gap between items when wrapping */
}

.page-header h1 {
    margin: 0;
    font-size: 1.8rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    display: flex;
    gap: 10px;
    flex-shrink: 0; /* Prevent controls from shrinking too much */
}

#current-month-year-display {
    font-weight: 500;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 5px 10px;
    border-radius: 4px;
}

.header-title-section {
    flex-grow: 1; /* Allow title section to take available space */
}

.header-title-section h1 {
    margin: 0 0 10px 0; /* Adjust margin */
    color: var(--primary-color);
    font-size: 1.8rem;
}
.header-title-section h1 i {
    margin-left: 10px;
}

/* Styles for Year Display and Month Selector */
.header-month-selector {
    display: flex;
    align-items: center;
    gap: 15px; /* Space between year text and dropdown */
    margin-top: 5px; /* Space below the main title */
}

.header-month-selector span {
    font-size: 1rem;
    color: var(--text-muted);
}

.header-month-selector strong {
    color: var(--secondary-color);
    font-weight: 600;
}

.month-selector {
    font-size: 0.95rem;
    padding: 6px 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: #fff;
    color: var(--secondary-color);
    cursor: pointer;
    max-width: 180px;
    font-family: inherit;
}

.month-selector:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
    opacity: 0.7;
}

.month-selector option:disabled {
    color: #adb5bd;
}

/* Main Content */
.main-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Cards */
.card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden; /* Ensures padding is respected with borders */
}

.card-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--light-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    margin: 0;
    font-size: 1.2rem;
    color: var(--primary-dark);
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-body {
    padding: 1.5rem;
}

/* Controls Section */
.controls-grid {
    display: grid;
    /* Adjusted grid for filters */
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem 1.5rem;
    align-items: end; /* Align items to the bottom */
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-size: 0.9rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.6rem 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.95rem;
    box-sizing: border-box; /* Include padding and border in element's total width and height */
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.control-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-start; /* Align buttons to the start */
    /* Adjusted grid placement if needed */
    grid-column: 1 / -1; /* Span full width */
    margin-top: 1rem; /* Add some space above buttons */
}

/* Buttons */
.control-btn {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s ease, transform 0.1s ease;
    white-space: nowrap;
}

.control-btn:hover {
    opacity: 0.9;
}
.control-btn:active {
    transform: scale(0.98);
}

.primary-btn { background-color: var(--primary-color); color: white; }
.secondary-btn { background-color: var(--secondary-color); color: white; }
.success-btn { background-color: var(--success-color); color: white; }
.danger-btn { background-color: var(--danger-color); }

/* REMOVE Table Section Styles */
/* Remove styles for .table-responsive, #subscriptions-table, th, td, .status, .action-btn */

/* ===== Groups Section Enhanced ===== */

/* Section Header */
.groups-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 2rem;
}

.section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.section-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.header-content {
    position: relative;
    z-index: 2;
    margin-bottom: 1.5rem;
}

.header-content h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.8rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.section-description {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    opacity: 0.9;
    line-height: 1.5;
}

.month-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    width: fit-content;
}

.month-label {
    opacity: 0.9;
    font-weight: 500;
}

.month-value {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 6px;
    font-weight: 700;
    color: #fff;
}

/* Header Stats */
.header-stats {
    display: flex;
    gap: 1rem;
    position: relative;
    z-index: 2;
}

.stat-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card.ungrouped-stat:hover {
    background: rgba(255, 193, 7, 0.2);
}

.stat-card.add-group-stat:hover {
    background: rgba(72, 187, 120, 0.2);
}

.stat-card.back-stat:hover {
    background: rgba(160, 174, 192, 0.2);
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.stat-icon.warning {
    background: rgba(255, 193, 7, 0.3);
    color: #FFD700; /* ذهبي فاتح للأيقونة */
}

.stat-icon.add {
    background: rgba(72, 187, 120, 0.3);
    color: #98FB98; /* أخضر فاتح للأيقونة */
}

.stat-icon.back {
    background: rgba(160, 174, 192, 0.3);
    color: #E6E6FA; /* بنفسجي فاتح للأيقونة */
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 800;
    line-height: 1;
    margin-bottom: 2px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.stat-label {
    font-size: 0.95rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* ألوان مختلفة لكل زر إحصائي */
.stat-card.ungrouped-stat .stat-number {
    color: #FFD700; /* ذهبي فاتح للأرقام */
}

.stat-card.ungrouped-stat .stat-label {
    color: #FFF8DC; /* كريمي فاتح للنصوص */
}

.stat-card.add-group-stat .stat-number {
    color: #98FB98; /* أخضر فاتح للأرقام */
}

.stat-card.add-group-stat .stat-label {
    color: #F0FFF0; /* أخضر فاتح جداً للنصوص */
}

.stat-card.back-stat .stat-number {
    color: #E6E6FA; /* بنفسجي فاتح للأرقام */
}

.stat-card.back-stat .stat-label {
    color: #F8F8FF; /* أبيض مائل للبنفسجي للنصوص */
}

/* Groups Container */
.groups-container {
    padding: 2rem;
}

/* Groups Grid */
.groups-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.group-card {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px; /* Add general padding */
}

.group-card .group-icon {
    font-size: 2.5rem; /* Slightly smaller icon */
    margin-bottom: 10px;
    color: var(--primary-color);
}

.group-card .group-name {
    font-size: 1.1rem; /* Slightly smaller name */
    font-weight: 600;
    margin-bottom: 15px;
    text-align: center;
}

/* Group Summary Styles */
.group-summary {
    width: 100%;
    margin-bottom: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color-light);
    font-size: 0.9rem;
    color: var(--text-muted);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 2px 5px; /* Add slight padding */
}

.summary-item span:first-child { /* Label */
    font-weight: 500;
    color: var(--secondary-color);
}

.summary-item span:last-child { /* Value */
    font-weight: 600;
    /* Different colors for values */
}
.summary-item .value-due { color: var(--primary-color); }
.summary-item .value-paid { color: var(--success-color); }
.summary-item .value-remaining { color: var(--danger-color); }
.summary-item .value-count { color: var(--info-color); }

/* Group Card Actions */
.group-card-actions {
    width: 100%; /* Take full width */
    margin-top: auto; /* Push actions to the bottom */
    padding-top: 10px; /* Space above actions */
    border-top: 1px solid var(--border-color-light); /* Separator line */
    display: flex;
    justify-content: center;
    gap: 10px;
}

/* Hide empty group card actions */
.group-card-actions:empty {
    display: none;
    margin-top: 0;
    padding-top: 0;
    border-top: none;
}

/* Loading and empty state messages */
.loading-message {
    text-align: center;
    color: var(--secondary-color);
    font-style: italic;
    padding: 2rem;
    grid-column: 1 / -1; /* Span full width in grid */
}

.badge {
    background-color: var(--primary-color);
    color: white;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Header badges container */
.header-badges {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Ungrouped students button badge */
.badge-warning {
    background-color: var(--warning-color);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.badge-warning:hover {
    background-color: #e67e22;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(243, 156, 18, 0.3);
}

.ungrouped-students-btn {
    position: relative;
}

.ungrouped-students-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Ungrouped students modal styles */
.ungrouped-students-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--light-color);
}

.ungrouped-students-list {
    padding: 1rem;
}

.ungrouped-student-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 15px;
    margin-bottom: 8px;
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
}

.ungrouped-student-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.ungrouped-student-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.ungrouped-student-name {
    font-weight: 500;
    color: var(--dark-color);
}

.ungrouped-student-details {
    font-size: 0.85rem;
    color: var(--secondary-color);
}

.ungrouped-student-actions {
    display: flex;
    gap: 8px;
}

.ungrouped-student-actions .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* Loading placeholder for ungrouped students */
.ungrouped-students-list .loading-placeholder {
    text-align: center;
    padding: 2rem;
    color: var(--secondary-color);
}

.ungrouped-students-list .loading-placeholder.error {
    color: var(--error-color);
}

.ungrouped-students-list .loading-placeholder i {
    margin-left: 8px;
}

/* Empty state styling */
.ungrouped-students-list .loading-placeholder:not(.error):not(:has(i)) {
    color: var(--success-color);
    font-weight: 500;
}

/* Modal controls */
.modal-controls {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--light-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.selection-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.selected-count .badge {
    background-color: #17a2b8;
}

.badge-info {
    background-color: #17a2b8;
}

/* Student item with selection */
.ungrouped-student-item.selectable {
    cursor: pointer;
    transition: all 0.3s ease;
}

.ungrouped-student-item.selectable:hover {
    background-color: #f8f9fa;
    border-color: var(--primary-color);
}

.ungrouped-student-item.selected {
    background-color: #e3f2fd;
    border-color: var(--primary-color);
    border-width: 2px;
}

.student-checkbox {
    margin-left: 10px;
    transform: scale(1.2);
}

/* Groups selection view */
.groups-selection-container {
    max-height: 400px;
    overflow-y: auto;
}

.groups-list {
    padding: 1rem;
}

.group-selection-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    margin-bottom: 10px;
    background-color: white;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
}

.group-selection-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.group-selection-item.selected {
    background-color: #e3f2fd;
    border-color: var(--primary-color);
    border-width: 2px;
}

.group-selection-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.group-selection-name {
    font-weight: 500;
    color: var(--dark-color);
    font-size: 1.1rem;
}

.group-selection-details {
    font-size: 0.9rem;
    color: var(--secondary-color);
}

.group-selection-icon {
    font-size: 1.5rem;
    color: var(--primary-color);
}

/* Modal footer improvements */
.modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    background-color: var(--light-color);
}

.footer-left, .footer-right {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Success button */
.success-btn {
    background-color: var(--success-color);
    color: white;
}

.success-btn:hover {
    background-color: #27ae60;
}

/* Modal responsive design */
@media (max-width: 768px) {
    .ungrouped-student-item {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .ungrouped-student-actions {
        justify-content: center;
    }

    .ungrouped-students-container {
        max-height: 300px;
    }

    .modal-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .selection-controls {
        justify-content: center;
        flex-wrap: wrap;
    }

    .modal-footer {
        flex-direction: column;
        gap: 10px;
    }

    .footer-left, .footer-right {
        width: 100%;
        justify-content: center;
    }
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1.5rem;
    gap: 8px;
}

.pagination button {
    min-width: 35px;
    height: 35px;
    padding: 0 10px;
    border: 1px solid var(--border-color);
    background-color: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}
.pagination button:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}
.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
.pagination .page-info {
    font-size: 0.9rem;
    color: var(--secondary-color);
}

/* Message Area */
.message {
    padding: 0.8rem 1rem;
    border-radius: 6px;
    margin: 1rem 0;
    font-weight: 500;
    border: 1px solid transparent;
}
.message.info { background-color: #eaf5fd; color: #3498db; border-color: #aed6f1; }
.message.success { background-color: #eafaf1; color: #2ecc71; border-color: #a3e4d7; }
.message.error { background-color: #fdedec; color: #e74c3c; border-color: #f5b7b1; }
.message.warning { background-color: #fef9e7; color: #f39c12; border-color: #fad7a0; }

/* Modal Styles (Basic) */
.modal {
    display: none; /* Hidden by default */
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: var(--card-bg);
    margin: auto;
    padding: 0; /* Remove padding, handle in header/body/footer */
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 700px; /* Adjust as needed */
    position: relative;
    animation: fadeInModal 0.3s ease-out;
    max-height: 85vh;
    display: flex;
    flex-direction: column;
}

.modal-content h2 {
    margin: 0;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    font-size: 1.3rem;
    color: var(--primary-dark);
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-modal-btn {
    position: absolute;
    left: 15px; /* Adjusted for RTL */
    top: 10px;
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    background: none;
    border: none;
    line-height: 1;
}

.close-modal-btn:hover,
.close-modal-btn:focus {
    color: var(--danger-color);
}

.modal-body {
    padding: 1.5rem;
    overflow-y: auto; /* Allow scrolling if content exceeds height */
    flex-grow: 1;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: center; /* Center buttons */
    gap: 1rem;
    background-color: var(--light-color);
}

@keyframes fadeInModal {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Footer */
.page-footer {
    text-align: center;
    margin-top: 2rem;
    padding: 1rem;
    color: var(--secondary-color);
    font-size: 0.9rem;
    border-top: 1px solid var(--border-color);
}

/* Responsive */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    .header-controls {
        width: 100%;
        justify-content: space-between;
    }
    .controls-grid {
        grid-template-columns: 1fr; /* Stack controls */
    }
    .control-buttons {
        justify-content: center;
    }
    .groups-grid {
        grid-template-columns: 1fr; /* Stack cards on smaller screens */
    }
    .group-card {
        padding: 1rem;
    }
    .group-icon {
        font-size: 2rem;
    }
    .group-name {
        font-size: 1.1rem;
    }
}

/* Group Card Enhancements */
.group-card {
    position: relative; /* Needed for positioning buttons inside */
    padding-bottom: 50px; /* Add space for buttons at the bottom */
    display: flex; /* Use flexbox for better control */
    flex-direction: column;
    justify-content: center; /* Center main content */
    align-items: center;
}

.group-card .group-icon {
    font-size: 3rem; /* Adjust icon size */
    margin-bottom: 15px;
    color: var(--primary-color);
}

.group-card .group-name {
    font-size: 1.2rem; /* Adjust name size */
    font-weight: 600;
    margin-bottom: 15px; /* Space before buttons */
}

.group-card-actions {
    position: absolute;
    bottom: 10px;
    left: 10px;
    right: 10px;
    display: flex;
    justify-content: center; /* Center button(s) */
    gap: 10px;
}

.group-card-actions .action-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--secondary-color);
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.group-card-actions .action-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.group-card-actions .action-btn i {
    font-size: 0.9em;
}

/* Add Student Modal Specific Styles (if needed) */
#add-student-to-group-modal .modal-content {
    max-width: 500px; /* Adjust width if needed */
}

#add-student-to-group-modal select {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-small);
    font-size: 1rem;
    box-sizing: border-box;
    margin-bottom: 5px; /* Space before the small text */
}

#add-student-to-group-modal small {
    display: block;
    text-align: right;
    font-size: 0.85rem;
    color: var(--text-muted);
}

/* Add Default Students Modal Specific Styles */
#add-default-students-modal .modal-content {
    max-width: 600px; /* Adjust width if needed */
}

.checkbox-list-container {
    max-height: 300px; /* Limit height and allow scrolling */
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-small, 4px);
    padding: 10px;
    background-color: #f8f9fa; /* Light background */
}

.student-checkbox-item {
    display: flex;
    align-items: center;
    padding: 8px 5px;
    border-bottom: 1px solid #eee;
}

.student-checkbox-item:last-child {
    border-bottom: none;
}

.student-checkbox-item input[type="checkbox"] {
    margin-left: 10px; /* Space between checkbox and label (RTL) */
    width: auto; /* Override default width if needed */
    cursor: pointer;
}

.student-checkbox-item label {
    margin-bottom: 0; /* Override default form-group label margin */
    font-weight: normal;
    cursor: pointer;
    flex-grow: 1;
}

.checkbox-list-container .empty-item {
    text-align: center;
    color: var(--text-muted);
    padding: 20px;
}

/* ===== Enhanced Loading Animation ===== */

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    text-align: center;
}

.loading-animation {
    position: relative;
    margin-bottom: 1.5rem;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.loading-dots {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.loading-dots span {
    width: 8px;
    height: 8px;
    background: #667eea;
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

.loading-text {
    color: #667eea;
    font-size: 1.1rem;
    font-weight: 500;
    margin: 0;
    opacity: 0.8;
}

/* ===== Enhanced Group Cards Override ===== */

.groups-grid .group-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    cursor: pointer;
    animation: fadeInUp 0.6s ease-out;
    padding: 0; /* Reset padding */
    display: flex;
    flex-direction: column;
}

.groups-grid .group-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(102, 126, 234, 0.3);
}

.groups-grid .group-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.groups-grid .group-card:hover::before {
    transform: scaleX(1);
}

.group-card-header {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    text-align: center;
}

.group-card-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    margin: 0 auto 1rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.group-card-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2d3748;
    text-align: center;
    margin: 0 0 0.5rem 0;
}

.group-card-subtitle {
    font-size: 0.9rem;
    color: #718096;
    text-align: center;
    margin: 0;
}

.group-card-body {
    padding: 1.5rem;
    flex-grow: 1;
}

.group-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: #edf2f7;
    transform: translateY(-2px);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
    display: block;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.8rem;
    color: #718096;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.group-card-footer {
    padding: 1rem 1.5rem;
    background: #f7fafc;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    gap: 0.75rem;
    justify-content: center;
}

.group-action-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.group-action-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.group-action-btn.secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.group-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* ===== Animations ===== */

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Staggered animation for group cards */
.groups-grid .group-card:nth-child(1) { animation-delay: 0.1s; }
.groups-grid .group-card:nth-child(2) { animation-delay: 0.2s; }
.groups-grid .group-card:nth-child(3) { animation-delay: 0.3s; }
.groups-grid .group-card:nth-child(4) { animation-delay: 0.4s; }
.groups-grid .group-card:nth-child(5) { animation-delay: 0.5s; }
.groups-grid .group-card:nth-child(6) { animation-delay: 0.6s; }

/* ===== Progress Bar and Status ===== */

.group-status-bar {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

.status-progress {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
    transition: width 0.8s ease;
    animation: progressFill 1.5s ease-out;
}

.status-text {
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
    display: block;
}

/* ===== Empty State ===== */

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px dashed #e2e8f0;
    grid-column: 1 / -1; /* Span full width of grid */
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #a0aec0;
    margin-bottom: 1.5rem;
    animation: pulse 2s infinite;
}

.empty-state h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0 0 0.5rem 0;
}

.empty-state p {
    font-size: 1rem;
    color: #718096;
    margin: 0 0 2rem 0;
    max-width: 400px;
    line-height: 1.6;
}

/* ===== Additional Animations ===== */

@keyframes progressFill {
    from {
        width: 0%;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

/* ===== Enhanced Modal Styles ===== */

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(20px);
    transition: transform 0.3s ease;
}

.modal.active .modal-content {
    transform: scale(1) translateY(0);
}

.modal-content h2 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin: 0;
    padding: 1.5rem 2rem;
    border-radius: 16px 16px 0 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-modal-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 1.5rem;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-modal-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1rem 2rem 2rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* ===== Form Enhancements ===== */

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2d3748;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.required {
    color: #f56565;
}

/* ===== Button Enhancements ===== */

.control-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.control-btn:hover::before {
    left: 100%;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.control-btn:active {
    transform: translateY(0);
}

.primary-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.secondary-btn {
    background: #e2e8f0;
    color: #4a5568;
}

.success-btn {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.danger-btn {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
}

/* ===== Responsive Enhancements ===== */

@media (max-width: 768px) {
    .section-header {
        padding: 1.5rem;
    }

    .header-content h2 {
        font-size: 1.5rem;
    }

    .header-stats {
        flex-direction: column;
        gap: 0.75rem;
    }

    .stat-card {
        min-width: auto;
        width: 100%;
    }

    .groups-container {
        padding: 1rem;
    }

    .groups-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .group-stats {
        grid-template-columns: 1fr 1fr;
    }

    .group-card-footer {
        flex-direction: column;
        gap: 0.5rem;
    }

    .group-action-btn {
        width: 100%;
        justify-content: center;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .modal-content h2 {
        padding: 1rem 1.5rem;
        font-size: 1.1rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        padding: 1rem 1.5rem 1.5rem;
        flex-direction: column;
    }

    .control-btn {
        width: 100%;
        justify-content: center;
    }
}

/* ===== Tooltip Styles ===== */

.tooltip-container {
    position: relative;
    display: inline-block;
    cursor: help;
}

.tooltip {
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    font-size: 0.85rem;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    min-width: 200px;
    max-width: 350px;
    white-space: normal;
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 6px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
}

.tooltip-container:hover .tooltip {
    opacity: 1;
    visibility: visible;
    bottom: 130%;
}

.tooltip-header {
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #ffd700;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 0.25rem;
}

.tooltip-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
    padding: 0.25rem 0;
}

.tooltip-item:last-child {
    margin-bottom: 0;
}

.tooltip-student-name {
    font-weight: 500;
    color: #e2e8f0;
}

.tooltip-amount {
    font-weight: 700;
    color: #48bb78;
}

.tooltip-amount.negative {
    color: #f56565;
}

.tooltip-total {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    font-weight: 700;
    color: #ffd700;
}

.tooltip-loading {
    text-align: center;
    color: #a0aec0;
    font-style: italic;
}

.tooltip-error {
    text-align: center;
    color: #f56565;
    font-style: italic;
}

/* Enhanced stat-item for tooltips */
.stat-item.has-tooltip {
    cursor: help;
    transition: all 0.3s ease;
}

.stat-item.has-tooltip:hover {
    background: #edf2f7;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* ===== Message Styles ===== */

.message {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
    animation: slideInDown 0.3s ease;
}

.message.success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.message.warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border: 1px solid #ffeaa7;
}

.message.info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.message::before {
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    font-size: 1.1rem;
}

.message.success::before {
    content: "\f00c"; /* check */
}

.message.error::before {
    content: "\f00d"; /* times */
}

.message.warning::before {
    content: "\f071"; /* exclamation-triangle */
}

.message.info::before {
    content: "\f05a"; /* info-circle */
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== Loading Placeholder Enhancements ===== */

.loading-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    color: #718096;
    font-size: 1.1rem;
    gap: 0.75rem;
}

.loading-placeholder.error {
    color: #f56565;
}

.loading-placeholder i {
    font-size: 1.2rem;
}

/* ===== Utility Classes ===== */

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }

/* ===== Scrollbar Styling ===== */

::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* ===== Print Styles ===== */

@media print {
    .no-print,
    .modal,
    .sidebar,
    .control-btn,
    .group-card-footer {
        display: none !important;
    }

    .groups-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1rem !important;
    }

    .group-card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
