-- جدول لتخزين سجلات إقفال الحسابات الشهرية للسائقين
CREATE TABLE public.driver_monthly_closings (
    id BIGSERIAL PRIMARY KEY, -- المعرف الأسا<PERSON>ي المتزايد تلقائيًا
    driver_id UUID REFERENCES public.drivers(id) ON DELETE SET NULL, -- معرّف السائق (مفتاح خارجي لجدول السائقين)
    budget_month_id UUID REFERENCES public.budget_months(id) ON DELETE RESTRICT, -- معرّف شهر الميزانية (مفتاح خارجي لجدول أشهر الميزانية)
    
    closing_date TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL, -- تاريخ ووقت عملية الإقفال
    
    -- القيم المالية في لحظة الإقفال
    previous_balance_at_closing DECIMAL(10, 2) DEFAULT 0.00, -- الر<PERSON>يد المرحل من الشهر السابق عند الإقفال
    base_salary_at_closing DECIMAL(10, 2) DEFAULT 0.00, -- الراتب الأساسي للشهر الحالي عند الإقفال
    total_commissions_at_closing DECIMAL(10, 2) DEFAULT 0.00, -- إجمالي العمولات للشهر الحالي عند الإقفال
    total_deductions_at_closing DECIMAL(10, 2) DEFAULT 0.00, -- إجمالي الخصومات (سلف، مخالفات، إلخ) للشهر الحالي عند الإقفال
    total_payments_at_closing DECIMAL(10, 2) DEFAULT 0.00, -- إجمالي الدفعات المسلمة للسائق خلال الشهر الحالي عند الإقفال
    
    calculated_net_due_at_closing DECIMAL(10, 2) DEFAULT 0.00, -- الصافي المستحق المحسوب عند الإقفال (الرصيد السابق + الراتب + العمولات - الخصومات)
    final_remaining_balance DECIMAL(10, 2) DEFAULT 0.00, -- الرصيد النهائي المتبقي بعد الدفعات (سيتم ترحيله للشهر القادم)

    closed_by_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL, -- معرّف المستخدم الذي قام بعملية الإقفال (مفتاح خارجي لجدول المستخدمين في Supabase Auth)
    
    notes TEXT, -- ملاحظات إضافية حول عملية الإقفال

    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL, -- تاريخ ووقت إنشاء السجل

    -- قيد لضمان عدم إقفال حساب السائق لنفس الشهر أكثر من مرة
    CONSTRAINT unique_driver_month_closing UNIQUE (driver_id, budget_month_id)
);

-- تعليقات توضيحية للجدول والأعمدة
COMMENT ON TABLE public.driver_monthly_closings IS 'سجلات إقفال الحسابات الشهرية للسائقين، تحتوي على لقطة من الوضع المالي للسائق عند إقفال الشهر.';
COMMENT ON COLUMN public.driver_monthly_closings.id IS 'المعرف الأساسي لسجل الإقفال.';
COMMENT ON COLUMN public.driver_monthly_closings.driver_id IS 'معرّف السائق الذي تم إقفال حسابه.';
COMMENT ON COLUMN public.driver_monthly_closings.budget_month_id IS 'معرّف شهر الميزانية الذي تم إقفاله.';
COMMENT ON COLUMN public.driver_monthly_closings.closing_date IS 'تاريخ ووقت تنفيذ عملية الإقفال.';
COMMENT ON COLUMN public.driver_monthly_closings.previous_balance_at_closing IS 'الرصيد الافتتاحي للشهر، كما كان عند لحظة الإقفال.';
COMMENT ON COLUMN public.driver_monthly_closings.base_salary_at_closing IS 'الراتب الأساسي المعتمد للسائق لهذا الشهر عند الإقفال.';
COMMENT ON COLUMN public.driver_monthly_closings.total_commissions_at_closing IS 'مجموع العمولات المستحقة للسائق عن هذا الشهر عند الإقفال.';
COMMENT ON COLUMN public.driver_monthly_closings.total_deductions_at_closing IS 'مجموع الخصومات المطبقة على السائق عن هذا الشهر عند الإقفال.';
COMMENT ON COLUMN public.driver_monthly_closings.total_payments_at_closing IS 'مجموع المبالغ المدفوعة للسائق كتسوية لهذا الشهر عند الإقفال.';
COMMENT ON COLUMN public.driver_monthly_closings.calculated_net_due_at_closing IS 'المبلغ الصافي المستحق للسائق قبل خصم الدفعات، عند الإقفال.';
COMMENT ON COLUMN public.driver_monthly_closings.final_remaining_balance IS 'الرصيد النهائي المتبقي للسائق بعد تسوية الشهر، والذي سيُرحل كرصيد افتتاحي للشهر التالي.';
COMMENT ON COLUMN public.driver_monthly_closings.closed_by_user_id IS 'معرّف المستخدم الذي قام بتنفيذ عملية الإقفال.';
COMMENT ON COLUMN public.driver_monthly_closings.notes IS 'أي ملاحظات إضافية متعلقة بعملية الإقفال.';
COMMENT ON COLUMN public.driver_monthly_closings.created_at IS 'تاريخ ووقت إنشاء هذا السجل في قاعدة البيانات.';

-- تفعيل RLS (Row Level Security) للجدول - مهم لأمان البيانات في Supabase
ALTER TABLE public.driver_monthly_closings ENABLE ROW LEVEL SECURITY;

-- مثال على سياسة RLS (يجب تكييفها حسب الحاجة)
-- السماح للمستخدمين المصادق عليهم بقراءة جميع سجلات الإقفال
CREATE POLICY "Allow authenticated users to read closings"
ON public.driver_monthly_closings
FOR SELECT
TO authenticated
USING (true);

-- السماح للمستخدمين الذين لديهم دور 'admin' (أو دور مناسب) بإنشاء وتعديل وحذف سجلات الإقفال
-- (افترض أن لديك آلية لإدارة الأدوار أو عمود مخصص في جدول المستخدمين)
/*
CREATE POLICY "Allow admins to manage closings"
ON public.driver_monthly_closings
FOR ALL
TO authenticated
USING ( (SELECT auth.uid() IN (SELECT user_id FROM user_roles WHERE role = 'admin')) ) -- مثال، قد تحتاج لتعديل هذا الشرط
WITH CHECK ( (SELECT auth.uid() IN (SELECT user_id FROM user_roles WHERE role = 'admin')) );
*/

-- تأكد من وجود الجداول المشار إليها (drivers, budget_months) قبل إنشاء هذا الجدول.
-- جدول budget_months يمكن أن يكون بسيطًا:
/*
CREATE TABLE public.budget_months (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    month_year TEXT UNIQUE NOT NULL, -- e.g., "01-2024" for January 2024
    month_number INT NOT NULL CHECK (month_number >= 1 AND month_number <= 12),
    year_number INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
*/
