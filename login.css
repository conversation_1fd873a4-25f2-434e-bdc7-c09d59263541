.login-main {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - var(--navbar-height) - var(--footer-height) - 40px); /* Adjust based on container padding */
    padding: 20px 0;
}

.login-card {
    width: 100%;
    max-width: 450px; /* Limit width of the login card */
    text-align: center;
}

.login-card .card-header h2 {
    width: 100%; /* Ensure header text is centered */
    text-align: center;
}

.login-card .card-header h2 i {
    margin-left: 10px;
}

.login-instructions {
    color: var(--text-muted);
    margin-bottom: 20px;
    font-size: 0.95rem;
}

#login-form .form-group input[type="tel"] {
    text-align: left; /* Keep phone number LTR */
    direction: ltr;
    font-size: 1.1rem; /* Make input slightly larger */
    letter-spacing: 1px;
}

#login-form .form-actions {
    justify-content: center; /* Center the login button */
}

.login-btn {
    width: 100%; /* Make button full width */
    padding: 12px;
    font-size: 1.1rem;
}

.login-btn i {
    margin-left: 8px;
}
