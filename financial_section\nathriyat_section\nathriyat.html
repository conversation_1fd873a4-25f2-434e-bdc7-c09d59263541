<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة معاملات النثريات</title>
    <link rel="stylesheet" href="../../shared_styles.css">
    <!-- إضافة: رابط لملف CSS الخاص بالصفحة -->
    <link rel="stylesheet" href="nathriyat.css">
    <!-- نهاية الإضافة -->
    <!-- Include Supabase JS library -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Include Config Script -->
    <script src="../../config.js"></script>
    <!-- Auth Script -->
    <script src="../../auth.js"></script>
    <!-- Defer loading the script for this page -->
    <script defer src="nathriyat.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <header class="dashboard-header">
            <h1><i class="fas fa-coins"></i> إدارة معاملات النثريات</h1>
            <p>تسجيل ومتابعة معاملات المصروفات النثرية.</p>
            <!-- إعادة إضافة: عرض الشهر النشط -->
            <div id="active-month-display" class="active-month-info">
                الشهر النشط: <span id="active-month-name">جاري التحميل...</span>
                <!-- إضافة: قائمة منسدلة لتبديل الشهر -->
                <select id="month-switcher-select" class="month-switcher" title="تبديل الشهر النشط" style="display: none;">
                    <option value="">جاري تحميل الأشهر...</option>
                </select>
            </div>
            <!-- تغيير أيقونة زر تبديل القائمة -->
            <button id="sidebar-toggle-btn" class="control-btn sidebar-toggle" title="إظهار/إخفاء قائمة الأنواع">
                <i class="fas fa-list-ul"></i>
            </button>
            <button id="back-to-dashboard-btn" class="control-btn back-btn" style="position: absolute; top: 15px; left: 15px;">
                <i class="fas fa-arrow-left"></i> العودة للرئيسية
            </button>
        </header>

        <main class="dashboard-main">
            <div id="message-area" class="message" style="display: none;"></div>

            <!-- إضافة: زر إضافة نوع نثريات جديد -->
            <div class="action-buttons">
                <button id="add-nathriyat-type-btn" class="control-btn primary-btn">
                    <i class="fas fa-tags"></i> إدارة أنواع النثريات
                </button>
                <!-- إضافة: زر إضافة معاملة نثرية جديدة -->
                <button id="add-new-nathriya-btn" class="control-btn save-btn">
                    <i class="fas fa-plus-circle"></i> إضافة معاملة نثرية جديدة
                </button>
            </div>

            <!-- Section for adding new nathriyat -->
            <section class="form-section" id="add-nathriya-form-section" style="display: none;"> <!-- Added style="display: none;" -->
                <div class="form-card">
                    <div class="card-header">
                        <h2><i class="fas fa-plus-circle"></i> إضافة معاملة نثرية جديدة</h2>
                    </div>
                    <div class="card-body">
                        <form id="nathriyat-form">
                            <input type="hidden" id="nathriya_id">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="transaction_date">التاريخ <span class="required">*</span></label>
                                    <input type="date" id="transaction_date" required>
                                </div>
                                <!-- حقل اختيار نوع النثرية (تمت إضافته مسبقاً بواسطة JS) -->
                                <!-- <div class="form-group"> ... </div> -->
                                <div class="form-group">
                                    <label for="amount">المبلغ <span class="required">*</span></label>
                                    <input type="number" id="amount" step="0.01" min="0.01" required placeholder="0.00">
                                </div>
                                <!-- إضافة: حقل اختيار البنك -->
                                <div class="form-group">
                                    <label for="bank_id">البنك (المصروف منه) <span class="required">*</span></label>
                                    <select id="bank_id" required>
                                        <option value="" disabled selected>اختر البنك...</option>
                                        <!-- سيتم ملء الخيارات بواسطة JavaScript -->
                                    </select>
                                </div>
                                <div class="form-group form-group-full-width">
                                    <label for="notes">الملاحظات <span class="required">*</span></label>
                                    <textarea id="notes" rows="2" required placeholder="ملاحظات حول المعاملة النثرية"></textarea>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="submit" id="save-nathriya-btn" class="control-btn save-btn">
                                    <i class="fas fa-save"></i> حفظ المعاملة
                                </button>
                                <button type="button" id="cancel-edit-btn" class="control-btn cancel-btn" style="display: none;">
                                    <i class="fas fa-times"></i> إلغاء التعديل
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </section>

            <!-- إضافة: قسم لعرض أنواع النثريات -->
            <section class="types-section">
                <div class="table-card">
                    <div class="card-header">
                        <h2><i class="fas fa-tags"></i> أنواع النثريات</h2>
                        <span class="badge" id="types-count">0</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="nathriyat-types-table">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>الأيقونة</th>
                                        <th>الوصف</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="nathriyat-types-tbody">
                                    <!-- Data will be loaded here -->
                                    <tr><td colspan="4" class="loading-message">جاري تحميل أنواع النثريات...</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <div id="types-message" class="message" style="display: none;"></div>
                    </div>
                </div>
            </section>

            <!-- Section for displaying nathriyat -->
            <section class="table-section">
                <div class="table-card">
                    <div class="card-header">
                        <!-- تعديل العنوان ليشمل الشهر النشط (اختياري) -->
                        <h2><i class="fas fa-list-ul"></i> قائمة المعاملات النثرية للشهر النشط</h2>
                        <span class="badge" id="nathriyat-count">0</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="nathriyat-table">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>النوع</th> <!-- إضافة عمود النوع -->
                                        <th>الملاحظات</th>
                                        <th>المبلغ</th>
                                        <th>البنك</th> <!-- إضافة عمود البنك -->
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="nathriyat-tbody">
                                    <!-- Data will be loaded here -->
                                    <tr><td colspan="6" class="loading-message">جاري تحميل المعاملات...</td></tr> <!-- Adjusted colspan -->
                                </tbody>
                            </table>
                        </div>
                        <div id="list-message" class="message" style="display: none;"></div>
                        <!-- Pagination (Optional) -->
                        <div class="pagination" id="pagination-controls"></div>
                    </div>
                </div>
            </section>
        </main>

        <!-- إضافة نافذة منبثقة لإضافة نثرية جديدة -->
        <div id="add-nathriya-modal" class="modal centered-modal">
            <div class="modal-content">
                <span class="close-modal-btn" id="close-add-nathriya-modal-btn">&times;</span>
                <h2 id="add-nathriya-title"><i class="fas fa-plus-circle"></i> إضافة معاملة نثرية جديدة</h2>

                <div id="add-nathriya-message" class="message" style="display: none;"></div>

                <form id="add-nathriya-form">
                    <input type="hidden" id="nathriya_id" name="nathriya_id">

                    <div class="form-grid">
                        <!-- نوع النثرية (مطلوب) -->
                        <div class="form-group">
                            <label for="nathriyat_type_id">نوع النثرية <span class="required">*</span></label>
                            <select id="nathriyat_type_id" name="nathriyat_type_id" required>
                                <option value="" disabled selected>اختر نوع النثرية...</option>
                            </select>
                        </div>

                        <!-- تاريخ المعاملة (مطلوب) -->
                        <div class="form-group">
                            <label for="transaction_date">تاريخ المعاملة <span class="required">*</span></label>
                            <input type="date" id="transaction_date" name="transaction_date" required>
                        </div>

                        <!-- المبلغ (مطلوب) -->
                        <div class="form-group">
                            <label for="amount">المبلغ <span class="required">*</span></label>
                            <input type="number" id="amount" name="amount" step="0.01" min="0.01" required placeholder="0.00">
                        </div>

                        <!-- البنك (اختياري) -->
                        <div class="form-group">
                            <label for="bank_id">البنك (اختياري)</label>
                            <select id="bank_id" name="bank_id">
                                <option value="">-- بدون بنك --</option>
                            </select>
                        </div>

                        <!-- الملاحظات (اختياري) -->
                        <div class="form-group form-group-full-width">
                            <label for="notes">ملاحظات (اختياري)</label>
                            <textarea id="notes" name="notes" rows="3" placeholder="أي ملاحظات إضافية حول المعاملة..."></textarea>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" id="save-nathriya-btn" class="control-btn save-btn">
                            <i class="fas fa-save"></i> حفظ المعاملة
                        </button>
                        <button type="button" id="cancel-nathriya-btn" class="control-btn cancel-btn">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <footer class="dashboard-footer">
        </footer>
    </div>

    <!-- تعديل: نافذة منبثقة لإضافة/تعديل نوع نثرية -->
    <div id="nathriyat-type-modal" class="modal centered-modal">
        <div class="modal-content">
            <span class="close-modal-btn" id="close-type-modal-btn">&times;</span>
            <h2 id="type-modal-title">إضافة نوع نثرية جديد</h2>
            <form id="nathriyat-type-form">
                <input type="hidden" id="type_id">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="type_name">الاسم <span class="required">*</span></label>
                        <input type="text" id="type_name" required placeholder="اسم نوع النثرية">
                    </div>
                    <div class="form-group">
                        <label>الأيقونة (اختياري)</label>
                        <input type="hidden" id="type_icon" value="">
                        <div class="icon-selector">
                            <div class="icon-grid">
                                <div class="icon-option" data-icon="fa-coins"><i class="fas fa-coins"></i></div>
                                <div class="icon-option" data-icon="fa-car"><i class="fas fa-car"></i></div>
                                <div class="icon-option" data-icon="fa-utensils"><i class="fas fa-utensils"></i></div>
                                <div class="icon-option" data-icon="fa-tools"><i class="fas fa-tools"></i></div>
                                <div class="icon-option" data-icon="fa-phone"><i class="fas fa-phone"></i></div>
                                <div class="icon-option" data-icon="fa-gift"><i class="fas fa-gift"></i></div>
                                <div class="icon-option" data-icon="fa-shopping-cart"><i class="fas fa-shopping-cart"></i></div>
                                <div class="icon-option" data-icon="fa-file-invoice-dollar"><i class="fas fa-file-invoice-dollar"></i></div>
                                <div class="icon-option" data-icon="fa-hand-holding-dollar"><i class="fas fa-hand-holding-dollar"></i></div>
                                <div class="icon-option" data-icon="fa-money-bill"><i class="fas fa-money-bill"></i></div>
                                <div class="icon-option" data-icon="fa-gas-pump"><i class="fas fa-gas-pump"></i></div>
                                <div class="icon-option" data-icon="fa-pen"><i class="fas fa-pen"></i></div>
                                <div class="icon-option" data-icon="fa-lightbulb"><i class="fas fa-lightbulb"></i></div>
                                <div class="icon-option" data-icon="fa-building"><i class="fas fa-building"></i></div>
                                <div class="icon-option" data-icon="fa-coffee"><i class="fas fa-coffee"></i></div>
                                <div class="icon-option" data-icon="fa-laptop"><i class="fas fa-laptop"></i></div>
                            </div>
                            <div class="selected-icon-display">
                                <span>الأيقونة المختارة:</span>
                                <div class="icon-preview"><i class="fas fa-coins"></i></div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group form-group-full-width">
                        <label for="type_description">الوصف (اختياري)</label>
                        <textarea id="type_description" rows="2" placeholder="وصف مختصر للنوع"></textarea>
                    </div>
                </div>
                <div id="type-form-message" class="message" style="display: none;"></div>
                <div class="form-actions">
                    <button type="submit" id="save-type-btn" class="control-btn save-btn">
                        <i class="fas fa-save"></i> حفظ النوع
                    </button>
                    <button type="button" id="cancel-type-btn" class="control-btn cancel-btn">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- إضافة: نافذة منبثقة لعرض تفاصيل نوع النثرية -->
    <div id="type-details-modal" class="modal centered-modal">
        <div class="modal-content large"> <!-- استخدام فئة large لعرض جدول -->
            <span class="close-modal-btn" id="close-type-details-modal-btn">&times;</span>
            <h2 id="type-details-title">تفاصيل نوع النثرية: <span></span></h2>
            <div id="type-details-message" class="message" style="display: none;"></div>
            <div class="table-responsive modal-table-container">
                <table id="type-details-table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>الملاحظات</th>
                            <th>المبلغ</th>
                            <th>البنك</th>
                        </tr>
                    </thead>
                    <tbody id="type-details-tbody">
                        <!-- سيتم تحميل تفاصيل المعاملات هنا -->
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="2" style="text-align: left; font-weight: bold;">المجموع لهذا الشهر:</td>
                            <td id="type-details-total" style="font-weight: bold;">0.00 ريال</td>
                            <td></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</body>
</html>
