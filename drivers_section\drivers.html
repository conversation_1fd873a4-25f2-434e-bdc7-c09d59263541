<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة السائقين</title>

    <!-- الترتيب مهم جداً حسب الدليل -->
    <!-- Shared Styles -->
    <link rel="stylesheet" href="../shared_styles.css">
    <!-- Page Specific Styles -->
    <link rel="stylesheet" href="style.css">
    <!-- Sidebar Component Styles -->
    <link rel="stylesheet" href="../shared_components/sidebar.css">

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="../config.js"></script>
    <script src="../auth.js"></script>
    <!-- Sidebar Component Script -->
    <script src="../shared_components/sidebar.js"></script>
    <!-- <PERSON> Script -->
    <script defer src="script.js"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body class="dashboard-layout" data-load-sidebar="true">
    <!-- Top Navigation Bar -->
    <nav class="top-navbar">
        <div class="navbar-left">
            <button id="sidebar-toggle" class="sidebar-toggle-btn">
                <i class="fas fa-bars"></i>
            </button>
            <div class="navbar-brand">
                <i class="fas fa-id-card-alt"></i>
                <span>نظام إدارة السائقين</span>
            </div>
        </div>
        <div class="navbar-right">
            <div class="navbar-user">
                <span id="navbar-username"></span>
                <button id="logout-btn" class="logout-btn" style="display: none;">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Sidebar Container (will be populated by sidebar.js) -->
    <div id="sidebar-container"></div>

    <!-- Main Content Area -->
    <main class="main-content">
        <div class="content-wrapper">
            <!-- Dashboard Header -->
            <header class="dashboard-header">
                <div class="header-content">
                    <h1>
                        <i class="fas fa-id-card-alt"></i>
                        نظام إدارة السائقين
                    </h1>
                    <p>إدارة بيانات السائقين والمصروفات</p>
                </div>
            </header>

            <!-- Statistics Cards -->
            <section class="stats-section">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-users"></i></div>
                    <div class="stat-info">
                        <h3 id="total-drivers">0</h3>
                        <p>إجمالي السائقين</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-user-check"></i></div>
                    <div class="stat-info">
                        <h3 id="active-drivers">0</h3>
                        <p>سائقون نشطون</p>
                    </div>
                </div>
                <!-- Removed Total Salary Stat Card -->
            </section>

            <!-- Controls Section -->
            <section class="controls-section">
                <div class="control-card">
                    <div class="card-header">
                        <h2>أدوات التحكم</h2>
                    </div>
                    <div class="card-body">
                        <div class="controls-grid">
                            <!-- Add Driver Button -->
                            <button id="add-driver-btn" class="control-btn add-btn">
                                <i class="fas fa-plus-circle"></i> إضافة سائق
                            </button>
                            <div class="search-container">
                                <input type="text" id="search-input" placeholder="ابحث عن سائق بالاسم أو الرقم...">
                                <button id="search-btn" class="search-btn"><i class="fas fa-search"></i></button>
                            </div>
                            <button id="print-report-btn" class="control-btn print-btn">
                                <i class="fas fa-print"></i> طباعة تقرير
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Drivers Table Section -->
            <section class="table-section">
                <div class="table-card">
                    <div class="card-header">
                        <h2>قائمة السائقين</h2>
                        <span class="badge" id="drivers-count">0</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="drivers-table">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>رقم الجوال</th>
                                        <th>نوع الدوام</th>
                                        <th>الحالة</th>
                                        <!-- Removed Salary Header -->
                                        <th>ملاحظات</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="drivers-tbody">
                                    <!-- Updated colspan to 6 -->
                                    <tr><td colspan="6" class="loading-message">جاري تحميل البيانات...</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <div id="list-message" class="message"></div>
                        <div class="pagination" id="pagination-controls">
                            <!-- Pagination will be added by JavaScript -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Driver Form Section (Modal) -->
            <section id="add-driver-section" class="form-section">
                <div class="form-card">
                    <div class="card-header">
                        <h2 id="form-title">إضافة سائق جديد</h2>
                        <button id="close-form-btn" class="close-btn"><i class="fas fa-times"></i></button>
                    </div>
                    <div class="card-body">
                        <form id="driver-form">
                            <input type="hidden" id="driver_id" name="driver_id">

                            <!-- Driver Information Section -->
                            <fieldset class="form-fieldset">
                                <legend>بيانات السائق</legend>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="driver_name">اسم السائق <span class="required">*</span></label>
                                        <input type="text" id="driver_name" name="driver_name" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="driver_phone">رقم الجوال <span class="required">*</span></label>
                                        <input type="tel" id="driver_phone" name="driver_phone" placeholder="05xxxxxxxx" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="work_shift">نوع الدوام <span class="required">*</span></label>
                                        <select id="work_shift" name="work_shift" required>
                                            <option value="" disabled selected>اختر نوع الدوام</option>
                                            <!-- Updated options -->
                                            <option value="كامل">كامل</option>
                                            <option value="جزئي">جزئي</option>
                                        </select>
                                    </div>
                                    <!-- Removed Monthly Salary form group -->
                                </div>
                                <div class="form-row">
                                    <div class="form-group form-group-checkbox">
                                        <label for="is_active">
                                            <input type="checkbox" id="is_active" name="is_active" value="true" checked>
                                            نشط
                                        </label>
                                    </div>
                                     <div class="form-group"> <!-- Notes can take full row now or adjust layout -->
                                        <label for="notes">ملاحظات</label>
                                        <textarea id="notes" name="notes" rows="3"></textarea>
                                    </div>
                                </div>
                            </fieldset>

                            <div class="form-actions">
                                <button type="submit" class="submit-btn">حفظ</button>
                                <button type="button" id="cancel-edit-btn" class="cancel-btn">إلغاء</button>
                            </div>
                        </form>
                        <div id="form-message" class="message"></div>
                    </div>
                </div>
            </section>
        </div>
    </main>
</body>
</html>
