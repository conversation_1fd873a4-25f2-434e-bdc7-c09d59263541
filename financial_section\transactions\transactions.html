<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المعاملات البنكية</title>
    <!-- Assuming a similar style.css exists or using inline styles -->
    <link rel="stylesheet" href="../subscriptions/style.css"> <!-- Using subscription styles for similarity -->
    <link rel="stylesheet" href="transactions_style.css"> <!-- Add specific styles if needed -->
    <!-- Include Supabase JS library -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Include Config and Main Script -->
    <script src="../../config.js"></script>
    <script defer src="transactions.js"></script> <!-- Ensure this JS file exists -->
    <!-- Add Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container with-sidebar"> <!-- Add 'with-sidebar' class -->

        <!-- Sidebar for Banks -->
        <aside class="dashboard-sidebar">
            <h2><i class="fas fa-university"></i> البنوك</h2>
            <ul id="banks-sidebar-list">
                <li class="loading-message">جاري تحميل البنوك...</li>
                <!-- Bank list items will be populated by JS -->
                <!-- Example:
                <li><a href="#" data-bank-id="all" class="active">كل البنوك</a></li>
                <li><a href="#" data-bank-id="1">بنك الرياض</a></li>
                <li><a href="#" data-bank-id="2">البنك الأهلي</a></li>
                 -->
            </ul>
            <div class="sidebar-footer">
                <button id="manage-banks-btn" class="control-btn secondary-btn" title="إدارة البنوك">
                    <i class="fas fa-cog"></i> إدارة البنوك
                </button>
            </div>
        </aside>

        <div class="main-content-area"> <!-- Wrapper for header, main, footer -->
            <header class="dashboard-header">
                <div class="header-content">
                    <h1><i class="fas fa-exchange-alt"></i> إدارة المعاملات البنكية</h1>
                    <p>عرض وتحليل حركة الأموال في حساباتك البنكية.</p>
                    <!-- Add Back Button if needed -->
                     <button id="back-to-finance-btn" class="control-btn back-btn" title="العودة للقائمة المالية">
                        <i class="fas fa-arrow-right"></i> العودة
                    </button>
                </div>
            </header>

            <main class="dashboard-main">
                <!-- Dashboard Cards Section -->
                <section class="dashboard-cards">
                    <div class="dash-card">
                        <div class="card-icon success"><i class="fas fa-arrow-down"></i></div>
                        <div class="card-content">
                            <h3>إجمالي الإيداعات</h3>
                            <p id="total-deposits-card">0 ريال</p>
                            <span>الفترة المحددة</span>
                        </div>
                    </div>
                    <div class="dash-card">
                        <div class="card-icon danger"><i class="fas fa-arrow-up"></i></div>
                        <div class="card-content">
                            <h3>إجمالي السحوبات</h3>
                            <p id="total-withdrawals-card">0 ريال</p>
                            <span>الفترة المحددة</span>
                        </div>
                    </div>
                    <div class="dash-card">
                        <div class="card-icon info"><i class="fas fa-wallet"></i></div>
                        <div class="card-content">
                            <h3>الرصيد الحالي (تقديري)</h3>
                            <p id="current-balance-card">0 ريال</p>
                            <span id="selected-bank-balance-label">لكل البنوك</span>
                        </div>
                    </div>
                    <div class="dash-card"> <!-- Placeholder for Chart -->
                        <div class="card-icon primary"><i class="fas fa-chart-line"></i></div>
                        <div class="card-content">
                            <h3>ملخص الحركة</h3>
                            <p>قيد التطوير</p>
                            <div id="transaction-chart-placeholder" style="height: 50px;"></div><!-- Chart Placeholder -->
                        </div>
                    </div>
                </section>

                <!-- Controls & Filters Section -->
                <section class="controls-section">
                    <div class="control-card">
                        <div class="card-header">
                            <h2><i class="fas fa-filter"></i> فلاتر البحث والتحكم</h2>
                        </div>
                        <div class="card-body">
                            <div class="controls-grid filters-grid">
                                <div class="form-group">
                                    <label for="filter-date-start">من تاريخ:</label>
                                    <input type="date" id="filter-date-start">
                                </div>
                                <div class="form-group">
                                    <label for="filter-date-end">إلى تاريخ:</label>
                                    <input type="date" id="filter-date-end">
                                </div>
                                <div class="form-group">
                                    <label for="filter-type">نوع المعاملة:</label>
                                    <select id="filter-type">
                                        <option value="">الكل</option>
                                        <option value="deposit">إيداع</option>
                                        <option value="withdrawal">سحب</option>
                                        <option value="transfer">تحويل</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="filter-bank">البنك:</label>
                                    <select id="filter-bank">
                                        <option value="">كل البنوك</option>
                                        <!-- Banks loaded by JS -->
                                    </select>
                                </div>
                                <div class="search-container form-group">
                                    <label for="search-input">بحث (الوصف):</label>
                                    <input type="text" id="search-input" placeholder="ابحث في الوصف...">
                                </div>
                                <button id="filter-btn" class="control-btn filter-btn" title="تطبيق الفلاتر والبحث">
                                    <i class="fas fa-search"></i> بحث/تحديث
                                </button>
                                <button id="reset-filter-btn" class="control-btn reset-btn" title="إعادة تعيين الفلاتر">
                                    <i class="fas fa-undo"></i> إعادة تعيين
                                </button>
                                <button id="add-transaction-btn" class="control-btn add-btn" title="إضافة معاملة جديدة">
                                    <i class="fas fa-plus"></i> إضافة معاملة
                                </button>
                            </div>
                            <div id="filter-message" class="message info" style="display: none;"></div>
                        </div>
                    </div>
                </section>

                <!-- Transactions Table Section -->
                <section class="table-section">
                    <div class="table-card">
                        <div class="card-header">
                            <h2><i class="fas fa-list"></i> قائمة المعاملات</h2>
                            <span class="badge" id="transactions-count">0</span>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="transactions-table">
                                    <thead>
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>البنك</th>
                                            <th>النوع</th>
                                            <th>المبلغ</th>
                                            <th>الوصف</th>
                                            <th>المصدر</th> <!-- e.g., subscription, expense -->
                                            <th>المرجع</th> <!-- e.g., subscription_id, expense_id -->
                                            <th>إجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="transactions-tbody">
                                        <tr><td colspan="8" class="loading-message">جاري تحميل المعاملات...</td></tr>
                                    </tbody>
                                </table>
                            </div>
                            <div id="list-message" class="message"></div>
                            <div class="pagination" id="pagination-controls"></div>
                        </div>
                    </div>
                </section>

                <!-- Add/Edit Transaction Form Section (Modal) -->
                <section id="transaction-form-section" class="form-section">
                    <div class="form-card"> <!-- Standard size modal -->
                        <div class="card-header">
                            <h2 id="transaction-form-title">إضافة معاملة جديدة</h2>
                            <button id="close-transaction-form-btn" class="close-btn"><i class="fas fa-times"></i></button>
                        </div>
                        <div class="card-body">
                            <form id="transaction-form">
                                <input type="hidden" id="transaction_id" name="transaction_id">

                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="transaction_date">التاريخ <span class="required">*</span></label>
                                        <input type="date" id="transaction_date" name="transaction_date" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="bank_id">البنك <span class="required">*</span></label>
                                        <select id="bank_id" name="bank_id" required>
                                            <option value="">اختر البنك...</option>
                                            <!-- Banks loaded by JS -->
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="transaction_type">النوع <span class="required">*</span></label>
                                        <select id="transaction_type" name="transaction_type" required>
                                            <option value="">اختر النوع...</option>
                                            <option value="deposit">إيداع</option>
                                            <option value="withdrawal">سحب</option>
                                            <option value="transfer">تحويل</option>
                                            <!-- Add other types if needed -->
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="amount">المبلغ <span class="required">*</span></label>
                                        <input type="number" id="amount" name="amount" required min="0.01" step="0.01">
                                    </div>
                                     <div class="form-group form-group-span-2"> <!-- Span across 2 columns -->
                                        <label for="description">الوصف <span class="required">*</span></label>
                                        <input type="text" id="description" name="description" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="transaction_source_type">المصدر (اختياري)</label>
                                        <select id="transaction_source_type" name="transaction_source_type">
                                            <option value="">لا يوجد</option>
                                            <option value="subscription">اشتراك طالب</option>
                                            <option value="expense">مصروف</option>
                                            <option value="salary">راتب موظف</option>
                                            <option value="other">أخرى</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="reference_id">المعرف المرجعي (اختياري)</label>
                                        <input type="text" id="reference_id" name="reference_id" placeholder="مثل: رقم الاشتراك أو المصروف">
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="submit-btn">حفظ المعاملة</button>
                                    <button type="button" id="cancel-transaction-btn" class="cancel-btn">إلغاء</button>
                                </div>
                            </form>
                            <div id="transaction-form-message" class="message"></div>
                        </div>
                    </div>
                </section>

            </main>

            <footer class="dashboard-footer">
                <p>© 2023 نظام الإدارة المالية - جميع الحقوق محفوظة</p>
            </footer>
        </div> <!-- End main-content-area -->
    </div> <!-- End dashboard-container -->

    <style>
        /* Basic styles to mimic subscriptions.html layout */
        :root {
            --primary-color: #2c3e50; /* Dark Blue-Gray */
            --primary-dark: #1a252f;
            --secondary-color: #34495e; /* Lighter Blue-Gray */
            --accent-color: #3498db; /* Blue */
            --light-color: #ecf0f1; /* Light Gray */
            --text-color: #333;
            --text-muted: #777;
            --border-color: #ddd;
            --card-bg: #fff;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --success-color: #2ecc71;
            --border-radius: 8px;
            --card-shadow: 0 2px 5px rgba(0,0,0,0.1);
            --sidebar-width: 250px; /* Width of the sidebar */
        }
        body {
            font-family: 'Cairo', sans-serif; /* Example Arabic font */
            background-color: var(--light-color);
            margin: 0;
            color: var(--text-color);
        }
        .dashboard-container.with-sidebar {
            display: flex;
        }
        .dashboard-sidebar {
            width: var(--sidebar-width);
            background-color: var(--primary-color);
            color: #fff;
            padding: 20px 0;
            height: 100vh;
            position: fixed; /* Fixed sidebar */
            top: 0;
            right: 0; /* Positioned on the right for RTL */
            display: flex;
            flex-direction: column;
            box-shadow: -2px 0 5px rgba(0,0,0,0.1);
        }
        .dashboard-sidebar h2 {
            text-align: center;
            margin-bottom: 20px;
            padding: 0 15px;
            font-size: 1.3rem;
            color: var(--light-color);
        }
        .dashboard-sidebar h2 i { margin-left: 10px; }

        #banks-sidebar-list {
            list-style: none;
            padding: 0;
            margin: 0;
            flex-grow: 1; /* Takes available space */
            overflow-y: auto; /* Scroll if list is long */
        }
        #banks-sidebar-list li a {
            display: block;
            padding: 12px 20px;
            color: #bdc3c7; /* Light gray text */
            text-decoration: none;
            transition: background-color 0.2s ease, color 0.2s ease;
            border-right: 4px solid transparent; /* Indicator for active item */
        }
        #banks-sidebar-list li a:hover {
            background-color: var(--secondary-color);
            color: #fff;
        }
        #banks-sidebar-list li a.active {
            background-color: var(--secondary-color);
            color: #fff;
            font-weight: bold;
            border-right-color: var(--accent-color);
        }
         #banks-sidebar-list .loading-message {
             padding: 15px 20px;
             color: var(--text-muted);
             font-style: italic;
         }
        .sidebar-footer {
            padding: 15px;
            border-top: 1px solid var(--secondary-color);
        }
        .sidebar-footer .control-btn {
            width: 100%;
            text-align: center;
        }
        .secondary-btn {
            background-color: var(--secondary-color);
            color: #fff;
        }
        .secondary-btn:hover {
            background-color: #4a637a;
        }

        .main-content-area {
            flex-grow: 1;
            padding-right: var(--sidebar-width); /* Space for the fixed sidebar */
            /* If sidebar was on left: padding-left: var(--sidebar-width); */
        }

        .dashboard-header {
            background-color: var(--primary-color);
            color: #fff;
            padding: 20px 30px;
            position: relative; /* For back button positioning */
        }
        .header-content h1 { margin: 0 0 10px 0; font-size: 1.8rem; }
        .header-content h1 i { margin-left: 10px; }
        .header-content p { margin: 0; color: var(--light-color); }
        .back-btn {
            position: absolute;
            top: 20px;
            left: 30px; /* Position top-left */
            background-color: var(--secondary-color);
            color: #fff;
        }
        .back-btn:hover { background-color: #4a637a; }

        .dashboard-main {
            padding: 30px;
        }
        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .dash-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .card-icon {
            font-size: 2rem;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
        }
        .card-icon.success { background-color: var(--success-color); }
        .card-icon.danger { background-color: var(--danger-color); }
        .card-icon.warning { background-color: var(--warning-color); }
        .card-icon.info { background-color: #17a2b8; }
        .card-icon.primary { background-color: var(--accent-color); }

        .card-content h3 { margin: 0 0 5px 0; font-size: 0.9rem; color: var(--text-muted); font-weight: 500; }
        .card-content p { margin: 0 0 8px 0; font-size: 1.5rem; font-weight: 700; color: var(--secondary-color); }
        .card-content span { font-size: 0.85rem; color: var(--text-muted); }

        .controls-section, .table-section { margin-bottom: 30px; }
        .control-card, .table-card, .form-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
        }
        .card-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-header h2 { margin: 0; font-size: 1.2rem; color: var(--primary-dark); }
        .card-header h2 i { margin-left: 8px; }
        .card-body { padding: 20px; }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px 20px;
            align-items: end;
        }
        .form-group { margin-bottom: 0; } /* Remove bottom margin for grid layout */
        .form-group label { display: block; margin-bottom: 5px; font-weight: 500; font-size: 0.9em; }
        .form-group input[type="text"],
        .form-group input[type="date"],
        .form-group input[type="number"],
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            box-sizing: border-box;
            font-size: 0.95rem;
        }
        .control-btn {
            padding: 10px 15px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.2s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #fff;
        }
        .filter-btn { background-color: var(--accent-color); }
        .filter-btn:hover { background-color: #2980b9; }
        .reset-btn { background-color: var(--secondary-color); }
        .reset-btn:hover { background-color: #4a637a; }
        .add-btn { background-color: var(--success-color); }
        .add-btn:hover { background-color: #27ae60; }

        .table-responsive { overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; }
        th, td {
            padding: 12px 15px;
            text-align: right;
            border-bottom: 1px solid var(--border-color);
            white-space: nowrap;
        }
        th { background-color: #f8f9fa; font-weight: 600; color: var(--secondary-color); }
        tbody tr:hover { background-color: #f1f1f1; }
        .loading-message, .message { text-align: center; padding: 20px; color: var(--text-muted); }
        .message.info { background-color: #e2f3fb; border: 1px solid #bde0ee; color: #0c5464; }
        .message.success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .message.warning { background-color: #fff3cd; border: 1px solid #ffeeba; color: #856404; }
        .message.error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .message { border-radius: var(--border-radius); margin-top: 15px; display: block; }
        .message.show { display: block; } /* Ensure message is visible */

        .pagination { margin-top: 20px; text-align: center; }
        .pagination button {
            background-color: var(--secondary-color);
            color: white;
            border: none;
            padding: 8px 12px;
            margin: 0 3px;
            border-radius: var(--border-radius);
            cursor: pointer;
        }
        .pagination button:disabled { background-color: #ccc; cursor: not-allowed; }
        .pagination button.active { background-color: var(--accent-color); }
        .pagination .pagination-ellipsis { margin: 0 5px; color: var(--text-muted); }

        /* Modal Styles */
        .form-section {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        .form-section.show {
            opacity: 1;
            visibility: visible;
        }
        .form-card {
            width: 90%;
            max-width: 700px; /* Adjust as needed */
            max-height: 90vh;
            overflow-y: auto;
        }
        .form-card .card-header { position: relative; }
        .close-btn {
            position: absolute;
            top: 10px;
            left: 15px;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-muted);
        }
        .close-btn:hover { color: var(--danger-color); }
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px 20px;
        }
        .form-group-span-2 { grid-column: span 2; } /* For wider elements */
        .form-actions {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        .submit-btn { background-color: var(--success-color); }
        .submit-btn:hover { background-color: #27ae60; }
        .cancel-btn { background-color: var(--text-muted); }
        .cancel-btn:hover { background-color: #666; }
        .required { color: var(--danger-color); margin-right: 3px; }

        /* Action buttons in table */
        .action-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 5px;
            margin: 0 3px;
            font-size: 1rem;
            color: var(--text-muted);
        }
        .action-btn:hover { color: var(--accent-color); }
        .action-btn.delete-btn:hover { color: var(--danger-color); }
        .action-btn:disabled { color: #ccc; cursor: not-allowed; }

        /* Transaction type specific colors (optional) */
        td:nth-child(3)[data-type="deposit"] { color: var(--success-color); }
        td:nth-child(3)[data-type="withdrawal"] { color: var(--danger-color); }
        td:nth-child(4)[data-type="deposit"] { font-weight: bold; }
        td:nth-child(4)[data-type="withdrawal"] { font-weight: bold; }

        /* Responsive adjustments */
        @media (max-width: 992px) {
            .dashboard-sidebar { display: none; } /* Hide sidebar on smaller screens */
            .main-content-area { padding-right: 0; }
            .form-group-span-2 { grid-column: span 1; } /* Stack elements */
        }
         @media (max-width: 768px) {
            .dashboard-header { padding: 15px 20px; }
            .header-content h1 { font-size: 1.5rem; }
            .back-btn { top: 15px; left: 15px; padding: 6px 10px; font-size: 0.8rem;}
            .dashboard-main { padding: 20px; }
            .dashboard-cards { grid-template-columns: 1fr; } /* Stack cards */
            .controls-grid { grid-template-columns: 1fr; } /* Stack filters */
            th, td { padding: 10px 8px; font-size: 0.9rem; }
         }

    </style>
</body>
</html>
