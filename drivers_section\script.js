// Supabase Initialization - using variables already defined in config.js
let supabaseUrl, supabaseAnonKey;

// Safely access the global variables
if (typeof SUPABASE_URL !== 'undefined') {
    supabaseUrl = SUPABASE_URL;
} else {
    supabaseUrl = 'YOUR_FALLBACK_SUPABASE_URL'; // Add fallback or handle error
    console.error('SUPABASE_URL not found in global scope.');
}

if (typeof SUPABASE_ANON_KEY !== 'undefined') {
    supabaseAnonKey = SUPABASE_ANON_KEY;
} else {
    supabaseAnonKey = 'YOUR_FALLBACK_SUPABASE_ANON_KEY'; // Add fallback or handle error
    console.error('SUPABASE_ANON_KEY not found in global scope.');
}

const { createClient } = supabase;
const _supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('Supabase Initialized for Drivers:', _supabase);

// --- DOM Elements ---
const driverForm = document.getElementById('driver-form');
const formMessage = document.getElementById('form-message');
const addDriverBtn = document.getElementById('add-driver-btn');
const addDriverSection = document.getElementById('add-driver-section');
const cancelEditBtn = document.getElementById('cancel-edit-btn');
const closeFormBtn = document.getElementById('close-form-btn');
const driversTableBody = document.getElementById('drivers-tbody');
const listMessage = document.getElementById('list-message');
const searchInput = document.getElementById('search-input');
const searchBtn = document.getElementById('search-btn');
const printReportBtn = document.getElementById('print-report-btn');
const driverIdField = document.getElementById('driver_id');
const formTitle = document.getElementById('form-title');
const totalDriversEl = document.getElementById('total-drivers');
const activeDriversEl = document.getElementById('active-drivers');
const driversCountBadge = document.getElementById('drivers-count');
const paginationControls = document.getElementById('pagination-controls');

// --- State ---
let currentDrivers = [];
let editMode = false;
let stats = {
    totalDrivers: 0,
    activeDrivers: 0
};

// Pagination variables
let currentPage = 1;
const driversPerPage = 12; // Number of drivers per page
let totalPages = 1;

// --- Functions ---

// Function to display messages (form or list)
const showMessage = (element, message, type = 'info') => {
    if (!element) return; // Guard clause
    element.textContent = message;
    element.className = `message ${type} show`; // Ensure 'show' class is added

    // Auto hide success messages after 5 seconds
    if (type === 'success') {
        setTimeout(() => {
            element.style.display = 'none'; // Hide element directly
            element.classList.remove('show');
        }, 5000);
    } else {
         element.style.display = 'block'; // Ensure non-success messages are visible
    }
};

// Function to render pagination controls
const renderPaginationControls = () => {
    if (!paginationControls) return;
    paginationControls.innerHTML = '';

    // Don't show pagination if no drivers or only one page
    if (!currentDrivers || currentDrivers.length <= driversPerPage) {
        return;
    }

    totalPages = Math.ceil(currentDrivers.length / driversPerPage);

    // Previous button
    const prevButton = document.createElement('button');
    prevButton.innerHTML = '<i class="fas fa-angle-right"></i>';
    prevButton.className = currentPage === 1 ? 'disabled' : '';
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', () => {
        if (currentPage > 1) {
            goToPage(currentPage - 1);
        }
    });
    paginationControls.appendChild(prevButton);

    // Page buttons logic (simplified for brevity, can use the detailed logic from students script)
     const maxPageButtons = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
    let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);

    if (endPage - startPage + 1 < maxPageButtons) {
        startPage = Math.max(1, endPage - maxPageButtons + 1);
    }

    if (startPage > 1) {
        const firstPageBtn = document.createElement('button');
        firstPageBtn.textContent = '1';
        firstPageBtn.addEventListener('click', () => goToPage(1));
        paginationControls.appendChild(firstPageBtn);
        if (startPage > 2) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'pagination-ellipsis';
            paginationControls.appendChild(ellipsis);
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.textContent = i.toString();
        pageBtn.className = i === currentPage ? 'active' : '';
        pageBtn.addEventListener('click', () => goToPage(i));
        paginationControls.appendChild(pageBtn);
    }

     if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
             const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'pagination-ellipsis';
            paginationControls.appendChild(ellipsis);
        }
        const lastPageBtn = document.createElement('button');
        lastPageBtn.textContent = totalPages.toString();
        lastPageBtn.addEventListener('click', () => goToPage(totalPages));
        paginationControls.appendChild(lastPageBtn);
    }

    // Next button
    const nextButton = document.createElement('button');
    nextButton.innerHTML = '<i class="fas fa-angle-left"></i>';
    nextButton.className = currentPage === totalPages ? 'disabled' : '';
    nextButton.disabled = currentPage === totalPages;
    nextButton.addEventListener('click', () => {
        if (currentPage < totalPages) {
            goToPage(currentPage + 1);
        }
    });
    paginationControls.appendChild(nextButton);
};

// Function to navigate to a specific page
const goToPage = (page) => {
    currentPage = page;
    renderDriversTable(currentDrivers); // Re-render table for the new page
};

// Function to get current page drivers
const getCurrentPageDrivers = () => {
    const startIndex = (currentPage - 1) * driversPerPage;
    const endIndex = startIndex + driversPerPage;
    return currentDrivers.slice(startIndex, endIndex);
};

// Function to render the drivers table
const renderDriversTable = (drivers) => {
    if (!driversTableBody) return;
    driversTableBody.innerHTML = '';
    if (listMessage) listMessage.style.display = 'none';

    if (!drivers || drivers.length === 0) {
        // Updated colspan to 6
        driversTableBody.innerHTML = '<tr><td colspan="6" class="loading-message">لا يوجد سائقون لعرضهم.</td></tr>';
        if (driversCountBadge) driversCountBadge.textContent = '0';
        if (paginationControls) paginationControls.innerHTML = ''; // No pagination needed
        return;
    }

    // Update total count badge
    if (driversCountBadge) driversCountBadge.textContent = drivers.length;

    // Get only drivers for the current page
    const pageDrivers = getCurrentPageDrivers();

    pageDrivers.forEach(driver => {
        // Determine status display
        let statusClass = driver.is_active ? 'status-active' : 'status-inactive';
        let statusText = driver.is_active ? 'نشط' : 'غير نشط';

        const row = document.createElement('tr');
        // Removed salary cell
        row.innerHTML = `
            <td>${driver.name || 'غير محدد'}</td>
            <td>${driver.phone || 'غير محدد'}</td>
            <td>${driver.work_shift || 'غير محدد'}</td>
            <td><span class="${statusClass}">${statusText}</span></td>
            <td>${driver.notes || ''}</td>
            <td>
                <button class="action-btn edit-btn" data-id="${driver.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                <button class="action-btn delete-btn" data-id="${driver.id}" title="حذف"><i class="fas fa-trash-alt"></i></button>
            </td>
        `;

        // Add event listeners for edit/delete buttons
        const editBtn = row.querySelector('.edit-btn');
        const deleteBtn = row.querySelector('.delete-btn');
        if (editBtn) {
            editBtn.addEventListener('click', () => handleEditDriver(driver));
        }
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => handleDeleteDriver(driver.id, driver.name));
        }

        driversTableBody.appendChild(row);
    });

    // Render pagination controls
    renderPaginationControls();
};

// Function to update dashboard stats
const updateDashboardStats = async () => {
    try {
        // Get total drivers count
        const { count: totalCount, error: totalError } = await _supabase
            .from('drivers')
            .select('*', { count: 'exact', head: true });

        // Get active drivers count
        const { count: activeCount, error: activeError } = await _supabase
            .from('drivers')
            .select('*', { count: 'exact', head: true })
            .eq('is_active', true);

        if (!totalError && !activeError) {
            stats.totalDrivers = totalCount || 0;
            stats.activeDrivers = activeCount || 0;

            // Update dashboard elements
            if (totalDriversEl) totalDriversEl.textContent = stats.totalDrivers;
            if (activeDriversEl) activeDriversEl.textContent = stats.activeDrivers;
        } else {
            console.error('Error fetching stats data:', totalError, activeError);
        }
    } catch (error) {
        console.error('Error updating stats:', error);
    }
};

// Function to fetch drivers from Supabase
const fetchDrivers = async (searchTerm = '') => {
    if (!driversTableBody) return;
    // Update colspan in the loading message
    // Updated colspan to 6
    driversTableBody.innerHTML = '<tr><td colspan="6" class="loading-message">جاري التحميل...</td></tr>';
    if (paginationControls) paginationControls.innerHTML = ''; // Clear pagination while loading

    try {
        let query = _supabase
            .from('drivers')
            .select('*'); // Select all columns from drivers table

        // Add search condition if searchTerm is provided
        if (searchTerm) {
            // Search by name or phone number
            query = query.or(`name.ilike.%${searchTerm}%,phone.ilike.%${searchTerm}%`);
        }

        // Order by name
        query = query.order('name', { ascending: true });

        const { data, error } = await query;

        if (error) {
            console.error('Supabase Fetch Error:', error);
            showMessage(listMessage, `خطأ في جلب البيانات: ${error.message}`, 'error');
            // Updated colspan to 6
            driversTableBody.innerHTML = '<tr><td colspan="6" class="loading-message">خطأ في تحميل البيانات.</td></tr>';
        } else {
            console.log('Fetched drivers:', data);
            currentDrivers = data || [];
            currentPage = 1; // Reset to first page
            renderDriversTable(currentDrivers); // Render the table with fetched data

            if (data.length === 0 && searchTerm) {
                showMessage(listMessage, `لا توجد نتائج للبحث عن "${searchTerm}".`, 'info');
            } else if (data.length === 0) {
                 showMessage(listMessage, 'لا يوجد سائقون مسجلون حالياً.', 'info');
            }

            // Update dashboard stats after fetching
            updateDashboardStats();
        }
    } catch (error) {
        console.error('JavaScript Fetch Error:', error);
        showMessage(listMessage, `حدث خطأ غير متوقع: ${error.message}`, 'error');
        // Updated colspan to 6
        driversTableBody.innerHTML = '<tr><td colspan="6" class="loading-message">خطأ غير متوقع.</td></tr>'; // Updated colspan
    }
};

// Function to handle form submission (Add or Update)
const handleFormSubmit = async (event) => {
    event.preventDefault();
    if (!driverForm || !formMessage) return;

    showMessage(formMessage, 'جاري حفظ البيانات...', 'info');
    const submitBtn = driverForm.querySelector('.submit-btn');
    if (submitBtn) submitBtn.disabled = true;

    const formData = new FormData(driverForm);
    const driverData = {};
    formData.forEach((value, key) => {
        // Removed salary check
        driverData[key] = value;
    });
    // Handle checkbox explicitly
    driverData.is_active = formData.has('is_active');

    const driverId = driverData.driver_id;

    // Basic validation
    if (!driverData.driver_name || !driverData.driver_phone || !driverData.work_shift) {
        showMessage(formMessage, 'الرجاء ملء جميع الحقول المطلوبة (*).', 'error');
        if (submitBtn) submitBtn.disabled = false;
        return;
    }

    // Prepare data for Supabase (match column names)
    const dataToUpsert = {
        name: driverData.driver_name,
        phone: driverData.driver_phone,
        work_shift: driverData.work_shift,
        is_active: driverData.is_active,
        // Removed monthly_salary
        notes: driverData.notes || null, // Ensure notes is null if empty
    };

    try {
        let result;
        if (editMode && driverId) {
            // Update existing driver
            result = await _supabase
                .from('drivers')
                .update(dataToUpsert)
                .eq('id', driverId) // Use 'id' as the primary key
                .select();
        } else {
            // Insert new driver
            result = await _supabase
                .from('drivers')
                .insert([dataToUpsert])
                .select();
        }

        const { data, error } = result;

        if (error) {
            console.error('Supabase Save Error:', error);
            // Provide more specific error feedback if possible
            let userMessage = `خطأ في الحفظ: ${error.message}`;
            if (error.code === '23505') { // Unique constraint violation
                 userMessage = 'خطأ: رقم الجوال أو اسم السائق موجود مسبقاً.';
            }
            showMessage(formMessage, userMessage, 'error');
        } else {
            console.log('Supabase Save Success:', data);
            showMessage(formMessage, `تم ${editMode ? 'تحديث' : 'إضافة'} السائق بنجاح!`, 'success');
            setTimeout(() => {
                toggleModal(false);
            }, 1500);
            fetchDrivers(); // Refresh list
        }
    } catch (error) {
        console.error('JavaScript Save Error:', error);
        showMessage(formMessage, `حدث خطأ غير متوقع: ${error.message}`, 'error');
    } finally {
        if (submitBtn) submitBtn.disabled = false;
    }
};

// Function to reset the form and UI elements
const resetForm = () => {
    editMode = false;
    if (driverForm) driverForm.reset();
    if (driverIdField) driverIdField.value = '';
    if (formMessage) formMessage.style.display = 'none';
    if (formTitle) formTitle.textContent = 'إضافة سائق جديد';
    const submitBtn = driverForm?.querySelector('.submit-btn');
    if (submitBtn) submitBtn.textContent = 'حفظ';
    // Ensure checkbox is checked by default for new entries
    const activeCheckbox = document.getElementById('is_active');
    if (activeCheckbox) activeCheckbox.checked = true;
};

// Function to populate form for editing
const handleEditDriver = (driver) => {
    if (!driver || !driverForm) return;
    toggleModal(true); // Open modal first
    editMode = true;

    if (formTitle) formTitle.textContent = 'تعديل بيانات السائق';
    const submitBtn = driverForm.querySelector('.submit-btn');
    if (submitBtn) submitBtn.textContent = 'تحديث';

    // Populate form fields
    if (driverIdField) driverIdField.value = driver.id || '';
    document.getElementById('driver_name').value = driver.name || '';
    document.getElementById('driver_phone').value = driver.phone || '';
    document.getElementById('work_shift').value = driver.work_shift || '';
    // Removed monthly_salary population
    document.getElementById('is_active').checked = driver.is_active === true;
    document.getElementById('notes').value = driver.notes || '';

    // Ensure the modal is fully rendered before focusing
    setTimeout(() => {
        document.getElementById('driver_name')?.focus();
    }, 100);
};

// Function to handle driver deletion
const handleDeleteDriver = async (driverId, driverName) => {
    const confirmDelete = confirm(`هل أنت متأكد من حذف السائق "${driverName || 'هذا السائق'}"؟ لا يمكن التراجع عن هذا الإجراء.`);

    if (confirmDelete) {
        try {
            const { error } = await _supabase
                .from('drivers')
                .delete()
                .eq('id', driverId); // Use 'id' as the primary key

            if (error) {
                console.error('Supabase Delete Error:', error);
                showMessage(listMessage, `خطأ في الحذف: ${error.message}`, 'error');
            } else {
                console.log('Driver deleted:', driverId);
                showMessage(listMessage, `تم حذف السائق بنجاح.`, 'success');
                fetchDrivers(); // Refresh the list
            }
        } catch (error) {
            console.error('JavaScript Delete Error:', error);
            showMessage(listMessage, `حدث خطأ غير متوقع أثناء الحذف: ${error.message}`, 'error');
        }
    }
};

// Function to handle search
const handleSearch = () => {
    if (!searchInput) return;
    const searchTerm = searchInput.value.trim();
    fetchDrivers(searchTerm);
};

// Function to handle printing report (Basic version, can be expanded like students)
const handlePrintReport = () => {
    const driversToPrint = [...currentDrivers]; // Use the currently fetched/filtered drivers
    let reportTitle = 'تقرير السائقين';

    // Optional: Add filtering/sorting options here if needed

    // Sort alphabetically by name
    driversToPrint.sort((a, b) => (a.name || '').localeCompare(b.name || ''));

    const printWindow = window.open('', '_blank', 'height=600,width=800');
    let reportContent = `
        <html dir="rtl">
        <head>
            <title>${reportTitle}</title>
            <style>
                body { font-family: 'Tajawal', sans-serif; padding: 20px; direction: rtl; }
                h1 { text-align: center; margin-bottom: 10px; color: #333; }
                .print-meta { display: flex; justify-content: space-between; margin-bottom: 20px; font-size: 0.9em; color: #555; }
                table { width: 100%; border-collapse: collapse; margin-top: 15px; }
                th, td { padding: 8px; text-align: right; border: 1px solid #ddd; font-size: 0.9em; } /* Smaller font */
                th { background-color: #f2f2f2; font-weight: bold; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                .status-active { color: #2ecc71; font-weight: bold; }
                .status-inactive { color: #e74c3c; font-weight: bold; }
                @media print {
                    .no-print { display: none; }
                    body { padding: 5px; }
                    h1 { font-size: 1.5em; }
                }
            </style>
        </head>
        <body>
            <div class="print-meta">
                <span>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</span>
                <span>عدد السائقين في التقرير: ${driversToPrint.length}</span>
            </div>
            <h1>${reportTitle}</h1>
            ${driversToPrint.length === 0 ? '<p style="text-align: center; color: #888;">لا يوجد سائقون لعرضهم.</p>' : `
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الاسم</th>
                        <th>رقم الجوال</th>
                        <th>نوع الدوام</th>
                        <th>الحالة</th>
                        <!-- Removed Salary Header -->
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
            `}
    `;

    driversToPrint.forEach((driver, index) => {
        let statusClass = driver.is_active ? 'status-active' : 'status-inactive';
        let statusText = driver.is_active ? 'نشط' : 'غير نشط';

        // Removed salary cell
        reportContent += `
            <tr>
                <td>${index + 1}</td>
                <td>${driver.name || ''}</td>
                <td>${driver.phone || ''}</td>
                <td>${driver.work_shift || ''}</td>
                <td><span class="${statusClass}">${statusText}</span></td>
                <td>${driver.notes || ''}</td>
            </tr>
        `;
    });

    if (driversToPrint.length > 0) {
        reportContent += `
                </tbody>
            </table>
        `;
    }

    reportContent += `
            <div class="no-print" style="margin-top: 20px; text-align: center;">
                <button onclick="window.print()">طباعة</button>
                <button onclick="window.close()">إغلاق</button>
            </div>
        </body>
        </html>
    `;

    printWindow.document.write(reportContent);
    printWindow.document.close();
    // printWindow.focus();
    // setTimeout(() => { printWindow.print(); }, 500);
};


// Function to handle modal toggling
function toggleModal(show = true) {
    if (!addDriverSection) return;
    console.log('Driver Modal toggled:', show ? 'show' : 'hide');

    if (show) {
        resetForm(); // Reset form before showing
        addDriverSection.classList.add('show');
        setTimeout(() => {
            document.getElementById('driver_name')?.focus();
        }, 300);
        document.body.style.overflow = 'hidden';
    } else {
        addDriverSection.classList.remove('show');
        document.body.style.overflow = '';
    }
}

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOM loaded, initializing drivers application...');

    // Setup event listeners
    setupEventListeners();

    // Fetch initial drivers data
    fetchDrivers();
});


// --- Event Listeners ---
const setupEventListeners = () => {
    if (driverForm) {
        driverForm.addEventListener('submit', handleFormSubmit);
    }

    if (addDriverBtn) {
        addDriverBtn.addEventListener('click', () => {
            editMode = false;
            toggleModal(true);
        });
    }

    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', () => {
            toggleModal(false);
        });
    }

    if (closeFormBtn) {
        closeFormBtn.addEventListener('click', () => {
            toggleModal(false);
        });
    }

    // Close modal on background click
    if (addDriverSection) {
        addDriverSection.addEventListener('click', (event) => {
            if (event.target === addDriverSection) {
                toggleModal(false);
            }
        });
    }

    if (searchBtn) {
        searchBtn.addEventListener('click', handleSearch);
    }

    if (searchInput) {
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
    }

    if (printReportBtn) {
        printReportBtn.addEventListener('click', handlePrintReport);
    }

    // Escape key listener for modal
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && addDriverSection && addDriverSection.classList.contains('show')) {
            toggleModal(false);
        }
    });
};
