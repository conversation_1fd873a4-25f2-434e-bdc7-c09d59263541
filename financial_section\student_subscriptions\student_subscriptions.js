// --- Auth Check ---
// Assuming checkAuth function exists in auth.js and is loaded
// Make sure the path to login.html is correct relative to student_subscriptions.html
checkAuth('../login.html');

// --- Supabase Initialization ---
let _supabase;
try {
    // Check if Supabase is loaded and config variables are available
    if (typeof supabase !== 'undefined' && supabase.createClient && typeof SUPABASE_URL !== 'undefined' && typeof SUPABASE_ANON_KEY !== 'undefined') {
        _supabase = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('Supabase client initialized for student subscriptions.');
    } else {
        console.error('Supabase client or config variables not found. Make sure Supabase library and config.js are loaded correctly.');
        alert('خطأ في تهيئة الاتصال بقاعدة البيانات. لا يمكن تحميل البيانات.');
    }
} catch (error) {
    console.error('Error initializing Supabase client:', error);
    alert('خطأ في تهيئة الاتصال بقاعدة البيانات. لا يمكن تحميل البيانات.');
}

// --- DOM Elements ---
const defaultStudentsList = document.getElementById('default-students-list'); // Sidebar List
const sidebarMessage = document.getElementById('sidebar-message'); // Sidebar Message Area
const groupsContainer = document.getElementById('groups-grid'); // Updated to match new HTML structure
const pageMessage = document.getElementById('page-message'); // Added General Page Messages
const groupsMessage = document.getElementById('groups-message'); // Added Groups Section Messages
const selectedMonthDisplay = document.getElementById('selected-month-display'); // Added Month Display Span
const addGroupBtn = document.getElementById('add-group-btn'); // Added Add Group Button
const addGroupModal = document.getElementById('add-group-modal'); // Added Add Group Modal
const addGroupForm = document.getElementById('add-group-form'); // Added Add Group Form
const groupNameInput = document.getElementById('group_name'); // Added Group Name Input
const addGroupMessageArea = document.getElementById('add-group-message-area'); // Added Modal Message Area
const closeAddGroupModalBtn = document.getElementById('close-add-group-modal-btn'); // Added Modal Close Button
const cancelAddGroupBtn = document.getElementById('cancel-add-group-btn'); // Added Modal Cancel Button
const confirmAddGroupBtn = document.getElementById('confirm-add-group-btn'); // Added Modal Confirm Button
const groupsCountBadge = document.getElementById('groups-count'); // Added Groups Count Badge
// == Add Student Modal Elements ==
const addStudentModal = document.getElementById('add-student-to-group-modal');
const addStudentForm = document.getElementById('add-student-to-group-form');
const addStudentGroupNameSpan = document.getElementById('add-student-group-name');
const addStudentGroupIdInput = document.getElementById('add_student_group_id');
const selectStudentDropdown = document.getElementById('select_student_to_add');
const addStudentMessageArea = document.getElementById('add-student-message-area');
const closeAddStudentModalBtn = document.getElementById('close-add-student-modal-btn');
const cancelAddStudentBtn = document.getElementById('cancel-add-student-btn');
const confirmAddStudentBtn = document.getElementById('confirm-add-student-btn');
// == End Add Student Modal Elements ==

// == Add Default Students Modal Elements (NEW) ==
const addDefaultStudentsModal = document.getElementById('add-default-students-modal');
const addDefaultStudentsForm = document.getElementById('add-default-students-form'); // Assumes form element in modal
const addDefaultStudentsGroupNameSpan = document.getElementById('add-default-students-group-name');
const addDefaultStudentsGroupIdInput = document.getElementById('add_default_students_group_id'); // Hidden input
const defaultStudentsCheckboxList = document.getElementById('default-students-checkbox-list'); // Container for checkboxes
const addDefaultStudentsMessageArea = document.getElementById('add-default-students-message-area');
const closeAddDefaultStudentsModalBtn = document.getElementById('close-add-default-students-modal-btn');
const cancelAddDefaultStudentsBtn = document.getElementById('cancel-add-default-students-btn');
const confirmAddDefaultStudentsBtn = document.getElementById('confirm-add-default-students-btn');
const backToDashboardBtn = document.getElementById('back-to-dashboard-btn'); // Added button reference
// == End Add Default Students Modal Elements ==

// --- State ---
let selectedBudgetMonthId = null;
let selectedBudgetMonthNumber = null;
let selectedBudgetYearNumber = null;
let studentsAvailableForGroups = []; // Store students not in groups (from sidebar fetch)

// --- Helper Functions ---
const showSidebarMessage = (message, type = 'info', duration = 0) => {
    if (!sidebarMessage) return;
    sidebarMessage.textContent = message;
    sidebarMessage.className = `message ${type}`; // Removed 'show' assuming CSS handles display
    sidebarMessage.style.display = 'block';
    if (duration > 0) {
        setTimeout(() => {
            if (sidebarMessage.textContent === message) { // Prevent hiding if message changed
                 sidebarMessage.style.display = 'none';
            }
        }, duration);
    }
};

// Added general message function
const showMessage = (element, message, type = 'info', duration = 3000) => {
    if (!element) return;
    element.textContent = message;
    element.className = `message ${type}`;
    element.style.display = 'block';
    if (duration > 0) {
        setTimeout(() => {
            if (element.textContent === message) { // Prevent hiding if message changed
                 element.style.display = 'none';
            }
        }, duration);
    }
};

// Function to get month name from number (Arabic)
const getMonthName = (monthNumber) => {
    const names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"];
    const index = parseInt(monthNumber, 10) - 1;
    return names[index] || `شهر ${monthNumber}`;
};

// Added currency formatter
const formatCurrency = (amount) => {
    const value = parseFloat(amount);
    return isNaN(value) ? '0.00' : value.toFixed(2);
};

// --- Modal Helper Functions ---
const openAddGroupModal = () => {
    if (!addGroupModal) return;
    // Reset form and message before opening
    if (addGroupForm) addGroupForm.reset();
    if (addGroupMessageArea) addGroupMessageArea.style.display = 'none';
    if (confirmAddGroupBtn) {
        confirmAddGroupBtn.disabled = false;
        confirmAddGroupBtn.innerHTML = '<i class="fas fa-save"></i> حفظ المجموعة';
    }
    addGroupModal.classList.add('active'); // Use 'active' class to show modal
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
};

const closeAddGroupModal = () => {
    if (!addGroupModal) return;
    addGroupModal.classList.remove('active');
    document.body.style.overflow = ''; // Restore background scrolling
};

// == Add Student Modal Helpers ==
const openAddStudentModal = (groupId, groupName) => {
    if (!addStudentModal || !selectStudentDropdown) return;

    // Reset form and message
    if (addStudentForm) addStudentForm.reset();
    if (addStudentMessageArea) addStudentMessageArea.style.display = 'none';
    if (confirmAddStudentBtn) {
        confirmAddStudentBtn.disabled = false;
        confirmAddStudentBtn.innerHTML = '<i class="fas fa-plus-circle"></i> إضافة الطالب للمجموعة';
    }

    // Set group info
    addStudentGroupNameSpan.textContent = groupName;
    addStudentGroupIdInput.value = groupId;

    // Populate student dropdown
    selectStudentDropdown.innerHTML = '<option value="">-- اختر طالباً --</option>'; // Clear previous options
    if (studentsAvailableForGroups.length === 0) {
        selectStudentDropdown.innerHTML += '<option value="" disabled>لا يوجد طلاب متاحون</option>';
        // Optionally disable the submit button
        if (confirmAddStudentBtn) confirmAddStudentBtn.disabled = true;
    } else {
        studentsAvailableForGroups.forEach(student => {
            const option = document.createElement('option');
            option.value = student.id;
            option.textContent = student.name;
            selectStudentDropdown.appendChild(option);
        });
    }

    addStudentModal.classList.add('active');
    document.body.style.overflow = 'hidden';
};

const closeAddStudentModal = () => {
    if (!addStudentModal) return;
    addStudentModal.classList.remove('active');
    document.body.style.overflow = '';
};
// == End Add Student Modal Helpers ==

// == Add Default Students Modal Helpers (NEW) ==
const openAddDefaultStudentsModal = (groupId, groupName) => {
    console.log(`[Modal] Attempting to open AddDefaultStudentsModal for Group ID: ${groupId}, Name: ${groupName}`); // Debug log
    if (!addDefaultStudentsModal || !defaultStudentsCheckboxList || !addDefaultStudentsGroupNameSpan || !addDefaultStudentsGroupIdInput || !confirmAddDefaultStudentsBtn || !addDefaultStudentsMessageArea || !addDefaultStudentsForm) {
        console.error('[Modal] One or more elements for the Add Default Students Modal are missing from the DOM. Cannot open modal.');
        showMessage(pageMessage, 'خطأ: لا يمكن فتح نافذة إضافة الطلاب الافتراضيين (عناصر مفقودة).', 'error');
        return;
    }

    // Reset form and message
    if (addDefaultStudentsForm) addDefaultStudentsForm.reset();
    if (addDefaultStudentsMessageArea) addDefaultStudentsMessageArea.style.display = 'none';
    if (confirmAddDefaultStudentsBtn) {
        confirmAddDefaultStudentsBtn.disabled = false;
        confirmAddDefaultStudentsBtn.innerHTML = '<i class="fas fa-users-cog"></i> إضافة المحدد للمجموعة';
    }

    // Set group info
    addDefaultStudentsGroupNameSpan.textContent = groupName;
    addDefaultStudentsGroupIdInput.value = groupId;

    // Populate student checkbox list using the same available students list
    defaultStudentsCheckboxList.innerHTML = ''; // Clear previous content
    if (studentsAvailableForGroups.length === 0) {
        defaultStudentsCheckboxList.innerHTML = '<p class="empty-item">لا يوجد طلاب افتراضيون متاحون للإضافة.</p>';
        if (confirmAddDefaultStudentsBtn) confirmAddDefaultStudentsBtn.disabled = true;
    } else {
        studentsAvailableForGroups.forEach(student => {
            const div = document.createElement('div');
            div.className = 'student-checkbox-item'; // Reuse styling if available
            div.innerHTML = `
                <input type="checkbox" id="default_student_${student.id}" name="default_students_to_add" value="${student.id}">
                <label for="default_student_${student.id}">${student.name}</label>
            `;
            defaultStudentsCheckboxList.appendChild(div);
        });
        if (confirmAddDefaultStudentsBtn) confirmAddDefaultStudentsBtn.disabled = false;
    }

    console.log('[Modal] Adding "active" class to addDefaultStudentsModal'); // Debug log
    addDefaultStudentsModal.classList.add('active');
    document.body.style.overflow = 'hidden';
};

const closeAddDefaultStudentsModal = () => {
    if (!addDefaultStudentsModal) return;
    addDefaultStudentsModal.classList.remove('active');
    document.body.style.overflow = '';
};
// == End Add Default Students Modal Helpers ==

// --- Data Fetching and Rendering ---

// Fetch and render default students for the sidebar (and store available students)
const fetchAndRenderDefaultStudentsSidebar = async () => {
    if (!defaultStudentsList) {
        console.error('[Sidebar] defaultStudentsList element not found in the DOM.');
        return;
    }
    if (!_supabase) {
        showSidebarMessage('خطأ في الاتصال بقاعدة البيانات.', 'error');
        defaultStudentsList.innerHTML = '<li class="empty-item error">فشل الاتصال</li>';
        return;
    }

    defaultStudentsList.innerHTML = '<li class="loading-item"><i class="fas fa-spinner fa-spin"></i> تحميل...</li>';
    if (sidebarMessage) sidebarMessage.style.display = 'none';

    try {
        // --- Step 1: Fetch IDs of students already in groups ---
        console.log('[Sidebar] Fetching IDs of students in groups...');
        const { data: groupMembersData, error: groupMembersError } = await _supabase
            .from('student_group_members')
            .select('student_id');

        if (groupMembersError) {
            console.error('[Sidebar] Error fetching group member IDs:', groupMembersError);
            throw new Error(`فشل في جلب أعضاء المجموعات: ${groupMembersError.message}`);
        }

        // Create a Set for efficient lookup of students in groups
        const studentsInGroups = new Set(groupMembersData.map(member => member.student_id));
        console.log(`[Sidebar] Found ${studentsInGroups.size} unique students already in groups.`);
        // --- End Step 1 ---

        // --- Step 2: Fetch default students ---
        console.log('[Sidebar] Fetching default students from "student_subscription_defaults"...');
        const { data: settingsData, error: settingsError, status } = await _supabase
            .from('student_subscription_defaults')
            .select(`
                students (
                    id,
                    name
                )
            `)
            .order('students(name)', { ascending: true });

        if (settingsError) {
            console.error(`[Sidebar] Error fetching default students (Status: ${status}):`, settingsError);
            if (settingsError.code === 'PGRST200' && settingsError.message.includes('Could not find a relationship')) {
                 throw new Error(`فشل جلب الطلاب: لا توجد علاقة معرفة بين جدول الإعدادات الافتراضية ('student_subscription_defaults') وجدول الطلاب ('students'). يرجى التأكد من تعريف Foreign Key بشكل صحيح في قاعدة البيانات.`);
            } else {
                throw new Error(`فشل جلب الطلاب الافتراضيين: ${settingsError.message} (Code: ${settingsError.code})`);
            }
        }
        console.log('[Sidebar] Fetched default student data:', settingsData);
        // --- End Step 2 ---

        // --- Step 3: Process and Filter ---
        const validStudentsWithDefaults = settingsData
            .map(item => item.students) // Extract the student object
            .filter(student => student !== null) // Filter out null students (if default exists but student deleted)
            .reduce((acc, current) => { // Remove duplicates (if any student has multiple defaults)
                if (!acc.find(item => item.id === current.id)) {
                    acc.push(current);
                }
                return acc;
            }, []);

        // Filter out students who are already in groups
        const studentsNotInGroups = validStudentsWithDefaults.filter(student => !studentsInGroups.has(student.id));
        console.log(`[Sidebar] Processed ${studentsNotInGroups.length} valid students with defaults who are not in groups.`);

        // *** Store the filtered list for the Add Student modal ***
        studentsAvailableForGroups = studentsNotInGroups;
        // *** End Store ***

        // --- Step 4: Render the filtered list ---
        defaultStudentsList.innerHTML = ''; // Clear loading
        if (studentsNotInGroups.length === 0) {
            defaultStudentsList.innerHTML = '<li class="empty-item">لا يوجد طلاب باشتراكات افتراضية خارج المجموعات.</li>';
        } else {
            studentsNotInGroups.forEach(student => {
                const listItem = document.createElement('li');
                listItem.dataset.studentId = student.id;
                listItem.innerHTML = `
                    <i class="fas fa-user"></i>
                    <span>${student.name}</span>
                `;
                defaultStudentsList.appendChild(listItem);
            });
            console.log('[Sidebar] Successfully rendered filtered default students list.');
        }
        // --- End Step 4 ---

    } catch (error) {
        console.error('[Sidebar] Final catch block error:', error);
        showSidebarMessage(error.message || 'خطأ غير معروف في تحميل قائمة الطلاب الافتراضيين.', 'error');
        defaultStudentsList.innerHTML = '<li class="empty-item error">فشل التحميل</li>';
        studentsAvailableForGroups = []; // Clear on error
    }
};

// Fetch and render student groups with summary based on defaults and actual payments
const fetchAndRenderGroups = async () => {
    console.log('[Groups] Fetching groups, members, defaults, and payments...');
    if (!groupsContainer || !_supabase || !selectedBudgetMonthId) {
        console.error('[Groups] Missing container, Supabase client, or selectedMonthId.');
        if (groupsContainer) groupsContainer.innerHTML = '<p class="loading-placeholder error">خطأ في تحميل المجموعات.</p>';
        return;
    }

    groupsContainer.innerHTML = '<div class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i> جاري تحميل المجموعات...</div>';

    try {
        // 1. Fetch groups and their members with student defaults
        const { data: groupsData, error: groupsError } = await _supabase
            .from('student_groups')
            .select(`
                id,
                name,
                student_group_members (
                    student_id,
                    students (
                        id,
                        name,
                        student_subscription_defaults ( default_amount, current_balance )
                    )
                )
            `)
            .order('name'); // Order groups by name

        if (groupsError) throw groupsError;
        console.log('[Groups] Fetched groups with members and defaults:', groupsData);

        if (!groupsData || groupsData.length === 0) {
            groupsContainer.innerHTML = '<p class="loading-placeholder">لم يتم إنشاء مجموعات بعد.</p>';
            return;
        }

        // Extract all unique student IDs from all groups for payment fetching
        const allStudentIdsInGroups = [...new Set(
            groupsData.flatMap(group =>
                group.student_group_members.map(member => member.students.id)
            )
        )];
        console.log('[Groups] All unique student IDs in fetched groups:', allStudentIdsInGroups);

        // --- إضافة: جلب مجموع الدفعات لجميع الطلاب في المجموعات للشهر المحدد ---
        let paymentsSumMap = {}; // student_id -> total_paid_this_month
        if (allStudentIdsInGroups.length > 0) {
            console.log(`[Groups] Fetching payments from student_payments for month ID: ${selectedBudgetMonthId} for ${allStudentIdsInGroups.length} students.`);
            const { data: payments, error: paymentsError } = await _supabase
                .from('student_payments')
                .select('student_id, amount')
                .eq('budget_month_id', selectedBudgetMonthId)
                .in('student_id', allStudentIdsInGroups);

            if (paymentsError) throw paymentsError;

            // حساب مجموع الدفعات لكل طالب
            payments.forEach(payment => {
                const studentId = payment.student_id;
                const currentAmount = parseFloat(payment.amount || 0);
                if (!paymentsSumMap[studentId]) {
                    paymentsSumMap[studentId] = 0;
                }
                paymentsSumMap[studentId] += currentAmount;
            });
            console.log('[Groups] Calculated payment sums for students in groups:', paymentsSumMap);
        }
        // --- نهاية الإضافة ---

        // --- إزالة: الاستعلام عن monthly_student_subscriptions ---
        // const { data: monthlySubs, error: monthlySubsError } = await _supabase
        //     .from('monthly_student_subscriptions') // <-- الجدول غير موجود
        //     .select('id, student_id')
        //     .eq('budget_month_id', selectedBudgetMonthId)
        //     .in('student_id', allStudentIdsInGroups);
        // if (monthlySubsError) throw monthlySubsError;
        // const monthlySubStudentIds = new Set(monthlySubs.map(sub => sub.student_id));
        // console.log('[Groups] Fetched monthly subscription student IDs (removed):', monthlySubStudentIds);
        // --- نهاية الإزالة ---


        // 3. Process group data to calculate summaries
        const groupsWithDetails = groupsData.map(group => {
            let totalExpected = 0;
            let totalPaid = 0;
            let membersCount = 0;
            let membersWithIssues = 0; // Count members missing default amount

            group.student_group_members.forEach(member => {
                if (member.students) { // Ensure student data exists
                    membersCount++;
                    // --- تعديل: استخدام student_subscription_defaults ---
                    const defaultsData = member.students.student_subscription_defaults;
                    let defaults = {};
                     if (Array.isArray(defaultsData)) {
                        defaults = defaultsData[0] || {};
                    } else if (typeof defaultsData === 'object' && defaultsData !== null) {
                        defaults = defaultsData;
                    }
                    // --- نهاية التعديل ---

                    const defaultAmount = parseFloat(defaults.default_amount || 0);
                    // --- تعديل: استخدام paymentsSumMap ---
                    const paidAmount = paymentsSumMap[member.students.id] || 0;
                    // --- نهاية التعديل ---

                    if (defaults.default_amount === null || defaults.default_amount === undefined) {
                        membersWithIssues++;
                        console.warn(`[Groups] Student ${member.students.name} (ID: ${member.students.id}) in group ${group.name} is missing default_amount.`);
                    }

                    totalExpected += defaultAmount;
                    totalPaid += paidAmount;
                } else {
                    console.warn(`[Groups] Member data missing student details in group ${group.name}.`);
                }
            });

            const totalRemaining = totalExpected - totalPaid;
            let status = 'غير مدفوع';
            if (totalPaid >= totalExpected && totalExpected > 0) {
                status = 'مدفوع بالكامل';
            } else if (totalPaid > 0) {
                status = 'مدفوع جزئياً';
            } else if (totalExpected === 0 && membersCount > 0) {
                status = 'لا يوجد مبلغ مطلوب'; // Or 'مبالغ غير محددة' if issues
            }

            if (membersWithIssues > 0) {
                status += ` (${membersWithIssues} طلاب بدون مبلغ افتراضي)`;
            }


            return {
                id: group.id,
                name: group.name,
                membersCount: membersCount,
                totalExpected: totalExpected,
                totalPaid: totalPaid,
                totalRemaining: totalRemaining,
                status: status,
                members: group.student_group_members // Keep members data for potential use
            };
        });

        console.log('[Groups] Processed groups with details:', groupsWithDetails);
        renderGroupCards(groupsWithDetails);

    } catch (error) {
        console.error('[Groups] Error fetching group data/summaries:', error); // Log the actual error
        groupsContainer.innerHTML = `<p class="loading-placeholder error">فشل تحميل بيانات المجموعات: ${error.message}</p>`;
    }
};

// Helper function to render group cards with enhanced design
const renderGroupCards = (groups) => {
    if (!groupsContainer) return;
    groupsContainer.innerHTML = ''; // Clear loading/previous

    if (!groups || groups.length === 0) {
        groupsContainer.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-layer-group"></i>
                </div>
                <h3>لا توجد مجموعات</h3>
                <p>لم يتم إنشاء أي مجموعات بعد. ابدأ بإضافة مجموعة جديدة.</p>
                <button class="group-action-btn primary" onclick="openAddGroupModal()">
                    <i class="fas fa-plus"></i>
                    إضافة مجموعة جديدة
                </button>
            </div>
        `;
        return;
    }

    groups.forEach((group, index) => {
        const card = document.createElement('div');
        card.className = 'group-card';
        card.dataset.groupId = group.id;
        card.dataset.groupName = group.name;
        card.style.animationDelay = `${index * 0.1}s`; // Staggered animation

        // Add click listener for navigation (excluding buttons)
        card.addEventListener('click', (event) => {
            if (!event.target.closest('.group-card-footer button')) {
                navigateToGroupDetails(group.id, group.name);
            }
        });

        // Determine status color and icon
        let statusColor = '#718096';
        let statusIcon = 'fas fa-info-circle';

        if (group.totalPaid >= group.totalExpected && group.totalExpected > 0) {
            statusColor = '#48bb78';
            statusIcon = 'fas fa-check-circle';
        } else if (group.totalPaid > 0) {
            statusColor = '#ed8936';
            statusIcon = 'fas fa-clock';
        } else if (group.totalExpected > 0) {
            statusColor = '#f56565';
            statusIcon = 'fas fa-exclamation-circle';
        }

        card.innerHTML = `
            <div class="group-card-header">
                <div class="group-card-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="group-card-title">${group.name}</h3>
                <p class="group-card-subtitle">${group.membersCount} طالب</p>
            </div>

            <div class="group-card-body">
                <div class="group-stats">
                    <div class="stat-item has-tooltip tooltip-container" data-group-id="${group.id}" data-type="expected">
                        <span class="stat-value">${formatCurrency(group.totalExpected)}</span>
                        <span class="stat-label">المستحق</span>
                        <div class="tooltip" id="tooltip-expected-${group.id}">
                            <div class="tooltip-loading">جاري تحميل التفاصيل...</div>
                        </div>
                    </div>
                    <div class="stat-item has-tooltip tooltip-container" data-group-id="${group.id}" data-type="paid">
                        <span class="stat-value" style="color: #48bb78;">${formatCurrency(group.totalPaid)}</span>
                        <span class="stat-label">المدفوع</span>
                        <div class="tooltip" id="tooltip-paid-${group.id}">
                            <div class="tooltip-loading">جاري تحميل التفاصيل...</div>
                        </div>
                    </div>
                    <div class="stat-item has-tooltip tooltip-container" data-group-id="${group.id}" data-type="remaining">
                        <span class="stat-value" style="color: #f56565;">${formatCurrency(group.totalRemaining)}</span>
                        <span class="stat-label">المتبقي</span>
                        <div class="tooltip" id="tooltip-remaining-${group.id}">
                            <div class="tooltip-loading">جاري تحميل التفاصيل...</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" style="color: ${statusColor};">
                            <i class="${statusIcon}"></i>
                        </span>
                        <span class="stat-label">الحالة</span>
                    </div>
                </div>

                <div class="group-status-bar">
                    <div class="status-progress">
                        <div class="progress-fill" style="width: ${group.totalExpected > 0 ? (group.totalPaid / group.totalExpected * 100) : 0}%"></div>
                    </div>
                    <span class="status-text" style="color: ${statusColor};">
                        ${getStatusText(group)}
                    </span>
                </div>
            </div>

            <div class="group-card-footer">
                <button class="group-action-btn primary" onclick="navigateToGroupDetails('${group.id}', '${group.name}')">
                    <i class="fas fa-eye"></i>
                    عرض التفاصيل
                </button>
                <button class="group-action-btn secondary" onclick="showGroupQuickActions('${group.id}', '${group.name}')">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
        `;

        groupsContainer.appendChild(card);
    });

    // Add tooltip event listeners after all cards are rendered
    setupTooltipEventListeners();

    console.log('[Groups] Successfully rendered enhanced group cards with animations.');
};

// Helper function to get status text
const getStatusText = (group) => {
    if (group.totalExpected === 0) {
        return 'لا يوجد مبلغ مطلوب';
    } else if (group.totalPaid >= group.totalExpected) {
        return 'مدفوع بالكامل';
    } else if (group.totalPaid > 0) {
        return 'مدفوع جزئياً';
    } else {
        return 'غير مدفوع';
    }
};

// Helper function for quick actions (placeholder)
const showGroupQuickActions = (groupId, groupName) => {
    // This can be expanded to show a dropdown menu with options like:
    // - Add students
    // - Edit group name
    // - Delete group
    // - Export data
    console.log(`Quick actions for group: ${groupName} (${groupId})`);
};

// === Tooltip Functions ===

// Setup tooltip event listeners
const setupTooltipEventListeners = () => {
    const tooltipContainers = document.querySelectorAll('.tooltip-container[data-group-id]');

    tooltipContainers.forEach(container => {
        const groupId = container.dataset.groupId;
        const type = container.dataset.type;

        container.addEventListener('mouseenter', () => {
            loadTooltipData(groupId, type);
        });
    });
};

// Load tooltip data for a specific group and type
const loadTooltipData = async (groupId, type) => {
    const tooltipId = `tooltip-${type}-${groupId}`;
    const tooltip = document.getElementById(tooltipId);

    if (!tooltip || !_supabase || !selectedBudgetMonthId) return;

    // Check if data is already loaded
    if (tooltip.dataset.loaded === 'true') return;

    try {
        // Get group members first
        const { data: groupMembers, error: membersError } = await _supabase
            .from('student_group_members')
            .select(`
                student_id,
                students (
                    id,
                    name,
                    student_subscription_defaults (
                        default_amount
                    )
                )
            `)
            .eq('group_id', groupId);

        if (membersError) throw membersError;

        if (!groupMembers || groupMembers.length === 0) {
            tooltip.innerHTML = '<div class="tooltip-error">لا يوجد أعضاء في هذه المجموعة</div>';
            return;
        }

        // Get student IDs
        const studentIds = groupMembers.map(member => member.student_id);

        // Get payments for these students in the selected month
        const { data: payments, error: paymentsError } = await _supabase
            .from('student_payments')
            .select('student_id, amount, payment_date')
            .eq('budget_month_id', selectedBudgetMonthId)
            .in('student_id', studentIds)
            .order('payment_date', { ascending: false });

        if (paymentsError) throw paymentsError;

        // Process data based on type
        if (type === 'paid') {
            renderPaidTooltip(tooltip, groupMembers, payments || []);
        } else if (type === 'remaining') {
            renderRemainingTooltip(tooltip, groupMembers, payments || []);
        } else if (type === 'expected') {
            renderExpectedTooltip(tooltip, groupMembers);
        }

        // Mark as loaded
        tooltip.dataset.loaded = 'true';

    } catch (error) {
        console.error(`Error loading tooltip data for group ${groupId}, type ${type}:`, error);
        tooltip.innerHTML = '<div class="tooltip-error">خطأ في تحميل البيانات</div>';
    }
};

// Render paid amounts tooltip
const renderPaidTooltip = (tooltip, groupMembers, payments) => {
    // Calculate payments per student
    const paymentsByStudent = {};
    payments.forEach(payment => {
        if (!paymentsByStudent[payment.student_id]) {
            paymentsByStudent[payment.student_id] = 0;
        }
        paymentsByStudent[payment.student_id] += parseFloat(payment.amount || 0);
    });

    let tooltipContent = '<div class="tooltip-header">الطلاب الذين دفعوا</div>';
    let totalPaid = 0;
    let studentsWhoPaid = [];

    // فقط الطلاب الذين دفعوا
    groupMembers.forEach(member => {
        const student = member.students;
        if (!student) return;

        const paidAmount = paymentsByStudent[student.id] || 0;
        if (paidAmount > 0) {
            studentsWhoPaid.push({
                name: student.name,
                amount: paidAmount
            });
            totalPaid += paidAmount;
        }
    });

    if (studentsWhoPaid.length === 0) {
        tooltipContent += '<div class="tooltip-item"><span class="tooltip-student-name">لا يوجد طلاب دفعوا بعد</span></div>';
    } else {
        studentsWhoPaid.forEach(student => {
            tooltipContent += `
                <div class="tooltip-item">
                    <span class="tooltip-student-name">${student.name}</span>
                    <span class="tooltip-amount">${formatCurrency(student.amount)}</span>
                </div>
            `;
        });

        tooltipContent += `
            <div class="tooltip-total">
                <div class="tooltip-item">
                    <span>المجموع:</span>
                    <span class="tooltip-amount">${formatCurrency(totalPaid)}</span>
                </div>
            </div>
        `;
    }

    tooltip.innerHTML = tooltipContent;
};

// Render remaining amounts tooltip
const renderRemainingTooltip = (tooltip, groupMembers, payments) => {
    // Calculate payments per student
    const paymentsByStudent = {};
    payments.forEach(payment => {
        if (!paymentsByStudent[payment.student_id]) {
            paymentsByStudent[payment.student_id] = 0;
        }
        paymentsByStudent[payment.student_id] += parseFloat(payment.amount || 0);
    });

    let tooltipContent = '<div class="tooltip-header">الطلاب الذين لديهم مبلغ متبقي</div>';
    let totalRemaining = 0;
    let studentsWithRemaining = [];

    groupMembers.forEach(member => {
        const student = member.students;
        if (!student) return;

        const defaults = student.student_subscription_defaults;
        const defaultAmount = Array.isArray(defaults) ?
            parseFloat(defaults[0]?.default_amount || 0) :
            parseFloat(defaults?.default_amount || 0);

        const paidAmount = paymentsByStudent[student.id] || 0;
        const remainingAmount = defaultAmount - paidAmount;

        // فقط الطلاب الذين لديهم مبلغ متبقي أكبر من صفر
        if (remainingAmount > 0) {
            studentsWithRemaining.push({
                name: student.name,
                amount: remainingAmount
            });
            totalRemaining += remainingAmount;
        }
    });

    if (studentsWithRemaining.length === 0) {
        tooltipContent += '<div class="tooltip-item"><span class="tooltip-student-name">جميع الطلاب دفعوا المبلغ المطلوب</span></div>';
    } else {
        studentsWithRemaining.forEach(student => {
            tooltipContent += `
                <div class="tooltip-item">
                    <span class="tooltip-student-name">${student.name}</span>
                    <span class="tooltip-amount negative">${formatCurrency(student.amount)}</span>
                </div>
            `;
        });

        tooltipContent += `
            <div class="tooltip-total">
                <div class="tooltip-item">
                    <span>المجموع:</span>
                    <span class="tooltip-amount negative">${formatCurrency(totalRemaining)}</span>
                </div>
            </div>
        `;
    }

    tooltip.innerHTML = tooltipContent;
};

// Render expected amounts tooltip
const renderExpectedTooltip = (tooltip, groupMembers) => {
    let tooltipContent = '<div class="tooltip-header">المبالغ المستحقة لكل طالب</div>';
    let totalExpected = 0;

    groupMembers.forEach(member => {
        const student = member.students;
        if (!student) return;

        const defaults = student.student_subscription_defaults;
        const defaultAmount = Array.isArray(defaults) ?
            parseFloat(defaults[0]?.default_amount || 0) :
            parseFloat(defaults?.default_amount || 0);

        totalExpected += defaultAmount;

        tooltipContent += `
            <div class="tooltip-item">
                <span class="tooltip-student-name">${student.name}</span>
                <span class="tooltip-amount">${formatCurrency(defaultAmount)}</span>
            </div>
        `;
    });

    tooltipContent += `
        <div class="tooltip-total">
            <div class="tooltip-item">
                <span>المجموع:</span>
                <span class="tooltip-amount">${formatCurrency(totalExpected)}</span>
            </div>
        </div>
    `;

    tooltip.innerHTML = tooltipContent;
};

// --- Add Group Logic ---
const handleAddGroupSubmit = async (event) => {
    event.preventDefault(); // Prevent standard form submission
    if (!groupNameInput || !confirmAddGroupBtn || !_supabase) return;

    const groupName = groupNameInput.value.trim();

    // Basic validation
    if (!groupName) {
        showMessage(addGroupMessageArea, 'الرجاء إدخال اسم للمجموعة.', 'error', 3000);
        return;
    }

    // Disable button and show loading state
    confirmAddGroupBtn.disabled = true;
    confirmAddGroupBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    showMessage(addGroupMessageArea, 'جاري إضافة المجموعة...', 'info', 0);

    try {
        const { data, error } = await _supabase
            .from('student_groups')
            .insert([{ name: groupName }])
            .select()
            .single(); // Use single() if you expect only one row back

        if (error) {
            console.error('[Add Group] Error inserting group:', error);
            // Check for unique constraint violation (PostgreSQL code 23505)
            if (error.code === '23505') {
                throw new Error('اسم المجموعة موجود بالفعل. الرجاء اختيار اسم آخر.');
            }
            throw error; // Re-throw other errors
        }

        console.log('[Add Group] Group added successfully:', data);
        showMessage(addGroupMessageArea, 'تمت إضافة المجموعة بنجاح!', 'success', 2000);

        // Refresh the groups list on the page
        await fetchAndRenderGroups();

        // Close modal after a short delay
        setTimeout(closeAddGroupModal, 1500);

    } catch (error) {
        console.error('[Add Group] Final catch block error:', error);
        showMessage(addGroupMessageArea, `خطأ في إضافة المجموعة: ${error.message}`, 'error', 0);
    } finally {
        // Re-enable button unless modal is closing anyway
        if (addGroupModal.classList.contains('active')) { // Check if modal is still intended to be open
             confirmAddGroupBtn.disabled = false;
             confirmAddGroupBtn.innerHTML = '<i class="fas fa-save"></i> حفظ المجموعة';
        }
    }
};

// == Add Student to Group Logic ==
const handleAddStudentToGroupSubmit = async (event) => {
    event.preventDefault();
    if (!selectStudentDropdown || !addStudentGroupIdInput || !confirmAddStudentBtn || !_supabase) return;

    const studentId = selectStudentDropdown.value;
    const groupId = addStudentGroupIdInput.value;

    // Validation
    if (!studentId) {
        showMessage(addStudentMessageArea, 'الرجاء اختيار طالب.', 'error', 3000);
        return;
    }
    if (!groupId) {
        showMessage(addStudentMessageArea, 'خطأ: معرف المجموعة غير موجود.', 'error', 3000);
        return;
    }

    // Disable button and show loading
    confirmAddStudentBtn.disabled = true;
    confirmAddStudentBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإضافة...';
    showMessage(addStudentMessageArea, 'جاري إضافة الطالب للمجموعة...', 'info', 0);

    try {
        const { data, error } = await _supabase
            .from('student_group_members')
            .insert([{ student_id: studentId, group_id: groupId }])
            .select()
            .single();

        if (error) {
            console.error('[Add Student] Error inserting student into group:', error);
            // Check for unique constraint violation (student already in group)
            if (error.code === '23505') {
                throw new Error('هذا الطالب موجود بالفعل في هذه المجموعة.');
            }
            throw error;
        }

        console.log('[Add Student] Student added to group successfully:', data);
        showMessage(addStudentMessageArea, 'تمت إضافة الطالب للمجموعة بنجاح!', 'success', 2000);

        // Refresh the sidebar list (as the student is no longer available)
        await fetchAndRenderDefaultStudentsSidebar();

        // Update ungrouped students count
        await updateUngroupedStudentsCount();

        // Close modal after delay
        setTimeout(closeAddStudentModal, 1500);

    } catch (error) {
        console.error('[Add Student] Final catch block error:', error);
        showMessage(addStudentMessageArea, `خطأ في إضافة الطالب: ${error.message}`, 'error', 0);
    } finally {
        // Re-enable button if modal is still open
        if (addStudentModal.classList.contains('active')) {
            confirmAddStudentBtn.disabled = false;
            confirmAddStudentBtn.innerHTML = '<i class="fas fa-plus-circle"></i> إضافة الطالب للمجموعة';
        }
    }
};
// == End Add Student to Group Logic ==

// == Add Default Students to Group Logic (NEW) ==
const handleAddDefaultStudentsSubmit = async (event) => {
    event.preventDefault();
    // Check if essential elements and Supabase client are available
    if (!defaultStudentsCheckboxList || !addDefaultStudentsGroupIdInput || !confirmAddDefaultStudentsBtn || !_supabase || !addDefaultStudentsMessageArea) {
        console.error('[Add Default Students] Submit handler cannot proceed: Missing required elements or Supabase client.');
        // Optionally show a generic error to the user if the message area itself is missing
        if (pageMessage) showMessage(pageMessage, 'حدث خطأ غير متوقع في واجهة المستخدم.', 'error');
        return;
    }

    // Get all checked student IDs from the checkboxes within the container
    const checkedCheckboxes = defaultStudentsCheckboxList.querySelectorAll('input[type="checkbox"][name="default_students_to_add"]:checked');
    const selectedStudentIds = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);

    const groupId = addDefaultStudentsGroupIdInput.value;

    // Validation: Ensure at least one student is selected
    if (selectedStudentIds.length === 0) {
        showMessage(addDefaultStudentsMessageArea, 'الرجاء تحديد طالب واحد على الأقل باستخدام علامة الصح.', 'error', 3000);
        return;
    }
    // Validation: Ensure group ID is present
    if (!groupId) {
        showMessage(addDefaultStudentsMessageArea, 'خطأ: معرف المجموعة غير موجود. لا يمكن إضافة الطلاب.', 'error', 3000);
        console.error('[Add Default Students] Group ID is missing.');
        return;
    }

    // Disable button and show loading state
    confirmAddDefaultStudentsBtn.disabled = true;
    confirmAddDefaultStudentsBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإضافة...';
    showMessage(addDefaultStudentsMessageArea, `جاري إضافة ${selectedStudentIds.length} طالب/طلاب للمجموعة...`, 'info', 0); // Show indefinite loading message

    try {
        // Prepare array of records for bulk insertion
        const recordsToInsert = selectedStudentIds.map(studentId => ({
            student_id: studentId,
            group_id: groupId
        }));

        console.log('[Add Default Students] Attempting to insert records:', recordsToInsert);

        // Perform the bulk insert operation
        const { data, error } = await _supabase
            .from('student_group_members')
            .insert(recordsToInsert)
            .select(); // Select the inserted rows to confirm insertion

        // Handle potential errors during insertion
        if (error) {
            console.error('[Add Default Students] Error inserting students into group:', error);
            // Check for unique constraint violation (student already in group)
            if (error.code === '23505') {
                // Provide a more specific message for uniqueness conflict
                throw new Error('فشل الإضافة: طالب واحد أو أكثر من الطلاب المحددين موجود بالفعل في هذه المجموعة.');
            }
            // Check for foreign key violation (e.g., invalid student_id or group_id)
            if (error.code === '23503') {
                 throw new Error('فشل الإضافة: خطأ في بيانات الطالب أو المجموعة. قد يكون تم حذف أحدهما.');
            }
            // Throw a generic error for other database issues
            throw new Error(`حدث خطأ في قاعدة البيانات: ${error.message}`);
        }

        // Insertion successful
        console.log(`[Add Default Students] ${data.length} students added to group ${groupId} successfully:`, data);
        showMessage(addDefaultStudentsMessageArea, `تمت إضافة ${data.length} طالب/طلاب للمجموعة بنجاح!`, 'success', 2000);

        // Refresh UI elements to reflect changes
        // 1. Refresh the sidebar list (added students are no longer "available")
        await fetchAndRenderDefaultStudentsSidebar();
        // 2. Refresh the group cards (to update member counts, totals, etc.)
        await fetchAndRenderGroups();
        // 3. Update ungrouped students count
        await updateUngroupedStudentsCount();

        // Close the modal after a short delay to show the success message
        setTimeout(closeAddDefaultStudentsModal, 1500);

    } catch (error) {
        // Catch any error thrown during the try block (including custom errors)
        console.error('[Add Default Students] Final catch block error:', error);
        // Display the specific error message to the user
        showMessage(addDefaultStudentsMessageArea, error.message || 'حدث خطأ غير متوقع أثناء إضافة الطلاب.', 'error', 0); // Show error indefinitely
    } finally {
        // This block executes regardless of success or failure,
        // but *only if* the modal wasn't closed by the success path's timeout.
        // Check if the modal is still active (meaning an error likely occurred)
        if (addDefaultStudentsModal?.classList.contains('active')) {
            // Re-enable the button and restore its original text/icon
            confirmAddDefaultStudentsBtn.disabled = false;
            confirmAddDefaultStudentsBtn.innerHTML = '<i class="fas fa-users-cog"></i> إضافة المحدد للمجموعة';
        }
    }
};
// == End Add Default Students to Group Logic ==

// --- Navigation ---
const navigateToGroupDetails = (groupId, groupName) => {
    // Keep existing checks for month details
    if (!selectedBudgetMonthId || !selectedBudgetMonthNumber || !selectedBudgetYearNumber) {
        showMessage(pageMessage, 'خطأ: الشهر المالي غير محدد بشكل كامل. لا يمكن الانتقال.', 'error');
        console.error('Cannot navigate: Month/Year details missing from state.');
        return;
    }
    if (!groupId || !groupName) {
        showMessage(pageMessage, 'خطأ: بيانات المجموعة غير كاملة.', 'error');
        console.error('Cannot navigate: Group ID or Name missing.');
        return;
    }

    // Store necessary info in sessionStorage (optional, URL params are primary)
    sessionStorage.setItem('selectedBudgetMonthId', selectedBudgetMonthId);
    sessionStorage.setItem('selectedBudgetMonthNumber', selectedBudgetMonthNumber);
    sessionStorage.setItem('selectedBudgetYearNumber', selectedBudgetYearNumber);

    // Construct URL with query parameters
    const params = new URLSearchParams({
        groupId: groupId,
        groupName: groupName,
        monthId: selectedBudgetMonthId,
        monthNumber: selectedBudgetMonthNumber,
        yearNumber: selectedBudgetYearNumber
    });

    console.log(`Navigating to group details with params: ${params.toString()}`);
    // *** تصحيح المسار النسبي هنا ***
    // المسار الصحيح هو الصعود مستوى واحد ثم الدخول إلى مجلد group_details
    window.location.href = `../group_details/group_details.html?${params.toString()}`;
};

// --- Initialization ---
const initializePage = async () => {
    console.log('Student Subscriptions page initializing...');

    // Ensure Supabase client is ready before fetching
    if (!_supabase) {
        console.error('Supabase client not initialized. Cannot proceed.');
        showMessage(pageMessage, 'فشل الاتصال بقاعدة البيانات.', 'error');
        return;
    }

    // 1. Get selected month ID from sessionStorage
    selectedBudgetMonthId = sessionStorage.getItem('selectedBudgetMonthId');
    console.log('[student_subscriptions] Retrieved selectedBudgetMonthId from sessionStorage:', selectedBudgetMonthId); // Log 1: Check ID retrieval

    // 2. Check if a month ID exists
    if (!selectedBudgetMonthId) {
        console.error('Selected Budget Month ID not found in sessionStorage.');
        showMessage(pageMessage, 'لم يتم تحديد الشهر المالي. الرجاء العودة واختيار شهر أولاً.', 'error');
        if (groupsContainer) groupsContainer.innerHTML = '<p class="loading-placeholder error">الشهر غير محدد.</p>';
        if (selectedMonthDisplay) selectedMonthDisplay.textContent = 'غير محدد';
        // Fetch sidebar anyway, as it doesn't depend on the month
        await fetchAndRenderDefaultStudentsSidebar();
        setupEventListeners(); // Setup listeners even if month isn't selected
        return; // Stop further execution if no month is selected
    }

    // 3. Fetch details for the selected month ID (Simplified Query)
    try {
        console.log(`[student_subscriptions] Attempting to fetch month details for ID: ${selectedBudgetMonthId}`); // Log 2: Before fetch
        // Step 3a: Fetch month data only
        const { data: monthData, error: monthError, status: monthStatus } = await _supabase
            .from('budget_months')
            .select('id, month_number, month_name, budget_year_id') // Select budget_year_id directly
            .eq('id', selectedBudgetMonthId)
            .single();

        console.log('[student_subscriptions] Month fetch result:', { monthData, monthError, monthStatus }); // Log 3a: Month fetch result

        if (monthError) {
            console.error('[student_subscriptions] Error fetching month details:', monthError);
            const errorMsg = `فشل في جلب تفاصيل الشهر (Status: ${monthStatus}): ${monthError.message}`;
            throw new Error(errorMsg);
        }

        if (!monthData) {
             console.error('[student_subscriptions] No month data returned from Supabase for ID:', selectedBudgetMonthId);
             throw new Error('لم يتم العثور على بيانات للشهر المحدد.');
        }

        if (!monthData.budget_year_id) {
            console.error('[student_subscriptions] Month data found, but budget_year_id is missing or null:', monthData);
            throw new Error('لم يتم العثور على معرّف السنة المرتبط بهذا الشهر.');
        }

        // Step 3b: Fetch year data using budget_year_id
        console.log(`[student_subscriptions] Attempting to fetch year details for budget_year_id: ${monthData.budget_year_id}`);
        const { data: yearData, error: yearError, status: yearStatus } = await _supabase
            .from('budget_years')
            .select('year_number')
            .eq('id', monthData.budget_year_id)
            .single();

        console.log('[student_subscriptions] Year fetch result:', { yearData, yearError, yearStatus }); // Log 3b: Year fetch result

        if (yearError) {
            console.error('[student_subscriptions] Error fetching year details:', yearError);
            const errorMsg = `فشل في جلب تفاصيل السنة (Status: ${yearStatus}): ${yearError.message}`;
            throw new Error(errorMsg);
        }

        if (!yearData) {
            console.error('[student_subscriptions] No year data returned from Supabase for ID:', monthData.budget_year_id);
            throw new Error('لم يتم العثور على بيانات السنة المرتبطة.');
        }

        // 4. Update state and display with fetched details
        selectedBudgetMonthNumber = monthData.month_number;
        selectedBudgetYearNumber = yearData.year_number; // Use year_number from yearData
        const monthName = monthData.month_name;

        console.log('[student_subscriptions] Successfully fetched and processed month and year details:', { selectedBudgetMonthId, selectedBudgetMonthNumber, selectedBudgetYearNumber, monthName }); // Log 4: Success

        // *** Add explicit check before updating DOM ***
        console.log(`[student_subscriptions] Values before updating display: monthName='${monthName}', yearNumber='${selectedBudgetYearNumber}'`); // Log 4.5: Values check

        if (selectedMonthDisplay) {
            // Ensure values are not undefined/null before setting textContent
            if (monthName && selectedBudgetYearNumber) {
                selectedMonthDisplay.textContent = `${monthName} ${selectedBudgetYearNumber}`;
                console.log('[student_subscriptions] Updated selectedMonthDisplay element.'); // Log 5: UI Update
            } else {
                selectedMonthDisplay.textContent = 'خطأ في البيانات'; // Show error in display
                console.error('[student_subscriptions] Month name or year number is missing after processing. Cannot update display correctly.'); // Log 5.5: Data missing error
            }
        } else {
            console.warn('[student_subscriptions] selectedMonthDisplay element not found in the DOM.'); // Log 6: Element missing
        }

        // 5. Fetch sidebar and groups data now that month is confirmed
        console.log('[student_subscriptions] Proceeding to fetch sidebar and groups data...'); // Log 7: Before data fetch
        await Promise.all([
            fetchAndRenderDefaultStudentsSidebar(),
            fetchAndRenderGroups(), // This function uses the global selectedBudgetMonthId
            updateUngroupedStudentsCount(), // Update ungrouped students count
            updateGroupsCount() // Update groups count
        ]);
        console.log('[student_subscriptions] Finished fetching sidebar and groups data.'); // Log 8: After data fetch

    } catch (error) {
        console.error('[student_subscriptions] Error during initialization phase (fetching month details or subsequent data):', error); // Log 9: Catch block
        showMessage(pageMessage, error.message || 'خطأ غير متوقع أثناء تهيئة الصفحة.', 'error');
        if (groupsContainer) groupsContainer.innerHTML = '<p class="loading-placeholder error">فشل تحميل البيانات.</p>';
        // Ensure display shows error state in catch block too
        if (selectedMonthDisplay) selectedMonthDisplay.textContent = 'خطأ في التحميل';
    }

    // 6. Setup Event Listeners
    setupEventListeners();

    console.log('Student Subscriptions page initialization complete.');
};

// --- Ungrouped Students Functions ---
// Fetch ungrouped students using RPC function
const fetchUngroupedStudents = async () => {
    console.log('[Ungrouped] Fetching students not in any group using RPC...');
    if (!_supabase) {
        console.error('[Ungrouped] Supabase client not available.');
        return [];
    }

    try {
        // Use RPC function to get ungrouped students
        const { data: ungroupedStudents, error } = await _supabase
            .rpc('get_ungrouped_students');

        if (error) throw error;

        console.log('[Ungrouped] Fetched ungrouped students via RPC:', ungroupedStudents);

        // Transform the data to match the expected format
        const transformedStudents = ungroupedStudents.map(student => ({
            id: student.id,
            name: student.name,
            parent_phone: student.parent_phone,
            schools: { name: student.school_name },
            neighborhoods: { name: student.neighborhood_name },
            student_subscription_defaults: {
                default_amount: student.default_amount,
                current_balance: student.current_balance
            }
        }));

        return transformedStudents || [];
    } catch (error) {
        console.error('[Ungrouped] Error fetching ungrouped students:', error);
        return [];
    }
};

// Update ungrouped students count (optimized version)
const updateUngroupedStudentsCount = async () => {
    const ungroupedCountElement = document.getElementById('ungrouped-count');
    const ungroupedBtn = document.getElementById('ungrouped-students-btn');

    if (!ungroupedCountElement || !ungroupedBtn) return;

    try {
        // Use the optimized count function for better performance
        const { data: countResult, error } = await _supabase
            .rpc('count_ungrouped_students');

        if (error) throw error;

        const count = countResult || 0;

        ungroupedCountElement.textContent = count;

        // Disable button if no ungrouped students
        if (count === 0) {
            ungroupedBtn.disabled = true;
            ungroupedBtn.title = 'جميع الطلاب منضمون لمجموعات';
        } else {
            ungroupedBtn.disabled = false;
            ungroupedBtn.title = `${count} طالب غير منضم لمجموعات`;
        }

        console.log('[Ungrouped] Updated count:', count);
    } catch (error) {
        console.error('[Ungrouped] Error updating count:', error);
        ungroupedCountElement.textContent = '?';
        ungroupedBtn.disabled = true;
        ungroupedBtn.title = 'خطأ في تحميل العدد';
    }
};

// Global variables for modal state
let isSelectionMode = false;
let selectedStudents = new Set();
let currentUngroupedStudents = [];

// Show ungrouped students modal
const showUngroupedStudentsModal = async () => {
    const modal = document.getElementById('ungrouped-students-modal');
    const messageArea = document.getElementById('ungrouped-students-message');
    const studentsList = document.getElementById('ungrouped-students-list');

    if (!modal || !studentsList) return;

    // Reset modal state
    resetModalState();

    // Show modal
    modal.classList.add('active');

    // Show loading
    studentsList.innerHTML = '<div class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i> جاري تحميل الطلاب...</div>';

    try {
        const ungroupedStudents = await fetchUngroupedStudents();
        currentUngroupedStudents = ungroupedStudents;

        if (ungroupedStudents.length === 0) {
            studentsList.innerHTML = '<div class="loading-placeholder">جميع الطلاب منضمون لمجموعات</div>';
            return;
        }

        // Render students list
        renderStudentsList(ungroupedStudents);

        // Hide message area
        if (messageArea) {
            messageArea.style.display = 'none';
        }

    } catch (error) {
        console.error('[Ungrouped] Error loading students for modal:', error);
        studentsList.innerHTML = '<div class="loading-placeholder error">خطأ في تحميل الطلاب</div>';

        if (messageArea) {
            messageArea.textContent = 'خطأ في تحميل بيانات الطلاب';
            messageArea.className = 'message error';
            messageArea.style.display = 'block';
        }
    }
};

// Reset modal state
const resetModalState = () => {
    isSelectionMode = false;
    selectedStudents.clear();

    // Reset UI elements
    const toggleBtn = document.getElementById('toggle-selection-mode');
    const selectAllBtn = document.getElementById('select-all-students');
    const deselectAllBtn = document.getElementById('deselect-all-students');
    const selectedCount = document.getElementById('selected-students-count');
    const addSelectedBtn = document.getElementById('add-selected-to-group-btn');
    const studentsView = document.getElementById('students-list-view');
    const groupsView = document.getElementById('groups-selection-view');
    const backBtn = document.getElementById('back-to-students-btn');

    if (toggleBtn) {
        toggleBtn.innerHTML = '<i class="fas fa-check-square"></i> تفعيل التحديد';
        toggleBtn.className = 'control-btn primary-btn';
    }
    if (selectAllBtn) selectAllBtn.style.display = 'none';
    if (deselectAllBtn) deselectAllBtn.style.display = 'none';
    if (selectedCount) selectedCount.style.display = 'none';
    if (addSelectedBtn) addSelectedBtn.style.display = 'none';
    if (studentsView) studentsView.style.display = 'block';
    if (groupsView) groupsView.style.display = 'none';
    if (backBtn) backBtn.style.display = 'none';
};

// Render students list
const renderStudentsList = (students) => {
    const studentsList = document.getElementById('ungrouped-students-list');
    if (!studentsList) return;

    studentsList.innerHTML = students.map(student => {
        const schoolName = student.schools?.name || 'غير محدد';
        const neighborhoodName = student.neighborhoods?.name || 'غير محدد';
        const defaultAmount = student.student_subscription_defaults?.default_amount || 0;
        const currentBalance = student.student_subscription_defaults?.current_balance || 0;

        const isSelected = selectedStudents.has(student.id);
        const selectionClass = isSelectionMode ? 'selectable' : '';
        const selectedClass = isSelected ? 'selected' : '';

        return `
            <div class="ungrouped-student-item ${selectionClass} ${selectedClass}" data-student-id="${student.id}">
                ${isSelectionMode ? `
                    <input type="checkbox" class="student-checkbox" ${isSelected ? 'checked' : ''}
                           onchange="toggleStudentSelection('${student.id}')">
                ` : ''}
                <div class="ungrouped-student-info">
                    <div class="ungrouped-student-name">${student.name}</div>
                    <div class="ungrouped-student-details">
                        <span><i class="fas fa-phone"></i> ${student.parent_phone || 'غير محدد'}</span> |
                        <span><i class="fas fa-school"></i> ${schoolName}</span> |
                        <span><i class="fas fa-map-marker-alt"></i> ${neighborhoodName}</span>
                    </div>
                    <div class="ungrouped-student-details">
                        <span><i class="fas fa-money-bill"></i> الاشتراك الافتراضي: ${defaultAmount} ريال</span> |
                        <span><i class="fas fa-wallet"></i> الرصيد الحالي: ${currentBalance} ريال</span>
                    </div>
                </div>
                ${!isSelectionMode ? `
                    <div class="ungrouped-student-actions">
                        <button class="btn primary-btn" onclick="addSingleStudentToGroup('${student.id}', '${student.name}')">
                            <i class="fas fa-plus"></i> إضافة لمجموعة
                        </button>
                    </div>
                ` : ''}
            </div>
        `;
    }).join('');

    // Add click listeners for selection mode
    if (isSelectionMode) {
        const studentItems = studentsList.querySelectorAll('.ungrouped-student-item.selectable');
        studentItems.forEach(item => {
            item.addEventListener('click', (e) => {
                if (e.target.type !== 'checkbox') {
                    const studentId = item.dataset.studentId;
                    const checkbox = item.querySelector('.student-checkbox');
                    checkbox.checked = !checkbox.checked;
                    toggleStudentSelection(studentId);
                }
            });
        });
    }
};

// Toggle selection mode
const toggleSelectionMode = () => {
    isSelectionMode = !isSelectionMode;

    const toggleBtn = document.getElementById('toggle-selection-mode');
    const selectAllBtn = document.getElementById('select-all-students');
    const deselectAllBtn = document.getElementById('deselect-all-students');
    const selectedCount = document.getElementById('selected-students-count');

    if (isSelectionMode) {
        // Enable selection mode
        toggleBtn.innerHTML = '<i class="fas fa-times"></i> إلغاء التحديد';
        toggleBtn.className = 'control-btn danger-btn';
        selectAllBtn.style.display = 'inline-block';
        deselectAllBtn.style.display = 'inline-block';
        selectedCount.style.display = 'inline-block';
    } else {
        // Disable selection mode
        toggleBtn.innerHTML = '<i class="fas fa-check-square"></i> تفعيل التحديد';
        toggleBtn.className = 'control-btn primary-btn';
        selectAllBtn.style.display = 'none';
        deselectAllBtn.style.display = 'none';
        selectedCount.style.display = 'none';
        selectedStudents.clear();
    }

    // Re-render students list
    renderStudentsList(currentUngroupedStudents);
    updateSelectedCount();
};

// Toggle student selection
const toggleStudentSelection = (studentId) => {
    if (selectedStudents.has(studentId)) {
        selectedStudents.delete(studentId);
    } else {
        selectedStudents.add(studentId);
    }

    // Update UI
    const studentItem = document.querySelector(`[data-student-id="${studentId}"]`);
    const checkbox = studentItem?.querySelector('.student-checkbox');

    if (studentItem) {
        if (selectedStudents.has(studentId)) {
            studentItem.classList.add('selected');
            if (checkbox) checkbox.checked = true;
        } else {
            studentItem.classList.remove('selected');
            if (checkbox) checkbox.checked = false;
        }
    }

    updateSelectedCount();
};

// Update selected count display
const updateSelectedCount = () => {
    const countElement = document.getElementById('selected-count-number');
    const addSelectedBtn = document.getElementById('add-selected-to-group-btn');

    if (countElement) {
        countElement.textContent = selectedStudents.size;
    }

    if (addSelectedBtn) {
        if (selectedStudents.size > 0) {
            addSelectedBtn.style.display = 'inline-block';
            addSelectedBtn.innerHTML = `<i class="fas fa-plus-circle"></i> إضافة ${selectedStudents.size} طالب للمجموعة`;
        } else {
            addSelectedBtn.style.display = 'none';
        }
    }
};

// Select all students
const selectAllStudents = () => {
    currentUngroupedStudents.forEach(student => {
        selectedStudents.add(student.id);
    });
    renderStudentsList(currentUngroupedStudents);
    updateSelectedCount();
};

// Deselect all students
const deselectAllStudents = () => {
    selectedStudents.clear();
    renderStudentsList(currentUngroupedStudents);
    updateSelectedCount();
};

// Show groups selection view
const showGroupsSelection = async () => {
    const studentsView = document.getElementById('students-list-view');
    const groupsView = document.getElementById('groups-selection-view');
    const addSelectedBtn = document.getElementById('add-selected-to-group-btn');
    const backBtn = document.getElementById('back-to-students-btn');
    const groupsList = document.getElementById('groups-list');

    // Switch views
    studentsView.style.display = 'none';
    groupsView.style.display = 'block';
    addSelectedBtn.style.display = 'none';
    backBtn.style.display = 'inline-block';

    // Load groups
    groupsList.innerHTML = '<div class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i> جاري تحميل المجموعات...</div>';

    try {
        const { data: groups, error } = await _supabase
            .from('student_groups')
            .select(`
                id,
                name,
                student_group_members (
                    student_id,
                    students ( name )
                )
            `)
            .order('name');

        if (error) throw error;

        if (!groups || groups.length === 0) {
            groupsList.innerHTML = '<div class="loading-placeholder">لا توجد مجموعات متاحة</div>';
            return;
        }

        // Render groups list
        groupsList.innerHTML = groups.map(group => {
            const memberCount = group.student_group_members?.length || 0;

            return `
                <div class="group-selection-item" data-group-id="${group.id}" onclick="selectGroup('${group.id}', '${group.name}')">
                    <div class="group-selection-info">
                        <div class="group-selection-name">${group.name}</div>
                        <div class="group-selection-details">
                            <i class="fas fa-users"></i> ${memberCount} طالب
                        </div>
                    </div>
                    <div class="group-selection-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                </div>
            `;
        }).join('');

    } catch (error) {
        console.error('[Groups] Error loading groups:', error);
        groupsList.innerHTML = '<div class="loading-placeholder error">خطأ في تحميل المجموعات</div>';
    }
};

// Select group and add students
const selectGroup = async (groupId, groupName) => {
    if (selectedStudents.size === 0) {
        alert('لم يتم تحديد أي طلاب');
        return;
    }

    const confirmation = confirm(`هل تريد إضافة ${selectedStudents.size} طالب إلى مجموعة "${groupName}"؟`);
    if (!confirmation) return;

    try {
        // Prepare records for insertion
        const recordsToInsert = Array.from(selectedStudents).map(studentId => ({
            student_id: studentId,
            group_id: groupId
        }));

        // Insert students into group
        const { data, error } = await _supabase
            .from('student_group_members')
            .insert(recordsToInsert)
            .select();

        if (error) throw error;

        alert(`تم إضافة ${data.length} طالب إلى مجموعة "${groupName}" بنجاح!`);

        // Refresh data and close modal
        await Promise.all([
            fetchAndRenderDefaultStudentsSidebar(),
            fetchAndRenderGroups(),
            updateUngroupedStudentsCount()
        ]);

        closeUngroupedStudentsModal();

    } catch (error) {
        console.error('[Groups] Error adding students to group:', error);
        alert(`خطأ في إضافة الطلاب: ${error.message}`);
    }
};

// Back to students view
const backToStudentsView = () => {
    const studentsView = document.getElementById('students-list-view');
    const groupsView = document.getElementById('groups-selection-view');
    const addSelectedBtn = document.getElementById('add-selected-to-group-btn');
    const backBtn = document.getElementById('back-to-students-btn');

    studentsView.style.display = 'block';
    groupsView.style.display = 'none';
    backBtn.style.display = 'none';

    if (selectedStudents.size > 0) {
        addSelectedBtn.style.display = 'inline-block';
    }
};

// Add single student to group (legacy function)
const addSingleStudentToGroup = (studentId, studentName) => {
    selectedStudents.clear();
    selectedStudents.add(studentId);
    showGroupsSelection();
};

// Close ungrouped students modal
const closeUngroupedStudentsModal = () => {
    const modal = document.getElementById('ungrouped-students-modal');
    if (modal) {
        modal.classList.remove('active');
        resetModalState();
    }
};

// Update groups count display
const updateGroupsCount = async () => {
    const groupsCountElement = document.getElementById('groups-count');
    if (!groupsCountElement || !_supabase) return;

    try {
        const { count, error } = await _supabase
            .from('student_groups')
            .select('*', { count: 'exact', head: true });

        if (error) throw error;

        groupsCountElement.textContent = count || 0;
        console.log('[Groups Count] Updated count:', count);
    } catch (error) {
        console.error('[Groups Count] Error updating count:', error);
        groupsCountElement.textContent = '!';
    }
};

// --- Event Listeners ---
const setupEventListeners = () => {
    // Listener for group card clicks (Navigation only - action buttons removed)
    if (groupsContainer) {
        groupsContainer.addEventListener('click', (event) => {
            // Since action buttons are removed, all clicks on group cards will navigate
            // Navigation is handled by individual listeners on each card in renderGroupCards
            console.log('[Event] Group card clicked - navigation handled by individual card listeners');
        });
    }

    // Listener for Add Group Button
    if (addGroupBtn) {
        addGroupBtn.addEventListener('click', openAddGroupModal);
    }

    // Listeners for Add Group Modal
    if (closeAddGroupModalBtn) {
        closeAddGroupModalBtn.addEventListener('click', closeAddGroupModal);
    }
    if (cancelAddGroupBtn) {
        cancelAddGroupBtn.addEventListener('click', closeAddGroupModal);
    }
    if (addGroupModal) {
        // Close modal if clicking outside the modal content
        addGroupModal.addEventListener('click', (event) => {
            if (event.target === addGroupModal) {
                closeAddGroupModal();
            }
        });
    }
    if (addGroupForm) {
        addGroupForm.addEventListener('submit', handleAddGroupSubmit);
    }

    // == Listeners for Add Student Modal ==
    if (closeAddStudentModalBtn) {
        closeAddStudentModalBtn.addEventListener('click', closeAddStudentModal);
    }
    if (cancelAddStudentBtn) {
        cancelAddStudentBtn.addEventListener('click', closeAddStudentModal);
    }
    if (addStudentModal) {
        addStudentModal.addEventListener('click', (event) => {
            if (event.target === addStudentModal) {
                closeAddStudentModal();
            }
        });
    }
    if (addStudentForm) {
        addStudentForm.addEventListener('submit', handleAddStudentToGroupSubmit);
    }
    // == End Listeners for Add Student Modal ==

    // == Listeners for Add Default Students Modal (NEW) ==
    if (closeAddDefaultStudentsModalBtn) {
        closeAddDefaultStudentsModalBtn.addEventListener('click', closeAddDefaultStudentsModal);
    }
    if (cancelAddDefaultStudentsBtn) {
        cancelAddDefaultStudentsBtn.addEventListener('click', closeAddDefaultStudentsModal);
    }
    if (addDefaultStudentsModal) {
        addDefaultStudentsModal.addEventListener('click', (event) => {
            if (event.target === addDefaultStudentsModal) {
                closeAddDefaultStudentsModal();
            }
        });
    }
    if (addDefaultStudentsForm) {
        addDefaultStudentsForm.addEventListener('submit', handleAddDefaultStudentsSubmit);
    }
    // == End Listeners for Add Default Students Modal ==

    // == Listeners for Ungrouped Students Modal ==
    const ungroupedStudentsBtn = document.getElementById('ungrouped-students-btn');
    const closeUngroupedModalBtn = document.getElementById('close-ungrouped-modal-btn');
    const closeUngroupedModalFooterBtn = document.getElementById('close-ungrouped-modal-footer-btn');
    const ungroupedStudentsModal = document.getElementById('ungrouped-students-modal');

    // Selection controls
    const toggleSelectionBtn = document.getElementById('toggle-selection-mode');
    const selectAllBtn = document.getElementById('select-all-students');
    const deselectAllBtn = document.getElementById('deselect-all-students');
    const addSelectedBtn = document.getElementById('add-selected-to-group-btn');
    const backToStudentsBtn = document.getElementById('back-to-students-btn');

    if (ungroupedStudentsBtn) {
        ungroupedStudentsBtn.addEventListener('click', showUngroupedStudentsModal);
    }
    if (closeUngroupedModalBtn) {
        closeUngroupedModalBtn.addEventListener('click', closeUngroupedStudentsModal);
    }
    if (closeUngroupedModalFooterBtn) {
        closeUngroupedModalFooterBtn.addEventListener('click', closeUngroupedStudentsModal);
    }
    if (ungroupedStudentsModal) {
        ungroupedStudentsModal.addEventListener('click', (event) => {
            if (event.target === ungroupedStudentsModal) {
                closeUngroupedStudentsModal();
            }
        });
    }

    // Selection control listeners
    if (toggleSelectionBtn) {
        toggleSelectionBtn.addEventListener('click', toggleSelectionMode);
    }
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', selectAllStudents);
    }
    if (deselectAllBtn) {
        deselectAllBtn.addEventListener('click', deselectAllStudents);
    }
    if (addSelectedBtn) {
        addSelectedBtn.addEventListener('click', showGroupsSelection);
    }
    if (backToStudentsBtn) {
        backToStudentsBtn.addEventListener('click', backToStudentsView);
    }
    // == End Listeners for Ungrouped Students Modal ==

    // Back to Dashboard Button
    if (backToDashboardBtn) {
        backToDashboardBtn.addEventListener('click', () => {
            window.location.href = '../financial_dashboard.html'; // Navigate back
        });
    }

    // Global Escape Listener (if not already present)
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            if (addGroupModal?.classList.contains('active')) closeAddGroupModal();
            if (addStudentModal?.classList.contains('active')) closeAddStudentModal();
            if (addDefaultStudentsModal?.classList.contains('active')) closeAddDefaultStudentsModal(); // New
        }
    });

    // Add other listeners later
};

// --- Initial Load ---
// Use DOMContentLoaded to ensure the DOM is ready before accessing elements
document.addEventListener('DOMContentLoaded', initializePage);
