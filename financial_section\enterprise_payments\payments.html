<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدفوعات المؤسسة</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="dashboard-container">
        <aside class="sidebar">
            <h2>المؤسسات</h2>
            <nav>
                <ul>
                    <!-- Enterprise List -->
                    <li><a href="#" data-enterprise-id="1">مؤسسة التقنية الحديثة</a></li>
                    <li><a href="#" data-enterprise-id="2">شركة الحلول المتكاملة</a></li>
                    <li><a href="#" data-enterprise-id="3">مجموعة الإبداع الرقمي</a></li>
                    <!-- Add more enterprises as needed -->
                </ul>
            </nav>
            <hr class="sidebar-divider"> <!-- Optional divider -->
            <h2>المدفوعات</h2>
             <nav>
                 <ul>
                    <li><a href="#">نظرة عامة</a></li>
                    <li class="active"><a href="#">عرض المدفوعات</a></li>
                    <li><a href="#">دفعة جديدة</a></li>
                    <li><a href="#">التقارير</a></li>
                 </ul>
             </nav>
        </aside>

        <main class="main-content">
            <header>
                <h1>مدفوعات: <span id="selected-enterprise-name">[اختر مؤسسة من القائمة]</span></h1>
                <button class="btn btn-primary">دفعة جديدة</button>
            </header>

            <section class="summary-cards">
                <div class="card">
                    <h3>إجمالي المدفوعات</h3>
                    <p>...</p> <!-- Data should load based on selection -->
                </div>
                <div class="card">
                    <h3>مدفوعات معلقة</h3>
                    <p>...</p> <!-- Data should load based on selection -->
                </div>
                <div class="card">
                    <h3>مدفوعات متأخرة</h3>
                    <p>...</p> <!-- Data should load based on selection -->
                </div>
            </section>

            <section class="payment-table">
                <h2>أحدث المدفوعات</h2>
                <!-- Table content should load based on selection -->
                <table>
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>رقم الفاتورة</th>
                            <th>المستلم</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Example Row - Clear or replace dynamically -->
                        <tr>
                            <td colspan="6" style="text-align: center;">الرجاء اختيار مؤسسة لعرض المدفوعات.</td>
                        </tr>
                    </tbody>
                </table>
            </section>
        </main>
    </div>
    <!-- Optional: Add script to update header/content based on selection -->
    <script>
        const enterpriseLinks = document.querySelectorAll('.sidebar nav ul li a[data-enterprise-id]');
        const selectedEnterpriseNameElement = document.getElementById('selected-enterprise-name');
        const paymentTableBody = document.querySelector('.payment-table tbody');
        const summaryCardValues = document.querySelectorAll('.summary-cards .card p'); // Select all value paragraphs

        enterpriseLinks.forEach(link => {
            link.addEventListener('click', (event) => {
                event.preventDefault();

                // Remove active class from all links
                enterpriseLinks.forEach(l => l.parentElement.classList.remove('active'));
                 // Add active class to the clicked link's parent li
                link.parentElement.classList.add('active');


                const enterpriseName = link.textContent;
                const enterpriseId = link.dataset.enterpriseId;
                selectedEnterpriseNameElement.textContent = enterpriseName;

                // --- Placeholder for loading data ---
                console.log(`Loading data for enterprise ID: ${enterpriseId}, Name: ${enterpriseName}`);

                // Example: Clear previous data and show loading state
                paymentTableBody.innerHTML = '<tr><td colspan="6" style="text-align: center;">جاري تحميل بيانات المدفوعات...</td></tr>';
                summaryCardValues.forEach(p => p.textContent = '...'); // Reset summary cards

                // TODO: Add actual AJAX call here to fetch and display data
                // based on enterpriseId. For now, we'll just log it.
                // Example of updating table after fake fetch:
                /*
                setTimeout(() => {
                    paymentTableBody.innerHTML = `
                        <tr>
                            <td>2024-05-21</td>
                            <td>INV-${enterpriseId}01</td>
                            <td>مورد ${enterpriseName}</td>
                            <td>${Math.random() * 10000} ريال</td>
                            <td><span class="status status-paid">مدفوعة</span></td>
                            <td><a href="#">عرض</a></td>
                        </tr>`;
                    summaryCardValues[0].textContent = `${Math.random() * 200000} ريال`;
                    summaryCardValues[1].textContent = `${Math.random() * 50000} ريال`;
                    summaryCardValues[2].textContent = `${Math.random() * 10000} ريال`;
                }, 500); // Simulate network delay
                */
            });
        });
    </script>
</body>
</html>
