create table public.bank_transactions (
  id uuid not null default gen_random_uuid (),
  bank_id uuid not null,
  amount numeric(10, 2) not null,
  transaction_type public.transaction_type not null,
  transaction_date date not null default CURRENT_DATE,
  description text null,
  transaction_source text null,
  reference_id uuid null,
  reference_code text null,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  transaction_source_type character varying(50) null,
  reference_table text null,
  budget_month_id uuid null,
  enterprise_subscription_id bigint null, -- عمود جديد لربط المعاملة بالاشتراك
  constraint bank_transactions_pkey primary key (id),
  constraint bank_transactions_bank_id_fkey foreign KEY (bank_id) references banks (id) on delete CASCADE,
  constraint fk_bank_transactions_budget_month foreign KEY (budget_month_id) references budget_months (id) on update CASCADE on delete set null,
  constraint bank_transactions_amount_check check ((amount >= (0)::numeric)),
  constraint bank_transactions_enterprise_subscription_id_fkey foreign key (enterprise_subscription_id) references public.enterprise_subscriptions (id) on delete set null -- قيد جديد
) TABLESPACE pg_default;

create index IF not exists idx_bank_transactions_bank_id on public.bank_transactions using btree (bank_id) TABLESPACE pg_default;

create index IF not exists idx_bank_transactions_transaction_date on public.bank_transactions using btree (transaction_date) TABLESPACE pg_default;

create index IF not exists idx_bank_transactions_transaction_source on public.bank_transactions using btree (transaction_source) TABLESPACE pg_default;

create index IF not exists idx_bank_transactions_reference_id on public.bank_transactions using btree (reference_id) TABLESPACE pg_default;

create index IF not exists idx_bank_transactions_budget_month_id on public.bank_transactions using btree (budget_month_id) TABLESPACE pg_default;

create index IF not exists idx_bank_transactions_enterprise_subscription_id on public.bank_transactions using btree (enterprise_subscription_id) TABLESPACE pg_default; -- فهرس للعمود الجديد

create trigger handle_updated_at BEFORE
update on bank_transactions for EACH row
execute FUNCTION trigger_set_timestamp ();







create table public.banks (
  id uuid not null default gen_random_uuid (),
  name text not null,
  account_number text null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  notes text null,
  branch character varying(255) null,
  contact_phone character varying(20) null,
  bank_type text null,
  constraint banks_pkey primary key (id),
  constraint banks_bank_type_check check (
    (
      bank_type = any (array['اجل'::text, 'مركزي'::text])
    )
  )
) TABLESPACE pg_default;

create trigger update_banks_timestamp BEFORE
update on banks for EACH row
execute FUNCTION update_timestamp ();

create trigger update_banks_updated_at BEFORE
update on banks for EACH row
execute FUNCTION update_updated_at_column ();




create table public.enterprise_subscriptions (
  id bigserial not null,
  enterprise_id integer not null,
  budget_month_id uuid not null,
  service_type text null,
  total_amount numeric(10, 2) not null default 0.00,
  paid_amount numeric(10, 2) not null default 0.00,
  payment_status text null default 'not_paid'::text,
  subscription_date date null,
  payment_date date null,
  notes text null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint enterprise_subscriptions_pkey primary key (id),
  payment_bank_id uuid null, -- عمود جديد لتخزين بنك الدفع
  constraint enterprise_subscriptions_enterprise_id_budget_month_id_key unique (enterprise_id, budget_month_id),
  constraint enterprise_subscriptions_budget_month_id_fkey foreign KEY (budget_month_id) references budget_months (id) on delete RESTRICT,
  constraint enterprise_subscriptions_enterprise_id_fkey foreign KEY (enterprise_id) references enterprises (id) on delete CASCADE,
  constraint enterprise_subscriptions_payment_bank_id_fkey foreign key (payment_bank_id) references public.banks (id) on delete set null -- قيد جديد
) TABLESPACE pg_default;

create index IF not exists idx_enterprise_subscriptions_payment_bank_id on public.enterprise_subscriptions using btree (payment_bank_id) TABLESPACE pg_default; -- فهرس للعمود الجديد