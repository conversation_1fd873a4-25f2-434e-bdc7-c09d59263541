-- =============================================
-- جدول دفعات اشتراكات الطلاب الشهرية
-- =============================================
-- يخزن هذا الجدول كل دفعة فردية يقوم بها الطالب لاشتراكه الشهري.
-- كل دفعة يجب أن تكون مرتبطة بسجل اشتراك شهري محدد.

CREATE TABLE public.student_subscription_payments (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY, -- المعرف الفريد للدفعة
    monthly_subscription_id uuid NOT NULL,                  -- معرف الاشتراك الشهري المرتبطة به الدفعة (مفتاح أجنبي إلزامي)
    student_id uuid NOT NULL,                               -- معرف الطالب (للسهولة في الاستعلام، مفتاح أجنبي)
    amount numeric(10, 2) NOT NULL,                         -- مبلغ الدفعة
    payment_date date NOT NULL,                             -- تاريخ الدفعة
    bank_id uuid NOT NULL,                                  -- معرف البنك الذي تمت عبره الدفعة (مفتاح أجنبي)
    created_at timestamp with time zone DEFAULT now() NOT NULL, -- تاريخ إنشاء سجل الدفعة

    -- قيد للتحقق من أن المبلغ موجب
    CONSTRAINT positive_payment_amount CHECK (amount > 0),

    -- الربط الإلزامي بجدول الاشتراكات الشهرية
    CONSTRAINT student_subscription_payments_monthly_subscription_id_fkey FOREIGN KEY (monthly_subscription_id)
        REFERENCES public.monthly_student_subscriptions (id) ON UPDATE CASCADE ON DELETE CASCADE, -- عند حذف اشتراك شهري، تحذف دفعاته

    -- الربط بجدول الطلاب
    CONSTRAINT student_subscription_payments_student_id_fkey FOREIGN KEY (student_id)
        REFERENCES public.students (id) ON UPDATE CASCADE ON DELETE CASCADE, -- عند حذف طالب، تحذف دفعاته

    -- الربط بجدول البنوك
    CONSTRAINT student_subscription_payments_bank_id_fkey FOREIGN KEY (bank_id)
        REFERENCES public.banks (id) ON UPDATE CASCADE ON DELETE RESTRICT -- لا تسمح بحذف بنك إذا كانت هناك دفعات مرتبطة به (أو استخدم SET NULL إذا أردت السماح بذلك)
);

-- إضافة تعليقات توضيحية
COMMENT ON TABLE public.student_subscription_payments IS 'سجلات الدفعات الفردية لاشتراكات الطلاب الشهرية.';
COMMENT ON COLUMN public.student_subscription_payments.monthly_subscription_id IS 'يشير إلى سجل الاشتراك الشهري المحدد في جدول monthly_student_subscriptions.';
COMMENT ON COLUMN public.student_subscription_payments.student_id IS 'معرف الطالب الذي قام بالدفعة.';
COMMENT ON COLUMN public.student_subscription_payments.amount IS 'المبلغ المدفوع في هذه الدفعة.';
COMMENT ON COLUMN public.student_subscription_payments.payment_date IS 'تاريخ إجراء الدفعة.';
COMMENT ON COLUMN public.student_subscription_payments.bank_id IS 'معرف البنك المستخدم في الدفعة.';

-- =============================================
-- تذكير هام بخصوص صلاحيات الوصول (RLS)
-- =============================================
-- تأكد من تفعيل سياسات الأمان على مستوى الصف (RLS) لهذا الجدول في Supabase.
-- يجب أن تسمح السياسة للمستخدمين المصادق عليهم (authenticated) بتنفيذ عملية INSERT.
-- بدون سياسة INSERT صحيحة، ستواجه خطأ "404 Not Found" أو "Forbidden" عند محاولة الحفظ من JavaScript.

-- مثال لسياسة INSERT بسيطة (للمستخدمين المسجلين):
/*
CREATE POLICY "Allow authenticated users to insert payments"
ON public.student_subscription_payments
FOR INSERT
TO authenticated
WITH CHECK (true);
*/

-- =============================================
-- مثال على كيفية إضافة دفعة (بافتراض وجود اشتراك شهري)
-- =============================================
/*
INSERT INTO public.student_subscription_payments
    (monthly_subscription_id, student_id, amount, payment_date, bank_id)
VALUES
    ('[EXISTING_MONTHLY_SUB_UUID]', '[STUDENT_UUID]', [AMOUNT], '[YYYY-MM-DD]', '[BANK_UUID]');
*/

-- مثال عملي:
/*
INSERT INTO public.student_subscription_payments
    (monthly_subscription_id, student_id, amount, payment_date, bank_id)
VALUES
    ('PUT_ACTUAL_MONTHLY_SUB_ID_HERE', 'a126dc80-c879-4e68-9132-aac50eb787e3', 500.00, '2025-04-15', 'PUT_ACTUAL_BANK_ID_HERE');
*/

