<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختيار الشهر المالي</title>
    <!-- Shared Styles -->
    <link rel="stylesheet" href="../shared_styles.css">
    <!-- Page Specific Styles -->
    <link rel="stylesheet" href="select_budget_month.css">
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Config -->
    <script src="../config.js"></script>
    <!-- Auth Script -->
    <script src="../auth.js"></script>
    <!-- Page Script -->
    <script defer src="select_budget_month.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts (Example: Tajawal) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navbar -->
    <nav class="main-navbar">
        <div class="navbar-brand">
            <i class="fas fa-calendar-day"></i>
            اختيار الشهر المالي
        </div>
        <div class="navbar-user">
            <span id="navbar-username"></span>
            <button id="logout-btn" class="logout-btn" style="display: none;">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </button>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container">
        <main class="selection-main">
            <div class="card selection-card">
                <div class="card-header">
                    <h2><i class="fas fa-th-large"></i> اختر الشهر المطلوب للسنة <span id="selected-year-display">...</span></h2>
                    <button id="back-to-year-btn" class="btn btn-secondary back-btn">
                        <i class="fas fa-arrow-right"></i> العودة لاختيار السنة
                    </button>
                </div>
                <div class="card-body">
                    <div id="message-area" class="message" style="display: none;"></div>
                    <div id="month-cards-container" class="cards-container">
                        <!-- Month cards will be loaded here by JS -->
                        <div class="loading-placeholder">
                            <i class="fas fa-spinner fa-spin"></i> جاري تحميل الشهور...
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Footer -->
    <footer class="main-footer">
         <p>&copy; 2024 نظام الإدارة المالية</p>
    </footer>
</body>
</html>
