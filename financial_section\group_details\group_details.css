/* Import or copy base styles from student_subscriptions.css or shared_styles.css */
@import url('../../shared_styles.css'); /* Example if using shared styles */

:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #7f8c8d;
    --success-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #dee2e6;
    --card-bg: #ffffff;
    --body-bg: #f4f7f6;
    --shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
}

/* Dashboard Layout */
.dashboard-layout {
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
    font-family: 'Tajawal', sans-serif;
    overflow-x: hidden;
}

/* Top Navigation Bar */
.top-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-toggle-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.sidebar-toggle-btn:hover {
    background-color: rgba(255,255,255,0.1);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 600;
}

.navbar-brand .highlight {
    background-color: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 700;
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.navbar-right .back-btn {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.navbar-right .back-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

/* Main Content */
.main-content {
    margin-top: 70px;
    padding: 0;
    min-height: calc(100vh - 70px);
    transition: margin-right 0.3s ease;
}

.content-wrapper {
    padding: 20px;
    max-width: 95%;
    margin: 0 auto;
}

/* Student Cards Container - Direct Layout */

.student-cards-container {
    display: flex; /* Enables flexbox */
    flex-wrap: wrap; /* Allows items to wrap to the next line */
    justify-content: flex-start; /* Aligns items to the start (respects LTR/RTL direction) */
    gap: 1rem; /* Adds space between cards */
    padding: 1rem; /* Adds padding around the container */
}

/* Bulk Actions Container */
.bulk-actions-container {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    display: none;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.bulk-actions-container.show {
    display: flex;
}

.bulk-actions-container .selected-count {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1rem;
}

.bulk-actions-container .btn {
    background-color: var(--danger-color);
    color: white;
    border: none;
    padding: 0.6rem 1.2rem;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.bulk-actions-container .btn:hover {
    background-color: #c0392b;
    transform: translateY(-1px);
}

/* Style for the selected month display */
.selected-month-display {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.85); /* Slightly transparent white */
    margin: 0;
    padding: 0;
}

#selected-month-name {
    font-weight: 600;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

#group-name-display {
    font-weight: 700;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 3px 8px;
    border-radius: 4px;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 5px; /* Align button slightly lower if needed */
}

/* Removed students container - not needed */

/* Main Content */
.main-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Cards */
.card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.card-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--light-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    margin: 0;
    font-size: 1.2rem;
    color: var(--primary-dark);
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-body {
    padding: 1.5rem;
}

/* Buttons */
.control-btn {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s ease, transform 0.1s ease;
    white-space: nowrap;
}
.control-btn:hover { opacity: 0.9; }
.control-btn:active { transform: scale(0.98); }
.primary-btn { background-color: var(--primary-color); color: white; }
.secondary-btn { background-color: var(--secondary-color); color: white; }
.success-btn { background-color: var(--success-color); color: white; }
.danger-btn { background-color: var(--danger-color); }

/* Loading and Message Styles */
.loading-message {
    text-align: center;
    color: var(--secondary-color);
    font-style: italic;
    padding: 2rem;
}
.message {
    padding: 0.8rem 1rem;
    border-radius: 6px;
    margin: 1rem 0;
    font-weight: 500;
    border: 1px solid transparent;
}
.message.info { background-color: #eaf5fd; color: #3498db; border-color: #aed6f1; }
.message.success { background-color: #eafaf1; color: #2ecc71; border-color: #a3e4d7; }
.message.error { background-color: #fdedec; color: #e74c3c; border-color: #f5b7b1; }
.message.warning { background-color: #fef9e7; color: #f39c12; border-color: #fad7a0; }

/* Footer */
.page-footer {
    text-align: center;
    margin-top: 2rem;
    padding: 1rem;
    color: var(--secondary-color);
    font-size: 0.9rem;
    border-top: 1px solid var(--border-color);
}

/* Add specific styles for student list/table later */

/* Table Styles */
.table-responsive {
    overflow-x: auto;
    margin-top: 1rem;
}

#student-subscriptions-table,
#payments-history-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

#student-subscriptions-table th,
#student-subscriptions-table td,
#payments-history-table th,
#payments-history-table td {
    padding: 0.8rem 1rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
    white-space: nowrap;
}

#student-subscriptions-table th,
#payments-history-table th {
    background-color: var(--light-color);
    font-weight: 600;
    color: var(--primary-dark);
    font-size: 0.9rem;
}

#student-subscriptions-table tbody tr:hover,
#payments-history-table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Status Badge Styles */
.status-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    color: white;
    text-align: center;
    display: inline-block;
    min-width: 80px;
}

.status-paid { background-color: var (--success-color); }
.status-partially_paid { background-color: var(--warning-color); color: var(--dark-color); }
.status-not_paid { background-color: var(--danger-color); }
.status-no_subscription { background-color: var(--secondary-color); }

/* Action Buttons in Table */
.action-btn {
    background: none;
    border: none;
    padding: 5px;
    font-size: 1rem;
    cursor: pointer;
    margin: 0 3px;
    color: var(--secondary-color);
    transition: color 0.2s ease;
}
.action-btn:hover { color: var(--primary-dark); }
.action-btn.add-payment-btn:hover { color: var(--success-color); }
.action-btn.view-payments-btn:hover { color: var(--info-color); }
.action-btn.delete-payment-btn:hover { color: var(--danger-color); }
.action-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.badge {
    background-color: var(--primary-color);
    color: white;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}
.modal.active { display: flex; }
.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}
.modal-content.large { max-width: 800px; }
.close-modal-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
}
.modal-body { padding: 1.5rem; overflow-y: auto; }
.modal-footer { padding: 1rem 1.5rem; border-top: 1px solid var(--border-color); display: flex; justify-content: center; gap: 1rem; background-color: var(--light-color); }
.modal hr { border: 0; border-top: 1px solid var(--border-color); margin: 1rem 0; }
.modal .form-group { margin-bottom: 1rem; }
.modal .form-group label { display: block; margin-bottom: 0.5rem; font-weight: 500; }
.modal .form-group input, .modal .form-group select, .modal .form-group textarea { width: 100%; padding: 0.7rem; border: 1px solid var(--border-color); border-radius: 6px; }

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1.5rem;
    gap: 8px;
}
/* ... other pagination styles if needed ... */

/* Responsive Design */
@media (max-width: 768px) and (min-width: 481px) {
    .navbar-brand span {
        font-size: 0.9rem;
    }

    .navbar-brand .highlight {
        display: none;
    }

    .content-wrapper {
        padding: 15px 10px;
    }

    .student-cards-container {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 12px;
        padding: 0 5px;
    }
}

@media (max-width: 480px) {
    .student-cards-container {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 0 10px;
    }
}

@media (max-width: 1200px) and (min-width: 769px) {
    .navbar-brand span {
        font-size: 1rem;
    }

    .student-cards-container {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }
}

/* Large screens optimization */
@media (min-width: 1200px) {
    .student-cards-container {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }

    .content-wrapper {
        max-width: 98%;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 5px;
    }

    .student-card {
        padding: 12px;
    }

    .student-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .student-card-header .student-name {
        font-size: 1.1rem;
    }

    .info-item {
        padding: 6px 10px;
    }

    .info-item .label {
        font-size: 0.8rem;
    }

    .info-item .value {
        font-size: 0.9rem;
    }

    .payment-calculation-box {
        font-size: 0.8rem;
        padding: 10px;
    }

    .calculation-row {
        margin: 3px 0;
        padding: 2px 0;
    }
}

.group-details-main {
    padding-top: 5px; /* Reduced padding */
    width: 100%;
    box-sizing: border-box;
}

/* Bulk Actions for Payment History */
.bulk-actions-container {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.bulk-actions-container span {
    font-weight: 600;
}

.bulk-actions-container .btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.bulk-actions-container .btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

/* Payment History Table Checkbox Styling */
.payment-history-table input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.payment-history-table th:first-child,
.payment-history-table td:first-child {
    width: 40px;
    text-align: center;
}

/* Edit Payment Calculation Box */
.payment-calculation-box {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.calculation-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 12px 15px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    transition: background 0.3s ease;
}

.calculation-header:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
}

.calculation-header i {
    transition: transform 0.3s ease;
}

.calculation-details {
    padding: 15px;
    background: white;
    border-top: 1px solid rgba(52, 152, 219, 0.2);
}

.payment-calculation-box.collapsed .calculation-details {
    display: none;
}

.calculation-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f2f6;
    font-size: 0.95rem;
}

.calculation-row:last-child {
    border-bottom: none;
}

.calculation-row.total {
    font-weight: 600;
    font-size: 1.05rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    margin: 8px -15px 0;
    padding: 12px 15px;
    border-top: 2px solid #3498db;
    border-bottom: none;
}

.calculation-row .amount {
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;
    background: #f8f9fa;
}

/* تكلفة الشهر - أسود */
.calculation-row .amount.month-cost {
    color: #2c3e50;
    background: #ecf0f1;
}

/* الرصيد الحالي - أحمر للموجب، أخضر للسالب */
.calculation-row .amount.balance-positive {
    color: #e74c3c;
    background: #fdeaea;
}

.calculation-row .amount.balance-negative {
    color: #27ae60;
    background: #d5f4e6;
}

.calculation-row .amount.balance-zero {
    color: #7f8c8d;
    background: #f8f9fa;
}

/* الدفعات المسجلة - أزرق */
.calculation-row .amount.payments {
    color: #3498db;
    background: #ebf3fd;
}

/* المتبقي النهائي - أسود */
.calculation-row .amount.remaining {
    color: #2c3e50;
    background: #ecf0f1;
}

/* المطلوب دفعه - أسود */
.calculation-row .amount.required {
    color: #2c3e50;
    background: #ecf0f1;
}

/* المبلغ المدخل - أزرق */
.calculation-row .amount.entered {
    color: #3498db;
    background: #ebf3fd;
}

/* قسم الدفعات المفصل */
#edit-calc-payments-section {
    border-left: 3px solid #3498db;
    padding-left: 10px;
    margin: 10px 0;
    background: rgba(52, 152, 219, 0.05);
    border-radius: 4px;
    padding: 10px;
}

#edit-calc-payments-section .calculation-row {
    font-size: 0.9rem;
    padding: 6px 0;
}

#edit-calc-payments-section .calculation-row:last-child {
    font-weight: 600;
    border-top: 1px solid #3498db;
    margin-top: 8px;
    padding-top: 8px;
}

.page-header .month-year-info {
    font-size: 1rem;
    color: var(--text-muted);
    margin-right: 10px; /* Adjust spacing */
}

.back-btn {
    display: inline-block;
    margin-top: 10px;
    padding: 8px 15px;
    font-size: 0.9rem;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    color: white;
}

/* Edit month cost button */
#edit-month-cost-btn {
    display: inline-block;
    margin-top: 10px;
    margin-right: 10px;
    padding: 8px 15px;
    font-size: 0.9rem;
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--primary-color);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

#edit-month-cost-btn:hover {
    background-color: white;
    transform: translateY(-1px);
}

/* Removed duplicate .student-cards-container - using the one above */

/* --- Student Card Relative Positioning --- */
.student-card {
    position: relative; /* Needed for absolute positioning of the corner button */
    /* ... other existing styles for student-card ... */
    padding-top: 35px; /* Add padding to prevent overlap with the corner button */
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px; /* Space between elements */
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform: translateY(0) scale(1);
    animation: cardFadeIn 0.6s ease-out;
}

/* Card fade in animation */
@keyframes cardFadeIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Hover effects for unlocked cards */
.student-card:not(.locked):hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 15px 35px rgba(52, 152, 219, 0.15),
        0 5px 15px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

/* Active/click effect */
.student-card:not(.locked):active {
    transform: translateY(-4px) scale(1.01);
    transition: all 0.1s ease;
}

/* Floating animation for unlocked cards */
.student-card:not(.locked) {
    animation: cardFadeIn 0.6s ease-out, cardFloat 6s ease-in-out infinite;
}

@keyframes cardFloat {
    0%, 100% {
        transform: translateY(0) scale(1);
    }
    50% {
        transform: translateY(-2px) scale(1);
    }
}

/* Staggered animation delay for multiple cards */
.student-card:nth-child(1) { animation-delay: 0s; }
.student-card:nth-child(2) { animation-delay: 0.1s; }
.student-card:nth-child(3) { animation-delay: 0.2s; }
.student-card:nth-child(4) { animation-delay: 0.3s; }
.student-card:nth-child(5) { animation-delay: 0.4s; }
.student-card:nth-child(6) { animation-delay: 0.5s; }
.student-card:nth-child(n+7) { animation-delay: 0.6s; }

.student-card-header {
    display: flex;
    align-items: center;
    gap: 15px;
    padding-bottom: 10px;
    border-bottom: 1px dashed var(--border-color-light);
    animation: slideInFromTop 0.8s ease-out;
}

/* Header animation */
@keyframes slideInFromTop {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.student-card-header i {
    font-size: 2.5rem;
    color: var(--primary-color);
    animation: iconBounce 1s ease-out;
    transition: all 0.3s ease;
}

/* Icon bounce animation */
@keyframes iconBounce {
    0% {
        opacity: 0;
        transform: scale(0.3) rotate(-10deg);
    }
    50% {
        transform: scale(1.1) rotate(5deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

/* Icon hover effect for unlocked cards */
.student-card:not(.locked) .student-card-header i:hover {
    transform: scale(1.1) rotate(5deg);
    color: var(--primary-dark);
}

.student-card-header .student-name {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--secondary-color);
}

.student-card-info {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* Two columns for info */
    gap: 10px 15px; /* Row and column gap */
    font-size: 0.95rem;
    animation: slideInFromLeft 1s ease-out;
}

/* Info section animation */
@keyframes slideInFromLeft {
    0% {
        opacity: 0;
        transform: translateX(-30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

.info-item {
    background-color: #f8f9fa;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid var(--border-color-light);
    transition: all 0.3s ease;
    animation: fadeInUp 0.8s ease-out;
}

/* Info item animation */
@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Staggered animation for info items */
.info-item:nth-child(1) { animation-delay: 0.1s; }
.info-item:nth-child(2) { animation-delay: 0.2s; }
.info-item:nth-child(3) { animation-delay: 0.3s; }
.info-item:nth-child(4) { animation-delay: 0.4s; }
.info-item:nth-child(5) { animation-delay: 0.5s; }
.info-item:nth-child(6) { animation-delay: 0.6s; }
.info-item:nth-child(7) { animation-delay: 0.7s; }
.info-item:nth-child(8) { animation-delay: 0.8s; }

/* Hover effect for info items in unlocked cards */
.student-card:not(.locked) .info-item:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.info-item .label {
    display: block;
    font-weight: 500;
    color: var(--text-muted);
    margin-bottom: 3px;
    font-size: 0.85rem;
}

.info-item .value {
    font-weight: 600;
    color: var (--secondary-color);
}

.info-item .value.amount-due { color: var(--primary-color); }
.info-item .value.amount-paid { color: var(--primary-color); } /* Blue for negative balance */
.info-item .value.amount-remaining { color: var(--danger-color); } /* Red for positive balance */
.info-item .value.amount-neutral { color: var(--text-muted); } /* Gray for zero balance */

/* --- Remove Student Corner Button --- */
.remove-student-corner-btn {
    position: absolute;
    top: 8px;
    left: 8px; /* Adjusted for RTL, use 'right: 8px;' for LTR */
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: var(--danger-color);
    color: white;
    border: none;
    font-size: 1rem;
    line-height: 1; /* Center icon vertically */
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease, transform 0.2s ease;
    z-index: 5; /* Ensure it's above other elements */
    padding: 0; /* Remove default button padding */
}

.remove-student-corner-btn:hover {
    background-color: #c82333; /* Darker danger color */
    transform: scale(1.1);
}

.remove-student-corner-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    opacity: 0.7;
}



/* --- Payment Form Button Adjustments --- */
.payment-form {
    display: grid;
    grid-template-columns: 1fr 1fr; /* Two columns for better organization */
    gap: 6px; /* Reduced gap for better fit with more buttons */
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
    animation: slideInFromBottom 1.2s ease-out;
}

/* Payment form animation */
@keyframes slideInFromBottom {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.payment-form .btn {
    padding: 8px 12px; /* Reduced padding */
    font-size: 0.85rem; /* Smaller font size */
    min-width: auto; /* Remove minimum width constraint */
    white-space: nowrap; /* Prevent text wrapping */
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    animation: buttonFadeIn 1s ease-out;
    position: relative;
    overflow: hidden;
}

/* Button fade in animation */
@keyframes buttonFadeIn {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Staggered animation for buttons */
.payment-form .btn:nth-child(1) { animation-delay: 0.8s; }
.payment-form .btn:nth-child(2) { animation-delay: 0.9s; }
.payment-form .btn:nth-child(3) { animation-delay: 1.0s; }
.payment-form .btn:nth-child(4) { animation-delay: 1.1s; }

/* Enhanced hover effects for unlocked card buttons */
.student-card:not(.locked) .payment-form .btn:not(:disabled):hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Ripple effect for button clicks */
.student-card:not(.locked) .payment-form .btn:not(:disabled):active {
    transform: translateY(-1px) scale(1.02);
    transition: all 0.1s ease;
}

/* Button ripple animation */
.payment-form .btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.student-card:not(.locked) .payment-form .btn:not(:disabled):active::before {
    width: 100%;
    height: 100%;
}

/* Special styling for specific buttons */
.payment-form .btn.full-width {
    grid-column: 1 / -1; /* Span full width when needed */
}

/* Checkbox selection styling */
.student-card-checkbox {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    z-index: 10;
}

.student-card.selected {
    border: 2px solid var(--primary-color);
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
}

/* Bulk actions container */
.bulk-actions-container {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    display: none;
    align-items: center;
    gap: 15px;
    z-index: 1000;
}

.bulk-actions-container.show {
    display: flex;
}

.bulk-actions-container .selected-count {
    font-weight: bold;
    margin-left: 10px;
}

.bulk-actions-container .btn {
    background: white;
    color: var(--primary-color);
    border: none;
    padding: 8px 15px;
    border-radius: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.bulk-actions-container .btn:hover {
    background: #f8f9fa;
    transform: scale(1.05);
}

/* Specific colors for pay button based on class */
.pay-action-btn.btn-success {
    background-color: var(--success-color);
    color: white;
}
.pay-action-btn.btn-success:hover:not(:disabled) {
    background-color: #218838; /* Darker success */
}

.pay-action-btn.btn-warning {
    background-color: var(--warning-color);
    color: #212529; /* Dark text for yellow */
}
.pay-action-btn.btn-warning:hover:not(:disabled) {
    background-color: #e0a800; /* Darker warning */
}

.pay-action-btn.btn-secondary {
    background-color: var(--secondary-color); /* Or use a specific grey like #6c757d */
    color: white;
}
.pay-action-btn.btn-secondary:hover:not(:disabled) {
    background-color: #5a6268; /* Darker secondary */
}

/* Edit Cost Button Styling */
.payment-form .edit-cost-btn {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.payment-form .edit-cost-btn:hover:not(:disabled) {
    background-color: #5a6268;
    transform: translateY(-1px);
}

/* Ensure disabled buttons look consistent */
.payment-form .btn:disabled {
    background-color: #e9ecef; /* Light grey for disabled */
    border-color: #ced4da;
    color: #6c757d;
    cursor: not-allowed;
    opacity: 0.65;
}

/* --- Lock Indicator --- */
.lock-indicator {
    background-color: var(--secondary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8em;
    margin-right: 10px; /* Adjust spacing */
}
.lock-indicator i {
    margin-left: 4px;
}

/* --- Locked Student Card Styles --- */
.student-card.locked {
    position: relative;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    border: 2px solid #e74c3c;
    transform: perspective(1000px) rotateY(5deg) rotateX(2deg);
    box-shadow:
        0 10px 30px rgba(231, 76, 60, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    overflow: visible;
}

.student-card.locked::before {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #e74c3c, #c0392b, #e74c3c);
    border-radius: inherit;
    z-index: -1;
    animation: lockGlow 2s ease-in-out infinite alternate;
}

@keyframes lockGlow {
    0% { opacity: 0.7; }
    100% { opacity: 1; }
}

.student-card.locked:hover {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg) translateY(-5px);
    box-shadow:
        0 15px 40px rgba(231, 76, 60, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.student-card.locked .student-card-header {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.9), rgba(192, 57, 43, 0.9));
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px 8px 0 0;
    margin: -20px -20px 15px -20px;
    padding: 15px 20px;
}

.student-card.locked .student-card-header i {
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.student-card.locked .student-name {
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    font-weight: 700;
}

.student-card.locked .lock-indicator {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Hide all info items in locked cards */
.student-card.locked .student-card-info {
    display: none;
}

/* Special styling for locked amount display */
.student-card.locked .locked-amount-display {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 30px 20px;
    border-radius: 12px;
    margin: 30px auto;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow:
        inset 0 2px 0 rgba(255, 255, 255, 0.2),
        0 8px 25px rgba(0, 0, 0, 0.3);
    text-align: center;
    font-weight: 600;
    width: 80%;
    max-width: 300px;
    position: relative;
    animation: lockedAmountPulse 2s ease-in-out infinite;
}

/* Locked amount pulse animation */
@keyframes lockedAmountPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow:
            inset 0 2px 0 rgba(255, 255, 255, 0.2),
            0 8px 25px rgba(0, 0, 0, 0.3);
    }
    50% {
        transform: scale(1.02);
        box-shadow:
            inset 0 2px 0 rgba(255, 255, 255, 0.3),
            0 12px 35px rgba(231, 76, 60, 0.4);
    }
}

.student-card.locked .locked-amount-display .locked-label {
    font-size: 1rem;
    opacity: 0.95;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.student-card.locked .locked-amount-display .locked-value {
    font-size: 2rem;
    font-weight: 900;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    margin-top: 5px;
    display: block;
}

/* Show only specific buttons in locked cards */
.student-card.locked .payment-form {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    padding: 15px;
    display: grid;
    grid-template-columns: 1fr 1fr; /* Two columns for unlock and print buttons */
    gap: 10px;
    opacity: 1;
    pointer-events: auto;
}

/* Hide pay and history buttons in locked cards */
.student-card.locked .pay-action-btn,
.student-card.locked .payment-history-btn {
    display: none;
}

/* Style unlock and print buttons in locked cards */
.student-card.locked .unlock-month-btn,
.student-card.locked .print-invoice-btn {
    background: rgba(255, 255, 255, 0.9);
    color: var(--secondary-color);
    border: 1px solid rgba(255, 255, 255, 0.5);
    font-weight: 600;
    transition: all 0.3s ease;
}

.student-card.locked .unlock-month-btn:hover,
.student-card.locked .print-invoice-btn:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.student-card.locked .btn:disabled {
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.3);
    cursor: not-allowed;
}



.modal-body .form-group.form-group-full {
    grid-column: 1 / -1; /* Make element span both columns */
}

.modal-warning {
    background-color: var(--warning-light-bg, #fff3cd); /* Use CSS variable or fallback */
    color: var(--warning-dark-text, #856404); /* Use CSS variable or fallback */
    border: 1px solid var(--warning-border, #ffeeba); /* Use CSS variable or fallback */
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.payment-form {
    margin-top: 10px;
    padding-top: 15px;
    border-top: 1px dashed var(--border-color-light);
    display: grid;
    grid-template-columns: 1fr 1fr auto; /* Amount, Date, Button */
    gap: 10px;
    align-items: flex-end; /* Align items to bottom */
}

.payment-form .form-group {
    display: flex;
    flex-direction: column;
}

.payment-form label {
    font-size: 0.85rem;
    margin-bottom: 4px;
    color: var(--text-muted);
}

.payment-form input[type="number"],
.payment-form input[type="date"] {
    padding: 8px 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.9rem;
}

.payment-form .save-payment-btn {
    padding: 8px 15px;
    white-space: nowrap; /* Prevent button text wrapping */
}

.payment-message {
    font-size: 0.85rem;
    margin-top: 5px;
    min-height: 1.2em; /* Reserve space for message */
}
.payment-message.success { color: var(--success-color); }
.payment-message.error { color: var(--danger-color); }
.payment-message.info { color: var(--info-color); }

.loading-placeholder {
    grid-column: 1 / -1; /* Span full width */
    text-align: center;
    padding: 40px 20px;
    font-size: 1.2rem;
    color: var(--text-muted);
}

.loading-placeholder i {
    margin-left: 10px;
    font-size: 1.5rem;
}

/* Payment Modal Styles */
.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1050; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto; /* Enable scroll if needed */
    background-color: rgba(0, 0, 0, 0.6); /* Darker overlay */
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    align-items: center; /* Vertical center */
    justify-content: center; /* Horizontal center */
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: #fff;
    margin: auto;
    padding: 25px 30px;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.25);
    width: 90%;
    max-width: 550px; /* Adjust width as needed */
    position: relative;
    animation: fadeInModal 0.3s ease-out;
}

@keyframes fadeInModal {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.modal-header .close-modal-btn {
    background: none;
    border: none;
    font-size: 2rem;
    line-height: 1;
    color: #aaa;
    cursor: pointer;
    padding: 0 5px;
}
.modal-header .close-modal-btn:hover {
    color: #333;
}

.modal-body .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr; /* Two columns */
    gap: 15px;
    margin-bottom: 15px;
}

.modal-body .form-group {
    display: flex;
    flex-direction: column;
}

.modal-body .form-group label {
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.modal-body .form-group input,
.modal-body .form-group select {
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
    width: 100%; /* Ensure full width within grid cell */
    box-sizing: border-box; /* Include padding and border in element's total width and height */
}
.modal-body .form-group input:disabled,
.modal-body .form-group select:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
}


.modal-footer {
    display: flex;
    justify-content: flex-end; /* Align button to the end (left in RTL) */
    padding-top: 20px;
    margin-top: 20px;
    border-top: 1px solid var(--border-color);
}

/* Style for the new "Pay" button in the card */
.pay-action-btn {
    margin-top: 15px; /* Add some space above the button */
    padding: 10px 20px;
    width: 100%; /* Make button full width of its container */
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.pay-action-btn:hover {
    background-color: #0056b3; /* Darker accent */
}

.pay-action-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.pay-action-btn i {
    margin-left: 5px;
}

/* Hide original payment form elements if needed, or remove them */
.student-card .payment-form {
    /* display: none; */ /* Or remove the form generation */
    border-top: 1px dashed var(--border-color);
    margin-top: 15px;
    padding-top: 15px;
}

/* Edit Payment Modal */
#edit-payment-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

#edit-payment-modal.active {
    display: flex;
}

#edit-payment-modal .modal-content {
    background: white;
    padding: 20px;
    border-radius: 8px;
    width: 400px;
    max-width: 90%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#edit-payment-modal .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

#edit-payment-modal .modal-header h2 {
    margin: 0;
}

#edit-payment-modal .modal-body {
    margin-bottom: 20px;
}

#edit-payment-modal .modal-footer {
    text-align: right;
}

#edit-payment-modal .close-modal-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Edit Month Cost Modal (Basic styles, reuse existing modal styles where possible) */

#edit-month-cost-modal .modal-content {
    /* Uses general .modal-content styles, adjust max-width if needed */
    max-width: 500px;
}

/* Info Notice Styling */
.info-notice {
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
    color: #856404;
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.95em;
}

.info-notice i {
    color: #856404;
    font-size: 1.1em;
}

/* ... Add more specific styles for #edit-month-cost-modal if needed ... */

/* Payment History Modal Styles */
.payment-history-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1050;
    justify-content: center;
    align-items: center;
}

.payment-history-modal.show {
    display: flex;
}

.payment-history-modal .modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.payment-history-modal .modal-header {
    background: var(--primary-color);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.payment-history-modal .modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.payment-history-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.payment-history-table th,
.payment-history-table td {
    padding: 10px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.payment-history-table th {
    background: var(--light-color);
    font-weight: 600;
    color: var(--primary-dark);
}

.payment-history-table tbody tr:hover {
    background: rgba(52, 152, 219, 0.05);
}

.payment-history-table .action-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.payment-history-table .action-buttons .btn {
    padding: 4px 8px;
    font-size: 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.payment-history-table .edit-btn {
    background: var(--warning-color);
    color: white;
}

.payment-history-table .delete-btn {
    background: var(--danger-color);
    color: white;
}

/* Balance Info Modal Styles */
.balance-info-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1050;
    justify-content: center;
    align-items: center;
}

.balance-info-modal.show {
    display: flex;
}

.balance-info-modal .modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 70vh;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.balance-breakdown-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.balance-breakdown-table th,
.balance-breakdown-table td {
    padding: 8px 12px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.balance-breakdown-table th {
    background: var(--light-color);
    font-weight: 600;
}

.balance-breakdown-table .positive-amount {
    color: var(--success-color);
    font-weight: 600;
}

.balance-breakdown-table .negative-amount {
    color: var(--danger-color);
    font-weight: 600;
}

.balance-breakdown-table .neutral-amount {
    color: var(--secondary-color);
    font-weight: 600;
}

.balance-breakdown-table .total-row {
    background: var(--light-color);
    font-weight: bold;
    border-top: 2px solid var(--primary-color);
}

/* Balance info button */
.balance-info-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 1rem;
    cursor: pointer;
    margin-right: 5px;
    padding: 2px;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.balance-info-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

/* Enhanced Payment Modal with Calculations */
.payment-calculation-box {
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    font-size: 0.9rem;
    max-height: 400px; /* Set maximum height */
    overflow-y: auto; /* Enable vertical scrolling */
    overflow-x: hidden; /* Hide horizontal scrolling */
}

.payment-calculation-box.collapsed {
    max-height: 40px;
    overflow: hidden;
    cursor: pointer;
    transition: max-height 0.3s ease;
}

.payment-calculation-box.expanded {
    max-height: 400px; /* Increased max height */
    overflow-y: auto; /* Enable scrolling when expanded */
    transition: max-height 0.3s ease;
}

.calculation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: var(--primary-color);
    cursor: pointer;
}

.calculation-details {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px dashed var(--border-color);
    /* Ensure smooth scrolling within details */
    scroll-behavior: smooth;
}

.calculation-row {
    display: flex;
    justify-content: space-between;
    margin: 5px 0;
    padding: 3px 0;
}

.calculation-row.total {
    border-top: 1px solid var(--border-color);
    margin-top: 10px;
    padding-top: 8px;
    font-weight: bold;
    color: var(--primary-dark);
}

.calculation-row .amount {
    font-weight: 600;
}

.calculation-row .amount.positive {
    color: var(--success-color);
}

.calculation-row .amount.negative {
    color: var(--danger-color);
}

.calculation-row .amount.neutral {
    color: var(--secondary-color);
}

/* Custom scrollbar for payment calculation box */
.payment-calculation-box::-webkit-scrollbar {
    width: 8px;
}

.payment-calculation-box::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.payment-calculation-box::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.payment-calculation-box::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* For Firefox */
.payment-calculation-box {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) #f1f1f1;
}

/* Sidebar Toggle Button */
.sidebar-toggle-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    margin-left: 10px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.sidebar-toggle-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 10px;
}
