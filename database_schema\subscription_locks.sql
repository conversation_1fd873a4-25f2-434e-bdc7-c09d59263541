-- =============================================
-- جدول إقفال الاشتراكات الشهرية للطلاب
-- =============================================
-- يخزن هذا الجدول حالة إقفال حساب الطالب لشهر مالي محدد،
-- ويسجل المبلغ المتبقي الذي تم ترحيله إلى الرصيد العام.

CREATE TABLE public.subscription_locks (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY, -- المعرف الفريد لسجل الإقفال
    student_id uuid NOT NULL,                               -- معرف الطالب (مفتاح أجنبي لجدول students)
    budget_month_id uuid NOT NULL,                          -- معرف الشهر المالي المقفل (مفتاح أجنبي لجدول budget_months)
    locked_remaining_amount numeric(10, 2) NOT NULL,        -- المبلغ المتبقي عند الإقفال (قد يكون سالبًا إذا كان هناك دفع زائد)
    locked_at timestamp with time zone DEFAULT now() NOT NULL, -- تاريخ ووقت الإقفال

    -- الربط بجدول الطلاب
    CONSTRAINT subscription_locks_student_id_fkey FOREIGN KEY (student_id)
        REFERENCES public.students (id) ON UPDATE CASCADE ON DELETE CASCADE,

    -- الربط بجدول الشهور المالية
    CONSTRAINT subscription_locks_budget_month_id_fkey FOREIGN KEY (budget_month_id)
        REFERENCES public.budget_months (id) ON UPDATE CASCADE ON DELETE CASCADE,

    -- ضمان عدم إقفال نفس الشهر لنفس الطالب أكثر من مرة
    CONSTRAINT unique_student_month_lock UNIQUE (student_id, budget_month_id)
);

-- إضافة تعليقات توضيحية
COMMENT ON TABLE public.subscription_locks IS 'يسجل حالة إقفال الاشتراكات الشهرية للطلاب والمبلغ المتبقي المرحل.';
COMMENT ON COLUMN public.subscription_locks.student_id IS 'معرف الطالب الذي تم إقفال شهره.';
COMMENT ON COLUMN public.subscription_locks.budget_month_id IS 'معرف الشهر المالي الذي تم إقفاله.';
COMMENT ON COLUMN public.subscription_locks.locked_remaining_amount IS 'المبلغ المتبقي (الافتراضي - المدفوع) في لحظة الإقفال.';
COMMENT ON COLUMN public.subscription_locks.locked_at IS 'الوقت الذي تمت فيه عملية الإقفال.';

-- =============================================
-- تذكير هام بخصوص صلاحيات الوصول (RLS)
-- =============================================
-- تأكد من تفعيل سياسات الأمان على مستوى الصف (RLS) لهذا الجدول في Supabase.
-- يجب أن تسمح السياسة للمستخدمين المصادق عليهم (authenticated) بتنفيذ عملية INSERT و SELECT.

-- مثال لسياسة INSERT بسيطة:
/*
CREATE POLICY "Allow authenticated users to insert locks"
ON public.subscription_locks
FOR INSERT
TO authenticated
WITH CHECK (true);
*/

-- مثال لسياسة SELECT بسيطة:
/*
CREATE POLICY "Allow authenticated users to select locks"
ON public.subscription_locks
FOR SELECT
TO authenticated
USING (true);
*/
