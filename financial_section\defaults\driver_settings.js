// --- Auth Check ---
checkAuth('../../login.html');

// --- Supabase Initialization ---
let _supabase;
try {
    if (typeof supabase !== 'undefined' && supabase.createClient && typeof SUPABASE_URL !== 'undefined' && typeof SUPABASE_ANON_KEY !== 'undefined') {
        _supabase = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('Supabase client initialized for driver settings.');
    } else {
        throw new Error('Supabase client or config variables not available.');
    }
} catch (error) {
    console.error('Error initializing Supabase client:', error);
    alert('خطأ فادح: لا يمكن الاتصال بقاعدة البيانات.');
}

// --- Global Variables ---
let allDrivers = [];
let filteredDrivers = [];
let currentPage = 1;
const itemsPerPage = 10;

// --- DOM Elements ---
const dashboardMessage = document.getElementById('dashboard-message');
const listMessage = document.getElementById('list-message');
const searchInput = document.getElementById('search-input');
const shiftFilter = document.getElementById('shift-filter');
const balanceFilter = document.getElementById('balance-filter');
const defaultsTable = document.getElementById('defaults-table');
const defaultsTbody = document.getElementById('defaults-tbody');
const driversCount = document.getElementById('drivers-count');
const paginationInfo = document.getElementById('pagination-info');
const paginationControls = document.getElementById('pagination-controls');
const refreshBtn = document.getElementById('refresh-btn');

// Statistics elements
const totalDrivers = document.getElementById('total-drivers');
const configuredDrivers = document.getElementById('configured-drivers');
const totalBalance = document.getElementById('total-balance');

// Modal elements
const editDriverModal = document.getElementById('edit-driver-modal');
const editDriverForm = document.getElementById('edit-driver-form');
const editDriverId = document.getElementById('edit-driver-id');
const editDriverName = document.getElementById('edit-driver-name');
const editWorkShift = document.getElementById('edit-work-shift');
const editDefaultSalary = document.getElementById('edit-default-salary');
const editOpeningBalance = document.getElementById('edit-opening-balance');
const editCurrentBalance = document.getElementById('edit-current-balance');
const editModalMessageArea = document.getElementById('edit-modal-message-area');
const closeEditModalBtn = document.getElementById('close-edit-modal-btn');
const cancelEditBtn = document.getElementById('cancel-edit-btn');

// Balance info modal elements
const balanceInfoModal = document.getElementById('balance-info-modal');
const balanceInfoDriverName = document.getElementById('balance-info-driver-name');
const balanceBreakdownTbody = document.getElementById('balance-breakdown-tbody');
const closeBalanceInfoModal = document.getElementById('close-balance-info-modal');

// --- Helper Functions ---
const showMessage = (element, message, type = 'info', duration = 4000) => {
    if (!element) return;
    element.textContent = message;
    element.className = `message ${type} show`;
    element.style.display = 'block';

    // Clear previous timeouts if any
    if (element.timeoutId) {
        clearTimeout(element.timeoutId);
    }

    if (duration > 0) {
        element.timeoutId = setTimeout(() => {
            if (element.textContent === message) {
                element.classList.remove('show');
                setTimeout(() => { element.style.display = 'none'; }, 300);
            }
            element.timeoutId = null;
        }, duration);
    } else {
        element.timeoutId = null;
    }
};

const showModalMessage = (modalMessageArea, message, type = 'info', duration = 4000) => {
    if (!modalMessageArea) return;
    modalMessageArea.textContent = message;
    modalMessageArea.className = `message ${type} show`;
    modalMessageArea.style.display = 'block';

    if (modalMessageArea.timeoutId) {
        clearTimeout(modalMessageArea.timeoutId);
    }

    if (duration > 0) {
        modalMessageArea.timeoutId = setTimeout(() => {
            if (modalMessageArea.textContent === message) {
                modalMessageArea.classList.remove('show');
                setTimeout(() => { modalMessageArea.style.display = 'none'; }, 300);
            }
            modalMessageArea.timeoutId = null;
        }, duration);
    } else {
        modalMessageArea.timeoutId = null;
    }
};

const formatCurrency = (amount) => {
    const num = parseFloat(amount);
    if (isNaN(num)) return '';
    return num.toFixed(2);
};

const formatCurrencyWithSymbol = (amount) => {
    const num = parseFloat(amount);
    if (isNaN(num)) return 'غير محدد';
    return num.toFixed(2) + ' ريال';
};

const closeModal = (modal) => {
    if (modal) {
        modal.style.display = 'none';
    }
};

const openModal = (modal) => {
    if (modal) {
        modal.style.display = 'block';
    }
};

// --- Data Fetching ---
const fetchDriverDefaults = async () => {
    if (!_supabase) {
        showMessage(dashboardMessage, 'خطأ في الاتصال بقاعدة البيانات.', 'error', 0);
        return;
    }

    showMessage(dashboardMessage, 'جاري تحميل البيانات...', 'info', 0);

    if (defaultsTbody) {
        defaultsTbody.innerHTML = '<tr><td colspan="7" class="loading-message"><i class="fas fa-spinner fa-spin"></i> جاري تحميل بيانات السائقين...</td></tr>';
    }

    try {
        // Fetch all drivers with their default settings
        const { data: driversData, error: driversError } = await _supabase
            .from('drivers')
            .select(`
                id,
                name,
                phone,
                work_shift,
                driver_default_settings (
                    default_base_salary,
                    opening_balance,
                    general_balance
                )
            `)
            .order('created_at', { ascending: false });

        if (driversError) throw driversError;

        allDrivers = driversData || [];
        updateStatistics();
        applyFilters();

        showMessage(dashboardMessage, 'تم تحميل البيانات بنجاح.', 'success', 3000);

    } catch (error) {
        console.error('Error fetching driver defaults:', error);
        showMessage(dashboardMessage, `خطأ في جلب بيانات السائقين: ${error.message}`, 'error', 0);
        if (defaultsTbody) {
            defaultsTbody.innerHTML = '<tr><td colspan="7" class="loading-message">فشل تحميل بيانات السائقين.</td></tr>';
        }
    }
};

const updateStatistics = () => {
    const total = allDrivers.length;
    const configured = allDrivers.filter(driver =>
        driver.driver_default_settings && driver.driver_default_settings.length > 0
    ).length;

    let totalBalanceAmount = 0;
    allDrivers.forEach(driver => {
        if (driver.driver_default_settings && driver.driver_default_settings.length > 0) {
            const balance = parseFloat(driver.driver_default_settings[0].general_balance) || 0;
            totalBalanceAmount += balance;
        }
    });

    if (totalDrivers) totalDrivers.textContent = total;
    if (configuredDrivers) configuredDrivers.textContent = configured;
    if (totalBalance) totalBalance.textContent = formatCurrency(totalBalanceAmount);
};

const applyFilters = () => {
    let filtered = [...allDrivers];

    // Search filter
    const searchTerm = searchInput ? searchInput.value.toLowerCase().trim() : '';
    if (searchTerm) {
        filtered = filtered.filter(driver =>
            driver.name.toLowerCase().includes(searchTerm) ||
            (driver.phone && driver.phone.includes(searchTerm))
        );
    }

    // Shift filter
    const shiftValue = shiftFilter ? shiftFilter.value : '';
    if (shiftValue) {
        filtered = filtered.filter(driver => driver.work_shift === shiftValue);
    }

    // Balance filter
    const balanceValue = balanceFilter ? balanceFilter.value : '';
    if (balanceValue) {
        filtered = filtered.filter(driver => {
            if (!driver.driver_default_settings || driver.driver_default_settings.length === 0) {
                return balanceValue === 'zero';
            }
            const balance = parseFloat(driver.driver_default_settings[0].general_balance) || 0;
            switch (balanceValue) {
                case 'positive': return balance > 0;
                case 'negative': return balance < 0;
                case 'zero': return balance === 0;
                default: return true;
            }
        });
    }

    filteredDrivers = filtered;
    currentPage = 1;
    renderTable();
    renderPagination();
};

// --- Rendering ---
const renderTable = () => {
    if (!defaultsTbody) return;

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageDrivers = filteredDrivers.slice(startIndex, endIndex);

    if (pageDrivers.length === 0) {
        defaultsTbody.innerHTML = '<tr><td colspan="7" class="loading-message">لا يوجد سائقون مطابقون للبحث.</td></tr>';
        if (driversCount) driversCount.textContent = '0 سائق';
        return;
    }

    if (driversCount) driversCount.textContent = `${filteredDrivers.length} سائق`;

    defaultsTbody.innerHTML = '';
    pageDrivers.forEach(driver => {
        const settings = driver.driver_default_settings && driver.driver_default_settings.length > 0
            ? driver.driver_default_settings[0]
            : null;

        const row = document.createElement('tr');

        const balance = settings ? parseFloat(settings.general_balance) || 0 : 0;
        const balanceClass = balance > 0 ? 'balance-positive' : balance < 0 ? 'balance-negative' : 'balance-zero';

        row.innerHTML = `
            <td>${driver.name || 'غير محدد'}</td>
            <td>${driver.work_shift || 'غير محدد'}</td>
            <td>${settings ? formatCurrencyWithSymbol(settings.default_base_salary) : 'غير محدد'}</td>
            <td>${settings ? formatCurrencyWithSymbol(settings.opening_balance) : 'غير محدد'}</td>
            <td>
                <span class="balance-display ${balanceClass} clickable" onclick="showBalanceInfo(${driver.id}, '${driver.name}')">
                    ${formatCurrencyWithSymbol(balance)}
                </span>
            </td>
            <td>
                <span class="badge ${settings ? 'badge-success' : 'badge-warning'}">
                    ${settings ? 'مُعد' : 'غير مُعد'}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    ${settings ?
                        `<button class="btn-icon btn-edit" onclick="editDriver(${driver.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>` :
                        `<button class="btn-icon btn-add-defaults" onclick="addDriverDefaults(${driver.id})" title="إضافة إعدادات">
                            <i class="fas fa-plus"></i>
                        </button>`
                    }
                </div>
            </td>
        `;

        defaultsTbody.appendChild(row);
    });
};

const renderPagination = () => {
    if (!paginationControls || !paginationInfo) return;

    const totalPages = Math.ceil(filteredDrivers.length / itemsPerPage);
    const startItem = (currentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(currentPage * itemsPerPage, filteredDrivers.length);

    paginationInfo.textContent = `عرض ${startItem} إلى ${endItem} من ${filteredDrivers.length} سائق`;

    paginationControls.innerHTML = '';

    if (totalPages <= 1) return;

    // Previous button
    const prevButton = document.createElement('button');
    prevButton.innerHTML = '<i class="fas fa-angle-right"></i>';
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', () => goToPage(currentPage - 1));
    paginationControls.appendChild(prevButton);

    // Page buttons
    const maxPageButtons = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
    let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);

    if (endPage === totalPages && endPage - startPage + 1 < maxPageButtons) {
        startPage = Math.max(1, endPage - maxPageButtons + 1);
    }

    if (startPage > 1) {
        const firstPageBtn = document.createElement('button');
        firstPageBtn.textContent = '1';
        firstPageBtn.addEventListener('click', () => goToPage(1));
        paginationControls.appendChild(firstPageBtn);
        if (startPage > 2) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            paginationControls.appendChild(ellipsis);
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.textContent = i.toString();
        pageBtn.className = i === currentPage ? 'active' : '';
        pageBtn.addEventListener('click', () => goToPage(i));
        paginationControls.appendChild(pageBtn);
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            paginationControls.appendChild(ellipsis);
        }
        const lastPageBtn = document.createElement('button');
        lastPageBtn.textContent = totalPages.toString();
        lastPageBtn.addEventListener('click', () => goToPage(totalPages));
        paginationControls.appendChild(lastPageBtn);
    }

    // Next button
    const nextButton = document.createElement('button');
    nextButton.innerHTML = '<i class="fas fa-angle-left"></i>';
    nextButton.disabled = currentPage === totalPages;
    nextButton.addEventListener('click', () => goToPage(currentPage + 1));
    paginationControls.appendChild(nextButton);
};

const goToPage = (page) => {
    currentPage = page;
    renderTable();
    renderPagination();
};

// --- Modal Functions ---
const editDriver = async (driverId) => {
    const driver = allDrivers.find(d => d.id === driverId);
    if (!driver) {
        showMessage(dashboardMessage, 'لم يتم العثور على السائق.', 'error');
        return;
    }

    const settings = driver.driver_default_settings && driver.driver_default_settings.length > 0
        ? driver.driver_default_settings[0]
        : null;

    // Populate form
    editDriverId.value = driverId;
    editDriverName.textContent = driver.name;
    editWorkShift.value = driver.work_shift || '';
    editDefaultSalary.value = settings ? settings.default_base_salary : '';
    editOpeningBalance.value = settings ? settings.opening_balance : '';
    editCurrentBalance.value = settings ? settings.general_balance : '';

    openModal(editDriverModal);
};

const addDriverDefaults = async (driverId) => {
    const driver = allDrivers.find(d => d.id === driverId);
    if (!driver) {
        showMessage(dashboardMessage, 'لم يتم العثور على السائق.', 'error');
        return;
    }

    // Populate form for new defaults
    editDriverId.value = driverId;
    editDriverName.textContent = driver.name;
    editWorkShift.value = driver.work_shift || '';
    editDefaultSalary.value = '';
    editOpeningBalance.value = '0';
    editCurrentBalance.value = '0';

    openModal(editDriverModal);
};

const showBalanceInfo = async (driverId, driverName) => {
    balanceInfoDriverName.textContent = driverName;

    // Show basic balance info for now
    balanceBreakdownTbody.innerHTML = `
        <tr>
            <td>رصيد الافتتاح</td>
            <td>قيد التطوير</td>
            <td>سيتم إضافة التفاصيل قريباً</td>
        </tr>
    `;

    openModal(balanceInfoModal);
};

const saveDriverSettings = async (event) => {
    event.preventDefault();

    const driverId = editDriverId.value;
    const workShift = editWorkShift.value;
    const defaultSalary = parseFloat(editDefaultSalary.value);
    const openingBalance = parseFloat(editOpeningBalance.value) || 0;

    if (!workShift || isNaN(defaultSalary) || defaultSalary < 0) {
        showModalMessage(editModalMessageArea, 'يرجى ملء جميع الحقول المطلوبة بقيم صحيحة.', 'error');
        return;
    }

    try {
        showModalMessage(editModalMessageArea, 'جاري حفظ البيانات...', 'info', 0);

        // Update driver work shift
        const { error: driverError } = await _supabase
            .from('drivers')
            .update({ work_shift: workShift })
            .eq('id', driverId);

        if (driverError) throw driverError;

        // Check if driver defaults exist
        const { data: existingDefaults, error: checkError } = await _supabase
            .from('driver_default_settings')
            .select('*')
            .eq('driver_id', driverId)
            .single();

        if (checkError && checkError.code !== 'PGRST116') throw checkError;

        if (existingDefaults) {
            // Update existing defaults
            const { error: updateError } = await _supabase
                .from('driver_default_settings')
                .update({
                    default_base_salary: defaultSalary,
                    opening_balance: openingBalance,
                    general_balance: openingBalance
                })
                .eq('driver_id', driverId);

            if (updateError) throw updateError;
        } else {
            // Insert new defaults
            const { error: insertError } = await _supabase
                .from('driver_default_settings')
                .insert({
                    driver_id: driverId,
                    default_base_salary: defaultSalary,
                    opening_balance: openingBalance,
                    general_balance: openingBalance
                });

            if (insertError) throw insertError;
        }

        showModalMessage(editModalMessageArea, 'تم حفظ البيانات بنجاح.', 'success', 2000);
        setTimeout(() => {
            closeModal(editDriverModal);
            fetchDriverDefaults();
        }, 2000);

    } catch (error) {
        console.error('Error saving driver settings:', error);
        showModalMessage(editModalMessageArea, `خطأ في حفظ البيانات: ${error.message}`, 'error');
    }
};

// --- Event Listeners Setup ---
document.addEventListener('DOMContentLoaded', () => {
    // Ensure Supabase is ready before proceeding
    if (!_supabase) {
        showMessage(dashboardMessage, 'خطأ فادح: لم يتم تهيئة الاتصال بقاعدة البيانات.', 'error', 0);
        return;
    }

    // Initial data load
    fetchDriverDefaults();

    // Search and filter event listeners
    if (searchInput) {
        searchInput.addEventListener('input', applyFilters);
    }

    if (shiftFilter) {
        shiftFilter.addEventListener('change', applyFilters);
    }

    if (balanceFilter) {
        balanceFilter.addEventListener('change', applyFilters);
    }

    // Refresh button
    if (refreshBtn) {
        refreshBtn.addEventListener('click', fetchDriverDefaults);
    }

    // Modal event listeners
    if (editDriverForm) {
        editDriverForm.addEventListener('submit', saveDriverSettings);
    }

    if (closeEditModalBtn) {
        closeEditModalBtn.addEventListener('click', () => closeModal(editDriverModal));
    }

    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', () => closeModal(editDriverModal));
    }

    if (closeBalanceInfoModal) {
        closeBalanceInfoModal.addEventListener('click', () => closeModal(balanceInfoModal));
    }

    // Close modals when clicking outside
    window.addEventListener('click', (event) => {
        if (event.target === editDriverModal) {
            closeModal(editDriverModal);
        }
        if (event.target === balanceInfoModal) {
            closeModal(balanceInfoModal);
        }
    });
});

// Make functions globally available for onclick handlers
window.editDriver = editDriver;
window.addDriverDefaults = addDriverDefaults;
window.showBalanceInfo = showBalanceInfo;
