<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الميزانية الشهرية</title>
    <link rel="stylesheet" href="style.css">
    <!-- Include Supabase JS library -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Include Config and Main Script -->
    <script src="../../config.js"></script> <!-- Adjusted path -->
    <script defer src="script.js"></script>
    <!-- Add Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <header class="dashboard-header">
            <div class="header-content">
                <h1><i class="fas fa-calendar-alt"></i> الميزانية الشهرية</h1>
                <p>عرض وإدارة حالة الشهور المالية</p>
                <!-- Optional: Add back button if needed -->
                <!-- <button onclick="window.history.back()" class="back-btn"><i class="fas fa-arrow-right"></i> رجوع</button> -->
            </div>
        </header>

        <main class="dashboard-main">
            <!-- Controls Section -->
            <section class="controls-section">
                <div class="control-card">
                    <div class="card-header">
                        <h2>اختيار السنة المالية</h2>
                    </div>
                    <div class="card-body">
                        <div class="controls-grid year-selector-grid"> <!-- Custom class for styling -->
                            <div class="form-group">
                                <label for="year-select">اختر السنة:</label>
                                <select id="year-select">
                                    <option value="">جاري تحميل السنوات...</option>
                                </select>
                            </div>
                            <button id="add-new-year-btn" class="control-btn add-btn" title="إضافة سنة مالية جديدة (إذا لم تكن موجودة)">
                                <i class="fas fa-plus"></i> إضافة سنة
                            </button>
                        </div>
                         <div id="year-message" class="message"></div>
                    </div>
                </div>
            </section>

            <!-- Months Table Section -->
            <section class="table-section" id="months-section" style="display: none;"> <!-- Initially hidden -->
                <div class="table-card">
                    <div class="card-header">
                        <h2>شهور السنة المالية <span id="selected-year-title"></span></h2>
                        <span class="badge" id="months-count">0</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="months-table">
                                <thead>
                                    <tr>
                                        <th>الشهر</th>
                                        <th>رقم الشهر</th>
                                        <th>إجمالي الدخل</th>
                                        <th>إجمالي المصروفات</th>
                                        <th>الرصيد الافتتاحي</th>
                                        <th>الرصيد الختامي</th>
                                        <th>الحالة</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="months-tbody">
                                    <!-- Data will be loaded by JavaScript -->
                                    <tr><td colspan="8" class="loading-message">اختر سنة لعرض الشهور...</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <div id="list-message" class="message"></div>
                    </div>
                </div>
            </section>

             <!-- Add Year Modal (Optional but helpful) -->
            <section id="add-year-section" class="form-section">
                <div class="form-card mini-form"> <!-- Use mini-form style -->
                    <div class="card-header">
                        <h2 id="year-form-title">إضافة سنة مالية جديدة</h2>
                        <button id="close-year-form-btn" class="close-btn"><i class="fas fa-times"></i></button>
                    </div>
                    <div class="card-body">
                        <form id="year-form">
                            <div class="form-group">
                                <label for="year_number">رقم السنة <span class="required">*</span></label>
                                <input type="number" id="year_number" name="year_number" required placeholder="مثال: 2024" min="2000" max="2100">
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="submit-btn">إضافة السنة</button>
                                <button type="button" id="cancel-year-btn" class="cancel-btn">إلغاء</button>
                            </div>
                        </form>
                        <div id="add-year-message" class="message"></div>
                    </div>
                </div>
            </section>

        </main>

        <footer class="dashboard-footer">
            <p>© 2023 نظام الإدارة المالية - جميع الحقوق محفوظة</p>
        </footer>
    </div>
    <style>
        /* Simple style for year selector grid */
        .year-selector-grid {
            grid-template-columns: 1fr auto; /* Input takes available space, button takes auto */
            align-items: end; /* Align items to the bottom */
        }
        .year-selector-grid .form-group {
            margin-bottom: 0; /* Remove default bottom margin */
        }
         /* Adjust mini-form width if needed */
        .mini-form {
            max-width: 450px;
        }
        .status-closed { color: var(--danger-color); font-weight: bold; }
        .status-open { color: var(--success-color); font-weight: bold; }
    </style>
</body>
</html>
