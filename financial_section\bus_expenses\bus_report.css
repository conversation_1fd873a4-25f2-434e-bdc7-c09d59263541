/* Import shared styles if needed, or define base styles */
@import url('../../shared_styles.css'); /* Adjust path if necessary */

body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    background-color: #f4f7f9;
    color: #333;
    line-height: 1.6;
    margin: 0;
}

.report-container {
    max-width: 1100px;
    margin: 20px auto;
    padding: 20px;
    background-color: #fff;
    box-shadow: 0 0 15px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    margin-bottom: 25px;
    border-bottom: 2px solid var(--primary-color, #3498db);
}

.report-header h1 {
    color: var(--primary-color, #3498db);
    margin: 0;
    font-size: 1.8rem;
}
.report-header h1 i {
    margin-left: 10px;
}

.print-btn {
    background-color: var(--success-color, #2ecc71);
    color: #fff;
    padding: 8px 15px;
}
.print-btn:hover {
    background-color: #27ae60;
}

.report-content {
    margin-bottom: 30px;
}

.report-meta {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #eee;
    border-radius: 5px;
    font-size: 0.95rem;
}
.report-meta p {
    margin: 5px 0;
}
.report-meta strong {
    font-weight: 600;
    color: var(--secondary-color, #2c3e50);
}

.report-summary {
    margin-bottom: 25px;
    padding: 15px;
    background-color: rgba(52, 152, 219, 0.05);
    border: 1px solid rgba(52, 152, 219, 0.2);
    border-radius: 5px;
}
.report-summary h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--primary-dark, #2980b9);
    border-bottom: 1px solid rgba(52, 152, 219, 0.3);
    padding-bottom: 8px;
}
.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 10px;
}
.summary-item {
    font-size: 0.9rem;
}
.summary-item .label {
    color: var(--text-muted, #777);
}
.summary-item .value {
    font-weight: 600;
    color: var(--secondary-color, #2c3e50);
}
.summary-item.total {
    font-size: 1.1rem;
    font-weight: bold;
    color: var(--primary-dark, #2980b9);
    grid-column: 1 / -1; /* Span full width */
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px dashed #ccc;
}

.bus-section {
    margin-bottom: 25px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    overflow: hidden; /* Ensure border radius applies to table */
}
.bus-section h4 {
    background-color: #f1f5f9;
    padding: 10px 15px;
    margin: 0;
    font-size: 1.1rem;
    color: var(--secondary-color, #2c3e50);
    border-bottom: 1px solid #e0e0e0;
}
.bus-section h4 i {
    margin-left: 8px;
}

.details-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}
.details-table th,
.details-table td {
    padding: 8px 12px;
    text-align: right;
    border-bottom: 1px solid #eee;
}
.details-table th {
    background-color: #f8f9fa;
    font-weight: 500;
    color: #555;
}
.details-table tbody tr:last-child td {
    border-bottom: none;
}
.details-table tbody tr:hover {
    background-color: #fdfdfe;
}
.details-table .total-row td {
    font-weight: bold;
    background-color: #f8f9fa;
    border-top: 1px solid #ddd;
}

.report-alerts {
    margin-top: 20px;
    padding: 15px;
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
    border-radius: 5px;
}
.report-alerts h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #8a6d3b;
}
.report-alerts ul {
    list-style: none;
    padding: 0;
    margin: 0;
}
.report-alerts li {
    margin-bottom: 5px;
    color: #66512c;
}
.report-alerts li i {
    margin-left: 5px;
    color: #f0ad4e;
}

.loading-placeholder {
    text-align: center;
    padding: 40px;
    font-size: 1.2rem;
    color: #888;
}
.loading-placeholder i {
    margin-left: 10px;
}

/* Charts Section */
.charts-section {
    margin-top: 30px; /* Reduced top margin */
    padding-top: 20px;
    border-top: 1px solid #eee;
}
.charts-grid {
    display: grid;
    /* Changed to 1fr to allow flexibility but constrained by container */
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
}
.chart-container {
    background-color: #fdfdfd;
    padding: 15px; /* Reduced padding */
    border-radius: 5px;
    border: 1px solid #eee;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    /* Add max-height or control aspect ratio via JS if needed */
    position: relative; /* Needed for responsive charts */
    /* Limit height */
    max-height: 400px; /* Example max height */
    overflow: hidden; /* Hide overflow if canvas is too big */
}
.chart-container canvas {
    max-width: 100%;
    /* Let Chart.js handle height based on aspect ratio or options */
    max-height: 350px; /* Ensure canvas doesn't exceed container */
}
.chart-container h3 {
    text-align: center;
    margin-top: 0;
    margin-bottom: 10px; /* Reduced margin */
    font-size: 0.95rem; /* Slightly smaller title */
    font-weight: 500;
    color: #555;
}

/* Analysis Section Styles */
.analysis-section {
    margin-top: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #eee;
    border-radius: 5px;
    text-align: center; /* Center align content */
}
.analysis-section h2 {
    margin-top: 0;
    margin-bottom: 10px;
    color: var(--secondary-color);
    text-align: center;
    font-size: 1.4rem; /* Slightly larger */
}
.analysis-section h2 i {
    margin-left: 8px;
}
.analysis-section p {
    text-align: center;
    color: var(--text-muted);
    margin-bottom: 15px;
    font-size: 0.9rem;
}

/* Style for the question display area */
.analysis-question-display {
    background-color: #e9ecef;
    padding: 15px 20px;
    border-radius: 5px;
    margin-bottom: 20px;
    min-height: 50px; /* Ensure some height while loading */
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ced4da;
}
#system-question-text {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--secondary-color, #2c3e50);
    margin: 0;
    text-align: center;
}

/* Style for the answer buttons container */
.analysis-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
}
.analysis-buttons .control-btn {
    padding: 10px 25px;
    font-size: 1rem;
    min-width: 100px;
}
.success-btn { /* Define if not already in shared_styles */
    background-color: var(--success-color, #2ecc71);
    color: #fff;
}
.success-btn:hover { background-color: #27ae60; }
.danger-btn { /* Define if not already in shared_styles */
    background-color: var(--danger-color, #e74c3c);
    color: #fff;
}
.danger-btn:hover { background-color: #c0392b; }

/* Style for the feedback area */
.analysis-feedback {
    background-color: #fff;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 15px;
    margin-top: 10px;
    text-align: right; /* Align text right */
}
.analysis-feedback p {
    margin: 8px 0;
    font-size: 1rem;
    color: var(--text-dark);
}
.analysis-feedback strong {
    color: var(--primary-dark);
}
#feedback-correctness {
    font-weight: bold;
}
#feedback-correctness.correct {
    color: var(--success-color);
}
#feedback-correctness.incorrect {
    color: var(--danger-color);
}
#feedback-reason {
    color: var(--text-muted);
    font-size: 0.9rem;
    display: block; /* Ensure it takes its own line */
    margin-top: 5px;
}
#next-question-btn {
    display: block; /* Make button block level */
    margin-left: auto; /* Push to the right */
    margin-right: auto; /* Center it horizontally */
    margin-top: 15px;
}

/* Remove old input group styles if no longer needed */
.analysis-input-group { display: none; }
.analysis-result { display: none; }

/* Print Styles */
@media print {
    body {
        background-color: #fff;
        font-size: 10pt; /* Adjust base font size for print */
        margin: 0;
        padding: 0;
    }
    .report-container {
        max-width: 100%;
        margin: 0;
        padding: 10mm; /* Add print margins */
        box-shadow: none;
        border-radius: 0;
        border: none;
    }
    .no-print {
        display: none !important; /* Hide elements not for printing */
    }
    .report-header {
        border-bottom: 1px solid #ccc;
        margin-bottom: 15px;
        padding-bottom: 10px;
    }
    .report-header h1 {
        font-size: 16pt;
    }
    .report-meta, .report-summary, .bus-section, .report-alerts, .chart-container {
        border: 1px solid #ccc;
        background-color: #fff !important; /* Ensure white background */
        box-shadow: none;
        page-break-inside: avoid; /* Try to keep sections together */
        margin-bottom: 15px;
        padding: 10px;
    }
    .details-table {
        font-size: 9pt;
    }
    .details-table th, .details-table td {
        padding: 5px 8px;
    }
    a {
        text-decoration: none;
        color: #000;
    }
    /* Ensure charts are reasonably sized for print */
    .chart-container {
        max-height: 200px !important; /* Further limit chart height in print */
        padding: 10px;
    }
    .chart-container canvas {
        max-height: 180px !important;
    }
    .charts-grid {
        grid-template-columns: 1fr; /* Stack charts vertically for print */
        gap: 15px;
    }
    .analysis-section {
        display: none; /* Hide analysis section in print */
    }
}
