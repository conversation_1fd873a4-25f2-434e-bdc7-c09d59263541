// Supabase Initialization
let supabaseUrl, supabaseAnonKey;
if (typeof SUPABASE_URL !== 'undefined') supabaseUrl = SUPABASE_URL;
else console.error('SUPABASE_URL not found.');
if (typeof SUPABASE_ANON_KEY !== 'undefined') supabaseAnonKey = SUPABASE_ANON_KEY;
else console.error('SUPABASE_ANON_KEY not found.');

const { createClient } = supabase;
const _supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('Supabase Initialized for Institution Details:', _supabase);

// --- DOM Elements ---
const institutionNameHeader = document.getElementById('institution-name-header');
const groupForm = document.getElementById('group-form');
const formMessage = document.getElementById('form-message');
const addGroupBtn = document.getElementById('add-group-btn');
const addGroupSection = document.getElementById('add-group-section');
const cancelEditBtn = document.getElementById('cancel-edit-btn');
const closeFormBtn = document.getElementById('close-form-btn');
// const groupsTableBody = document.getElementById('groups-tbody'); // Remove or comment out
const groupsCardsContainer = document.getElementById('groups-cards-container'); // New card container
const listMessage = document.getElementById('list-message');
const searchInput = document.getElementById('search-input');
const searchBtn = document.getElementById('search-btn');
const printReportBtn = document.getElementById('print-report-btn');
const groupIdField = document.getElementById('group_id');
const enterpriseIdField = document.getElementById('enterprise_id_field'); // Hidden field for FK
const formTitle = document.getElementById('form-title');
const groupsCountBadge = document.getElementById('groups-count');
const paginationControls = document.getElementById('pagination-controls');

// --- New Group Name Element ---
const groupNameInput = document.getElementById('group_name'); // Input for group name

// --- New Trip Type Elements ---
const tripTypeRadios = document.querySelectorAll('input[name="trip_type"]');
const departureFieldset = document.getElementById('departure-fieldset');
const returnFieldset = document.getElementById('return-fieldset');

// --- New Cost/VAT DOM Elements ---
const costInput = document.getElementById('cost');
const hasVatCheckbox = document.getElementById('has_vat');
const vatPercentageInput = document.getElementById('vat_percentage');
const vatAmountInput = document.getElementById('vat_amount');
const totalCostWithVatInput = document.getElementById('total_cost_with_vat');
const costFieldset = document.getElementById('cost-fieldset'); // Fieldset containing VAT details
const vatDetailsContainer = costFieldset?.querySelector('.vat-details'); // Container for VAT fields

// --- State ---
let currentInstitutionId = null;
let currentInstitutionName = '';
let currentGroups = [];
let editMode = false;

// Pagination variables (if needed for groups)
let currentGroupPage = 1;
const groupsPerPage = 10;
let totalGroupPages = 1;

// --- Functions ---

// Function to display messages
const showMessage = (element, message, type = 'info') => {
    if (!element) return;
    element.textContent = message;
    element.className = `message ${type} show`;
    if (type === 'success') {
        setTimeout(() => { element.style.display = 'none'; element.classList.remove('show'); }, 5000);
    } else {
        element.style.display = 'block';
    }
};

// Function to format time (HH:MM)
const formatTime = (timeString) => {
    if (!timeString) return '';
    try {
        const [hours, minutes] = timeString.split(':');
        return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`;
    } catch (e) {
        console.error("Error formatting time:", timeString, e);
        return timeString; // Return original if formatting fails
    }
};

// --- New VAT Calculation Function ---
const calculateVat = () => {
    const cost = parseFloat(costInput.value) || 0;
    const applyVat = hasVatCheckbox.checked;
    const vatPercentage = parseFloat(vatPercentageInput.value) || 0;

    let vatAmount = 0;
    let totalCost = cost; // Start with base cost

    if (applyVat && cost > 0 && vatPercentage >= 0) {
        vatAmount = (cost * vatPercentage) / 100;
        totalCost = cost + vatAmount;
    }

    // Update read-only fields
    if (vatAmountInput) vatAmountInput.value = vatAmount.toFixed(2);
    if (totalCostWithVatInput) totalCostWithVatInput.value = totalCost.toFixed(2);

    // Toggle visibility of VAT details section
    if (costFieldset) {
        if (applyVat) {
            costFieldset.classList.add('show-vat');
        } else {
            costFieldset.classList.remove('show-vat');
            // Optionally clear percentage when unchecked
            // vatPercentageInput.value = '15.00'; // Or keep the value
        }
    }
};

// --- New Function to handle Trip Type Change ---
const handleTripTypeChange = () => {
    const selectedType = document.querySelector('input[name="trip_type"]:checked')?.value;
    console.log("Trip type changed to:", selectedType); // Debug log

    if (!departureFieldset || !returnFieldset) {
        console.error("Departure or Return fieldset not found!");
        return;
    }

    switch (selectedType) {
        case 'departure':
            departureFieldset.style.display = ''; // Show departure
            returnFieldset.style.display = 'none'; // Hide return
            break;
        case 'return':
            departureFieldset.style.display = 'none'; // Hide departure
            returnFieldset.style.display = ''; // Show return
            break;
        case 'both':
        default:
            departureFieldset.style.display = ''; // Show departure
            returnFieldset.style.display = ''; // Show return
            break;
    }
};
// --- End New Function ---

// --- Renamed and Modified Function to render group cards ---
const renderGroupCards = (groups) => {
    if (!groupsCardsContainer) return;
    groupsCardsContainer.innerHTML = ''; // Clear previous content
    if (listMessage) listMessage.style.display = 'none';

    if (!groups || groups.length === 0) {
        groupsCardsContainer.innerHTML = `<div class="loading-placeholder">لا توجد مجموعات لهذه المؤسسة.</div>`;
        if (groupsCountBadge) groupsCountBadge.textContent = '0';
        if (paginationControls) paginationControls.innerHTML = '';
        return;
    }

    if (groupsCountBadge) groupsCountBadge.textContent = groups.length; // Count original groups

    groups.forEach((group, index) => {
        // Check if departure or return data exists
        const hasDepartureData = group.departure_time || group.departure_meeting_time || group.departure_meeting_point || group.departure_employees_count;
        const hasReturnData = group.return_time || group.return_meeting_time || group.return_meeting_point || group.return_employees_count;

        // Calculate cost
        const totalCost = group.has_vat ? (group.total_cost_with_vat ?? 0) : (group.cost ?? 0);
        let costForDeparture = 0;
        let costForReturn = 0;

        if (hasDepartureData && hasReturnData) {
            costForDeparture = (totalCost / 2).toFixed(2);
            costForReturn = (totalCost / 2).toFixed(2);
        } else if (hasDepartureData) {
            costForDeparture = totalCost.toFixed(2);
        } else if (hasReturnData) {
            costForReturn = totalCost.toFixed(2);
        }

        // --- Create Departure Card (if data exists) ---
        if (hasDepartureData) {
            const departureCard = document.createElement('div');
            departureCard.className = 'group-card departure-card';
            departureCard.innerHTML = `
                <div class="card-header-trip">
                    <h3>${group.name} - رحلة الذهاب</h3>
                    <div class="card-actions">
                        <button class="action-btn edit-btn" data-id="${group.id}" title="تعديل المجموعة الأصلية"><i class="fas fa-edit"></i></button>
                        <button class="action-btn delete-btn" data-id="${group.id}" title="حذف المجموعة الأصلية"><i class="fas fa-trash-alt"></i></button>
                    </div>
                </div>
                <div class="card-body-trip">
                    <div class="info-item">
                        <span class="label"><i class="fas fa-users"></i> عدد الموظفين:</span>
                        <span class="value">${group.departure_employees_count ?? 0}</span>
                    </div>
                    <div class="info-item">
                        <span class="label"><i class="fas fa-clock"></i> وقت التجمع:</span>
                        <span class="value">${formatTime(group.departure_meeting_time) || '-'}</span>
                    </div>
                    <div class="info-item">
                        <span class="label"><i class="fas fa-bus-alt"></i> وقت الانطلاق:</span>
                        <span class="value">${formatTime(group.departure_time) || '-'}</span>
                    </div>
                    <div class="info-item info-item-full">
                        <span class="label"><i class="fas fa-map-marker-alt"></i> نقطة التجمع:</span>
                        <span class="value">${group.departure_meeting_point ? `<a href="${group.departure_meeting_point}" target="_blank" title="فتح الرابط">عرض على الخريطة <i class="fas fa-external-link-alt"></i></a>` : '-'}</span>
                    </div>
                    <div class="info-item info-item-full cost-item">
                        <span class="label"><i class="fas fa-dollar-sign"></i> تكلفة الرحلة:</span>
                        <span class="value cost-value">${costForDeparture} ريال</span>
                    </div>
                    ${group.notes ? `<div class="info-item info-item-full notes-item"><span class="label"><i class="fas fa-sticky-note"></i> ملاحظات:</span><span class="value">${group.notes}</span></div>` : ''}
                </div>
            `;
            // Add event listeners
            const editBtn = departureCard.querySelector('.edit-btn');
            const deleteBtn = departureCard.querySelector('.delete-btn');
            if (editBtn) editBtn.addEventListener('click', () => handleEditGroup(group));
            if (deleteBtn) deleteBtn.addEventListener('click', () => handleDeleteGroup(group.id));

            groupsCardsContainer.appendChild(departureCard);
        }

        // --- Create Return Card (if data exists) ---
        if (hasReturnData) {
            const returnCard = document.createElement('div');
            returnCard.className = 'group-card return-card';
            returnCard.innerHTML = `
                <div class="card-header-trip">
                    <h3>${group.name} - رحلة العودة</h3>
                    <div class="card-actions">
                        <button class="action-btn edit-btn" data-id="${group.id}" title="تعديل المجموعة الأصلية"><i class="fas fa-edit"></i></button>
                        <button class="action-btn delete-btn" data-id="${group.id}" title="حذف المجموعة الأصلية"><i class="fas fa-trash-alt"></i></button>
                    </div>
                </div>
                <div class="card-body-trip">
                    <div class="info-item">
                        <span class="label"><i class="fas fa-users"></i> عدد الموظفين:</span>
                        <span class="value">${group.return_employees_count ?? 0}</span>
                    </div>
                    <div class="info-item">
                        <span class="label"><i class="fas fa-clock"></i> وقت التجمع:</span>
                        <span class="value">${formatTime(group.return_meeting_time) || '-'}</span>
                    </div>
                    <div class="info-item">
                        <span class="label"><i class="fas fa-undo-alt"></i> وقت العودة:</span>
                        <span class="value">${formatTime(group.return_time) || '-'}</span>
                    </div>
                    <div class="info-item info-item-full">
                        <span class="label"><i class="fas fa-map-marker-alt"></i> نقطة التجمع:</span>
                        <span class="value">${group.return_meeting_point ? `<a href="${group.return_meeting_point}" target="_blank" title="فتح الرابط">عرض على الخريطة <i class="fas fa-external-link-alt"></i></a>` : '-'}</span>
                    </div>
                    <div class="info-item info-item-full cost-item">
                        <span class="label"><i class="fas fa-dollar-sign"></i> تكلفة الرحلة:</span>
                        <span class="value cost-value">${costForReturn} ريال</span>
                    </div>
                    ${group.notes ? `<div class="info-item info-item-full notes-item"><span class="label"><i class="fas fa-sticky-note"></i> ملاحظات:</span><span class="value">${group.notes}</span></div>` : ''}
                </div>
            `;
            // Add event listeners
            const editBtn = returnCard.querySelector('.edit-btn');
            const deleteBtn = returnCard.querySelector('.delete-btn');
            if (editBtn) editBtn.addEventListener('click', () => handleEditGroup(group));
            if (deleteBtn) deleteBtn.addEventListener('click', () => handleDeleteGroup(group.id));

            groupsCardsContainer.appendChild(returnCard);
        }
    });

    // Render pagination controls if needed
    // renderGroupPaginationControls(); // Assuming pagination logic remains similar
};
// --- End Card Rendering Function ---

// Function to fetch Institution Name
const fetchInstitutionDetails = async (id) => {
    if (!id) {
        if (institutionNameHeader) institutionNameHeader.textContent = 'خطأ: لم يتم تحديد المؤسسة';
        return;
    }
    try {
        const { data, error } = await _supabase
            .from('enterprises') // Use the correct table name
            .select('name')
            .eq('id', id)
            .single(); // Expecting only one result

        if (error) {
            console.error("Error fetching institution name:", error);
            if (institutionNameHeader) institutionNameHeader.textContent = 'خطأ في التحميل';
        } else if (data) {
            currentInstitutionName = data.name;
            if (institutionNameHeader) institutionNameHeader.textContent = data.name;
            document.title = `تفاصيل: ${data.name}`; // Update page title
        } else {
             if (institutionNameHeader) institutionNameHeader.textContent = 'المؤسسة غير موجودة';
        }
    } catch (error) {
        console.error("JS Error fetching institution name:", error);
        if (institutionNameHeader) institutionNameHeader.textContent = 'خطأ';
    }
};


// Function to fetch groups for the current institution
const fetchGroups = async (institutionId, searchTerm = '') => {
    // Use the new card container for loading message
    if (!institutionId || !groupsCardsContainer) return;
    groupsCardsContainer.innerHTML = `<div class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i> جاري تحميل المجموعات...</div>`;
    if (paginationControls) paginationControls.innerHTML = '';

    try {
        let query = _supabase
            .from('enterprise_groups')
            .select('*')
            .eq('enterprise_id', institutionId);

        if (searchTerm) {
             query = query.or(`name.ilike.%${searchTerm}%,notes.ilike.%${searchTerm}%`);
        }

        // Order by name is still relevant for the original groups
        query = query.order('name', { ascending: true });

        const { data, error } = await query;

        if (error) {
            console.error('Supabase Fetch Groups Error:', error);
            showMessage(listMessage, `خطأ في جلب المجموعات: ${error.message}`, 'error');
            groupsCardsContainer.innerHTML = `<div class="loading-placeholder error">خطأ في تحميل المجموعات.</div>`; // Error in card container
        } else {
            console.log('Fetched groups:', data);
            currentGroups = data || [];
            renderGroupCards(currentGroups); // *** Call the new card rendering function ***

            if (data.length === 0 && searchTerm) {
                showMessage(listMessage, `لا توجد مجموعات تطابق البحث "${searchTerm}".`, 'info');
            } else if (data.length === 0) {
                 showMessage(listMessage, 'لا توجد مجموعات مضافة لهذه المؤسسة بعد.', 'info');
                 // Message is also shown inside renderGroupCards
            }
        }
    } catch (error) {
        console.error('JavaScript Fetch Groups Error:', error);
        showMessage(listMessage, `حدث خطأ غير متوقع: ${error.message}`, 'error');
        groupsCardsContainer.innerHTML = `<div class="loading-placeholder error">خطأ غير متوقع.</div>`; // Error in card container
    }
};

// Function to handle group form submission (Add/Update)
const handleGroupFormSubmit = async (event) => {
    event.preventDefault();
    if (!groupForm || !formMessage || !currentInstitutionId) return;

    console.log("Current Institution ID before submit:", currentInstitutionId, typeof currentInstitutionId);

    showMessage(formMessage, 'جاري حفظ المجموعة...', 'info');
    const submitBtn = groupForm.querySelector('.submit-btn');
    if (submitBtn) submitBtn.disabled = true;

    const formData = new FormData(groupForm);
    const groupData = {};
    formData.forEach((value, key) => {
        const trimmedValue = typeof value === 'string' ? value.trim() : value;
        if (key === 'has_vat') {
            groupData[key] = hasVatCheckbox.checked;
        } else if ((key.includes('_count') || key.includes('_time') || key === 'cost' || key.includes('vat_')) && trimmedValue === '') {
            groupData[key] = null;
        } else if (key.includes('_point') && trimmedValue === '') {
             groupData[key] = null;
        }
         else {
            groupData[key] = trimmedValue;
        }
    });

    const groupId = groupData.group_id;
    const selectedTripType = groupData.trip_type || 'both'; // Get selected trip type

    // --- Validation for Group Name ---
    if (!groupData.name) {
        showMessage(formMessage, 'اسم المجموعة مطلوب.', 'error');
        if (submitBtn) submitBtn.disabled = false;
        groupNameInput?.focus(); // Focus on the name field
        return; // Stop submission
    }
    // --- End Validation ---


    calculateVat();

    const dataToUpsert = {
        enterprise_id: currentInstitutionId,
        name: groupData.name,
        // Departure fields (set based on trip type)
        departure_employees_count: selectedTripType !== 'return' && groupData.departure_employees_count ? parseInt(groupData.departure_employees_count, 10) : null,
        departure_meeting_point: selectedTripType !== 'return' ? groupData.departure_meeting_point : null,
        departure_meeting_time: selectedTripType !== 'return' ? groupData.departure_meeting_time : null,
        departure_time: selectedTripType !== 'return' ? groupData.departure_time : null,
        // Return fields (set based on trip type)
        return_employees_count: selectedTripType !== 'departure' && groupData.return_employees_count ? parseInt(groupData.return_employees_count, 10) : null,
        return_meeting_point: selectedTripType !== 'departure' ? groupData.return_meeting_point : null,
        return_meeting_time: selectedTripType !== 'departure' ? groupData.return_meeting_time : null,
        return_time: selectedTripType !== 'departure' ? groupData.return_time : null,
        // Other fields
        notes: groupData.notes || null,
        cost: parseFloat(costInput.value) || null,
        has_vat: hasVatCheckbox.checked,
        vat_percentage: hasVatCheckbox.checked ? (parseFloat(vatPercentageInput.value) || 0) : null,
        vat_amount: hasVatCheckbox.checked ? (parseFloat(vatAmountInput.value) || 0) : null,
        total_cost_with_vat: hasVatCheckbox.checked ? (parseFloat(totalCostWithVatInput.value) || 0) : (parseFloat(costInput.value) || null)
    };

    console.log("Data being sent to Supabase:", JSON.stringify(dataToUpsert, null, 2));

    try {
        let result;
        if (editMode && groupId) {
            result = await _supabase
                .from('enterprise_groups')
                .update(dataToUpsert)
                .eq('id', groupId)
                .select();
        } else {
            result = await _supabase
                .from('enterprise_groups')
                .insert([dataToUpsert])
                .select();
        }

        const { data, error } = result;

        if (error) {
            console.error('Supabase Save Group Error:', error);
            console.error('Supabase error details:', JSON.stringify(error, null, 2));

            let userMessage = `خطأ في حفظ المجموعة: ${error.message}`;
            // ... (keep existing error handling) ...
            if (error.code === '23505' && error.message.includes('enterprise_groups_name_enterprise_id_key')) { // Unique constraint violation
                 userMessage = `خطأ في الحفظ: اسم المجموعة "${groupData.name}" موجود بالفعل لهذه المؤسسة. الرجاء اختيار اسم آخر.`;
            } else if (error.code === '23502' && error.message.includes('"name"')) { // Not null violation
                 userMessage = 'خطأ في الحفظ: اسم المجموعة مطلوب.';
            }
            showMessage(formMessage, userMessage, 'error', 0);
        } else {
            console.log('Supabase Save Group Success:', data);
            showMessage(formMessage, `تم ${editMode ? 'تحديث' : 'إضافة'} المجموعة بنجاح!`, 'success');
            setTimeout(() => {
                toggleGroupModal(false);
            }, 1500);
            fetchGroups(currentInstitutionId); // Refresh group list
        }
    } catch (error) {
        console.error('JavaScript Save Group Error:', error);
        showMessage(formMessage, `حدث خطأ غير متوقع: ${error.message}`, 'error');
    } finally {
        if (submitBtn) submitBtn.disabled = false;
    }
};

// Function to reset the group form
const resetGroupForm = () => {
    editMode = false;
    if (groupForm) groupForm.reset();
    if (groupIdField) groupIdField.value = '';
    if (enterpriseIdField) enterpriseIdField.value = currentInstitutionId || ''; // Pre-fill FK
    if (formMessage) formMessage.style.display = 'none';
    if (formTitle) formTitle.textContent = 'إضافة مجموعة جديدة';
    const submitBtn = groupForm?.querySelector('.submit-btn');
    if (submitBtn) submitBtn.textContent = 'حفظ المجموعة';

    // --- Reset Group Name field ---
    if (groupNameInput) groupNameInput.value = '';

    // --- Reset Trip Type to 'both' and show sections ---
    const bothRadio = document.getElementById('trip_type_both');
    if (bothRadio) bothRadio.checked = true;
    handleTripTypeChange(); // Apply visibility based on default

    // --- Reset Cost/VAT fields ---
    if (costInput) costInput.value = '';
    if (hasVatCheckbox) hasVatCheckbox.checked = false;
    if (vatPercentageInput) vatPercentageInput.value = '15.00'; // Reset to default
    if (vatAmountInput) vatAmountInput.value = '';
    if (totalCostWithVatInput) totalCostWithVatInput.value = '';
    if (costFieldset) costFieldset.classList.remove('show-vat'); // Hide VAT details
    calculateVat(); // Recalculate VAT/Total based on reset values
};

// Function to populate form for editing a group
const handleEditGroup = (group) => {
    if (!group || !groupForm) return;
    toggleGroupModal(true);
    editMode = true;

    if (formTitle) formTitle.textContent = 'تعديل بيانات المجموعة';
    const submitBtn = groupForm.querySelector('.submit-btn');
    if (submitBtn) submitBtn.textContent = 'تحديث المجموعة';

    // Populate standard form fields
    if (groupIdField) groupIdField.value = group.id || '';
    if (enterpriseIdField) enterpriseIdField.value = group.enterprise_id || currentInstitutionId || '';

    // --- Populate Group Name ---
    if (groupNameInput) groupNameInput.value = group.name || '';

    // --- Determine and set Trip Type ---
    const hasDeparture = group.departure_time || group.departure_meeting_time || group.departure_meeting_point || group.departure_employees_count;
    const hasReturn = group.return_time || group.return_meeting_time || group.return_meeting_point || group.return_employees_count;
    let tripType = 'both'; // Default
    if (hasDeparture && !hasReturn) {
        tripType = 'departure';
    } else if (!hasDeparture && hasReturn) {
        tripType = 'return';
    }
    const typeRadio = document.getElementById(`trip_type_${tripType}`);
    if (typeRadio) typeRadio.checked = true;
    handleTripTypeChange(); // Show/hide sections based on determined type
    // --- End Trip Type Handling ---

    // Populate visible fields based on determined type
    if (tripType === 'departure' || tripType === 'both') {
        document.getElementById('departure_employees_count').value = group.departure_employees_count ?? '';
        document.getElementById('departure_meeting_point').value = group.departure_meeting_point || '';
        document.getElementById('departure_meeting_time').value = group.departure_meeting_time || '';
        document.getElementById('departure_time').value = group.departure_time || '';
    }
    if (tripType === 'return' || tripType === 'both') {
        document.getElementById('return_employees_count').value = group.return_employees_count ?? '';
        document.getElementById('return_meeting_point').value = group.return_meeting_point || '';
        document.getElementById('return_meeting_time').value = group.return_meeting_time || '';
        document.getElementById('return_time').value = group.return_time || '';
    }

    document.getElementById('notes').value = group.notes || '';

    // --- Populate Cost/VAT fields ---
    if (costInput) costInput.value = group.cost ?? '';
    if (hasVatCheckbox) hasVatCheckbox.checked = group.has_vat || false;
    if (vatPercentageInput) vatPercentageInput.value = group.vat_percentage ?? '15.00'; // Use saved or default
    calculateVat(); // Trigger calculation

    // Focus on the group name field
    setTimeout(() => {
        groupNameInput?.focus();
    }, 100);
};

// Function to handle group deletion
const handleDeleteGroup = async (groupId) => {
    const confirmDelete = confirm(`هل أنت متأكد من حذف هذه المجموعة؟ لا يمكن التراجع عن هذا الإجراء.`);

    if (confirmDelete) {
        try {
            const { error } = await _supabase
                .from('enterprise_groups')
                .delete()
                .eq('id', groupId); // Use group's primary key 'id'

            if (error) {
                console.error('Supabase Delete Group Error:', error);
                showMessage(listMessage, `خطأ في حذف المجموعة: ${error.message}`, 'error');
            } else {
                console.log('Group deleted:', groupId);
                showMessage(listMessage, `تم حذف المجموعة بنجاح.`, 'success');
                fetchGroups(currentInstitutionId); // Refresh the list
            }
        } catch (error) {
            console.error('JavaScript Delete Group Error:', error);
            showMessage(listMessage, `حدث خطأ غير متوقع أثناء الحذف: ${error.message}`, 'error');
        }
    }
};

// Function to handle search within groups (optional)
const handleGroupSearch = () => {
    if (!searchInput || !currentInstitutionId) return;
    const searchTerm = searchInput.value.trim();
    fetchGroups(currentInstitutionId, searchTerm);
};

// Function to handle printing group report (basic example)
const handlePrintGroupReport = () => {
    // Similar logic to other print functions, adapt for group columns
    alert('Print group report functionality to be implemented.');
};

// Function to toggle the group modal
function toggleGroupModal(show = true) {
    if (!addGroupSection) return;
    console.log('Group Modal toggled:', show ? 'show' : 'hide');
    if (show) {
        resetGroupForm();
        addGroupSection.classList.add('show');
         setTimeout(() => {
             // Focus on group name input first
             groupNameInput?.focus();
         }, 300);
        document.body.style.overflow = 'hidden';
    } else {
        addGroupSection.classList.remove('show');
        document.body.style.overflow = '';
    }
}

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOM loaded, initializing institution details page...');

    // Get institution ID from URL
    const urlParams = new URLSearchParams(window.location.search);
    currentInstitutionId = urlParams.get('id'); // Make sure 'id' matches the parameter name used

    if (!currentInstitutionId) {
        console.error("Institution ID not found in URL.");
        showMessage(listMessage, 'خطأ: لم يتم تحديد المؤسسة في الرابط.', 'error');
        if (institutionNameHeader) institutionNameHeader.textContent = 'خطأ';
        return; // Stop further execution if no ID
    }

     console.log("Institution ID from URL:", currentInstitutionId);

    // Fetch institution details (like name)
    await fetchInstitutionDetails(currentInstitutionId);

    // Fetch groups for this institution
    await fetchGroups(currentInstitutionId);

    // Setup event listeners
    setupEventListeners();
});


// --- Event Listeners ---
const setupEventListeners = () => {
    if (groupForm) {
        groupForm.addEventListener('submit', handleGroupFormSubmit);
    }
    if (addGroupBtn) {
        addGroupBtn.addEventListener('click', () => {
            editMode = false;
            toggleGroupModal(true);
        });
    }
    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', () => toggleGroupModal(false));
    }
    if (closeFormBtn) {
        closeFormBtn.addEventListener('click', () => toggleGroupModal(false));
    }
    if (addGroupSection) {
        addGroupSection.addEventListener('click', (event) => {
            if (event.target === addGroupSection) toggleGroupModal(false);
        });
    }
    if (searchBtn) {
        searchBtn.addEventListener('click', handleGroupSearch);
    }
    if (searchInput) {
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') handleGroupSearch();
        });
    }
    if (printReportBtn) {
        printReportBtn.addEventListener('click', handlePrintGroupReport);
    }
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && addGroupSection && addGroupSection.classList.contains('show')) {
            toggleGroupModal(false);
        }
    });

    // --- New Trip Type Listener ---
    tripTypeRadios.forEach(radio => {
        radio.addEventListener('change', handleTripTypeChange);
    });
    // --- End New Listener ---

    // --- New VAT Event Listeners ---
    if (hasVatCheckbox) {
        hasVatCheckbox.addEventListener('change', calculateVat);
    }
    if (costInput) {
        costInput.addEventListener('input', calculateVat);
    }
    if (vatPercentageInput) {
        vatPercentageInput.addEventListener('input', calculateVat);
    }
};
