<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل المؤسسة والمجموعات</title>
    <link rel="stylesheet" href="style.css">
    <!-- Include Supabase JS library -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Include Config and Main Script -->
    <script src="../config.js"></script>
    <script defer src="script.js"></script>
    <!-- Add Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <header class="dashboard-header">
            <div class="header-content">
                <h1><i class="fas fa-sitemap"></i> تفاصيل المؤسسة: <span id="institution-name-header">جاري التحميل...</span></h1>
                <p>إدارة مجموعات النقل للمؤسسة</p>
                 <button onclick="window.history.back()" class="back-btn"><i class="fas fa-arrow-right"></i> الرجوع لقائمة المؤسسات</button>
            </div>
        </header>

        <main class="dashboard-main">
             <!-- Controls Section -->
            <section class="controls-section">
                <div class="control-card">
                    <div class="card-header">
                        <h2>أدوات التحكم بالمجموعات</h2>
                    </div>
                    <div class="card-body">
                        <div class="controls-grid">
                            <!-- Add Group Button -->
                            <button id="add-group-btn" class="control-btn add-btn">
                                <i class="fas fa-plus-circle"></i> إضافة مجموعة
                            </button>
                            <div class="search-container"> <!-- Optional: Search within groups -->
                                <input type="text" id="search-input" placeholder="ابحث في المجموعات...">
                                <button id="search-btn" class="search-btn"><i class="fas fa-search"></i></button>
                            </div>
                             <button id="print-report-btn" class="control-btn print-btn">
                                <i class="fas fa-print"></i> طباعة تقرير المجموعات
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Groups Table Section -->
            <section class="table-section"> <!-- Keep section, but change content -->
                <div class="table-card"> <!-- Keep card container -->
                    <div class="card-header">
                        <h2><i class="fas fa-route"></i> قائمة رحلات المجموعات</h2> <!-- Updated Title -->
                        <span class="badge" id="groups-count">0</span> <!-- Badge now counts groups, not cards -->
                    </div>
                    <div class="card-body">
                        <!-- New Card Container -->
                        <div id="groups-cards-container" class="groups-cards-container">
                            <div class="loading-placeholder"> <!-- Loading Placeholder -->
                                <i class="fas fa-spinner fa-spin"></i> جاري تحميل بيانات المجموعات...
                            </div>
                        </div>
                        <div id="list-message" class="message" style="display: none;"></div>
                        <!-- Pagination might still be relevant for groups -->
                         <div class="pagination" id="pagination-controls"></div>
                    </div>
                </div>
            </section>

            <!-- Group Form Section (Modal) -->
            <section id="add-group-section" class="form-section">
                <div class="form-card">
                    <div class="card-header">
                        <h2 id="form-title">إضافة مجموعة جديدة</h2>
                        <button id="close-form-btn" class="close-btn"><i class="fas fa-times"></i></button>
                    </div>
                    <div class="card-body">
                        <form id="group-form">
                            <input type="hidden" id="group_id" name="group_id"> <!-- Hidden input for group ID (PK) -->
                             <input type="hidden" id="enterprise_id_field" name="enterprise_id"> <!-- Hidden input for FK -->

                            <!-- Group Name Field -->
                            <fieldset class="form-fieldset">
                                <legend>معلومات المجموعة الأساسية</legend>
                                <div class="form-row">
                                    <div class="form-group form-group-full"> <!-- Use full width -->
                                        <label for="group_name">اسم المجموعة <span class="required">*</span></label>
                                        <input type="text" id="group_name" name="name" required placeholder="أدخل اسمًا مميزًا للمجموعة">
                                    </div>
                                </div>
                            </fieldset>

                            <!-- Trip Type Selection -->
                            <fieldset class="form-fieldset">
                                <legend>نوع الرحلة <span class="required">*</span></legend>
                                <div class="form-row trip-type-options">
                                    <div class="form-group form-group-radio">
                                        <input type="radio" id="trip_type_both" name="trip_type" value="both" checked>
                                        <label for="trip_type_both">ذهاب وعودة</label>
                                    </div>
                                    <div class="form-group form-group-radio">
                                        <input type="radio" id="trip_type_departure" name="trip_type" value="departure">
                                        <label for="trip_type_departure">ذهاب فقط</label>
                                    </div>
                                    <div class="form-group form-group-radio">
                                        <input type="radio" id="trip_type_return" name="trip_type" value="return">
                                        <label for="trip_type_return">عودة فقط</label>
                                    </div>
                                </div>
                            </fieldset>

                            <!-- Departure Info - Added ID -->
                            <fieldset class="form-fieldset" id="departure-fieldset">
                                <legend>بيانات الذهاب</legend>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="departure_employees_count">عدد الموظفين</label>
                                        <input type="number" id="departure_employees_count" name="departure_employees_count" min="0">
                                    </div>
                                    <div class="form-group">
                                        <label for="departure_meeting_point">نقطة التجمع (رابط)</label>
                                        <input type="url" id="departure_meeting_point" name="departure_meeting_point" placeholder="https://maps.google.com/...">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="departure_meeting_time">وقت التجمع</label>
                                        <input type="time" id="departure_meeting_time" name="departure_meeting_time">
                                    </div>
                                    <div class="form-group">
                                        <label for="departure_time">وقت الانطلاق</label>
                                        <input type="time" id="departure_time" name="departure_time">
                                    </div>
                                </div>
                            </fieldset>

                            <!-- Return Info - Added ID -->
                             <fieldset class="form-fieldset" id="return-fieldset">
                                <legend>بيانات العودة</legend>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="return_employees_count">عدد الموظفين</label>
                                        <input type="number" id="return_employees_count" name="return_employees_count" min="0">
                                    </div>
                                    <div class="form-group">
                                        <label for="return_meeting_point">نقطة التجمع (رابط)</label>
                                        <input type="url" id="return_meeting_point" name="return_meeting_point" placeholder="https://maps.google.com/...">
                                    </div>
                                </div>
                                <div class="form-row">
                                     <div class="form-group">
                                        <label for="return_meeting_time">وقت التجمع</label>
                                        <input type="time" id="return_meeting_time" name="return_meeting_time">
                                    </div>
                                    <div class="form-group">
                                        <label for="return_time">وقت العودة</label>
                                        <input type="time" id="return_time" name="return_time">
                                    </div>
                                </div>
                            </fieldset>

                             <!-- Notes -->
                             <fieldset class="form-fieldset">
                                 <legend>ملاحظات إضافية</legend>
                                 <div class="form-row">
                                     <div class="form-group">
                                        <label for="notes">ملاحظات</label>
                                        <textarea id="notes" name="notes" rows="3"></textarea>
                                    </div>
                                 </div>
                             </fieldset>

                            <!-- Cost and VAT Section -->
                            <fieldset class="form-fieldset" id="cost-fieldset">
                                <legend>بيانات التكلفة والضريبة</legend>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="cost">التكلفة الأساسية</label>
                                        <input type="number" id="cost" name="cost" step="0.01" min="0" placeholder="0.00">
                                    </div>
                                    <div class="form-group form-group-checkbox">
                                        <label for="has_vat">
                                            <input type="checkbox" id="has_vat" name="has_vat">
                                            تطبيق القيمة المضافة (VAT)
                                        </label>
                                    </div>
                                </div>
                                <!-- VAT Details (Initially Hidden) -->
                                <div class="vat-details">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="vat_percentage">نسبة القيمة المضافة (%)</label>
                                            <input type="number" id="vat_percentage" name="vat_percentage" step="0.01" min="0" value="15.00"> <!-- Default 15% -->
                                        </div>
                                        <div class="form-group">
                                            <label for="vat_amount">مبلغ القيمة المضافة</label>
                                            <input type="number" id="vat_amount" name="vat_amount" step="0.01" readonly placeholder="0.00">
                                        </div>
                                    </div>
                                    <div class="form-row">
                                         <div class="form-group">
                                            <label for="total_cost_with_vat">التكلفة الإجمالية (مع الضريبة)</label>
                                            <input type="number" id="total_cost_with_vat" name="total_cost_with_vat" step="0.01" readonly placeholder="0.00">
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <div class="form-actions">
                                <button type="submit" class="submit-btn">حفظ المجموعة</button>
                                <button type="button" id="cancel-edit-btn" class="cancel-btn">إلغاء</button>
                            </div>
                        </form>
                        <div id="form-message" class="message"></div>
                    </div>
                </div>
            </section>
        </main>

        <footer class="dashboard-footer">
        </footer>
    </div>
     <style>
        /* Simple style for back button */
        .back-btn {
            background-color: var(--secondary-color);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            margin-top: 10px;
            transition: background-color 0.3s ease;
        }
        .back-btn:hover {
            background-color: #34495e;
        }
         .back-btn i {
             margin-left: 5px;
         }
    </style>
</body>
</html>
