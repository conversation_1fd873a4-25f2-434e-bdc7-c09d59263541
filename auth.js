/**
 * التحقق مما إذا كان المستخدم قد سجل الدخول عن طريق التحقق من sessionStorage.
 * يتم إعادة التوجيه إلى صفحة تسجيل الدخول إذا لم يتم تسجيل الدخول.
 *
 * @param {string} loginPagePath - المسار النسبي لصفحة تسجيل الدخول من الصفحة الحالية.
 */
function checkAuth(loginPagePath = '../login.html') { // Use 'function' keyword
    const isLoggedIn = sessionStorage.getItem('isLoggedIn'); // Use 'const' keyword

    if (isLoggedIn !== 'true') { // Use 'if' keyword
        console.warn('لم يتم التحقق من صحة المستخدم. تتم إعادة التوجيه إلى صفحة تسجيل الدخول.');
        // مسح أي بيانات جلسة حساسة محتملة قبل إعادة التوجيه
        sessionStorage.clear();
        // إعادة التوجيه إلى صفحة تسجيل الدخول
        window.location.href = loginPagePath; // Correct semicolon
        // منع تنفيذ المزيد من البرامج النصية على الصفحة الحالية
        throw new Error("لم يتم التحقق من صحة المستخدم."); // اختياري: إيقاف تنفيذ البرنامج النصي
    } else { // Use 'else' keyword
        console.log('تم التحقق من صحة المستخدم.');
        // اختياريًا، يمكنك إضافة منطق هنا لتحديث عناصر واجهة المستخدم
        // مشترك بين الصفحات المصادق عليها (على سبيل المثال، عرض اسم المستخدم، زر تسجيل الخروج)
        setupLogoutButton(); // إعداد وظيفة تسجيل الخروج إذا كان المستخدم مسجلاً الدخول
    }
}

/**
 * إعداد وظيفة زر تسجيل الخروج.
 */
function setupLogoutButton() { // Use 'function' keyword
    const logoutBtn = document.getElementById('logout-btn'); // Use 'const' keyword
    if (logoutBtn) { // Use 'if' keyword
        logoutBtn.style.display = 'inline-block'; // تأكد من أنه مرئي
        logoutBtn.addEventListener('click', () => { // Use correct variable name 'logoutBtn'
            console.log('جاري تسجيل خروج المستخدم...');
            sessionStorage.clear(); // مسح جميع بيانات الجلسة
            // إعادة التوجيه إلى صفحة تسجيل الدخول (اضبط المسار إذا لزم الأمر)
            // Determine the correct path relative to the current page
            // This might need adjustment depending on the folder structure
            let loginPath = '../login.html'; // Default assumption
            // Example logic (adjust as needed for your structure)
            if (window.location.pathname.includes('/financial_section/') ||
                window.location.pathname.includes('/students_section/') ||
                window.location.pathname.includes('/nathriyat_section/') || // Added nathriyat path
                window.location.pathname.includes('/defaults/')) { // Added defaults path
                loginPath = '../login.html'; // If inside a subfolder
            } else if (window.location.pathname.endsWith('/') || window.location.pathname.endsWith('.html')) {
                 // Check if it's likely in the root or a sibling directory structure needs different path
                 // This part might need specific adjustment based on where auth.js is called from relative to login.html
                 // For now, keep the default '../login.html' which assumes login is one level up.
                 // If login.html is in the root, and auth.js is called from root, it should be 'login.html'
                 // If auth.js is in root, and called from subfolder, '../login.html' is correct.
                 // If login.html is in root, and this script is called from root html, adjust path:
                 // Example: if current path is /index.html, login path should be 'login.html'
                 // A more robust check might be needed depending on deployment structure.
                 // Consider using absolute paths or a base URL if structure gets complex.
            }
            window.location.href = loginPath; // اضبط المسار بناءً على المكان الذي يتم استدعاء هذه الوظيفة منه
        });
    }

    // عرض اسم المستخدم اختياريًا إذا تم تخزينه
    const userDisplay = document.getElementById('navbar-username'); // Use 'const' keyword
    const userPhone = sessionStorage.getItem('loggedInUserPhone'); // Use 'const' keyword
    if (userDisplay && userPhone) { // Use 'if' keyword
        userDisplay.textContent = `مرحباً (${userPhone})`; // عرض المثال
    }
}

// مثال لكيفية إضافة زر تسجيل الخروج وعرض اسم المستخدم إلى شريط التنقل HTML:
/*
<nav class="main-navbar">
    <div class="navbar-brand">...</div>
    <ul class="navbar-links">...</ul>
    <div class="navbar-user">
        <span id="navbar-username"></span> // عنصر لعرض اسم المستخدم
        <button id="logout-btn" class="logout-btn" style="display: none;"> // مخفي افتراضيًا
            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
        </button>
    </div>
</nav>
*/

// ملاحظة: سوف تحتاج إلى استدعاء checkAuth() في بداية البرنامج النصي لكل صفحة محمية.
// مثال للاستخدام في نص برمجي آخر (على سبيل المثال, select_budget_year.js):
// checkAuth('../login.html'); // اضبط المسار حسب الحاجة
// // ... بقية البرنامج النصي للصفحة المحمية ...
