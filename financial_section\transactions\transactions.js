// Supabase Initialization (assuming config.js is loaded)
let supabaseUrl, supabaseAnonKey;
if (typeof SUPABASE_URL !== 'undefined') supabaseUrl = SUPABASE_URL;
else console.error('SUPABASE_URL not found.');
if (typeof SUPABASE_ANON_KEY !== 'undefined') supabaseAnonKey = SUPABASE_ANON_KEY;
else console.error('SUPABASE_ANON_KEY not found.');

const { createClient } = supabase;
const _supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('Supabase Initialized for Transactions:', _supabase);

// --- DOM Elements ---
const banksSidebarList = document.getElementById('banks-sidebar-list');
const manageBanksBtn = document.getElementById('manage-banks-btn');
const backToFinanceBtn = document.getElementById('back-to-finance-btn');

// Dashboard Cards
const totalDepositsCard = document.getElementById('total-deposits-card');
const totalWithdrawalsCard = document.getElementById('total-withdrawals-card');
const currentBalanceCard = document.getElementById('current-balance-card');
const selectedBankBalanceLabel = document.getElementById('selected-bank-balance-label');
const transactionChartPlaceholder = document.getElementById('transaction-chart-placeholder');

// Filters
const filterDateStart = document.getElementById('filter-date-start');
const filterDateEnd = document.getElementById('filter-date-end');
const filterType = document.getElementById('filter-type');
const filterBank = document.getElementById('filter-bank'); // Filter dropdown
const searchInput = document.getElementById('search-input');
const filterBtn = document.getElementById('filter-btn');
const resetFilterBtn = document.getElementById('reset-filter-btn');
const addTransactionBtn = document.getElementById('add-transaction-btn');
const filterMessage = document.getElementById('filter-message');

// Table
const transactionsTableBody = document.getElementById('transactions-tbody');
const transactionsCountBadge = document.getElementById('transactions-count');
const listMessage = document.getElementById('list-message');
const paginationControls = document.getElementById('pagination-controls');

// Transaction Form (Modal)
const transactionFormSection = document.getElementById('transaction-form-section');
const transactionForm = document.getElementById('transaction-form');
const transactionFormTitle = document.getElementById('transaction-form-title');
const closeTransactionFormBtn = document.getElementById('close-transaction-form-btn');
const cancelTransactionBtn = document.getElementById('cancel-transaction-btn');
const transactionIdField = document.getElementById('transaction_id');
const transactionDateField = document.getElementById('transaction_date');
const bankIdSelect = document.getElementById('bank_id'); // Form select
const transactionTypeSelect = document.getElementById('transaction_type');
const amountInput = document.getElementById('amount');
const descriptionInput = document.getElementById('description');
const transactionSourceTypeSelect = document.getElementById('transaction_source_type');
const referenceIdInput = document.getElementById('reference_id');
const transactionFormMessage = document.getElementById('transaction-form-message');

// --- State ---
let availableBanks = [];
let currentTransactions = [];
let selectedBankId = 'all'; // 'all' or bank ID
let currentPage = 1;
const itemsPerPage = 20;
let totalItems = 0;

// --- Functions ---

// Function to display messages (reuse from subscriptions or define here)
const showMessage = (element, message, type = 'info', duration = 5000) => {
    if (!element) return;
    element.textContent = message;
    element.className = `message ${type} show`;
    element.style.display = 'block';
    if (duration > 0 && (type === 'success' || (type !== 'error' && type !== 'warning'))) {
        setTimeout(() => {
            if (element.textContent === message) {
                element.style.display = 'none';
                element.classList.remove('show');
            }
        }, duration);
    }
};

// Function to format currency (reuse or define)
const formatCurrency = (amount) => {
    if (amount === null || amount === undefined || isNaN(amount)) return '0.00';
    return parseFloat(amount).toFixed(2);
};

// Function to format date (reuse or define)
const formatDate = (dateString) => {
    if (!dateString) return '';
    try {
        const date = new Date(dateString);
        const offset = date.getTimezoneOffset();
        const adjustedDate = new Date(date.getTime() - (offset * 60 * 1000));
        return adjustedDate.toISOString().split('T')[0];
    } catch (e) { return dateString; }
};

// Function to translate transaction type (optional)
const translateType = (type) => {
    switch (type) {
        case 'deposit': return 'إيداع';
        case 'withdrawal': return 'سحب';
        case 'transfer': return 'تحويل';
        default: return type || 'غير محدد';
    }
};

// --- Fetching Functions ---

// Fetch available banks
const fetchBanks = async () => {
    try {
        const { data, error } = await _supabase
            .from('banks')
            .select('id, name') // REMOVED: initial_balance
            .order('name', { ascending: true });
        if (error) throw error;
        availableBanks = data || [];
        populateBankDropdowns();
        populateBankSidebar();
    } catch (error) {
        console.error('Error fetching banks:', error);
        if (banksSidebarList) banksSidebarList.innerHTML = '<li class="message error">خطأ في تحميل البنوك</li>';
    }
};

// Populate bank dropdowns in filter and form
const populateBankDropdowns = () => {
    const selects = [filterBank, bankIdSelect];
    selects.forEach(select => {
        if (!select) return;
        // Keep the first option (All/Select)
        const firstOption = select.options[0];
        select.innerHTML = '';
        if (firstOption) select.appendChild(firstOption);

        availableBanks.forEach(bank => {
            const option = document.createElement('option');
            option.value = bank.id;
            option.textContent = bank.name;
            select.appendChild(option);
        });
    });
};

// Populate bank list in the sidebar
const populateBankSidebar = () => {
    if (!banksSidebarList) return;
    banksSidebarList.innerHTML = ''; // Clear previous content

    // Add "All Banks" option
    const allLi = document.createElement('li');
    const allLink = document.createElement('a');
    allLink.href = '#';
    allLink.textContent = 'كل البنوك';
    allLink.dataset.bankId = 'all';
    if (selectedBankId === 'all') allLink.classList.add('active');
    allLink.addEventListener('click', handleSidebarBankClick);
    allLi.appendChild(allLink);
    banksSidebarList.appendChild(allLi);

    // Add each bank
    availableBanks.forEach(bank => {
        const li = document.createElement('li');
        const link = document.createElement('a');
        link.href = '#';
        link.textContent = bank.name;
        link.dataset.bankId = bank.id;
        if (selectedBankId === bank.id.toString()) link.classList.add('active');
        link.addEventListener('click', handleSidebarBankClick);
        li.appendChild(link);
        banksSidebarList.appendChild(li);
    });
};

// Handle clicking on a bank in the sidebar
const handleSidebarBankClick = (event) => {
    event.preventDefault();
    const clickedLink = event.target;
    const bankId = clickedLink.dataset.bankId;

    if (bankId === selectedBankId) return; // No change

    selectedBankId = bankId;

    // Update active state in sidebar
    banksSidebarList.querySelectorAll('a').forEach(link => link.classList.remove('active'));
    clickedLink.classList.add('active');

    // Update filter dropdown to match sidebar selection
    if (filterBank) filterBank.value = selectedBankId === 'all' ? '' : selectedBankId;

    // Reset pagination and fetch data
    currentPage = 1;
    fetchTransactions();
};


// Fetch transactions based on filters and pagination
const fetchTransactions = async () => {
    if (!transactionsTableBody) return;
    transactionsTableBody.innerHTML = `<tr><td colspan="8" class="loading-message">جاري تحميل المعاملات...</td></tr>`;
    if (paginationControls) paginationControls.innerHTML = '';
    if (listMessage) listMessage.style.display = 'none';

    // Reset dashboard cards
    if (totalDepositsCard) totalDepositsCard.textContent = '0 ريال';
    if (totalWithdrawalsCard) totalWithdrawalsCard.textContent = '0 ريال';
    if (currentBalanceCard) currentBalanceCard.textContent = '0 ريال';
    if (selectedBankBalanceLabel) selectedBankBalanceLabel.textContent = selectedBankId === 'all' ? 'لكل البنوك' : availableBanks.find(b => b.id == selectedBankId)?.name || '';


    try {
        let query = _supabase
            .from('bank_transactions')
            .select('*, banks(name)', { count: 'exact' }); // Join with banks table

        // Apply Filters
        const dateStart = filterDateStart.value;
        const dateEnd = filterDateEnd.value;
        const type = filterType.value;
        const bankFilter = filterBank.value; // Use the filter dropdown value
        const searchTerm = searchInput.value.trim().toLowerCase();

        // Apply sidebar bank filter (takes precedence if set)
        if (selectedBankId !== 'all') {
            query = query.eq('bank_id', selectedBankId);
        } else if (bankFilter) { // Apply dropdown filter if 'all' is selected in sidebar but specific bank in dropdown
             query = query.eq('bank_id', bankFilter);
        }


        if (dateStart) query = query.gte('transaction_date', dateStart);
        if (dateEnd) query = query.lte('transaction_date', dateEnd);
        if (type) query = query.eq('transaction_type', type);
        if (searchTerm) query = query.ilike('description', `%${searchTerm}%`);

        // Ordering
        query = query.order('transaction_date', { ascending: false }).order('created_at', { ascending: false });

        // Pagination
        const startIndex = (currentPage - 1) * itemsPerPage;
        query = query.range(startIndex, startIndex + itemsPerPage - 1);

        const { data, error, count } = await query;

        if (error) throw error;

        currentTransactions = data || [];
        totalItems = count || 0;

        if (transactionsCountBadge) transactionsCountBadge.textContent = totalItems;

        renderTransactionsTable(currentTransactions);
        renderPaginationControls(); // Need to implement this based on totalItems
        calculateDashboardTotals(currentTransactions); // Calculate totals from fetched page data (approximation)
        // For accurate totals for the *entire* filtered range, a separate aggregate query might be needed.

        if (totalItems === 0) {
            showMessage(listMessage, 'لا توجد معاملات تطابق البحث أو الفلاتر.', 'info');
            transactionsTableBody.innerHTML = `<tr><td colspan="8" class="loading-message">لا توجد معاملات.</td></tr>`;
        }

    } catch (error) {
        console.error('Error fetching transactions:', error);
        showMessage(listMessage, `خطأ في جلب المعاملات: ${error.message}`, 'error', 0);
        transactionsTableBody.innerHTML = `<tr><td colspan="8" class="loading-message">خطأ في تحميل البيانات.</td></tr>`;
    }
};

// --- Rendering Functions ---

// Render table rows
const renderTransactionsTable = (data) => {
    transactionsTableBody.innerHTML = ''; // Clear existing rows
    if (!data || data.length === 0) return; // Message handled by fetchTransactions

    data.forEach(tx => {
        const row = document.createElement('tr');
        const typeClass = tx.transaction_type === 'deposit' ? 'success' : (tx.transaction_type === 'withdrawal' ? 'danger' : '');
        const amountPrefix = tx.transaction_type === 'deposit' ? '+' : (tx.transaction_type === 'withdrawal' ? '-' : '');

        row.innerHTML = `
            <td>${formatDate(tx.transaction_date)}</td>
            <td>${tx.banks?.name || 'غير محدد'}</td>
            <td data-type="${tx.transaction_type}" class="${typeClass}">${translateType(tx.transaction_type)}</td>
            <td data-type="${tx.transaction_type}" class="${typeClass}">${amountPrefix}${formatCurrency(tx.amount)}</td>
            <td>${tx.description || ''}</td>
            <td>${tx.transaction_source_type || ''}</td>
            <td>${tx.reference_id || ''}</td>
            <td>
                <button class="action-btn edit-btn" data-id="${tx.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                <button class="action-btn delete-btn" data-id="${tx.id}" title="حذف"><i class="fas fa-trash-alt"></i></button>
            </td>
        `;

        // Add event listeners for edit/delete buttons
        row.querySelector('.edit-btn').addEventListener('click', () => openTransactionModal(tx));
        row.querySelector('.delete-btn').addEventListener('click', () => handleDeleteTransaction(tx.id));

        transactionsTableBody.appendChild(row);
    });
};

// Render pagination controls (similar to subscriptions)
const renderPaginationControls = () => {
    if (!paginationControls || totalItems <= itemsPerPage) {
        if (paginationControls) paginationControls.innerHTML = '';
        return;
    }
    // ... (Copy pagination logic from subscriptions/script.js, adjusting goToPage call) ...
     paginationControls.innerHTML = ''; // Clear existing controls
    const totalPages = Math.ceil(totalItems / itemsPerPage);

    // Previous button
    const prevButton = document.createElement('button');
    prevButton.innerHTML = '<i class="fas fa-angle-right"></i>';
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', () => goToPage(currentPage - 1));
    paginationControls.appendChild(prevButton);

    // Page buttons logic (simplified)
    const maxPageButtons = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
    let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);
    if (endPage - startPage + 1 < maxPageButtons) {
        startPage = Math.max(1, endPage - maxPageButtons + 1);
    }

    if (startPage > 1) {
        const firstPageBtn = document.createElement('button');
        firstPageBtn.textContent = '1';
        firstPageBtn.addEventListener('click', () => goToPage(1));
        paginationControls.appendChild(firstPageBtn);
        if (startPage > 2) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'pagination-ellipsis';
            paginationControls.appendChild(ellipsis);
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.textContent = i.toString();
        pageBtn.className = i === currentPage ? 'active' : '';
        pageBtn.addEventListener('click', () => goToPage(i));
        paginationControls.appendChild(pageBtn);
    }

     if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
             const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'pagination-ellipsis';
            paginationControls.appendChild(ellipsis);
        }
        const lastPageBtn = document.createElement('button');
        lastPageBtn.textContent = totalPages.toString();
        lastPageBtn.addEventListener('click', () => goToPage(totalPages));
        paginationControls.appendChild(lastPageBtn);
    }

    // Next button
    const nextButton = document.createElement('button');
    nextButton.innerHTML = '<i class="fas fa-angle-left"></i>';
    nextButton.disabled = currentPage === totalPages;
    nextButton.addEventListener('click', () => goToPage(currentPage + 1));
    paginationControls.appendChild(nextButton);
};

// Navigate to a specific page
const goToPage = (page) => {
    if (page < 1 || page > Math.ceil(totalItems / itemsPerPage)) return;
    currentPage = page;
    fetchTransactions(); // Re-fetch data for the new page
};

// Calculate and display dashboard totals (based on current page data - needs improvement for full range)
const calculateDashboardTotals = (transactions) => {
    let deposits = 0;
    let withdrawals = 0;

    transactions.forEach(tx => {
        if (tx.transaction_type === 'deposit') {
            deposits += parseFloat(tx.amount || 0);
        } else if (tx.transaction_type === 'withdrawal') {
            withdrawals += parseFloat(tx.amount || 0);
        }
    });

    if (totalDepositsCard) totalDepositsCard.textContent = `${formatCurrency(deposits)} ريال`;
    if (totalWithdrawalsCard) totalWithdrawalsCard.textContent = `${formatCurrency(withdrawals)} ريال`;

    // Note: Calculating current balance accurately based only on filtered data is complex.
    // This requires either fetching ALL transactions for the bank(s) up to the end date
    // or having a starting balance and applying the filtered transactions.
    // For now, we'll leave it as an estimate or placeholder.
    // A better approach might involve a dedicated Supabase function (RPC).
    if (currentBalanceCard) currentBalanceCard.textContent = `بحاجة لحساب`; // Placeholder
};

// --- Transaction Form Modal ---

const openTransactionModal = (transaction = null) => {
    if (!transactionFormSection) return;
    resetTransactionForm();

    if (transaction) {
        // Edit mode
        transactionFormTitle.textContent = 'تعديل معاملة';
        transactionIdField.value = transaction.id;
        transactionDateField.value = formatDate(transaction.transaction_date);
        bankIdSelect.value = transaction.bank_id;
        transactionTypeSelect.value = transaction.transaction_type;
        amountInput.value = transaction.amount;
        descriptionInput.value = transaction.description || '';
        transactionSourceTypeSelect.value = transaction.transaction_source_type || '';
        referenceIdInput.value = transaction.reference_id || '';
    } else {
        // Add mode
        transactionFormTitle.textContent = 'إضافة معاملة جديدة';
        transactionDateField.value = formatDate(new Date()); // Default to today
        // Pre-fill from URL parameters if available (handled in initial load)
    }

    transactionFormSection.classList.add('show');
    document.body.style.overflow = 'hidden';
};

const closeTransactionModal = () => {
    if (!transactionFormSection) return;
    transactionFormSection.classList.remove('show');
    document.body.style.overflow = '';
};

const resetTransactionForm = () => {
    transactionForm.reset();
    transactionIdField.value = ''; // Clear hidden ID
    if (transactionFormMessage) transactionFormMessage.style.display = 'none';
    if (transactionFormMessage) transactionFormMessage.className = 'message';
    // Set default date?
    // transactionDateField.value = formatDate(new Date());
};

// Handle form submission (Add/Edit)
const handleTransactionSubmit = async (event) => {
    event.preventDefault();
    if (!transactionForm) return;

    const formData = new FormData(transactionForm);
    const transactionData = Object.fromEntries(formData.entries());
    const id = transactionData.transaction_id;

    // Basic validation
    if (!transactionData.transaction_date || !transactionData.bank_id || !transactionData.transaction_type || !transactionData.amount || !transactionData.description) {
        showMessage(transactionFormMessage, 'الرجاء ملء جميع الحقول المطلوبة (*).', 'error');
        return;
    }

    // Prepare data for Supabase (convert amount, handle optional fields)
    const dataToSave = {
        transaction_date: transactionData.transaction_date,
        bank_id: transactionData.bank_id,
        transaction_type: transactionData.transaction_type,
        amount: parseFloat(transactionData.amount),
        description: transactionData.description.trim(),
        transaction_source_type: transactionData.transaction_source_type || null,
        reference_id: transactionData.reference_id || null,
    };

    showMessage(transactionFormMessage, id ? 'جاري تحديث المعاملة...' : 'جاري إضافة المعاملة...', 'info');
    const submitBtn = transactionForm.querySelector('.submit-btn');
    if (submitBtn) submitBtn.disabled = true;

    try {
        let error;
        if (id) {
            // Update existing transaction
            const { error: updateError } = await _supabase
                .from('bank_transactions')
                .update(dataToSave)
                .eq('id', id);
            error = updateError;
        } else {
            // Insert new transaction
            const { error: insertError } = await _supabase
                .from('bank_transactions')
                .insert([dataToSave]);
            error = insertError;
        }

        if (error) throw error;

        showMessage(transactionFormMessage, id ? 'تم تحديث المعاملة بنجاح!' : 'تمت إضافة المعاملة بنجاح!', 'success');
        setTimeout(closeTransactionModal, 1500);
        fetchTransactions(); // Refresh the table

    } catch (error) {
        console.error('Error saving transaction:', error);
        showMessage(transactionFormMessage, `خطأ في حفظ المعاملة: ${error.message}`, 'error');
    } finally {
        if (submitBtn) submitBtn.disabled = false;
    }
};

// --- Delete Transaction ---
const handleDeleteTransaction = async (id) => {
    if (!id) return;
    const confirmation = confirm('هل أنت متأكد من حذف هذه المعاملة؟ لا يمكن التراجع عن هذا الإجراء.');
    if (confirmation) {
        showMessage(listMessage, 'جاري حذف المعاملة...', 'info', 0);
        try {
            const { error } = await _supabase
                .from('bank_transactions')
                .delete()
                .eq('id', id);
            if (error) throw error;
            showMessage(listMessage, 'تم حذف المعاملة بنجاح.', 'success');
            fetchTransactions(); // Refresh list
        } catch (error) {
            console.error('Error deleting transaction:', error);
            showMessage(listMessage, `خطأ في حذف المعاملة: ${error.message}`, 'error', 0);
        }
    }
};

// --- Handle URL Parameters ---
const handleUrlParameters = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const autoAdd = urlParams.get('amount') && urlParams.get('bankId') && urlParams.get('date'); // Check if essential params exist

    if (autoAdd) {
        console.log("URL parameters found for auto-add:", Object.fromEntries(urlParams.entries()));
        // Open the modal for adding
        openTransactionModal(); // Open in add mode

        // Pre-fill the form
        if (transactionDateField) transactionDateField.value = urlParams.get('date') || formatDate(new Date());
        if (bankIdSelect) bankIdSelect.value = urlParams.get('bankId') || '';
        if (transactionTypeSelect) transactionTypeSelect.value = urlParams.get('type') || 'deposit';
        if (amountInput) amountInput.value = urlParams.get('amount') || '';
        if (descriptionInput) descriptionInput.value = urlParams.get('description') || '';
        if (transactionSourceTypeSelect) transactionSourceTypeSelect.value = urlParams.get('source') || '';
        if (referenceIdInput) referenceIdInput.value = urlParams.get('refId') || '';

        // Optional: Remove parameters from URL after reading to avoid re-triggering on refresh
        // window.history.replaceState({}, document.title, window.location.pathname);
    }
};


// --- Event Listeners Setup ---
const setupEventListeners = () => {
    filterBtn.addEventListener('click', () => {
        currentPage = 1;
        fetchTransactions();
    });
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            currentPage = 1;
            fetchTransactions();
        }
    });
    resetFilterBtn.addEventListener('click', () => {
        filterDateStart.value = '';
        filterDateEnd.value = '';
        filterType.value = '';
        filterBank.value = ''; // Reset dropdown filter
        searchInput.value = '';
        selectedBankId = 'all'; // Reset sidebar selection state
        populateBankSidebar(); // Update sidebar visual state
        currentPage = 1;
        fetchTransactions();
        if (filterMessage) filterMessage.style.display = 'none';
    });

    // Auto-filter on dropdown change (optional)
    filterType.addEventListener('change', () => { currentPage = 1; fetchTransactions(); });
    filterBank.addEventListener('change', () => {
        // Sync sidebar if dropdown changes
        selectedBankId = filterBank.value || 'all';
        populateBankSidebar();
        currentPage = 1;
        fetchTransactions();
    });
    filterDateStart.addEventListener('change', () => { currentPage = 1; fetchTransactions(); });
    filterDateEnd.addEventListener('change', () => { currentPage = 1; fetchTransactions(); });


    // Modal listeners
    addTransactionBtn.addEventListener('click', () => openTransactionModal());
    closeTransactionFormBtn.addEventListener('click', closeTransactionModal);
    cancelTransactionBtn.addEventListener('click', closeTransactionModal);
    transactionFormSection.addEventListener('click', (e) => {
        if (e.target === transactionFormSection) closeTransactionModal();
    });
    transactionForm.addEventListener('submit', handleTransactionSubmit);

    // Back button listener (if exists)
    if (backToFinanceBtn) {
        backToFinanceBtn.addEventListener('click', () => {
            // Navigate back to the main financial section page
            window.location.href = '../financial_dashboard.html'; // Adjust path if needed
        });
    }

    // Manage banks button (placeholder)
    if (manageBanksBtn) {
        manageBanksBtn.addEventListener('click', () => {
             alert('سيتم توجيهك لصفحة إدارة البنوك (قيد الإنشاء).');
             // window.location.href = '../banks/banks.html'; // Example path
        });
    }

    // Global Escape Listener
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            if (transactionFormSection.classList.contains('show')) closeTransactionModal();
        }
    });
};

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOM loaded, initializing transactions page...');

    // Set default date range (e.g., current month) - Optional
    // const today = new Date();
    // const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    // filterDateStart.value = formatDate(firstDay);
    // filterDateEnd.value = formatDate(today);

    await fetchBanks(); // Fetch banks first to populate dropdowns/sidebar
    handleUrlParameters(); // Check for URL params and pre-fill form if needed
    await fetchTransactions(); // Fetch initial transactions
    setupEventListeners();
});
