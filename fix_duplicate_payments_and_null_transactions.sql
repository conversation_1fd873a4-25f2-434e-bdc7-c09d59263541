-- إصلاح مشكلة تكرار الدفعات والمعاملات البنكية NULL
-- المشكلة 1: تكرار إرسال الدفع
-- المشكلة 2: ظهور المعاملات البنكية حتى لو كان financial_transaction_log_id = NULL

-- الخطوة 1: إزالة جميع التريجرات الحالية لتجنب التضارب
DROP TRIGGER IF EXISTS student_payments_enhanced_trigger ON public.student_payments;
DROP TRIGGER IF EXISTS student_payments_trigger ON public.student_payments;
DROP TRIGGER IF EXISTS auto_cleanup_reversed_trigger ON public.financial_transactions_log;
DROP TRIGGER IF EXISTS financial_log_sync_trigger ON public.financial_transactions_log;
DROP TRIGGER IF EXISTS sync_financial_log_to_bank_transactions_trigger ON public.financial_transactions_log;

-- الخطوة 2: حذف المعاملات البنكية التي لها financial_transaction_log_id = NULL
DELETE FROM public.bank_transactions 
WHERE reference_table = 'student_payments' 
  AND financial_transaction_log_id IS NULL;

-- الخطوة 3: حذف الدفعات المكررة (الاحتفاظ بأحدث دفعة لكل طالب في نفس الشهر)
WITH duplicate_payments AS (
    SELECT id,
           ROW_NUMBER() OVER (
               PARTITION BY student_id, budget_month_id 
               ORDER BY created_at DESC, id DESC
           ) as rn
    FROM public.student_payments
)
DELETE FROM public.student_payments 
WHERE id IN (
    SELECT id FROM duplicate_payments WHERE rn > 1
);

-- الخطوة 4: إنشاء دالة محسنة لمعالجة دفعات الطلاب (بدون تكرار)
CREATE OR REPLACE FUNCTION public.handle_student_payment_changes_v2()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_description text;
    v_log_id uuid;
    v_old_log_id uuid;
    v_existing_payment_count integer;
BEGIN
    -- التحقق من عدم وجود دفعة مكررة للطالب في نفس الشهر (للإدراج فقط)
    IF TG_OP = 'INSERT' THEN
        -- التحقق من وجود دفعة أخرى لنفس الطالب في نفس الشهر
        SELECT COUNT(*) INTO v_existing_payment_count
        FROM public.student_payments 
        WHERE student_id = NEW.student_id 
          AND budget_month_id = NEW.budget_month_id
          AND id != NEW.id;
        
        -- إذا كان هناك دفعة موجودة، لا تنشئ سجلات جديدة
        IF v_existing_payment_count > 0 THEN
            RAISE NOTICE 'Duplicate payment detected for student % in month %. Skipping trigger.', NEW.student_id, NEW.budget_month_id;
            RETURN NEW;
        END IF;
        
        -- إنشاء سجل جديد في financial_transactions_log
        v_description := 'Student payment: ' || NEW.student_id::text || ' - Month: ' || NEW.budget_month_id::text;
        
        INSERT INTO public.financial_transactions_log (
            transaction_date, amount, transaction_type, description,
            source_table, source_record_id, bank_id, budget_month_id, is_reversal
        )
        VALUES (
            NEW.payment_date, NEW.amount, 'deposit', v_description,
            'student_payments', NEW.id::text, NEW.bank_id, NEW.budget_month_id, false
        )
        RETURNING id INTO v_log_id;
        
        -- إنشاء معاملة بنكية مرتبطة بالسجل
        INSERT INTO public.bank_transactions (
            bank_id, amount, transaction_type, transaction_date, description,
            transaction_source, reference_table, reference_id,
            budget_month_id, financial_transaction_log_id
        )
        VALUES (
            NEW.bank_id, NEW.amount, 'deposit', NEW.payment_date, v_description,
            'student_payments-' || NEW.id::text, 'student_payments', NEW.id,
            NEW.budget_month_id, v_log_id
        );
        
        RAISE NOTICE 'Created payment log % and bank transaction for payment %', v_log_id, NEW.id;
        
    ELSIF TG_OP = 'UPDATE' THEN
        -- التحقق من وجود تغييرات مهمة
        IF (OLD.amount IS DISTINCT FROM NEW.amount OR 
            OLD.bank_id IS DISTINCT FROM NEW.bank_id OR 
            OLD.payment_date IS DISTINCT FROM NEW.payment_date) THEN
            
            -- العثور على السجل القديم في financial_transactions_log
            SELECT id INTO v_old_log_id
            FROM public.financial_transactions_log
            WHERE source_table = 'student_payments' 
              AND source_record_id = OLD.id::text
              AND is_reversal = false
            ORDER BY created_at DESC
            LIMIT 1;
            
            -- إنشاء سجل عكسي للمعاملة القديمة
            IF v_old_log_id IS NOT NULL THEN
                v_description := 'Reversal for student payment: ' || OLD.student_id::text;
                
                INSERT INTO public.financial_transactions_log (
                    transaction_date, amount, transaction_type, description,
                    source_table, source_record_id, bank_id, budget_month_id, 
                    is_reversal, original_log_entry_id
                )
                VALUES (
                    OLD.payment_date, OLD.amount, 'withdrawal', v_description,
                    'student_payments', OLD.id::text, OLD.bank_id, OLD.budget_month_id, 
                    true, v_old_log_id
                );
                
                -- حذف المعاملة البنكية القديمة بدلاً من إلغاء الربط
                DELETE FROM public.bank_transactions 
                WHERE financial_transaction_log_id = v_old_log_id;
                
                RAISE NOTICE 'Created reversal entry and deleted old bank transaction for payment %', OLD.id;
            END IF;
            
            -- إنشاء سجل جديد للمعاملة المحدثة
            v_description := 'Student payment (updated): ' || NEW.student_id::text;
            
            INSERT INTO public.financial_transactions_log (
                transaction_date, amount, transaction_type, description,
                source_table, source_record_id, bank_id, budget_month_id, is_reversal
            )
            VALUES (
                NEW.payment_date, NEW.amount, 'deposit', v_description,
                'student_payments', NEW.id::text, NEW.bank_id, NEW.budget_month_id, false
            )
            RETURNING id INTO v_log_id;
            
            -- إنشاء معاملة بنكية جديدة مرتبطة بالسجل الجديد
            INSERT INTO public.bank_transactions (
                bank_id, amount, transaction_type, transaction_date, description,
                transaction_source, reference_table, reference_id,
                budget_month_id, financial_transaction_log_id
            )
            VALUES (
                NEW.bank_id, NEW.amount, 'deposit', NEW.payment_date, v_description,
                'student_payments-' || NEW.id::text, 'student_payments', NEW.id,
                NEW.budget_month_id, v_log_id
            );
            
            RAISE NOTICE 'Created new payment log % and bank transaction for updated payment %', v_log_id, NEW.id;
        END IF;
        
    ELSIF TG_OP = 'DELETE' THEN
        -- العثور على السجل في financial_transactions_log
        SELECT id INTO v_old_log_id
        FROM public.financial_transactions_log
        WHERE source_table = 'student_payments' 
          AND source_record_id = OLD.id::text
          AND is_reversal = false
        ORDER BY created_at DESC
        LIMIT 1;
        
        -- إنشاء سجل عكسي للحذف
        IF v_old_log_id IS NOT NULL THEN
            v_description := 'Deletion reversal for student payment: ' || OLD.student_id::text;
            
            INSERT INTO public.financial_transactions_log (
                transaction_date, amount, transaction_type, description,
                source_table, source_record_id, bank_id, budget_month_id, 
                is_reversal, original_log_entry_id
            )
            VALUES (
                OLD.payment_date, OLD.amount, 'withdrawal', v_description,
                'student_payments', OLD.id::text, OLD.bank_id, OLD.budget_month_id, 
                true, v_old_log_id
            );
            
            -- حذف المعاملة البنكية بدلاً من إلغاء الربط
            DELETE FROM public.bank_transactions 
            WHERE financial_transaction_log_id = v_old_log_id;
            
            RAISE NOTICE 'Created deletion reversal and deleted bank transaction for deleted payment %', OLD.id;
        END IF;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$;

-- الخطوة 5: إنشاء التريجر الجديد المحسن
CREATE TRIGGER student_payments_no_duplicate_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.student_payments
FOR EACH ROW EXECUTE FUNCTION public.handle_student_payment_changes_v2();

-- الخطوة 6: دالة لتنظيف المعاملات البنكية غير المربوطة
CREATE OR REPLACE FUNCTION public.cleanup_null_bank_transactions()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    -- حذف المعاملات البنكية التي لها financial_transaction_log_id = NULL
    DELETE FROM public.bank_transactions 
    WHERE reference_table = 'student_payments'
      AND financial_transaction_log_id IS NULL;
    
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
    
    RAISE NOTICE 'Deleted % NULL bank transactions', v_deleted_count;
    
    RETURN v_deleted_count;
END;
$$;

-- الخطوة 7: دالة للتحقق من عدم وجود دفعات مكررة
CREATE OR REPLACE FUNCTION public.check_duplicate_payments()
RETURNS TABLE(
    student_id UUID,
    budget_month_id UUID,
    payment_count INTEGER,
    payment_ids TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sp.student_id,
        sp.budget_month_id,
        COUNT(*)::INTEGER as payment_count,
        STRING_AGG(sp.id::text, ', ') as payment_ids
    FROM public.student_payments sp
    GROUP BY sp.student_id, sp.budget_month_id
    HAVING COUNT(*) > 1
    ORDER BY payment_count DESC;
END;
$$;

-- الخطوة 8: دالة للتحقق من صحة الربط (محدثة)
CREATE OR REPLACE FUNCTION public.verify_payment_linking_v2()
RETURNS TABLE(
    payment_id UUID,
    student_id UUID,
    payment_amount NUMERIC,
    has_financial_log BOOLEAN,
    has_bank_transaction BOOLEAN,
    bank_transaction_linked BOOLEAN,
    status TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sp.id as payment_id,
        sp.student_id,
        sp.amount as payment_amount,
        EXISTS(
            SELECT 1 FROM public.financial_transactions_log ftl 
            WHERE ftl.source_table = 'student_payments' 
              AND ftl.source_record_id = sp.id::text
              AND ftl.is_reversal = false
        ) as has_financial_log,
        EXISTS(
            SELECT 1 FROM public.bank_transactions bt 
            WHERE bt.reference_table = 'student_payments' 
              AND bt.reference_id = sp.id
        ) as has_bank_transaction,
        EXISTS(
            SELECT 1 FROM public.bank_transactions bt 
            WHERE bt.reference_table = 'student_payments' 
              AND bt.reference_id = sp.id
              AND bt.financial_transaction_log_id IS NOT NULL
        ) as bank_transaction_linked,
        CASE 
            WHEN EXISTS(
                SELECT 1 FROM public.bank_transactions bt 
                WHERE bt.reference_table = 'student_payments' 
                  AND bt.reference_id = sp.id
                  AND bt.financial_transaction_log_id IS NOT NULL
            ) THEN 'مربوط بشكل صحيح'
            WHEN EXISTS(
                SELECT 1 FROM public.bank_transactions bt 
                WHERE bt.reference_table = 'student_payments' 
                  AND bt.reference_id = sp.id
                  AND bt.financial_transaction_log_id IS NULL
            ) THEN 'معاملة بنكية غير مربوطة (يجب حذفها)'
            WHEN NOT EXISTS(
                SELECT 1 FROM public.bank_transactions bt 
                WHERE bt.reference_table = 'student_payments' 
                  AND bt.reference_id = sp.id
            ) THEN 'لا توجد معاملة بنكية'
            ELSE 'حالة غير معروفة'
        END as status
    FROM public.student_payments sp
    ORDER BY sp.created_at DESC;
END;
$$;

-- الخطوة 9: تنظيف البيانات الحالية
-- حذف المعاملات البنكية غير المربوطة
SELECT public.cleanup_null_bank_transactions();

-- الخطوة 10: إعادة ربط المعاملات البنكية الموجودة بالسجلات الصحيحة
UPDATE public.bank_transactions bt
SET financial_transaction_log_id = (
    SELECT ftl.id 
    FROM public.financial_transactions_log ftl
    WHERE ftl.source_table = 'student_payments'
      AND ftl.source_record_id = bt.reference_id::text
      AND ftl.is_reversal = false
    ORDER BY ftl.created_at DESC
    LIMIT 1
)
WHERE bt.reference_table = 'student_payments'
  AND bt.reference_id IS NOT NULL
  AND bt.financial_transaction_log_id IS NULL;

-- الخطوة 11: حذف المعاملات البنكية التي لا تزال غير مربوطة بعد المحاولة
DELETE FROM public.bank_transactions 
WHERE reference_table = 'student_payments'
  AND financial_transaction_log_id IS NULL;

-- الخطوة 12: عرض النتائج
SELECT 'تم إصلاح مشكلة التكرار والمعاملات NULL بنجاح!' as status;

-- عرض إحصائيات
SELECT 
    'دفعات الطلاب' as table_name,
    COUNT(*) as total_count
FROM public.student_payments

UNION ALL

SELECT 
    'سجلات المعاملات المالية' as table_name,
    COUNT(*) as total_count
FROM public.financial_transactions_log
WHERE source_table = 'student_payments'

UNION ALL

SELECT 
    'المعاملات البنكية المربوطة' as table_name,
    COUNT(*) as total_count
FROM public.bank_transactions
WHERE reference_table = 'student_payments'
  AND financial_transaction_log_id IS NOT NULL

UNION ALL

SELECT 
    'المعاملات البنكية غير المربوطة (NULL)' as table_name,
    COUNT(*) as total_count
FROM public.bank_transactions
WHERE reference_table = 'student_payments'
  AND financial_transaction_log_id IS NULL;

-- تعليمات الاستخدام
SELECT 'للتحقق من الدفعات المكررة: SELECT * FROM public.check_duplicate_payments();' as instruction
UNION ALL
SELECT 'للتحقق من صحة الربط: SELECT * FROM public.verify_payment_linking_v2();' as instruction
UNION ALL
SELECT 'لتنظيف المعاملات NULL: SELECT public.cleanup_null_bank_transactions();' as instruction;
