-- =============================================
-- جدول دفعات الطلاب الشهرية (مبسط)
-- =============================================
-- يخزن هذا الجدول كل دفعة فردية يقوم بها الطالب لشهر مالي محدد.
-- يربط الدفعة مباشرة بالطالب والشهر المالي والبنك.

CREATE TABLE public.student_payments (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY, -- المعرف الفريد للدفعة
    student_id uuid NOT NULL,                               -- معرف الطالب الذي قام بالدفعة (مفتاح أجنبي لجدول students)
    budget_month_id uuid NOT NULL,                          -- معرف الشهر المالي الذي تمت فيه الدفعة (مفتاح أجنبي لجدول budget_months)
    bank_id uuid NOT NULL,                                  -- معرف البنك الذي تمت عبره الدفعة (مفتاح أجنبي لجدول banks)
    amount numeric(10, 2) NOT NULL,                         -- مبلغ الدفعة
    payment_date date NOT NULL,                             -- تاريخ الدفعة
    payment_status text DEFAULT 'paid'::text NOT NULL,      -- حالة هذه الدفعة المحددة ('paid', 'partial', 'not_paid') - قد تحتاج لتعديل المنطق
    created_at timestamp with time zone DEFAULT now() NOT NULL, -- تاريخ إنشاء سجل الدفعة

    -- قيد للتحقق من أن المبلغ موجب (إذا كانت الحالة مدفوع أو جزئي)
    CONSTRAINT positive_payment_amount CHECK ( (payment_status = 'not_paid') OR (amount > 0) ),

    -- قيد للتحقق من قيم حالة الدفع المسموح بها
    CONSTRAINT valid_payment_status CHECK (payment_status IN ('paid', 'partial', 'not_paid')),

    -- الربط بجدول الطلاب
    CONSTRAINT student_payments_student_id_fkey FOREIGN KEY (student_id)
        REFERENCES public.students (id) ON UPDATE CASCADE ON DELETE CASCADE, -- عند حذف طالب، تحذف دفعاته

    -- الربط بجدول الشهور المالية
    CONSTRAINT student_payments_budget_month_id_fkey FOREIGN KEY (budget_month_id)
        REFERENCES public.budget_months (id) ON UPDATE CASCADE ON DELETE CASCADE, -- عند حذف شهر، تحذف دفعاته

    -- الربط بجدول البنوك
    CONSTRAINT student_payments_bank_id_fkey FOREIGN KEY (bank_id)
        REFERENCES public.banks (id) ON UPDATE CASCADE ON DELETE RESTRICT -- لا تسمح بحذف بنك إذا كانت هناك دفعات مرتبطة به
);

-- إضافة تعليقات توضيحية
COMMENT ON TABLE public.student_payments IS 'سجلات الدفعات الفردية للطلاب لشهر مالي محدد (هيكل مبسط).';
COMMENT ON COLUMN public.student_payments.student_id IS 'معرف الطالب الذي قام بالدفعة.';
COMMENT ON COLUMN public.student_payments.budget_month_id IS 'معرف الشهر المالي المرتبطة به هذه الدفعة.';
COMMENT ON COLUMN public.student_payments.bank_id IS 'معرف البنك المستخدم في الدفعة.';
COMMENT ON COLUMN public.student_payments.amount IS 'المبلغ المدفوع في هذه الدفعة.';
COMMENT ON COLUMN public.student_payments.payment_date IS 'تاريخ إجراء الدفعة.';
COMMENT ON COLUMN public.student_payments.payment_status IS 'حالة هذه الدفعة المحددة (paid, partial, not_paid). ملاحظة: لا يمثل الحالة الإجمالية للشهر.';

-- =============================================
-- ملاحظات هامة
-- =============================================
-- 1. حالة الدفع (payment_status): في هذا الهيكل، يمثل هذا الحقل حالة *هذه الدفعة الفردية*.
--    إذا أردت معرفة الحالة الإجمالية لاشتراك الطالب في الشهر (هل دفع المبلغ كاملاً أم لا)،
--    ستحتاج إلى حساب ذلك في كود التطبيق عن طريق:
--      أ. جلب المبلغ المتوقع للاشتراك (من جدول آخر مثل student_subscription_defaults).
--      ب. جمع كل الدفعات (`amount`) من هذا الجدول لنفس الطالب (`student_id`) ونفس الشهر (`budget_month_id`).
--      ج. مقارنة المجموع بالمبلغ المتوقع.
-- 2. صلاحيات الوصول (RLS): لا تنس تفعيل سياسات RLS لهذا الجدول في Supabase للسماح بعمليات INSERT للمستخدمين المصادق عليهم.

-- مثال لسياسة INSERT بسيطة:
/*
CREATE POLICY "Allow authenticated users to insert payments"
ON public.student_payments
FOR INSERT
TO authenticated
WITH CHECK (true);
*/

-- =============================================
-- مثال على كيفية إضافة دفعة
-- =============================================
/*
INSERT INTO public.student_payments
    (student_id, budget_month_id, bank_id, amount, payment_date, payment_status)
VALUES
    ('[STUDENT_UUID]', '[BUDGET_MONTH_UUID]', '[BANK_UUID]', [AMOUNT], '[YYYY-MM-DD]', 'paid'); -- أو 'partial'
*/

-- مثال عملي:
/*
INSERT INTO public.student_payments
    (student_id, budget_month_id, bank_id, amount, payment_date, payment_status)
VALUES
    ('a126dc80-c879-4e68-9132-aac50eb787e3', '********-cb33-489b-a75a-ca5a64ca8cfe', 'PUT_ACTUAL_BANK_ID_HERE', 500.00, '2025-04-15', 'partial');
*/
