<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة اشتراكات الطلاب</title>
    <link rel="icon" href="../../favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../shared_styles.css"> <!-- Optional: Link shared styles -->
    <link rel="stylesheet" href="student_subscriptions.css">
    <!-- Sidebar Component Styles -->
    <link rel="stylesheet" href="../../shared_components/sidebar.css">
    <!-- Google Fonts (Optional) -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
</head>
<body data-load-sidebar="true">
    <!-- Top Navigation Bar -->
    <nav class="top-navbar">
        <div class="navbar-left">
            <button id="sidebar-toggle" class="sidebar-toggle-btn">
                <i class="fas fa-bars"></i>
            </button>
            <div class="navbar-brand">
                <i class="fas fa-users"></i>
                <span>إدارة اشتراكات الطلاب</span>
            </div>
        </div>
        <div class="navbar-right">
            <div class="navbar-user">
                <span id="navbar-username"></span>
                <button id="logout-btn" class="logout-btn" style="display: none;">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Sidebar Container (will be populated by sidebar.js) -->
    <div id="sidebar-container"></div>

    <!-- Main Content Wrapper -->
    <div class="page-container with-sidebar"> <!-- Added 'with-sidebar' class -->

        <!-- Sidebar for Default Students -->
        <aside class="sidebar default-students-sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-star"></i> طلاب باشتراكات افتراضية</h3>
            </div>
            <div id="sidebar-message" class="message info" style="display: none; margin: 10px;"></div>
            <ul id="default-students-list" class="sidebar-list">
                <li class="loading-item"><i class="fas fa-spinner fa-spin"></i> تحميل...</li>
                <!-- Default students will be loaded here -->
            </ul>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content subscriptions-main"> <!-- Adjusted class -->

            <div id="page-message" class="message" style="display: none;"></div>

            <!-- Groups Section -->
            <section class="groups-section">
                <div class="section-header">
                    <div class="header-content">
                        <h2><i class="fas fa-layer-group"></i> مجموعات الطلاب</h2>
                        <p class="section-description">إدارة وتنظيم الطلاب في مجموعات لتسهيل متابعة الاشتراكات</p>
                        <div class="month-display">
                            <span class="month-label">الشهر المحدد:</span>
                            <span id="selected-month-display" class="month-value">جاري التحميل...</span>
                        </div>
                    </div>
                    <div class="header-stats">
                        <div class="stat-card groups-stat">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number" id="groups-count">0</span>
                                <span class="stat-label">مجموعة</span>
                            </div>
                        </div>
                        <div class="stat-card ungrouped-stat" id="ungrouped-students-btn" title="الطلاب غير المنضمين لمجموعات">
                            <div class="stat-icon warning">
                                <i class="fas fa-user-slash"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number" id="ungrouped-count">0</span>
                                <span class="stat-label">غير مجمع</span>
                            </div>
                        </div>
                        <div class="stat-card add-group-stat" id="add-group-btn" title="إضافة مجموعة جديدة">
                            <div class="stat-icon add">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">+</span>
                                <span class="stat-label">إضافة مجموعة</span>
                            </div>
                        </div>
                        <div class="stat-card back-stat" id="back-to-dashboard-btn" title="العودة للوحة المعلومات">
                            <div class="stat-icon back">
                                <i class="fas fa-arrow-left"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">←</span>
                                <span class="stat-label">العودة</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="groups-container">
                    <div id="groups-message" class="message" style="display: none;"></div>
                    <div id="groups-grid" class="groups-grid">
                        <!-- Group cards structure will be updated by JS -->
                        <div class="loading-state">
                            <div class="loading-animation">
                                <div class="loading-spinner"></div>
                                <div class="loading-dots">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                            </div>
                            <p class="loading-text">جاري تحميل المجموعات...</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Add Group Modal -->
            <div id="add-group-modal" class="modal">
                <div class="modal-content"> <!-- Smaller modal, adjust max-width in CSS if needed -->
                    <span class="close-modal-btn" id="close-add-group-modal-btn">&times;</span>
                    <h2><i class="fas fa-folder-plus"></i> إضافة مجموعة طلاب جديدة</h2>
                    <form id="add-group-form">
                        <div class="modal-body">
                            <div id="add-group-message-area" class="message" style="display: none;"></div>
                            <div class="form-group">
                                <label for="group_name">اسم المجموعة <span class="required">*</span></label>
                                <input type="text" id="group_name" name="group_name" required placeholder="أدخل اسم المجموعة الجديد">
                            </div>
                            <!-- Add other fields if needed in the future -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" id="cancel-add-group-btn" class="control-btn secondary-btn">إلغاء</button>
                            <button type="submit" id="confirm-add-group-btn" class="control-btn primary-btn">
                                <i class="fas fa-save"></i> حفظ المجموعة
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- == Add Student to Group Modal == -->
            <div id="add-student-to-group-modal" class="modal">
                <div class="modal-content">
                    <span class="close-modal-btn" id="close-add-student-modal-btn">&times;</span>
                    <h2><i class="fas fa-user-plus"></i> إضافة طالب إلى مجموعة: <span id="add-student-group-name"></span></h2>
                    <form id="add-student-to-group-form">
                        <div class="modal-body">
                            <div id="add-student-message-area" class="message" style="display: none;"></div>
                            <input type="hidden" id="add_student_group_id">
                            <div class="form-group">
                                <label for="select_student_to_add">اختر الطالب <span class="required">*</span></label>
                                <select id="select_student_to_add" name="student_id" required>
                                    <option value="">-- اختر طالباً --</option>
                                    <!-- Student options will be populated by JS -->
                                </select>
                                <small>سيتم عرض الطلاب الذين ليسوا في أي مجموعة حالياً.</small>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" id="cancel-add-student-btn" class="control-btn secondary-btn">إلغاء</button>
                            <button type="submit" id="confirm-add-student-btn" class="control-btn primary-btn">
                                <i class="fas fa-plus-circle"></i> إضافة الطالب للمجموعة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <!-- == End Add Student to Group Modal == -->

            <!-- == Add Default Students Modal (NEW) == -->
            <div id="add-default-students-modal" class="modal">
                <div class="modal-content">
                    <span class="close-modal-btn" id="close-add-default-students-modal-btn">&times;</span>
                    <h2><i class="fas fa-users-cog"></i> إضافة طلاب افتراضيين إلى مجموعة: <span id="add-default-students-group-name"></span></h2>
                    <form id="add-default-students-form">
                        <div class="modal-body">
                            <div id="add-default-students-message-area" class="message" style="display: none;"></div>
                            <input type="hidden" id="add_default_students_group_id" name="group_id">
                            <div class="form-group">
                                <label>الطلاب المتاحون (باشتراكات افتراضية):</label>
                                <div id="default-students-checkbox-list" class="checkbox-list-container">
                                    <!-- Checkboxes will be populated by JS -->
                                    <p class="empty-item">جاري تحميل الطلاب...</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" id="cancel-add-default-students-btn" class="control-btn secondary-btn">إلغاء</button>
                            <button type="submit" id="confirm-add-default-students-btn" class="control-btn primary-btn">
                                <i class="fas fa-users-cog"></i> إضافة المحدد للمجموعة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <!-- == End Add Default Students Modal == -->

            <!-- == Ungrouped Students Modal == -->
            <div id="ungrouped-students-modal" class="modal">
                <div class="modal-content">
                    <span class="close-modal-btn" id="close-ungrouped-modal-btn">&times;</span>
                    <h2><i class="fas fa-user-slash"></i> الطلاب غير المنضمين لمجموعات</h2>

                    <!-- Selection Controls -->
                    <div class="modal-controls">
                        <div class="selection-controls">
                            <button type="button" class="control-btn primary-btn" id="toggle-selection-mode">
                                <i class="fas fa-check-square"></i> تفعيل التحديد
                            </button>
                            <button type="button" class="control-btn secondary-btn" id="select-all-students" style="display: none;">
                                <i class="fas fa-check-double"></i> تحديد الكل
                            </button>
                            <button type="button" class="control-btn secondary-btn" id="deselect-all-students" style="display: none;">
                                <i class="fas fa-times"></i> إلغاء التحديد
                            </button>
                        </div>
                        <div class="selected-count" id="selected-students-count" style="display: none;">
                            <span class="badge badge-info">
                                <i class="fas fa-users"></i> محدد: <span id="selected-count-number">0</span>
                            </span>
                        </div>
                    </div>

                    <div class="modal-body">
                        <div id="ungrouped-students-message" class="message" style="display: none;"></div>

                        <!-- Students List View -->
                        <div id="students-list-view" class="ungrouped-students-container">
                            <div id="ungrouped-students-list" class="ungrouped-students-list">
                                <div class="loading-placeholder">
                                    <i class="fas fa-spinner fa-spin"></i> جاري تحميل الطلاب...
                                </div>
                            </div>
                        </div>

                        <!-- Groups Selection View -->
                        <div id="groups-selection-view" class="groups-selection-container" style="display: none;">
                            <h3><i class="fas fa-layer-group"></i> اختر المجموعة لإضافة الطلاب المحددين</h3>
                            <div id="groups-list" class="groups-list">
                                <div class="loading-placeholder">
                                    <i class="fas fa-spinner fa-spin"></i> جاري تحميل المجموعات...
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <div class="footer-left">
                            <button type="button" class="control-btn secondary-btn" id="close-ungrouped-modal-footer-btn">إغلاق</button>
                        </div>
                        <div class="footer-right">
                            <button type="button" class="control-btn success-btn" id="add-selected-to-group-btn" style="display: none;">
                                <i class="fas fa-plus-circle"></i> إضافة المحددين للمجموعة
                            </button>
                            <button type="button" class="control-btn primary-btn" id="back-to-students-btn" style="display: none;">
                                <i class="fas fa-arrow-right"></i> العودة للطلاب
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- == End Ungrouped Students Modal == -->

        </main> <!-- End Main Content Area -->

    </div> <!-- End Page Container -->

    <!-- Footer -->
    <footer class="main-footer">
         <p>&copy; 2024 نظام الإدارة المالية</p>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="../../config.js"></script>
    <script src="../../auth.js"></script>
    <!-- Sidebar Component Script -->
    <script src="../../shared_components/sidebar.js"></script>
    <script defer src="student_subscriptions.js"></script>

</body>
</html>
