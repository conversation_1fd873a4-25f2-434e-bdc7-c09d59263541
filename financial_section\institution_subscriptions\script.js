// --- التحقق من المصادقة ---
checkAuth('../../login.html'); // اضبط المسار حسب الحاجة

// --- تهيئة Supabase ---
let _supabase;
try {
    if (typeof supabase !== 'undefined' && supabase.createClient) {
        _supabase = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('تم تهيئة عميل Supabase لاشتراكات المؤسسات.');
        // --- تهيئة Linker ---
        if (typeof InstitutionSubscriptionsLinker !== 'undefined' && InstitutionSubscriptionsLinker.initialize) {
            InstitutionSubscriptionsLinker.initialize(_supabase);
        } else {
            console.error("InstitutionSubscriptionsLinker is not available or not initialized.");
        }
        // --- نهاية تهيئة Linker ---
    } else {
        console.error('لم يتم العثور على عميل Supabase.');
        // معالجة الخطأ
    }
} catch (error) {
    console.error('خطأ في تهيئة عميل Supabase:', error);
}

// --- عناصر DOM ---
const currentMonthYearDisplay = document.getElementById('current-month-year'); // عرض الشهر والسنة الحالية
const messageArea = document.getElementById('message-area'); // منطقة الرسائل
// const enterpriseFilter = document.getElementById('enterprise-filter'); // تمت إزالته
const statusFilter = document.getElementById('status-filter'); // فلتر الحالة
const addSubscriptionBtn = document.getElementById('add-subscription-btn'); // زر إضافة اشتراك
const refreshDataBtn = document.getElementById('refresh-data-btn'); // زر تحديث البيانات
const subscriptionsTableBody = document.getElementById('subscriptions-table-body'); // جسم جدول الاشتراكات
const prevPageBtn = document.getElementById('prev-page-btn'); // زر الصفحة السابقة
const nextPageBtn = document.getElementById('next-page-btn'); // زر الصفحة التالية
const pageInfo = document.getElementById('page-info'); // معلومات الصفحة

// عناصر الشريط الجانبي
const enterpriseListContainer = document.getElementById('enterprise-list'); // حاوية قائمة المؤسسات
const enterpriseSearchInput = document.getElementById('enterprise-search-input'); // حقل البحث عن المؤسسات

// عناصر قسم المجموعات (جديد)
const enterpriseGroupsSection = document.getElementById('enterprise-groups-section'); // قسم مجموعات المؤسسة
const enterpriseGroupsList = document.getElementById('enterprise-groups-list'); // قائمة مجموعات المؤسسة
const enterpriseGroupsTitle = document.getElementById('enterprise-groups-title'); // عنوان مجموعات المؤسسة

// عناصر النافذة المنبثقة (Modal)
const subscriptionModal = document.getElementById('subscription-modal'); // النافذة المنبثقة للاشتراك
const closeSubscriptionModalBtn = document.getElementById('close-subscription-modal'); // زر إغلاق نافذة الاشتراك
const modalTitle = document.getElementById('modal-title'); // عنوان النافذة المنبثقة
const subscriptionForm = document.getElementById('subscription-form'); // نموذج الاشتراك
const subscriptionIdInput = document.getElementById('subscription-id'); // حقل معرف الاشتراك
const modalEnterpriseSelect = document.getElementById('modal-enterprise'); // قائمة اختيار المؤسسة في النافذة المنبثقة (يُحتفظ به للنافذة)
const modalServiceTypeInput = document.getElementById('modal-service-type'); // حقل نوع الخدمة في النافذة المنبثقة
const modalTotalAmountInput = document.getElementById('modal-total-amount'); // حقل المبلغ الإجمالي في النافذة المنبثقة
const modalPaidAmountInput = document.getElementById('modal-paid-amount'); // حقل المبلغ المدفوع في النافذة المنبثقة
const modalSubscriptionDateInput = document.getElementById('modal-subscription-date'); // حقل تاريخ الاشتراك في النافذة المنبثقة
const modalPaymentDateInput = document.getElementById('modal-payment-date'); // حقل تاريخ الدفع في النافذة المنبثقة
const modalNotesInput = document.getElementById('modal-notes'); // حقل الملاحظات في النافذة المنبثقة
const modalMessage = document.getElementById('modal-message'); // رسالة النافذة المنبثقة
const saveSubscriptionBtn = document.getElementById('save-subscription-btn'); // زر حفظ الاشتراك
const cancelSubscriptionBtn = document.getElementById('cancel-subscription-btn'); // زر إلغاء الاشتراك
const groupDetailsModal = document.getElementById('group-details-modal'); // النافذة المنبثقة لتفاصيل المجموعة
const closeGroupDetailsModalBtn = document.getElementById('close-group-details-modal'); // زر إغلاق نافذة تفاصيل المجموعة
const cancelGroupDetailsBtn = document.getElementById('cancel-group-details-btn'); // زر إلغاء نافذة تفاصيل المجموعة
const groupDetailsModalTitle = document.getElementById('group-details-modal-title'); // عنوان نافذة تفاصيل المجموعة
const groupDetailsModalBody = document.getElementById('group-details-modal-body'); // محتوى نافذة تفاصيل المجموعة

// --- عناصر نافذة الدفع المنبثقة ---
const groupPaymentModal = document.getElementById('group-payment-modal'); // النافذة المنبثقة لدفع المجموعة
const closeGroupPaymentModalBtn = document.getElementById('close-group-payment-modal'); // زر إغلاق نافذة دفع المجموعة
const cancelGroupPaymentBtn = document.getElementById('cancel-group-payment-btn'); // زر إلغاء نافذة دفع المجموعة
const confirmGroupPaymentBtn = document.getElementById('confirm-group-payment-btn'); // زر تأكيد دفع المجموعة
const groupPaymentForm = document.getElementById('group-payment-form'); // نموذج دفع المجموعة
const groupPaymentGroupName = document.getElementById('group-payment-group-name'); // اسم مجموعة الدفع
const groupPaymentGroupId = document.getElementById('group-payment-group-id'); // معرف مجموعة الدفع
const groupPaymentAmount = document.getElementById('group-payment-amount'); // مبلغ دفع المجموعة
const groupPaymentDate = document.getElementById('group-payment-date'); // تاريخ دفع المجموعة
const groupPaymentNotes = document.getElementById('group-payment-notes'); // ملاحظات دفع المجموعة
const groupPaymentMessage = document.getElementById('group-payment-message'); // رسالة نافذة دفع المجموعة

// الحقول الجديدة لنافذة دفع المجموعة (تمت إضافتها)
const groupPaymentTotalAmountInput = document.getElementById('group-payment-total-amount');
const groupPaymentCurrentPaidInput = document.getElementById('group-payment-current-paid');
const groupPaymentRemainingInput = document.getElementById('group-payment-remaining-amount');
const groupPaymentStatusSelect = document.getElementById('group-payment-status');
const groupPaymentSubscriptionDateInput = document.getElementById('group-payment-subscription-date');
const groupPaymentBankSelect = document.getElementById('group-payment-bank'); // عنصر اختيار البنك الجديد

// عناصر قسم إقفال الشهر (جديد)
const monthClosingSection = document.getElementById('month-closing-section');
const selectedEnterpriseForClosingDisplay = document.getElementById('selected-enterprise-for-closing');
const currentMonthForClosingDisplay = document.getElementById('current-month-for-closing');
const monthClosingStatusDisplay = document.getElementById('month-closing-status');
const monthClosingMessage = document.getElementById('month-closing-message');
const closeMonthBtn = document.getElementById('close-month-btn');
const reopenMonthBtn = document.getElementById('reopen-month-btn');

// --- الحالة ---
let selectedMonthId = null; // معرف الشهر المحدد
let selectedMonthNumber = null; // رقم الشهر المحدد
let selectedYearNumber = null; // رقم السنة المحددة
let currentPage = 1; // الصفحة الحالية
const ITEMS_PER_PAGE = 15; // عدد العناصر في كل صفحة
let currentSubscriptions = []; // الاشتراكات الحالية
let totalItems = 0; // إجمالي عدد العناصر
let enterprisesMap = {}; // يُحتفظ به لربط المعرف بالاسم لعرض الجدول والقائمة المنسدلة في النافذة المنبثقة
let selectedEnterpriseId = null; // معرف المؤسسة المحددة في الشريط الجانبي (null لـ 'الكل')
let allEnterprises = []; // تخزين جميع المؤسسات التي تم جلبها لفلترة الشريط الجانبي
let currentUserId = null; // لتخزين معرف المستخدم الحالي (جديد)

// --- دوال مساعدة ---

/**
 * @function showMessage
 * @description دالة لعرض رسالة للمستخدم في عنصر محدد.
 * @param {HTMLElement} element - العنصر الذي ستُعرض فيه الرسالة.
 * @param {string} message - نص الرسالة.
 * @param {string} [type='info'] - نوع الرسالة (info, success, error).
 * @param {number} [duration=4000] - مدة عرض الرسالة بالمللي ثانية (0 لعدم الإخفاء التلقائي).
 */
const showMessage = (element, message, type = 'info', duration = 4000) => {
    if (!element) return;
    element.textContent = message;
    element.className = `message ${type} show`;
    element.style.display = 'block';

    if (element.timeoutId) clearTimeout(element.timeoutId);

    if (duration > 0) {
        element.timeoutId = setTimeout(() => {
            element.style.display = 'none';
            element.textContent = '';
            element.className = 'message';
        }, duration);
    }
};

/**
 * @function formatCurrency
 * @description دالة لتنسيق الأرقام كعملة.
 * @param {number|string} amount - المبلغ المراد تنسيقه.
 * @returns {string} - المبلغ المنسق كعملة (مثال: "123.45").
 */
const formatCurrency = (amount) => {
    const value = parseFloat(amount);
    return isNaN(value) ? '0.00' : value.toFixed(2);
};

/**
 * @function formatDate
 * @description دالة لتنسيق سلسلة التاريخ.
 * @param {string} dateString - سلسلة التاريخ المراد تنسيقها.
 * @returns {string} - التاريخ المنسق أو السلسلة الأصلية إذا كانت غير صالحة.
 */
const formatDate = (dateString) => {
    if (!dateString) return '---';
    try {
        const date = new Date(dateString);
        // اضبط المنطقة الزمنية إذا لزم الأمر، وإلا فقم بالتنسيق فقط
        return date.toLocaleDateString('ar-EG'); // مثال على التنسيق
    } catch (e) {
        return dateString; // إرجاع الأصل إذا كان غير صالح
    }
};

/**
 * @function getMonthName
 * @description دالة للحصول على اسم الشهر باللغة العربية من رقمه.
 * @param {number|string} monthNumber - رقم الشهر (1-12).
 * @returns {string} - اسم الشهر باللغة العربية.
 */
const getMonthName = (monthNumber) => {
    const names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"];
    const index = parseInt(monthNumber, 10) - 1;
    return names[index] || `شهر ${monthNumber}`;
};

/**
 * @function getStatusBadge
 * @description دالة لإنشاء شارة (badge) لحالة الدفع.
 * @param {string} status - حالة الدفع ('paid', 'not_paid', 'partially_paid').
 * @returns {string} - شفرة HTML لشارة الحالة.
 */
const getStatusBadge = (status) => {
    switch (status) {
        case 'paid': return '<span class="status-badge status-paid">مدفوع</span>';
        case 'not_paid': return '<span class="status-badge status-not_paid">غير مدفوع</span>';
        case 'partially_paid': return '<span class="status-badge status-partially_paid">مدفوع جزئياً</span>';
        default: return `<span class="status-badge">${status || 'غير معروف'}</span>`;
    }
};

/**
 * @function formatTime
 * @description دالة مساعدة لتنسيق الوقت (HH:MM).
 * @param {string} timeString - سلسلة الوقت (مثال: "14:30").
 * @returns {string} - الوقت المنسق أو السلسلة الأصلية عند الخطأ.
 */
const formatTime = (timeString) => {
    if (!timeString) return '---';
    try {
        const [hours, minutes] = timeString.split(':');
        // تحقق أساسي من أجزاء الوقت الصالحة
        if (hours && minutes) {
            // تحويل إلى تنسيق 12 ساعة مع AM/PM (اختياري)
            let hours12 = parseInt(hours, 10);
            const ampm = hours12 >= 12 ? 'م' : 'ص';
            hours12 = hours12 % 12;
            hours12 = hours12 ? hours12 : 12; // التعامل مع منتصف الليل (00 -> 12)
            return `${hours12}:${minutes} ${ampm}`;
        }
        return timeString; // إرجاع الأصل إذا كان التنسيق غير متوقع
    } catch (e) {
        return timeString; // إرجاع الأصل عند الخطأ
    }
};

/**
 * @function isUrl
 * @description دالة مساعدة للتحقق مما إذا كانت السلسلة النصية عنوان URL محتمل.
 * @param {string} string - السلسلة النصية للتحقق.
 * @returns {boolean} - `true` إذا كانت السلسلة عنوان URL، وإلا `false`.
 */
const isUrl = (string) => {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
};

// --- إدارة النافذة المنبثقة ---

/**
 * @function openModal
 * @description دالة لفتح النافذة المنبثقة لإضافة أو تعديل اشتراك.
 * @param {object|null} [subscription=null] - كائن الاشتراك للتعديل، أو null للإضافة.
 */
const openModal = (subscription = null) => {
    console.log('تم استدعاء دالة openModal. الاشتراك:', subscription); // <-- إضافة سجل
    subscriptionForm.reset();
    subscriptionIdInput.value = '';
    modalMessage.style.display = 'none';
    populateModalEnterpriseDropdown(); // التأكد من أن تعبئة القائمة المنسدلة في النافذة لا تزال تعمل

    if (subscription) {
        modalTitle.textContent = 'تعديل اشتراك منشأة'; // تم تغيير الاسم
        subscriptionIdInput.value = subscription.id;
        modalEnterpriseSelect.value = subscription.enterprise_id; // حقل مُعاد تسميته
        modalServiceTypeInput.value = subscription.service_type || '';
        modalTotalAmountInput.value = subscription.total_amount || '';
        modalPaidAmountInput.value = subscription.paid_amount || '';
        modalSubscriptionDateInput.value = subscription.subscription_date || '';
        modalPaymentDateInput.value = subscription.payment_date || '';
        modalNotesInput.value = subscription.notes || '';
    } else {
        modalTitle.textContent = 'إضافة اشتراك منشأة'; // تم تغيير الاسم
        // تعيين تاريخ الاشتراك الافتراضي إلى اليوم؟
        modalSubscriptionDateInput.value = new Date().toISOString().split('T')[0];
    }

    // تحديث عرض المبلغ المتبقي بعد تعبئة البيانات
    setTimeout(updateRemainingAmount, 100);

    subscriptionModal.classList.add('show');
};

/**
 * @function closeModal
 * @description دالة لإغلاق النافذة المنبثقة للاشتراك.
 */
const closeModal = () => {
    subscriptionModal.classList.remove('show');
};

/**
 * @function populateModalEnterpriseDropdown
 * @description دالة لتعبئة القائمة المنسدلة للمؤسسات داخل النافذة المنبثقة.
 */
const populateModalEnterpriseDropdown = () => {
    if (!modalEnterpriseSelect) return;
    // الحفاظ على الخيار الأول ("اختر...")
    const firstOption = modalEnterpriseSelect.options[0];
    modalEnterpriseSelect.innerHTML = '';
    if (firstOption) modalEnterpriseSelect.appendChild(firstOption);

    // فرز المؤسسات حسب الاسم قبل إضافتها
    const sortedEnterprises = Object.entries(enterprisesMap).sort(([, aName], [, bName]) => aName.localeCompare(bName));

    sortedEnterprises.forEach(([id, name]) => {
        const option = document.createElement('option');
        option.value = id;
        option.textContent = name;
        modalEnterpriseSelect.appendChild(option);
    });
};

// --- نافذة تفاصيل المجموعة ---

/**
 * @function openGroupDetailsModal
 * @description دالة لفتح النافذة المنبثقة لعرض تفاصيل مجموعة معينة.
 * @param {string|number} groupId - معرف المجموعة.
 * @param {string} groupName - اسم المجموعة.
 */
const openGroupDetailsModal = async (groupId, groupName) => {
    if (!groupDetailsModal || !groupDetailsModalTitle || !groupDetailsModalBody) return;

    groupDetailsModalTitle.textContent = `تفاصيل تكلفة: ${groupName}`;
    groupDetailsModalBody.innerHTML = '<div class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i> جاري تحميل التفاصيل...</div>';
    groupDetailsModal.classList.add('show');

    if (!_supabase) {
        groupDetailsModalBody.innerHTML = '<div class="loading-placeholder error">خطأ في الاتصال.</div>';
        return;
    }

    try {
        // جلب تفاصيل المجموعة المحددة مرة أخرى (يمكن تحسينه بتمرير البيانات)
        const { data: group, error } = await _supabase
            .from('enterprise_groups')
            .select('*')
            .eq('id', groupId)
            .single();

        if (error) throw error;
        if (!group) {
             groupDetailsModalBody.innerHTML = '<div class="loading-placeholder error">لم يتم العثور على المجموعة.</div>';
             return;
        }

        // عرض تفاصيل تكلفة المجموعة
        // ملاحظة: تفاصيل الدفع الشهري (المدفوع، المتبقي، التاريخ، البنك) ليست في enterprise_groups
        // نعرض هيكل التكلفة الأساسي هنا.
        groupDetailsModalBody.innerHTML = `
            <p><strong>اسم المجموعة:</strong> ${group.name || '---'}</p>
            <p><strong>التكلفة الأساسية:</strong> ${formatCurrency(group.cost)} ريال</p>
            ${group.has_vat ? `
                <p><strong>نسبة الضريبة:</strong> ${group.vat_percentage || 0}%</p>
                <p><strong>مبلغ الضريبة:</strong> ${formatCurrency(group.vat_amount)} ريال</p>
                <p><strong>الإجمالي شامل الضريبة:</strong> ${formatCurrency(group.total_cost_with_vat)} ريال</p>
            ` : `
                <p><strong>الإجمالي (لا توجد ضريبة):</strong> ${formatCurrency(group.cost)} ريال</p>
            `}
            <hr>
            <p><strong>تفاصيل الدفع الشهري:</strong></p>
            <p><strong>المبلغ المدفوع هذا الشهر:</strong> <span class="text-muted">(غير متوفر حالياً)</span></p>
            <p><strong>المبلغ المتبقي هذا الشهر:</strong> <span class="text-muted">(غير متوفر حالياً)</span></p>
            <p><strong>تاريخ آخر دفعة:</strong> <span class="text-muted">(غير متوفر حالياً)</span></p>
            <p><strong>البنك المستخدم:</strong> <span class="text-muted">(غير متوفر حالياً)</span></p>
            ${group.notes ? `<hr><p><strong>ملاحظات المجموعة:</strong> ${group.notes}</p>` : ''}
        `;

    } catch (error) {
        console.error('خطأ في جلب تفاصيل المجموعة للنافذة المنبثقة:', error);
        groupDetailsModalBody.innerHTML = `<div class="loading-placeholder error">فشل تحميل التفاصيل: ${error.message}</div>`;
    }
};

/**
 * @function closeGroupDetailsModal
 * @description دالة لإغلاق النافذة المنبثقة لتفاصيل المجموعة.
 */
const closeGroupDetailsModal = () => {
    if (groupDetailsModal) {
        groupDetailsModal.classList.remove('show');
    }
};

// --- منطق نافذة الدفع ---

/**
 * @function openGroupPaymentModal
 * @description دالة لفتح نافذة تسجيل دفعة لمجموعة معينة.
 * @param {string|number} groupId - معرف المجموعة.
 * @param {string} groupName - اسم المجموعة.
 */
async function openGroupPaymentModal(groupId, groupName) { // Make async
    // جلب وتعبئة البنوك
    if (typeof InstitutionSubscriptionsLinker !== 'undefined') {
        try {
            await InstitutionSubscriptionsLinker.fetchBanksForPaymentModal();
            InstitutionSubscriptionsLinker.populateBankSelectInPaymentModal('group-payment-bank');
        } catch (e) {
            console.error("Error fetching/populating banks in payment modal:", e);
            if (groupPaymentBankSelect) {
                groupPaymentBankSelect.innerHTML = '<option value="" disabled selected>خطأ في تحميل البنوك</option>';
            }
        }
    } else {
        console.error("InstitutionSubscriptionsLinker is not available for fetching banks.");
        if (groupPaymentBankSelect) {
            groupPaymentBankSelect.innerHTML = '<option value="" disabled selected>خطأ في تحميل البنوك</option>';
        }
    }
    console.log('[openGroupPaymentModal] Function called. groupId:', groupId, 'groupName:', groupName);
    console.log('[openGroupPaymentModal] groupPaymentModal DOM element:', groupPaymentModal);

    if (!groupPaymentModal) {
        console.error('[openGroupPaymentModal] ERROR: groupPaymentModal element is NULL. Modal cannot be opened.');
        return;
    }
    groupPaymentForm.reset();
    groupPaymentMessage.style.display = 'none';
    groupPaymentGroupName.textContent = groupName || '...';
    groupPaymentGroupId.value = groupId;
    confirmGroupPaymentBtn.disabled = false;
    confirmGroupPaymentBtn.innerHTML = '<i class="fas fa-check"></i> تسجيل الدفعة';

    // مسح الحقول الجديدة قبل تعبئتها
    if (groupPaymentTotalAmountInput) groupPaymentTotalAmountInput.value = '';
    if (groupPaymentCurrentPaidInput) groupPaymentCurrentPaidInput.value = '';
    if (groupPaymentRemainingInput) groupPaymentRemainingInput.value = '';
    if (groupPaymentStatusSelect) groupPaymentStatusSelect.value = ''; // أو قيمة افتراضية
    if (groupPaymentSubscriptionDateInput) groupPaymentSubscriptionDateInput.value = '';


    (async () => {
        if (!_supabase || !selectedMonthId || !selectedEnterpriseId || !selectedYearNumber || !selectedMonthNumber) { // التأكد من وجود selectedYearNumber و selectedMonthNumber
            showMessage(groupPaymentMessage, 'خطأ: بيانات الشهر أو المؤسسة أو السنة غير مكتملة.', 'error');
            return;
        }

        try {
            // 1. جلب تفاصيل تكلفة المجموعة الحالية
            const { data: groupData, error: groupError } = await _supabase
                .from('enterprise_groups')
                .select('total_cost_with_vat, name')
                .eq('id', groupId)
                .eq('enterprise_id', selectedEnterpriseId)
                .single();

            if (groupError || !groupData) {
                console.error('Error fetching group details for modal:', groupError);
                showMessage(groupPaymentMessage, 'لم يتم العثور على تفاصيل المجموعة المحددة.', 'error');
                return;
            }
            const currentGroupCost = parseFloat(groupData.total_cost_with_vat) || 0;

            // 2. تحديد الشهر السابق
            let previousMonthNumber = parseInt(selectedMonthNumber, 10) - 1;
            let previousYearNumber = parseInt(selectedYearNumber, 10);
            if (previousMonthNumber === 0) { // إذا كان الشهر الحالي يناير
                previousMonthNumber = 12; // الشهر السابق هو ديسمبر
                previousYearNumber -= 1;  // من السنة السابقة
            }

            console.log(`[openGroupPaymentModal] Current month: ${selectedMonthNumber}/${selectedYearNumber}. Previous month for balance: ${previousMonthNumber}/${previousYearNumber}`);

            let previousMonthClosingBalance = 0;

            // 2a. جلب budget_year_id للسنة السابقة (أو الحالية إذا كان الشهر السابق في نفس السنة)
            const { data: budgetYearForPrevMonth, error: budgetYearError } = await _supabase
                .from('budget_years')
                .select('id')
                .eq('year_number', previousYearNumber)
                .single();

            if (budgetYearError || !budgetYearForPrevMonth) {
                console.warn(`[openGroupPaymentModal] Budget year ${previousYearNumber} not found. Cannot fetch previous month's closing balance.`, budgetYearError);
            } else {
                // 2b. جلب budget_month_id للشهر السابق
                const { data: previousBudgetData, error: previousMonthError } = await _supabase
                    .from('budget_months')
                    .select('id') // We need the ID of the previous budget_month
                    .eq('budget_year_id', budgetYearForPrevMonth.id)
                    .eq('month_number', previousMonthNumber)
                    .single();

                if (previousMonthError || !previousBudgetData) {
                    console.warn(`[openGroupPaymentModal] Budget month ${previousMonthNumber}/${previousYearNumber} (Year ID: ${budgetYearForPrevMonth.id}) not found. Cannot fetch previous month's closing balance.`, previousMonthError);
                } else {
                    const previousBudgetMonthIdToQuery = previousBudgetData.id;
                    // 2c. جلب الرصيد المقفل للشهر السابق من enterprise_monthly_closings
                    console.log(`[openGroupPaymentModal] Querying enterprise_monthly_closings for enterprise_id: ${selectedEnterpriseId}, budget_month_id: ${previousBudgetMonthIdToQuery}`);
                    const { data: prevClosing, error: prevClosingError } = await _supabase
                        .from('enterprise_monthly_closings') // اسم الجدول الجديد
                        .select('closing_balance')
                        .eq('enterprise_id', selectedEnterpriseId)
                        .eq('budget_month_id', previousBudgetMonthIdToQuery)
                        .eq('is_closed', true) // التأكد من أن الشهر السابق مقفل
                        .maybeSingle();

                    if (prevClosingError) {
                        console.error('[openGroupPaymentModal] Error fetching previous month closing balance:', prevClosingError);
                    } else if (prevClosing) {
                        previousMonthClosingBalance = parseFloat(prevClosing.closing_balance) || 0;
                        console.log(`[openGroupPaymentModal] Previous month (${previousMonthNumber}/${previousYearNumber}) closing balance for enterprise ${selectedEnterpriseId}: ${previousMonthClosingBalance}`);
                        if (previousMonthClosingBalance > 0) {
                           showMessage(groupPaymentMessage, `تم ترحيل رصيد سابق: ${formatCurrency(previousMonthClosingBalance)} ريال`, 'info', 5000);
                        }
                    } else {
                        console.log(`[openGroupPaymentModal] No *closed* previous month record found in enterprise_monthly_closings for budget_month_id ${previousBudgetMonthIdToQuery}, enterprise ${selectedEnterpriseId}.`);
                    }
                }
            }

            // 3. حساب الإجمالي المستحق الجديد
            const totalDueForGroupInModal = currentGroupCost + previousMonthClosingBalance;
            if (groupPaymentTotalAmountInput) {
                groupPaymentTotalAmountInput.value = formatCurrency(totalDueForGroupInModal);
            }
            // تعبئة حقل المبلغ المطلوب دفعه مبدئياً بتكلفة المجموعة الحالية
            if (groupPaymentAmount) {
                groupPaymentAmount.value = formatCurrency(currentGroupCost);
            }

            // 4. جلب الاشتراك الرئيسي للمؤسسة للشهر *الحالي*
            console.log('[openGroupPaymentModal] Attempting to fetch *current* enterprise subscription with:');
            console.log(`  Enterprise ID (selectedEnterpriseId): ${selectedEnterpriseId}`);
            console.log(`  Budget Month ID (selectedMonthId): ${selectedMonthId}`);

            const { data: enterpriseSubscription, error: subscriptionError } = await _supabase
                .from('enterprise_subscriptions')
                .select('id, total_amount, paid_amount, payment_status, subscription_date, notes, payment_bank_id') // جلب payment_bank_id
                .eq('enterprise_id', selectedEnterpriseId)
                .eq('budget_month_id', selectedMonthId)
                .maybeSingle();

            if (subscriptionError && subscriptionError.code !== 'PGRST116') throw subscriptionError;

            let currentMonthPaidAmount = 0;
            if (enterpriseSubscription) {
                currentMonthPaidAmount = parseFloat(enterpriseSubscription.paid_amount) || 0;
                if (groupPaymentCurrentPaidInput) groupPaymentCurrentPaidInput.value = formatCurrency(currentMonthPaidAmount);
                if (groupPaymentStatusSelect) groupPaymentStatusSelect.value = enterpriseSubscription.payment_status || 'not_paid';
                if (groupPaymentSubscriptionDateInput) groupPaymentSubscriptionDateInput.value = enterpriseSubscription.subscription_date ? enterpriseSubscription.subscription_date.split('T')[0] : new Date().toISOString().split('T')[0];
                // تعبئة بنك الدفع إذا كان موجودًا
                if (groupPaymentBankSelect && enterpriseSubscription.payment_bank_id) {
                    groupPaymentBankSelect.value = enterpriseSubscription.payment_bank_id;
                }
            } else {
                // تعديل الرسالة هنا لتعكس الوضع بشكل أفضل
                if (previousMonthClosingBalance === 0) {
                    showMessage(groupPaymentMessage, 'لم يتم العثور على اشتراك حالي للمؤسسة ولا رصيد سابق. سيتم إنشاء اشتراك جديد عند الحفظ.', 'warning', 0);
                } else if (previousMonthClosingBalance > 0 && !enterpriseSubscription){
                    showMessage(groupPaymentMessage, `تم ترحيل رصيد سابق: ${formatCurrency(previousMonthClosingBalance)} ريال. لا يوجد اشتراك حالي، سيتم إنشاء واحد.`, 'info', 0);
                }
                if (groupPaymentCurrentPaidInput) groupPaymentCurrentPaidInput.value = formatCurrency(0);
                if (groupPaymentStatusSelect) groupPaymentStatusSelect.value = 'not_paid';
                if (groupPaymentSubscriptionDateInput) groupPaymentSubscriptionDateInput.value = new Date().toISOString().split('T')[0];
            }

            // 5. حساب المتبقي في النافذة
            const remainingInModal = totalDueForGroupInModal - currentMonthPaidAmount;
            if (groupPaymentRemainingInput) {
                groupPaymentRemainingInput.value = formatCurrency(remainingInModal);
            }

            // تعبئة حقل "المبلغ المدفوع" بقيمة "المتبقي" لتسهيل الدفع
            if (groupPaymentAmount) {
                // تأكد من أن المتبقي ليس سالبًا قبل تعبئته في حقل الدفع
                groupPaymentAmount.value = formatCurrency(Math.max(0, remainingInModal));
            }

            // تعبئة حقل تاريخ الدفع افتراضياً باليوم الحالي
            if (groupPaymentDate) {
                groupPaymentDate.value = new Date().toISOString().split('T')[0];
            }

            // إزالة التفاصيل القديمة إذا كانت موجودة (div.payment-details)
            const modalBody = groupPaymentForm.querySelector('.modal-body');
            if (modalBody) {
                const oldDetails = modalBody.querySelector('.payment-details');
                if (oldDetails) {
                    oldDetails.remove();
                }
            }

        } catch (err) {
            console.error('Error in openGroupPaymentModal async IIFE:', err);
            let userMessage = `حدث خطأ: ${err.message || 'خطأ غير معروف'}`;

            if (err.message && (err.message.toLowerCase().includes("multiple rows") || err.message.toLowerCase().includes("query returned more than one row"))) {
                userMessage = 'خطأ: تم العثور على اشتراكات متعددة للمؤسسة في هذا الشهر. يرجى مراجعة البيانات وإزالة التكرار.';
            } else if (err.status === 406) {
                 userMessage = 'خطأ 406: الطلب غير مقبول. قد يكون هذا بسبب وجود بيانات مكررة (أكثر من اشتراك واحد للمؤسسة في هذا الشهر). يرجى التحقق من البيانات.';
            }

            console.log('Detailed error object in catch block:', {
                message: err.message,
                details: err.details,
                hint: err.hint,
                code: err.code,
                status: err.status,
                stack: err.stack
            });
            showMessage(groupPaymentMessage, userMessage, 'error', 0);
        }
    })();

    console.log('[openGroupPaymentModal] Attempting to make modal visible.');
    groupPaymentModal.classList.add('show');
    document.body.style.overflow = 'hidden';
}

/**
 * @function closeGroupPaymentModal
 * @description دالة لإغلاق نافذة تسجيل دفعة المجموعة.
 */
function closeGroupPaymentModal() {
    if (!groupPaymentModal) return;
    groupPaymentModal.classList.remove('show');
    document.body.style.overflow = '';
     // إزالة تفاصيل الدفع عند إغلاق النافذة
    const modalBody = groupPaymentForm.querySelector('.modal-body');
    if (modalBody) {
        const paymentDetailsDiv = modalBody.querySelector('.payment-details');
        if (paymentDetailsDiv) {
            paymentDetailsDiv.remove();
        }
    }
}
// --- نهاية منطق نافذة الدفع ---

// --- منطق إقفال الشهر (جديد) ---

/**
 * @function displayMonthClosingControls
 * @description يعرض عناصر التحكم في إقفال الشهر ويجلب الحالة الحالية.
 */
const displayMonthClosingControls = async () => {
    if (!monthClosingSection || !selectedEnterpriseId || !selectedMonthId || !enterprisesMap[selectedEnterpriseId] || !selectedMonthNumber || !selectedYearNumber) {
        if (monthClosingSection) monthClosingSection.style.display = 'none';
        return;
    }

    monthClosingSection.style.display = 'block';
    selectedEnterpriseForClosingDisplay.textContent = enterprisesMap[selectedEnterpriseId] || 'المحددة';
    currentMonthForClosingDisplay.textContent = `${getMonthName(selectedMonthNumber)} ${selectedYearNumber}`;
    monthClosingStatusDisplay.textContent = 'جاري التحقق...';
    closeMonthBtn.style.display = 'none';
    reopenMonthBtn.style.display = 'none';
    showMessage(monthClosingMessage, '', 'info', 1); // Clear previous messages

    try {
        const { data: closingRecord, error } = await _supabase
            .from('enterprise_monthly_closings')
            .select('is_closed, closing_balance, closing_date')
            .eq('enterprise_id', selectedEnterpriseId)
            .eq('budget_month_id', selectedMonthId)
            .maybeSingle();

        if (error) {
            console.error('Error fetching month closing status:', error);
            monthClosingStatusDisplay.textContent = 'خطأ في جلب الحالة';
            showMessage(monthClosingMessage, `خطأ: ${error.message}`, 'error');
            return;
        }

        if (closingRecord && closingRecord.is_closed) {
            monthClosingStatusDisplay.textContent = `مُقفل (الرصيد الختامي: ${formatCurrency(closingRecord.closing_balance)} ريال، بتاريخ: ${formatDate(closingRecord.closing_date)})`;
            reopenMonthBtn.style.display = 'inline-block';
            closeMonthBtn.style.display = 'none';
            // تعطيل أزرار الدفع للمجموعات
            toggleGroupPayButtons(false, true); // false لتعطيل الأزرار, true للإشارة إلى أن الشهر مقفل
        } else {
            monthClosingStatusDisplay.textContent = 'مفتوح حالياً';
            closeMonthBtn.style.display = 'inline-block';
            reopenMonthBtn.style.display = 'none';
            // تمكين أزرار الدفع للمجموعات
            toggleGroupPayButtons(true, false); // true لتمكين الأزرار, false للإشارة إلى أن الشهر مفتوح
        }
    } catch (err) {
        console.error('Exception in displayMonthClosingControls:', err);
        monthClosingStatusDisplay.textContent = 'خطأ استثنائي';
        showMessage(monthClosingMessage, `خطأ: ${err.message}`, 'error');
    }
};

/**
 * @function toggleGroupPayButtons
 * @description دالة لتبديل حالة أزرار الدفع للمجموعات.
 * @param {boolean} enable - `true` لتمكين الأزرار، `false` لتعطيلها.
 * @param {boolean} isMonthClosed - `true` إذا كان الشهر مقفل، `false` إذا كان مفتوح.
 */
const toggleGroupPayButtons = (enable, isMonthClosed) => {
    if (!enterpriseGroupsList) return;
    const payButtons = enterpriseGroupsList.querySelectorAll('.pay-btn');
    payButtons.forEach(btn => {
        btn.disabled = !enable;
        if (isMonthClosed) {
            btn.innerHTML = '<i class="fas fa-lock"></i> مقفل';
            btn.title = 'الشهر مقفل، لا يمكن تسجيل دفعات.';
        } else {
            btn.innerHTML = '<i class="fas fa-credit-card"></i> دفع';
            btn.title = 'تسجيل دفعة لهذه المجموعة';
        }
    });
};

/**
 * @function handleCloseMonth
 * @description يعالج عملية إقفال الشهر للمؤسسة المحددة.
 */
const handleCloseMonth = async () => {
    if (!selectedEnterpriseId || !selectedMonthId) {
        showMessage(monthClosingMessage, 'يرجى تحديد مؤسسة وشهر أولاً.', 'error');
        return;
    }

    if (!confirm(`هل أنت متأكد من أنك تريد إقفال شهر ${getMonthName(selectedMonthNumber)} ${selectedYearNumber} للمؤسسة ${enterprisesMap[selectedEnterpriseId]}؟\nلا يمكن تسجيل دفعات جديدة أو تعديل الاشتراكات بعد الإقفال إلا بعد إعادة الفتح.`)) {
        return;
    }

    showMessage(monthClosingMessage, 'جاري إقفال الشهر...', 'info', 0);
    closeMonthBtn.disabled = true;

    try {
        // 1. جلب الرصيد السابق المقفل (إذا وجد)
        let previousMonthClosingBalance = 0;
        let previousMonthNumberCalc = parseInt(selectedMonthNumber, 10) - 1;
        let previousYearNumberCalc = parseInt(selectedYearNumber, 10);
        if (previousMonthNumberCalc === 0) {
            previousMonthNumberCalc = 12;
            previousYearNumberCalc -= 1;
        }

        const { data: budgetYearForPrevMonth, error: budgetYearError } = await _supabase
            .from('budget_years')
            .select('id')
            .eq('year_number', previousYearNumberCalc)
            .single();

        if (!budgetYearError && budgetYearForPrevMonth) {
            const { data: previousBudgetData, error: previousMonthError } = await _supabase
                .from('budget_months')
                .select('id')
                .eq('budget_year_id', budgetYearForPrevMonth.id)
                .eq('month_number', previousMonthNumberCalc)
                .single();

            if (!previousMonthError && previousBudgetData) {
                const { data: prevClosing, error: prevClosingError } = await _supabase
                    .from('enterprise_monthly_closings')
                    .select('closing_balance')
                    .eq('enterprise_id', selectedEnterpriseId)
                    .eq('budget_month_id', previousBudgetData.id)
                    .eq('is_closed', true)
                    .maybeSingle();
                if (!prevClosingError && prevClosing) {
                    previousMonthClosingBalance = parseFloat(prevClosing.closing_balance) || 0;
                }
            }
        }
        console.log(`[handleCloseMonth] Previous month closing balance used for calculation: ${previousMonthClosingBalance}`);

        // 2. حساب الرصيد الختامي للشهر الحالي
        const { data: currentSubscriptionData, error: subError } = await _supabase
            .from('enterprise_subscriptions')
            .select('total_amount, paid_amount')
            .eq('enterprise_id', selectedEnterpriseId)
            .eq('budget_month_id', selectedMonthId)
            .maybeSingle();

        if (subError) throw subError;

        let currentMonthTotalSubscriptionAmount = 0;
        let currentMonthTotalPaidAmount = 0;
        if (currentSubscriptionData) {
            currentMonthTotalSubscriptionAmount = parseFloat(currentSubscriptionData.total_amount) || 0;
            currentMonthTotalPaidAmount = parseFloat(currentSubscriptionData.paid_amount) || 0;
        }

        // الرصيد الختامي الصحيح = (إجمالي تكاليف الشهر الحالي المسجلة في enterprise_subscriptions + الرصيد المرحل من الشهر السابق) - إجمالي المدفوعات للشهر الحالي المسجلة في enterprise_subscriptions
        const actualTotalDueIncludingPrevious = currentMonthTotalSubscriptionAmount + previousMonthClosingBalance;
        const finalClosingBalanceForThisMonth = actualTotalDueIncludingPrevious - currentMonthTotalPaidAmount;

        console.log(`[handleCloseMonth] Calculated finalClosingBalance: ${finalClosingBalanceForThisMonth} (currentMonthSubscriptionTotal: ${currentMonthTotalSubscriptionAmount} + prevBalance: ${previousMonthClosingBalance} - currentMonthSubscriptionPaid: ${currentMonthTotalPaidAmount})`);

        const closingData = {
            enterprise_id: selectedEnterpriseId,
            budget_month_id: selectedMonthId,
            closing_balance: finalClosingBalanceForThisMonth, // استخدام الرصيد الختامي المصحح
            is_closed: true,
            closing_date: new Date().toISOString(),
            closed_by_user_id: currentUserId,
            notes: `تم إقفال الشهر. الرصيد المرحل من الشهر السابق: ${formatCurrency(previousMonthClosingBalance)}. إجمالي اشتراك الشهر: ${formatCurrency(currentMonthTotalSubscriptionAmount)}. إجمالي مدفوعات الشهر: ${formatCurrency(currentMonthTotalPaidAmount)}. الرصيد الختامي المحسوب: ${formatCurrency(finalClosingBalanceForThisMonth)}.`
        };

        const { error: upsertError } = await _supabase
            .from('enterprise_monthly_closings')
            .upsert(closingData, { onConflict: 'enterprise_id, budget_month_id' });

        if (upsertError) throw upsertError;

        showMessage(monthClosingMessage, 'تم إقفال الشهر بنجاح.', 'success');

        // لا تستدعي displayMonthClosingControls و fetchSubscriptions هنا مباشرة
        // سيتم استدعاؤها في نهاية recalculateSuccessiveMonthlyClosings أو إذا لم يكن هناك شلال

        // *** بدء إعادة الحساب التسلسلي ***
        const { data: currentMonthDetails, error: currentMonthDetailsError } = await _supabase
            .from('budget_months')
            .select('month_number, budget_years(year_number)')
            .eq('id', selectedMonthId)
            .single();

        if (currentMonthDetailsError || !currentMonthDetails || !currentMonthDetails.budget_years) {
            console.error('[handleCloseMonth] Could not get current month details or budget_years relation for recalculation trigger.', currentMonthDetailsError);
            await displayMonthClosingControls();
            await fetchSubscriptions();
        } else {
            let nextMonthNumberRecalc = currentMonthDetails.month_number + 1;
            let nextYearNumberRecalc = currentMonthDetails.budget_years.year_number;
            if (nextMonthNumberRecalc > 12) {
                nextMonthNumberRecalc = 1;
                nextYearNumberRecalc += 1;
            }

            const { data: nextBudgetYearRecalc, error: nextBudgetYearErrRecalc } = await _supabase
                .from('budget_years')
                .select('id')
                .eq('year_number', nextYearNumberRecalc)
                .single();

            if (!nextBudgetYearErrRecalc && nextBudgetYearRecalc) {
                const { data: nextBudgetMonthRecalc, error: nextBudgetMonthErrRecalc } = await _supabase
                    .from('budget_months')
                    .select('id')
                    .eq('budget_year_id', nextBudgetYearRecalc.id)
                    .eq('month_number', nextMonthNumberRecalc)
                    .single();

                if (!nextBudgetMonthErrRecalc && nextBudgetMonthRecalc) {
                    console.log(`[handleCloseMonth] Triggering recalculation for subsequent months starting from month ID: ${nextBudgetMonthRecalc.id} with new opening balance: ${finalClosingBalanceForThisMonth}`);
                    await recalculateSuccessiveMonthlyClosings(selectedEnterpriseId, nextBudgetMonthRecalc.id, finalClosingBalanceForThisMonth, nextYearNumberRecalc, nextMonthNumberRecalc);
                } else {
                    console.warn('[handleCloseMonth] Could not find next budget month to start recalculation. No cascade needed.');
                    await displayMonthClosingControls();
                    await fetchSubscriptions();
                }
            } else {
                 console.warn('[handleCloseMonth] Could not find next budget year to start recalculation. No cascade needed.');
                 await displayMonthClosingControls();
                 await fetchSubscriptions();
            }
        }
    } catch (error) {
        console.error('Error closing month:', error);
        showMessage(monthClosingMessage, `فشل إقفال الشهر: ${error.message}`, 'error', 0);
        closeMonthBtn.disabled = false;
    }
    // finally { // تم نقل closeMonthBtn.disabled = false; إلى كتلة catch
        // closeMonthBtn.disabled = false;
    // }
};

/**
 * @function handleReopenMonth
 * @description يعالج عملية فتح إقفال الشهر للمؤسسة المحددة.
 */
const handleReopenMonth = async () => {
    if (!selectedEnterpriseId || !selectedMonthId) {
        showMessage(monthClosingMessage, 'يرجى تحديد مؤسسة وشهر أولاً.', 'error');
        return;
    }
    // جلب السجل الحالي قبل طلب التأكيد للحصول على الملاحظات الحالية
    const { data: currentClosingRecord, error: fetchClosingError } = await _supabase
        .from('enterprise_monthly_closings')
        .select('notes')
        .eq('enterprise_id', selectedEnterpriseId)
        .eq('budget_month_id', selectedMonthId)
        .maybeSingle();

    if (fetchClosingError) {
        console.error('Error fetching current closing record before reopening:', fetchClosingError);
        showMessage(monthClosingMessage, `خطأ في جلب بيانات الإقفال الحالية: ${fetchClosingError.message}`, 'error');
        return;
    }

    if (!confirm(`هل أنت متأكد من أنك تريد فتح إقفال شهر ${getMonthName(selectedMonthNumber)} ${selectedYearNumber} للمؤسسة ${enterprisesMap[selectedEnterpriseId]}؟ \nسيؤدي هذا إلى إعادة حساب أرصدة الشهور اللاحقة المقفلة.`)) {
        return;
    }

    showMessage(monthClosingMessage, 'جاري فتح إقفال الشهر...', 'info', 0);
    reopenMonthBtn.disabled = true;

    try {
        const updateNotes = (currentClosingRecord?.notes || '') + `\n[تم إعادة فتح الشهر بتاريخ ${new Date().toLocaleString('ar-EG')}]`;

        const { error } = await _supabase
            .from('enterprise_monthly_closings')
            .update({
                is_closed: false,
                closing_balance: 0, // إعادة تعيين الرصيد الختامي عند الفتح، سيعاد حسابه إذا أغلق مرة أخرى
                notes: updateNotes
            })
            .eq('enterprise_id', selectedEnterpriseId)
            .eq('budget_month_id', selectedMonthId);

        if (error) throw error;

        showMessage(monthClosingMessage, 'تم فتح إقفال الشهر بنجاح. جاري إعادة حساب الشهور اللاحقة...', 'success', 0);

        const { data: currentMonthDetails, error: currentMonthDetailsError } = await _supabase
            .from('budget_months')
            .select('month_number, budget_years(year_number)')
            .eq('id', selectedMonthId)
            .single();

        if (currentMonthDetailsError || !currentMonthDetails || !currentMonthDetails.budget_years) {
            console.error('[handleReopenMonth] Could not get current month details for recalculation trigger.', currentMonthDetailsError);
            await displayMonthClosingControls();
            await fetchSubscriptions();
        } else {
            let nextMonthNumberRecalc = currentMonthDetails.month_number + 1;
            let nextYearNumberRecalc = currentMonthDetails.budget_years.year_number;
            if (nextMonthNumberRecalc > 12) {
                nextMonthNumberRecalc = 1;
                nextYearNumberRecalc += 1;
            }

            const { data: nextBudgetYearRecalc, error: nextBudgetYearErrRecalc } = await _supabase
                .from('budget_years')
                .select('id')
                .eq('year_number', nextYearNumberRecalc)
                .single();

            if (!nextBudgetYearErrRecalc && nextBudgetYearRecalc) {
                const { data: nextBudgetMonthRecalc, error: nextBudgetMonthErrRecalc } = await _supabase
                    .from('budget_months')
                    .select('id')
                    .eq('budget_year_id', nextBudgetYearRecalc.id)
                    .eq('month_number', nextMonthNumberRecalc)
                    .single();

                if (!nextBudgetMonthErrRecalc && nextBudgetMonthRecalc) {
                    console.log(`[handleReopenMonth] Triggering recalculation for subsequent months starting from month ID: ${nextBudgetMonthRecalc.id} with opening balance: 0`);
                    await recalculateSuccessiveMonthlyClosings(selectedEnterpriseId, nextBudgetMonthRecalc.id, 0, nextYearNumberRecalc, nextMonthNumberRecalc);
                } else {
                    console.warn('[handleReopenMonth] Could not find next budget month to start recalculation. No cascade needed.');
                    await displayMonthClosingControls();
                    await fetchSubscriptions();
                }
            } else {
                 console.warn('[handleReopenMonth] Could not find next budget year to start recalculation. No cascade needed.');
                 await displayMonthClosingControls();
                 await fetchSubscriptions();
            }
        }
    } catch (error) {
        console.error('Error reopening month:', error);
        showMessage(monthClosingMessage, `فشل فتح إقفال الشهر: ${error.message}`, 'error', 0);
    } finally {
        reopenMonthBtn.disabled = false;
    }
};
// --- نهاية منطق إقفال الشهر ---

// --- دالة إعادة الحساب التسلسلي (جديدة) ---
async function recalculateSuccessiveMonthlyClosings(enterpriseId, firstSuccessiveBudgetMonthId, openingBalanceForFirstSuccessive, firstSuccessiveYear, firstSuccessiveMonthNumber) {
    console.log(`[recalculateSuccessiveClosings] Starting for Enterprise ID: ${enterpriseId}, from month: ${firstSuccessiveMonthNumber}/${firstSuccessiveYear} (ID: ${firstSuccessiveBudgetMonthId}), with opening: ${openingBalanceForFirstSuccessive}`);

    let currentYear = parseInt(firstSuccessiveYear, 10);
    let currentMonthNum = parseInt(firstSuccessiveMonthNumber, 10);
    let currentBudgetMonthId = firstSuccessiveBudgetMonthId;
    let previousMonthClosingBalanceForCurrentIteration = openingBalanceForFirstSuccessive; // هذا هو الرصيد الافتتاحي للشهر الحالي في الحلقة

    for (let i = 0; i < 24; i++) { // حد أقصى للدوران لمنع الحلقات اللانهائية (يمكن تعديله)
        if (!currentBudgetMonthId) {
            console.log('[recalculateSuccessiveClosings] No current budget month ID to process. Stopping.');
            break;
        }

        const { data: closingRecord, error: closingErr } = await _supabase
            .from('enterprise_monthly_closings')
            .select('id, is_closed, closing_balance, notes')
            .eq('enterprise_id', enterpriseId)
            .eq('budget_month_id', currentBudgetMonthId)
            .maybeSingle();

        if (closingErr) {
            console.error(`[recalculateSuccessiveClosings] Error fetching closing record for month ID ${currentBudgetMonthId}:`, closingErr);
            break;
        }

        if (!closingRecord || !closingRecord.is_closed) {
            console.log(`[recalculateSuccessiveClosings] Month ID ${currentBudgetMonthId} (${currentMonthNum}/${currentYear}) is not closed or no closing record exists. Stopping cascade.`);
            break;
        }

        const { data: subData, error: subErr } = await _supabase
            .from('enterprise_subscriptions')
            .select('total_amount, paid_amount')
            .eq('enterprise_id', enterpriseId)
            .eq('budget_month_id', currentBudgetMonthId)
            .maybeSingle();

        if (subErr) {
            console.error(`[recalculateSuccessiveClosings] Error fetching subscription for month ID ${currentBudgetMonthId}:`, subErr);
            break;
        }

        let monthTotalAmount = 0;
        let monthPaidAmount = 0;
        if (subData) {
            monthTotalAmount = parseFloat(subData.total_amount) || 0;
            monthPaidAmount = parseFloat(subData.paid_amount) || 0;
        }

        const newCalculatedClosingBalance = (monthTotalAmount + previousMonthClosingBalanceForCurrentIteration) - monthPaidAmount;

        console.log(`[recalculateSuccessiveClosings] Month: ${currentMonthNum}/${currentYear} (ID: ${currentBudgetMonthId})`);
        console.log(`  Opening Balance (from prev month closing): ${formatCurrency(previousMonthClosingBalanceForCurrentIteration)}`);
        console.log(`  Subscription Total: ${formatCurrency(monthTotalAmount)}`);
        console.log(`  Subscription Paid: ${formatCurrency(monthPaidAmount)}`);
        console.log(`  New Calculated Closing Balance: ${formatCurrency(newCalculatedClosingBalance)}`);
        console.log(`  Stored Closing Balance: ${formatCurrency(closingRecord.closing_balance)}`);

        if (Math.abs(newCalculatedClosingBalance - (parseFloat(closingRecord.closing_balance) || 0)) > 0.001) {
            console.log(`[recalculateSuccessiveClosings] Updating closing balance for month ID ${currentBudgetMonthId} from ${closingRecord.closing_balance} to ${newCalculatedClosingBalance}`);

            let updatedNotes = closingRecord.notes || '';
            updatedNotes += `\n[إعادة حساب تلقائي بتاريخ ${new Date().toLocaleDateString('ar-EG')} ${new Date().toLocaleTimeString('ar-EG')}] الرصيد الافتتاحي المستخدم: ${formatCurrency(previousMonthClosingBalanceForCurrentIteration)}. الرصيد الختامي الجديد: ${formatCurrency(newCalculatedClosingBalance)}.`;

            const { error: updateErr } = await _supabase
                .from('enterprise_monthly_closings')
                .update({
                    closing_balance: newCalculatedClosingBalance,
                    notes: updatedNotes,
                    updated_at: new Date().toISOString()
                })
                .eq('id', closingRecord.id);
            if (updateErr) {
                console.error(`[recalculateSuccessiveClosings] Error updating closing balance for month ID ${currentBudgetMonthId}:`, updateErr);
                break;
            }
            previousMonthClosingBalanceForCurrentIteration = newCalculatedClosingBalance;
        } else {
            console.log(`[recalculateSuccessiveClosings] Closing balance for month ID ${currentBudgetMonthId} is already correct. No update needed.`);
            previousMonthClosingBalanceForCurrentIteration = parseFloat(closingRecord.closing_balance) || 0;
        }

        currentMonthNum++;
        if (currentMonthNum > 12) {
            currentMonthNum = 1;
            currentYear++;
        }

        const { data: nextYearForLoop, error: nextYearErrLoop } = await _supabase
            .from('budget_years')
            .select('id')
            .eq('year_number', currentYear)
            .single();

        if (nextYearErrLoop || !nextYearForLoop) {
            console.log(`[recalculateSuccessiveClosings] Next budget year ${currentYear} not found. Stopping.`);
            break;
        }

        const { data: nextMonthForLoop, error: nextMonthErrLoop } = await _supabase
            .from('budget_months')
            .select('id')
            .eq('budget_year_id', nextYearForLoop.id)
            .eq('month_number', currentMonthNum)
            .single();

        if (nextMonthErrLoop || !nextMonthForLoop) {
            console.log(`[recalculateSuccessiveClosings] Next budget month ${currentMonthNum}/${currentYear} not found. Stopping.`);
            break;
        }
        currentBudgetMonthId = nextMonthForLoop.id;
    }
    console.log('[recalculateSuccessiveClosings] Recalculation process finished.');
    await displayMonthClosingControls();
    await fetchSubscriptions();
}
// --- نهاية دالة إعادة الحساب التسلسلي ---

// --- إدارة الشريط الجانبي ---

/**
 * @function renderEnterpriseSidebar
 * @description دالة لعرض قائمة المؤسسات في الشريط الجانبي.
 * @param {Array<object>} enterprisesToRender - مصفوفة من كائنات المؤسسات لعرضها.
 */
const renderEnterpriseSidebar = (enterprisesToRender) => {
    if (!enterpriseListContainer) return;
    enterpriseListContainer.innerHTML = ''; // مسح القائمة الحالية

    // إضافة خيار "الكل"
    const allItem = document.createElement('li');
    allItem.textContent = 'الكل';
    allItem.dataset.enterpriseId = ''; // استخدام سلسلة فارغة لـ 'الكل'
    if (selectedEnterpriseId === null || selectedEnterpriseId === '') {
        allItem.classList.add('active');
    }
    allItem.addEventListener('click', handleSidebarClick);
    enterpriseListContainer.appendChild(allItem);

    // إضافة عناصر المؤسسات
    enterprisesToRender.forEach(ent => {
        const listItem = document.createElement('li');
        listItem.dataset.enterpriseId = ent.id;
        listItem.innerHTML = `<i class="fas fa-building"></i> ${ent.name}`; // إضافة أيقونة
        if (String(selectedEnterpriseId) === String(ent.id)) { // مقارنة كسلاسل نصية
            listItem.classList.add('active');
        }
        listItem.addEventListener('click', handleSidebarClick);
        enterpriseListContainer.appendChild(listItem);
    });

    if (enterprisesToRender.length === 0 && enterpriseSearchInput.value) {
         enterpriseListContainer.innerHTML += '<li class="loading-placeholder">لا توجد نتائج بحث.</li>';
    }
};

/**
 * @function handleSidebarClick
 * @description دالة لمعالجة النقر على عنصر في الشريط الجانبي (اختيار مؤسسة).
 * @param {Event} event - كائن الحدث.
 */
const handleSidebarClick = (event) => {
    const clickedLi = event.currentTarget;
    const enterpriseId = clickedLi.dataset.enterpriseId;
    const enterpriseName = clickedLi.textContent.replace('الكل', '').trim(); // الحصول على الاسم للعنوان

    // تحديث الحالة
    selectedEnterpriseId = enterpriseId === '' ? null : enterpriseId; // تخزين null لـ 'الكل'

    // تحديث الفئة النشطة في الشريط الجانبي
    enterpriseListContainer.querySelectorAll('li').forEach(li => li.classList.remove('active'));
    clickedLi.classList.add('active');

    // إعادة تعيين ترقيم الصفحات وجلب الاشتراكات
    currentPage = 1;
    fetchSubscriptions();

    // جلب وعرض المجموعات فقط إذا تم تحديد مؤسسة معينة
    if (selectedEnterpriseId) {
        fetchAndDisplayEnterpriseGroups(selectedEnterpriseId, enterpriseName);
        displayMonthClosingControls(); // تحديث عناصر التحكم بالإقفال عند اختيار مؤسسة
    } else {
        // إخفاء أو مسح قسم المجموعات إذا تم تحديد "الكل"
        if (enterpriseGroupsSection) enterpriseGroupsSection.style.display = 'none';
        if (enterpriseGroupsList) enterpriseGroupsList.innerHTML = '';
        if (monthClosingSection) monthClosingSection.style.display = 'none'; // إخفاء إذا تم اختيار "الكل"
    }
};

/**
 * @function filterSidebar
 * @description دالة لفلترة قائمة المؤسسات في الشريط الجانبي بناءً على إدخال البحث.
 */
const filterSidebar = () => {
    const searchTerm = enterpriseSearchInput.value.toLowerCase();
    const filteredEnterprises = allEnterprises.filter(ent =>
        ent.name.toLowerCase().includes(searchTerm)
    );
    renderEnterpriseSidebar(filteredEnterprises);
};

// --- جديد: جلب وعرض مجموعات المؤسسة ---

/**
 * @function fetchAndDisplayEnterpriseGroups
 * @description دالة لجلب وعرض مجموعات لمؤسسة محددة.
 * @param {string|number} enterpriseId - معرف المؤسسة.
 * @param {string} enterpriseName - اسم المؤسسة (لعرضه في العنوان).
 */
const fetchAndDisplayEnterpriseGroups = async (enterpriseId, enterpriseName) => {
    if (!enterpriseGroupsSection || !enterpriseGroupsList || !enterpriseGroupsTitle) return;

    enterpriseGroupsSection.style.display = 'block'; // إظهار القسم
    enterpriseGroupsTitle.textContent = `مجموعات: ${enterpriseName}`; // تحديث العنوان
    enterpriseGroupsList.innerHTML = '<div class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i> جاري تحميل المجموعات...</div>';

    if (!_supabase) {
        enterpriseGroupsList.innerHTML = '<div class="loading-placeholder error">خطأ في الاتصال.</div>';
        return;
    }

    try {
        const { data: groups, error } = await _supabase
            .from('enterprise_groups')
            .select('*') // تحديد جميع الأعمدة للعرض
            .eq('enterprise_id', enterpriseId)
            .order('name'); // الترتيب حسب اسم المجموعة

        if (error) throw error;

        enterpriseGroupsList.innerHTML = ''; // مسح رسالة التحميل

        if (!groups || groups.length === 0) {
            enterpriseGroupsList.innerHTML = '<div class="no-data-placeholder">لا توجد مجموعات لهذه المنشأة.</div>';
            return;
        }

        groups.forEach(group => {
            const groupCard = document.createElement('div');
            groupCard.className = 'group-card';
            // إضافة سمات البيانات للنافذة المنبثقة
            groupCard.dataset.groupId = group.id;
            groupCard.dataset.groupName = group.name || 'مجموعة بدون اسم';

            // إعداد عرض نقطة الالتقاء (نص أو زر)
            const departurePointDisplay = isUrl(group.departure_meeting_point)
                ? `<a href="${group.departure_meeting_point}" target="_blank" rel="noopener noreferrer" class="location-link-btn"><i class="fas fa-map-marker-alt"></i> عرض الموقع</a>`
                : group.departure_meeting_point || '---';

            const returnPointDisplay = isUrl(group.return_meeting_point)
                ? `<a href="${group.return_meeting_point}" target="_blank" rel="noopener noreferrer" class="location-link-btn"><i class="fas fa-map-marker-alt"></i> عرض الموقع</a>`
                : group.return_meeting_point || '---';

            groupCard.innerHTML = `
                <h4><i class="fas fa-users"></i> ${group.name || 'مجموعة بدون اسم'}</h4>
                <p><strong>نقطة الانطلاق (ذهاب):</strong> ${departurePointDisplay} (${formatTime(group.departure_meeting_time)})</p>
                <p><strong>وقت الانطلاق (ذهاب):</strong> ${formatTime(group.departure_time) || '---'}</p>
                <p><strong>عدد الموظفين (ذهاب):</strong> ${group.departure_employees_count || '0'}</p>
                <hr style="border-top: 1px dashed #eee; margin: 10px 0;">
                <p><strong>نقطة الالتقاء (عودة):</strong> ${returnPointDisplay} (${formatTime(group.return_meeting_time)})</p>
                <p><strong>وقت الوصول المتوقع (عودة):</strong> ${formatTime(group.return_time) || '---'}</p>
                <p><strong>عدد الموظفين (عودة):</strong> ${group.return_employees_count || '0'}</p>
                <div class="cost-details">
                    <span><strong>التكلفة الأساسية:</strong> ${formatCurrency(group.cost)} ريال</span>
                    ${group.has_vat ? `
                        <span><strong>ضريبة القيمة المضافة (${group.vat_percentage}%):</strong> ${formatCurrency(group.vat_amount)} ريال</span>
                        <span><strong>الإجمالي شامل الضريبة:</strong> ${formatCurrency(group.total_cost_with_vat)} ريال</span>
                    ` : `<span><strong>الإجمالي:</strong> ${formatCurrency(group.cost)} ريال</span>`}
                </div>
                ${group.notes ? `<p><strong>ملاحظات:</strong> ${group.notes}</p>` : ''}
                <button class="action-btn pay-btn"><i class="fas fa-credit-card"></i> دفع</button>
            `;
            enterpriseGroupsList.appendChild(groupCard);

            // إضافة مستمع النقر لفتح نافذة التفاصيل
            groupCard.addEventListener('click', (e) => {
                // التأكد من أن النقر ليس على زر الدفع
                if (e.target.closest('.pay-btn')) {
                    return;
                }
                openGroupDetailsModal(group.id, group.name || 'مجموعة بدون اسم');
            });

            const payBtn = groupCard.querySelector('.pay-btn');
            payBtn.addEventListener('click', (e) => {
                e.stopPropagation(); // منع انتشار الحدث إلى البطاقة نفسها
                openGroupPaymentModal(group.id, group.name || 'مجموعة بدون اسم');
            });
        });

        // بعد عرض جميع المجموعات، قم بتحديث حالة أزرار الدفع بناءً على حالة الإقفال الحالية
        // هذا يضمن أن الأزرار تعكس الحالة الصحيحة حتى لو تم استدعاء fetchAndDisplayEnterpriseGroups بشكل منفصل
        // نحتاج إلى طريقة للوصول إلى is_closed الحالية هنا، أو الاعتماد على displayMonthClosingControls ليتم استدعاؤه دائمًا بعد ذلك.
        // كحل أبسط الآن، displayMonthClosingControls سيقوم بتحديثها.
        // إذا لم يتم استدعاء displayMonthClosingControls بعد، قد تكون الأزرار ممكنة بشكل خاطئ مؤقتًا.
        // استدعاء displayMonthClosingControls مرة أخرى هنا قد يكون مفيدًا إذا كان هذا هو آخر تحديث للواجهة.
        // displayMonthClosingControls(); // يمكن النظر في هذا إذا لزم الأمر

    } catch (error) {
        console.error('خطأ في جلب مجموعات المؤسسة:', error);
        enterpriseGroupsList.innerHTML = `<div class="loading-placeholder error">فشل تحميل المجموعات: ${error.message}</div>`;
    }
};

// --- جلب البيانات ---

/**
 * @function fetchEnterprises
 * @description دالة لجلب قائمة المؤسسات من قاعدة البيانات.
 */
const fetchEnterprises = async () => { // تم تغيير اسم الدالة
    if (!_supabase) return;
    try {
        const { data, error } = await _supabase
            .from('enterprises')
            .select('id, name')
            .order('name');

        if (error) throw error;

        allEnterprises = data || []; // تخزين لفلترة الشريط الجانبي
        enterprisesMap = allEnterprises.reduce((map, ent) => {
            map[ent.id] = ent.name;
            return map;
        }, {});

        renderEnterpriseSidebar(allEnterprises); // تعبئة الشريط الجانبي
        populateModalEnterpriseDropdown(); // تعبئة القائمة المنسدلة في النافذة بشكل منفصل

    } catch (error) {
        console.error('خطأ في جلب المؤسسات:', error);
        showMessage(messageArea, `خطأ في جلب المنشآت: ${error.message}`, 'error');
        if (enterpriseListContainer) enterpriseListContainer.innerHTML = '<li class="loading-placeholder error">فشل التحميل</li>';
    }
};

// تمت إزالة populateEnterpriseDropdown (تتم معالجتها الآن بواسطة renderEnterpriseSidebar و populateModalEnterpriseDropdown)

/**
 * @function fetchSubscriptions
 * @description دالة لجلب بيانات المجموعات ودفعاتها منفصلة لكل مجموعة.
 */
const fetchSubscriptions = async () => {
    if (!_supabase || !selectedMonthId) {
        subscriptionsTableBody.innerHTML = '<tr><td colspan="12" class="loading-placeholder error">خطأ: لم يتم تحديد الشهر أو لا يمكن الاتصال.</td></tr>';
        return;
    }

    subscriptionsTableBody.innerHTML = '<tr><td colspan="12" class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i> جاري تحميل البيانات...</td></tr>';

    try {
        // جلب المجموعات مع بيانات الدفعات
        let groupsQuery = _supabase
            .from('enterprise_groups')
            .select(`
                id,
                enterprise_id,
                name,
                total_cost_with_vat,
                enterprises(name)
            `);

        // تطبيق فلتر المؤسسة
        if (selectedEnterpriseId) {
            groupsQuery = groupsQuery.eq('enterprise_id', selectedEnterpriseId);
        }

        const { data: groups, error: groupsError } = await groupsQuery;
        if (groupsError) throw groupsError;

        if (!groups || groups.length === 0) {
            subscriptionsTableBody.innerHTML = '<tr><td colspan="12" class="loading-placeholder">لا توجد مجموعات لعرضها.</td></tr>';
            totalItems = 0;
            updatePagination();
            return;
        }

        // جلب بيانات الدفعات لكل مجموعة
        const groupsWithPayments = [];

        for (const group of groups) {
            // جلب الاشتراك الخاص بهذه المؤسسة والشهر
            const { data: subscription, error: subError } = await _supabase
                .from('enterprise_subscriptions')
                .select('*')
                .eq('enterprise_id', group.enterprise_id)
                .eq('budget_month_id', selectedMonthId)
                .maybeSingle();

            if (subError && subError.code !== 'PGRST116') {
                console.error(`خطأ في جلب اشتراك المؤسسة ${group.enterprise_id}:`, subError);
                continue;
            }

            // حساب الرصيد السابق لهذه المجموعة
            const { data: previousClosing, error: prevError } = await _supabase
                .from('enterprise_group_monthly_closings')
                .select('closing_balance')
                .eq('group_id', group.id)
                .eq('budget_month_id', getPreviousMonthId()) // دالة لحساب الشهر السابق
                .eq('is_closed', true)
                .maybeSingle();

            const previousBalance = previousClosing?.closing_balance || 0;
            const groupCost = parseFloat(group.total_cost_with_vat) || 0;
            const totalDue = previousBalance + groupCost;

            // حساب المدفوع لهذه المجموعة (من ملاحظات الاشتراك)
            let paidForThisGroup = 0;
            if (subscription && subscription.notes) {
                // استخراج المدفوع لهذه المجموعة من ملاحظات الدفعات
                const groupPayments = extractGroupPaymentsFromNotes(subscription.notes, group.id);
                paidForThisGroup = groupPayments.reduce((sum, payment) => sum + payment.amount, 0);
            }

            const remainingAmount = totalDue - paidForThisGroup;
            const paymentStatus = remainingAmount <= 0 ? 'paid' : (paidForThisGroup > 0 ? 'partially_paid' : 'not_paid');

            // التحقق من حالة الإقفال
            const { data: closingRecord, error: closingError } = await _supabase
                .from('enterprise_group_monthly_closings')
                .select('is_closed')
                .eq('group_id', group.id)
                .eq('budget_month_id', selectedMonthId)
                .maybeSingle();

            const isClosed = closingRecord?.is_closed || false;

            groupsWithPayments.push({
                id: group.id,
                enterprise_id: group.enterprise_id,
                enterprise_name: group.enterprises?.name || 'غير محدد',
                group_name: group.name,
                group_cost: groupCost,
                previous_balance: previousBalance,
                total_due: totalDue,
                paid_amount: paidForThisGroup,
                remaining_amount: remainingAmount,
                payment_status: paymentStatus,
                subscription_date: subscription?.subscription_date || null,
                payment_date: subscription?.payment_date || null,
                notes: subscription?.notes || '',
                is_closed: isClosed
            });
        }

        // تطبيق فلتر الحالة
        const status = statusFilter.value;
        if (status) {
            currentSubscriptions = groupsWithPayments.filter(group => group.payment_status === status);
        } else {
            currentSubscriptions = groupsWithPayments;
        }

        totalItems = currentSubscriptions.length;

        // تطبيق الترقيم
        const offset = (currentPage - 1) * ITEMS_PER_PAGE;
        currentSubscriptions = currentSubscriptions.slice(offset, offset + ITEMS_PER_PAGE);

        renderTable();
        updatePagination();

    } catch (error) {
        console.error('خطأ في جلب بيانات المجموعات:', error);
        showMessage(messageArea, `خطأ في جلب البيانات: ${error.message}`, 'error');
        subscriptionsTableBody.innerHTML = `<tr><td colspan="12" class="loading-placeholder error">فشل تحميل البيانات: ${error.message}</td></tr>`;
    }
};

// دالة مساعدة لاستخراج دفعات المجموعة من ملاحظات الاشتراك
const extractGroupPaymentsFromNotes = (notes, groupId) => {
    const payments = [];
    if (!notes) return payments;

    // البحث عن نمط دفعات المجموعة في الملاحظات
    // نمط: "دفعة للمجموعة '...' (ID: groupId) عبر بنك ID ...: amount ريال"
    const regex = new RegExp(`دفعة للمجموعة '[^']*' \\(ID: ${groupId}\\) عبر بنك ID [^:]*: ([0-9,\.]+) ريال`, 'g');

    let match;
    while ((match = regex.exec(notes)) !== null) {
        const amount = parseFloat(match[1].replace(/,/g, ''));
        if (!isNaN(amount)) {
            payments.push({ amount });
        }
    }

    return payments;
};

// دالة مساعدة لحساب معرف الشهر السابق
const getPreviousMonthId = () => {
    // هذه دالة مبسطة - يمكن تحسينها بناءً على هيكل بيانات budget_months
    // للآن سنعيد null لتجنب الأخطاء
    return null;
};

// --- العرض ---

/**
 * @function renderTable
 * @description دالة لعرض بيانات المجموعات ودفعاتها في الجدول.
 */
const renderTable = () => {
    subscriptionsTableBody.innerHTML = ''; // مسح البيانات السابقة أو رسالة التحميل

    if (currentSubscriptions.length === 0) {
        subscriptionsTableBody.innerHTML = '<tr><td colspan="12" class="no-data-placeholder">لا توجد مجموعات لعرضها حسب الفلاتر المحددة.</td></tr>';
        return;
    }

    currentSubscriptions.forEach(group => {
        const row = document.createElement('tr');

        // إضافة فئة CSS بناءً على حالة الإقفال
        if (group.is_closed) {
            row.classList.add('closed-group');
        }

        row.innerHTML = `
            <td>${group.enterprise_name}</td>
            <td>${group.group_name}</td>
            <td>${formatCurrency(group.previous_balance)}</td>
            <td>${formatCurrency(group.group_cost)}</td>
            <td>${formatCurrency(group.total_due)}</td>
            <td>${formatCurrency(group.paid_amount)}</td>
            <td>${formatCurrency(group.remaining_amount)}</td>
            <td>${getStatusBadge(group.payment_status)}</td>
            <td>${formatDate(group.subscription_date)}</td>
            <td>${formatDate(group.payment_date)}</td>
            <td>${group.is_closed ? '<span class="status-badge closed">مقفل</span>' : '<span class="status-badge open">مفتوح</span>'}</td>
            <td>
                <button class="action-btn edit-btn" data-group-id="${group.id}" title="تعديل الدفعات"><i class="fas fa-edit"></i></button>
                ${!group.is_closed ?
                    `<button class="action-btn close-btn" data-group-id="${group.id}" title="إقفال المجموعة"><i class="fas fa-lock"></i></button>` :
                    `<button class="action-btn reopen-btn" data-group-id="${group.id}" title="إعادة فتح المجموعة"><i class="fas fa-unlock"></i></button>`
                }
                <button class="action-btn view-btn" data-group-id="${group.id}" title="عرض تفاصيل الدفعات"><i class="fas fa-eye"></i></button>
            </td>
        `;

        // إضافة مستمعي الأحداث للأزرار
        const editBtn = row.querySelector('.edit-btn');
        const closeBtn = row.querySelector('.close-btn');
        const reopenBtn = row.querySelector('.reopen-btn');
        const viewBtn = row.querySelector('.view-btn');

        if (editBtn) editBtn.addEventListener('click', () => handleEditGroupPayments(group.id));
        if (closeBtn) closeBtn.addEventListener('click', () => handleCloseGroup(group.id));
        if (reopenBtn) reopenBtn.addEventListener('click', () => handleReopenGroup(group.id));
        if (viewBtn) viewBtn.addEventListener('click', () => handleViewGroupDetails(group.id));

        subscriptionsTableBody.appendChild(row);
    });
};

// --- ترقيم الصفحات ---

/**
 * @function updatePagination
 * @description دالة لتحديث عناصر التحكم في ترقيم الصفحات.
 */
const updatePagination = () => {
    const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE);
    pageInfo.textContent = `صفحة ${currentPage} من ${totalPages || 1}`;
    prevPageBtn.disabled = currentPage <= 1;
    nextPageBtn.disabled = currentPage >= totalPages;
};

/**
 * @function goToPage
 * @description دالة للانتقال إلى صفحة محددة.
 * @param {number} page - رقم الصفحة للانتقال إليها.
 */
const goToPage = (page) => {
    const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        fetchSubscriptions();
    }
};
/**
 * @function handleFormSubmit
 * @description دالة لمعالجة إرسال نموذج إضافة/تعديل الاشتراك (الاشتراك العام).
 * @param {Event} event - كائن الحدث.
 */
const handleFormSubmit = async (event) => {
    event.preventDefault();
    showMessage(modalMessage, 'جاري الحفظ...', 'info', 0);
    saveSubscriptionBtn.disabled = true;

    const subscriptionId = subscriptionIdInput.value;
    const totalAmount = parseFloat(modalTotalAmountInput.value) || 0;
    const paidAmount = parseFloat(modalPaidAmountInput.value) || 0;
    let status = 'not_paid';
    if (paidAmount >= totalAmount && totalAmount > 0) {
        status = 'paid';
    } else if (paidAmount > 0 && paidAmount < totalAmount) {
        status = 'partially_paid';
    } else if (totalAmount <= 0 && paidAmount <=0) { // Consider if total is zero or negative
        status = 'paid'; // Or some other status like 'n_a' if applicable
    }


    const subscriptionData = {
        enterprise_id: modalEnterpriseSelect.value,
        budget_month_id: selectedMonthId,
        service_type: modalServiceTypeInput.value.trim(),
        total_amount: totalAmount,
        paid_amount: paidAmount,
        payment_status: status,
        subscription_date: modalSubscriptionDateInput.value || null,
        payment_date: modalPaymentDateInput.value || null, // تاريخ آخر دفعة عامة
        notes: modalNotesInput.value.trim() || null,
        // payment_bank_id لا يتم تحديثه مباشرة من هذا النموذج
    };

    try {
        let result;
        let changeType = '';
        let oldSubscriptionData = null; // لتخزين بيانات الاشتراك القديمة إذا كان تحديثًا

        if (subscriptionId) {
            changeType = 'UPDATE_SUBSCRIPTION';
            // جلب البيانات القديمة للاشتراك لتمريرها إلى دالة الربط إذا لزم الأمر
            const {data: oldData, error: fetchOldError} = await _supabase
                .from('enterprise_subscriptions')
                .select('*')
                .eq('id', subscriptionId)
                .single();
            if(fetchOldError) {
                console.warn("Could not fetch old subscription data for comparison:", fetchOldError);
                // يمكنك اختيار إيقاف العملية هنا أو المتابعة بحذر
            } else {
                oldSubscriptionData = oldData;
                // إذا كان النموذج لا يعدل payment_bank_id، احتفظ بالقديم
                subscriptionData.payment_bank_id = oldData.payment_bank_id;
            }

            result = await _supabase
                .from('enterprise_subscriptions')
                .update(subscriptionData)
                .eq('id', subscriptionId)
                .select() // لجلب البيانات المحدثة كاملة
                .single();
        } else {
            changeType = 'INSERT_PAYMENT'; // نفترض أن الإضافة من هنا هي كدفعة أولى لاشتراك جديد
                                        // وقد لا يكون لها بنك مرتبط مباشرة من هذا النموذج
            result = await _supabase
                .from('enterprise_subscriptions')
                .insert([subscriptionData])
                .select()
                .single();
        }

        const { data: savedSubscription, error } = result;
        if (error) throw error;

        showMessage(modalMessage, 'تم الحفظ بنجاح!', 'success');

        // استدعاء دالة الربط بالمعاملات البنكية
        if (typeof InstitutionSubscriptionsLinker !== 'undefined' && savedSubscription) {
            // نستخدم payment_bank_id من البيانات المحفوظة (التي قد تكون NULL إذا لم يتم تعيينها)
            // أو من البيانات القديمة إذا كان تحديثًا ولم يتم تغيير البنك عبر هذا النموذج
            const bankIdToUse = savedSubscription.payment_bank_id;

            // عند تحديث اشتراك عام، قد يتغير المبلغ المدفوع الكلي.
            // إذا أصبح المبلغ المدفوع صفرًا، يجب حذف المعاملات المرتبطة.
            // إذا كان هناك مبلغ مدفوع وبنك، يجب التأكد من وجود معاملة (أو تحديثها إذا كان المنطق يدعم ذلك).
            // السيناريو الحالي في linker يعالج حالة paidAmount <= 0 بحذف المعاملات.
            // إذا كان paidAmount > 0 وبنك موجود، linker سيحاول إنشاء/تحديث.
            // بما أن هذا النموذج لا يسجل دفعة محددة، لا نمرر paymentAmountForThisTransaction
            await InstitutionSubscriptionsLinker.handleSubscriptionChangeForBankTransactions(
                changeType, // 'UPDATE_SUBSCRIPTION' or 'INSERT_PAYMENT'
                savedSubscription,
                bankIdToUse // قد يكون null إذا لم يتم تعيين بنك بعد
                // لا نمرر paymentAmountForThisTransaction لأن هذا النموذج لا يسجل دفعة محددة
            );
        }

        setTimeout(() => {
            closeModal();
            fetchSubscriptions();
            if (selectedEnterpriseId) {
                const selectedLi = enterpriseListContainer.querySelector(`li[data-enterprise-id="${selectedEnterpriseId}"]`);
                const enterpriseName = selectedLi ? selectedLi.textContent.trim().replace('الكل','') : '';
                fetchAndDisplayEnterpriseGroups(selectedEnterpriseId, enterpriseName);
            }
        }, 1000);

    } catch (error) {
        console.error('خطأ في حفظ الاشتراك:', error);
        showMessage(modalMessage, `خطأ في الحفظ: ${error.message}`, 'error', 0);
    } finally {
        saveSubscriptionBtn.disabled = false;
    }
};/**
 * @function handleEdit
 * @description دالة لمعالجة النقر على زر التعديل (فتح النافذة المنبثقة مع بيانات الاشتراك).
 * @param {string|number} id - معرف الاشتراك المراد تعديله.
 */
const handleEdit = (id) => {
    const subscription = currentSubscriptions.find(sub => sub.id === id);
    if (subscription) {
        openModal(subscription);
    }
};
/**
 * @function handleDelete
 * @description دالة لمعالجة النقر على زر الحذف (حذف الاشتراك).
 * @param {string|number} id - معرف الاشتراك المراد حذفه.
 */
const handleDelete = async (id) => {
    const subscriptionToDelete = currentSubscriptions.find(sub => sub.id === id);
    if (!subscriptionToDelete) {
        showMessage(messageArea, 'خطأ: لم يتم العثور على الاشتراك المراد حذفه.', 'error');
        return;
    }

    if (!confirm('هل أنت متأكد من حذف هذا الاشتراك؟ لا يمكن التراجع عن هذا الإجراء.')) {
        return;
    }

    showMessage(messageArea, 'جاري الحذف...', 'info', 0);
    try {
        const { error } = await _supabase
            .from('enterprise_subscriptions')
            .delete()
            .eq('id', id);

        if (error) throw error;

        showMessage(messageArea, 'تم الحذف بنجاح.', 'success');

        // استدعاء دالة الربط لإعلامها بالحذف
        if (typeof InstitutionSubscriptionsLinker !== 'undefined' && InstitutionSubscriptionsLinker.handleSubscriptionChangeForBankTransactions) {
            // عند الحذف، لا يوجد بنك دفع جديد، نمرر بيانات الاشتراك المحذوف
            await InstitutionSubscriptionsLinker.handleSubscriptionChangeForBankTransactions('DELETE_SUBSCRIPTION', subscriptionToDelete, null);
        }

        if (currentSubscriptions.length === 1 && currentPage > 1) {
            currentPage--;
        }
        fetchSubscriptions();
        if (selectedEnterpriseId) { // تحديث المجموعات إذا كانت مؤسسة محددة
            const selectedLi = enterpriseListContainer.querySelector(`li[data-enterprise-id="${selectedEnterpriseId}"]`);
            const enterpriseName = selectedLi ? selectedLi.textContent.trim().replace('الكل','') : '';
            fetchAndDisplayEnterpriseGroups(selectedEnterpriseId, enterpriseName);
        }

    } catch (error) {
        console.error('خطأ في حذف الاشتراك:', error);
        showMessage(messageArea, `خطأ في الحذف: ${error.message}`, 'error');
    }
};
const initializePage = async () => {
    // الحصول على الشهر/السنة المحددة من sessionStorage
    selectedMonthId = sessionStorage.getItem('selectedBudgetMonthId');
    selectedMonthNumber = sessionStorage.getItem('selectedBudgetMonthNumber');
    selectedYearNumber = sessionStorage.getItem('selectedBudgetYearNumber');

    if (selectedMonthNumber && selectedYearNumber && currentMonthYearDisplay) {
        currentMonthYearDisplay.textContent = `${getMonthName(selectedMonthNumber)} ${selectedYearNumber}`;
    } else {
        currentMonthYearDisplay.textContent = 'غير محدد';
    }

    // جلب معرف المستخدم الحالي
    try {
        const { data: { user }, error: userError } = await _supabase.auth.getUser();
        if (userError) {
            console.warn('Error fetching current user:', userError.message);
        } else if (user) {
            currentUserId = user.id;
            console.log('Current user ID:', currentUserId);
        }
    } catch (e) {
        console.warn('Exception fetching user:', e);
    }

    if (!selectedMonthId) {
        showMessage(messageArea, 'خطأ: الشهر المالي غير محدد. يرجى العودة واختيار شهر.', 'error', 0);
        // ربما تعطيل عناصر التحكم
        return;
    }

    // إخفاء قسم المجموعات وقسم إقفال الشهر مبدئيًا
    if (enterpriseGroupsSection) enterpriseGroupsSection.style.display = 'none';
    if (monthClosingSection) monthClosingSection.style.display = 'none'; // إخفاء مبدئي

    await fetchEnterprises(); // جلب المؤسسات للشريط الجانبي والخريطة
    await fetchSubscriptions(); // جلب البيانات الأولية (سيعرض 'الكل' افتراضيًا)
    await displayMonthClosingControls(); // استدعاء لعرض عناصر التحكم بالإقفال

    // التحقق من وجود عنصر الزر
    if (!addSubscriptionBtn) {
        console.error('لم يتم العثور على زر إضافة الاشتراك (add-subscription-btn) في DOM.');
    } else {
        console.log('تم العثور على زر إضافة الاشتراك.'); // <-- إضافة سجل
    }

    // إعداد مستمعي الأحداث
    if (addSubscriptionBtn) { // التحقق مرة أخرى قبل إضافة المستمع
        addSubscriptionBtn.addEventListener('click', () => {
            console.log('تم النقر على زر إضافة الاشتراك.'); // <-- إضافة سجل
            openModal();
        });
        console.log('تمت إضافة مستمع الحدث إلى زر إضافة الاشتراك.'); // <-- إضافة سجل
    }
    if (closeSubscriptionModalBtn) closeSubscriptionModalBtn.addEventListener('click', closeModal);
    if (cancelSubscriptionBtn) cancelSubscriptionBtn.addEventListener('click', closeModal);
    if (subscriptionForm) subscriptionForm.addEventListener('submit', handleFormSubmit);
    if (refreshDataBtn) {
        refreshDataBtn.addEventListener('click', () => {
            // تحديث كل من الاشتراكات والمجموعات إذا تم تحديد مؤسسة
            fetchSubscriptions();
            if (selectedEnterpriseId) {
                const selectedLi = enterpriseListContainer.querySelector(`li[data-enterprise-id="${selectedEnterpriseId}"]`);
                const enterpriseName = selectedLi ? selectedLi.textContent.trim() : '';
                fetchAndDisplayEnterpriseGroups(selectedEnterpriseId, enterpriseName);
            }
        });
    }
    if (statusFilter) statusFilter.addEventListener('change', () => { currentPage = 1; fetchSubscriptions(); });
    if (prevPageBtn) prevPageBtn.addEventListener('click', () => goToPage(currentPage - 1));
    if (nextPageBtn) nextPageBtn.addEventListener('click', () => goToPage(currentPage + 1));
    if (enterpriseSearchInput) enterpriseSearchInput.addEventListener('input', filterSidebar); // إضافة مستمع لبحث الشريط الجانبي
    if (closeMonthBtn) closeMonthBtn.addEventListener('click', handleCloseMonth);
    if (reopenMonthBtn) reopenMonthBtn.addEventListener('click', handleReopenMonth);

    // إضافة event listeners لحساب المبلغ الإجمالي تلقائياً
    setupAutoCalculation();

    // إغلاق النافذة المنبثقة عند الضغط على مفتاح Escape
    document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape') {
            if (subscriptionModal && subscriptionModal.classList.contains('show')) {
                closeModal();
            }
            if (groupDetailsModal && groupDetailsModal.classList.contains('show')) { // إغلاق نافذة المجموعة أيضًا
                closeGroupDetailsModal();
            }
            if (groupPaymentModal && groupPaymentModal.classList.contains('show')) { // إغلاق نافذة الدفع أيضًا
                closeGroupPaymentModal();
            }
        }
    });
     // إغلاق النوافذ المنبثقة عند النقر في الخارج
    if (subscriptionModal) {
        subscriptionModal.addEventListener('click', (event) => {
            if (event.target === subscriptionModal) {
                closeModal();
            }
        });
    }
    if (groupDetailsModal) { // إغلاق نافذة المجموعة أيضًا
        groupDetailsModal.addEventListener('click', (event) => {
            if (event.target === groupDetailsModal) {
                closeGroupDetailsModal();
            }
        });
    }
    if (groupPaymentModal) {
        groupPaymentModal.addEventListener('click', (event) => {
            if (event.target === groupPaymentModal) {
                closeGroupPaymentModal();
            }
        });
    }
    if (closeGroupPaymentModalBtn) closeGroupPaymentModalBtn.addEventListener('click', closeGroupPaymentModal);
    if (cancelGroupPaymentBtn) cancelGroupPaymentBtn.addEventListener('click', closeGroupPaymentModal);
    if (groupPaymentForm) {
        groupPaymentForm.addEventListener('submit', async function(event) {
            event.preventDefault();
            if (!groupPaymentGroupId || !groupPaymentAmount || !groupPaymentDate || !_supabase || !selectedMonthId || !selectedEnterpriseId || !groupPaymentBankSelect) {
                showMessage(groupPaymentMessage, 'خطأ: البيانات الأساسية غير مكتملة أو الاتصال غير متوفر.', 'error', 4000);
                return;
            }
            const groupId = groupPaymentGroupId.value;
            const amountPaidForThisGroup = parseFloat(groupPaymentAmount.value);
            const paymentDate = groupPaymentDate.value;
            const paymentNotes = groupPaymentNotes.value.trim();
            const selectedBankIdForPayment = groupPaymentBankSelect.value;

            if (!groupId || isNaN(amountPaidForThisGroup) || amountPaidForThisGroup <= 0 || !paymentDate || !selectedBankIdForPayment) {
                showMessage(groupPaymentMessage, 'يرجى إدخال جميع بيانات الدفع المطلوبة بشكل صحيح، بما في ذلك اختيار البنك والمبلغ.', 'error', 4000);
                return;
            }

            confirmGroupPaymentBtn.disabled = true;
            showMessage(groupPaymentMessage, 'جاري تسجيل الدفعة...', 'info', 0);

            try {
                const { data: groupDetails, error: groupFetchError } = await _supabase
                    .from('enterprise_groups')
                    .select('name, total_cost_with_vat')
                    .eq('id', groupId)
                    .single();

                if (groupFetchError || !groupDetails) {
                    throw new Error('لم يتم العثور على تفاصيل المجموعة.');
                }
                const groupName = groupDetails.name;
                // const groupTotalCost = parseFloat(groupDetails.total_cost_with_vat);  // لا نستخدمها مباشرة لتحديث إجمالي الاشتراك

                let { data: enterpriseSubscription, error: fetchError } = await _supabase
                    .from('enterprise_subscriptions')
                    .select('*')
                    .eq('enterprise_id', selectedEnterpriseId)
                    .eq('budget_month_id', selectedMonthId)
                    .maybeSingle();

                let changeTypeForLinker = ''; // تم تعريف المتغير هنا
                let savedSubscriptionForLinker; //  لتخزين بيانات الاشتراك بعد الحفظ/التحديث

                const paymentNoteForSubscription = `دفعة للمجموعة '${groupName}' (ID: ${groupId}) عبر بنك ID ${selectedBankIdForPayment}: ${formatCurrency(amountPaidForThisGroup)} ريال. ${paymentNotes ? 'ملاحظات الدفعة: ' + paymentNotes : ''}`;

                if (fetchError && fetchError.code !== 'PGRST116') {
                    throw fetchError;
                }

                if (!enterpriseSubscription) {
                    changeTypeForLinker = 'INSERT_PAYMENT';
                    const newSubscriptionData = {
                        enterprise_id: selectedEnterpriseId,
                        budget_month_id: selectedMonthId,
                        total_amount: 0, // الإجمالي يمكن تحديثه لاحقًا أو يدويًا
                        paid_amount: amountPaidForThisGroup,
                        payment_status: 'partially_paid', //  أو يتم تحديده بناءً على total_amount إذا كان معروفًا
                        subscription_date: new Date().toISOString().split('T')[0],
                        payment_date: paymentDate, //  تاريخ هذه الدفعة
                        notes: paymentNoteForSubscription,
                        service_type: `اشتراك المؤسسة لشهر ${getMonthName(selectedMonthNumber)} ${selectedYearNumber}`,
                        payment_bank_id: selectedBankIdForPayment
                    };
                    const { data: newSub, error: insertError } = await _supabase
                        .from('enterprise_subscriptions')
                        .insert([newSubscriptionData])
                        .select()
                        .single();
                    if (insertError) throw insertError;
                    savedSubscriptionForLinker = newSub;
                    showMessage(groupPaymentMessage, 'تم إنشاء اشتراك جديد للمؤسسة وتسجيل الدفعة.', 'success', 3000);

                } else {
                    changeTypeForLinker = 'INSERT_PAYMENT'; //  كل دفعة من هنا هي "إضافة دفعة"
                    const newOverallPaidAmount = (parseFloat(enterpriseSubscription.paid_amount) || 0) + amountPaidForThisGroup;
                    const newNotes = enterpriseSubscription.notes
                                   ? enterpriseSubscription.notes + "\n" + paymentNoteForSubscription
                                   : paymentNoteForSubscription;

                    const updatedSubscriptionData = {
                        paid_amount: newOverallPaidAmount,
                        payment_date: paymentDate, //  تحديث تاريخ آخر دفعة للاشتراك
                        notes: newNotes,
                        payment_bank_id: selectedBankIdForPayment //  تحديث البنك المستخدم لهذه الدفعة
                    };

                    // تحديث حالة الدفع بناءً على الإجمالي المدفوع مقابل الإجمالي المستحق للاشتراك
                    if (parseFloat(enterpriseSubscription.total_amount) > 0) {
                        updatedSubscriptionData.payment_status = newOverallPaidAmount >= parseFloat(enterpriseSubscription.total_amount) ? 'paid' : (newOverallPaidAmount > 0 ? 'partially_paid' : 'not_paid');
                    } else {
                         updatedSubscriptionData.payment_status = 'paid'; //  إذا كان الإجمالي صفرًا أو أقل، اعتبره مدفوعًا
                    }

                    const { data: updatedSub, error: updateError } = await _supabase
                        .from('enterprise_subscriptions')
                        .update(updatedSubscriptionData)
                        .eq('id', enterpriseSubscription.id) // Corrected: 'id' instead of 'id\'
                        .select()
                        .single();
                    if (updateError) throw updateError;
                    savedSubscriptionForLinker = updatedSub;
                    showMessage(groupPaymentMessage, 'تم تحديث اشتراك المؤسسة بالدفعة الجديدة.', 'success', 3000);
                }

                // تم حذف إنشاء المعاملة البنكية يدوياً - التريجر في قاعدة البيانات يقوم بهذا تلقائياً
                console.log(`[Payment Success] Enterprise subscription payment saved. Bank transaction will be created automatically by database trigger.`);

                fetchSubscriptions();
                if (selectedEnterpriseId) {
                     const selectedLi = enterpriseListContainer.querySelector(`li[data-enterprise-id="${selectedEnterpriseId}"]`);
                     const enterpriseName = selectedLi ? selectedLi.textContent.trim().replace('الكل','') : '';
                     fetchAndDisplayEnterpriseGroups(selectedEnterpriseId, enterpriseName);
                }
                setTimeout(closeGroupPaymentModal, 1500);

            } catch (error) {
                console.error('خطأ في تسجيل دفعة المجموعة:', error);
                showMessage(groupPaymentMessage, `خطأ في تسجيل الدفعة: ${error.message}`, 'error', 0);
            } finally {
                confirmGroupPaymentBtn.disabled = false;
                confirmGroupPaymentBtn.innerHTML = '<i class="fas fa-check"></i> تسجيل الدفعة';
            }
        });
    }
    if (closeGroupPaymentModalBtn) closeGroupPaymentModalBtn.addEventListener('click', closeGroupPaymentModal);
    if (closeGroupDetailsModalBtn) closeGroupDetailsModalBtn.addEventListener('click', closeGroupDetailsModal);
    if (cancelGroupDetailsBtn) cancelGroupDetailsBtn.addEventListener('click', closeGroupDetailsModal);
};

// دالة إعداد حساب المبلغ الإجمالي تلقائياً
const setupAutoCalculation = () => {
    // حساب المبلغ الإجمالي عند تغيير المبلغ المدفوع
    if (modalPaidAmountInput) {
        modalPaidAmountInput.addEventListener('input', calculateTotalAmount);
        modalPaidAmountInput.addEventListener('change', calculateTotalAmount);
    }

    // حساب المبلغ الإجمالي عند تغيير المبلغ الإجمالي يدوياً
    if (modalTotalAmountInput) {
        modalTotalAmountInput.addEventListener('input', updateRemainingAmount);
        modalTotalAmountInput.addEventListener('change', updateRemainingAmount);
    }
};

// دالة حساب المبلغ الإجمالي
const calculateTotalAmount = () => {
    const paidAmount = parseFloat(modalPaidAmountInput.value) || 0;
    const currentTotalAmount = parseFloat(modalTotalAmountInput.value) || 0;

    // إذا كان المبلغ المدفوع أكبر من المبلغ الإجمالي الحالي، حدث المبلغ الإجمالي
    if (paidAmount > currentTotalAmount) {
        modalTotalAmountInput.value = paidAmount.toFixed(2);

        // إضافة تأثير بصري للتنبيه
        modalTotalAmountInput.style.backgroundColor = '#e8f5e8';
        setTimeout(() => {
            modalTotalAmountInput.style.backgroundColor = '';
        }, 1000);
    }

    updateRemainingAmount();
};

// دالة تحديث المبلغ المتبقي (للعرض فقط)
const updateRemainingAmount = () => {
    const totalAmount = parseFloat(modalTotalAmountInput.value) || 0;
    const paidAmount = parseFloat(modalPaidAmountInput.value) || 0;
    const remainingAmount = totalAmount - paidAmount;

    // عرض المبلغ المتبقي في عنصر معلوماتي (إذا وجد)
    const remainingDisplay = document.getElementById('remaining-amount-display');
    if (remainingDisplay) {
        remainingDisplay.textContent = `المبلغ المتبقي: ${remainingAmount.toFixed(2)} ريال`;

        // تغيير لون النص حسب الحالة
        if (remainingAmount > 0) {
            remainingDisplay.style.color = '#dc3545'; // أحمر للمبلغ المتبقي
        } else if (remainingAmount === 0) {
            remainingDisplay.style.color = '#28a745'; // أخضر للمدفوع بالكامل
        } else {
            remainingDisplay.style.color = '#ffc107'; // أصفر للزيادة في الدفع
        }
    }
};

// دوال إدارة المجموعات

// دالة تعديل دفعات المجموعة
const handleEditGroupPayments = (groupId) => {
    console.log(`تعديل دفعات المجموعة: ${groupId}`);
    // هنا يمكن فتح نموذج لتعديل الدفعات
    showMessage(messageArea, 'ميزة تعديل الدفعات قيد التطوير', 'info');
};

// دالة إقفال المجموعة
const handleCloseGroup = async (groupId) => {
    if (!confirm('هل أنت متأكد من إقفال هذه المجموعة؟ لن يمكن تعديل الدفعات بعد الإقفال.')) {
        return;
    }

    try {
        // العثور على بيانات المجموعة
        const group = currentSubscriptions.find(g => g.id === groupId);
        if (!group) {
            throw new Error('لم يتم العثور على بيانات المجموعة');
        }

        // إنشاء سجل إقفال في جدول enterprise_group_monthly_closings
        const closingData = {
            enterprise_id: group.enterprise_id,
            group_id: groupId,
            budget_month_id: selectedMonthId,
            group_name: group.group_name,
            group_total_cost: group.group_cost,
            previous_month_balance: group.previous_balance,
            current_month_total_due: group.total_due,
            current_month_paid_amount: group.paid_amount,
            closing_balance: group.remaining_amount, // الرصيد المتبقي سيرحل للشهر القادم
            is_closed: true,
            closing_date: new Date().toISOString(),
            notes: `تم إقفال المجموعة تلقائياً`
        };

        const { data, error } = await _supabase
            .from('enterprise_group_monthly_closings')
            .insert(closingData)
            .select()
            .single();

        if (error) throw error;

        showMessage(messageArea, `تم إقفال المجموعة "${group.group_name}" بنجاح!`, 'success');

        // إعادة تحميل البيانات
        await fetchSubscriptions();

    } catch (error) {
        console.error('خطأ في إقفال المجموعة:', error);
        showMessage(messageArea, `خطأ في إقفال المجموعة: ${error.message}`, 'error');
    }
};

// دالة إعادة فتح المجموعة
const handleReopenGroup = async (groupId) => {
    if (!confirm('هل أنت متأكد من إعادة فتح هذه المجموعة؟')) {
        return;
    }

    try {
        // حذف سجل الإقفال
        const { error } = await _supabase
            .from('enterprise_group_monthly_closings')
            .delete()
            .eq('group_id', groupId)
            .eq('budget_month_id', selectedMonthId);

        if (error) throw error;

        showMessage(messageArea, 'تم إعادة فتح المجموعة بنجاح!', 'success');

        // إعادة تحميل البيانات
        await fetchSubscriptions();

    } catch (error) {
        console.error('خطأ في إعادة فتح المجموعة:', error);
        showMessage(messageArea, `خطأ في إعادة فتح المجموعة: ${error.message}`, 'error');
    }
};

// دالة عرض تفاصيل المجموعة
const handleViewGroupDetails = (groupId) => {
    console.log(`عرض تفاصيل المجموعة: ${groupId}`);
    // هنا يمكن فتح نموذج لعرض تفاصيل الدفعات
    showMessage(messageArea, 'ميزة عرض التفاصيل قيد التطوير', 'info');
};

document.addEventListener('DOMContentLoaded', initializePage);
