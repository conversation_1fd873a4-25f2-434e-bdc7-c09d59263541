/* ===== أنماط صفحة مصاريف الحافلات ===== */

/* الأنماط الأساسية */
:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --accent-color: #f39c12;
    --light-color: #f5f7fa;
    --border-color: #e1e8ed;
    --text-dark: #2c3e50;
    --text-muted: #7f8c8d;
    --text-light: #ffffff;
    --danger-color: #e74c3c;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --card-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    --hover-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
    --card-bg: #ffffff;
    --border-radius: 8px;
}

body {
    font-family: 'Taja<PERSON>', sans-serif;
    background-color: var(--light-color);
    color: var(--text-dark);
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

/* تنسيق بطاقات الإحصائيات */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.summary-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 20px;
    display: flex;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    position: relative;
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.card-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
    color: var(--primary-color);
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: 1rem;
    color: var(--text-muted);
    margin: 0 0 8px 0;
    font-weight: 500;
}

.card-content p {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    color: var(--text-dark);
}

.card-content span {
    font-size: 0.9rem;
    color: var (--text-muted);
}

/* تنسيقات خاصة بكل نوع من بطاقات الإحصائيات */
.total-expenses .card-icon {
    color: var(--danger-color);
}

.avg-expense .card-icon {
    color: var(--info-color);
}

.bus-count .card-icon {
    color: var(--secondary-color);
}

.recent-expense .card-icon {
    color: var(--accent-color);
}

/* تنسيقات الرسالة */
.message {
    padding: 12px 15px;
    border-radius: 8px;
    margin: 15px 0;
    font-weight: 500;
    display: none;
}

.message.info {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.message.success {
    background-color: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(46, 204, 113, 0.2);
}

.message.error {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.message.warning {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(243, 156, 18, 0.2);
}

/* تنسيقات البطاقات والجداول */
.control-card, .table-card, .filter-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    margin-bottom: 25px;
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    margin: 0;
    font-size: 1.3rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-body {
    padding: 20px;
}

/* تنسيقات الفلترة */
.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    grid-gap: 15px;
}

.filter-actions {
    grid-column: 1 / -1;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 10px;
}

.form-group {
    margin-bottom: 10px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--secondary-color);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-family: 'Tajawal', sans-serif;
    color: var(--text-dark);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
}

.search-group {
    grid-column: span 2;
}

.filter-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.apply-filters-btn {
    background-color: var(--primary-color);
    color: white;
}

.apply-filters-btn:hover {
    background-color: var(--primary-dark);
}

.reset-filters-btn {
    background-color: #f0f0f0;
    color: var(--text-dark);
}

.reset-filters-btn:hover {
    background-color: #e0e0e0;
}

/* تنسيقات الجدول */
.table-responsive {
    overflow-x: auto;
}

#expenses-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 5px;
}

#expenses-table th,
#expenses-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

#expenses-table th {
    background-color: rgba(52, 152, 219, 0.08);
    color: var(--primary-color);
    font-weight: 600;
}

#expenses-table tr:last-child td {
    border-bottom: none;
}

#expenses-table tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

.badge {
    background-color: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 30px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* تنسيقات أزرار الإجراءات في الجدول */
.action-btn {
    background-color: transparent;
    border: none;
    font-size: 1rem;
    padding: 5px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.edit-btn {
    color: var(--primary-color);
}

.edit-btn:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

.delete-btn {
    color: var(--danger-color);
}

.delete-btn:hover {
    background-color: rgba(231, 76, 60, 0.1);
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* تنسيقات ترقيم الصفحات */
.pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
}

.pagination button {
    min-width: 40px;
    height: 40px;
    padding: 0 12px;
    border: none;
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination button:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* تنسيقات أزرار الإضافة والتحكم */
.control-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.control-btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

.add-btn {
    background-color: var(--success-color);
}

/* تنسيقات النافذة المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 600px;
    animation: modalFadeIn 0.3s ease-out;
    max-height: 90vh;
    overflow-y: auto;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.close-modal-btn {
    position: absolute;
    left: 20px;
    top: 15px;
    font-size: 24px;
    color: var(--text-muted);
    background: none;
    border: none;
    cursor: pointer;
}

.close-modal-btn:hover {
    color: var(--danger-color);
}

.modal-content h2 {
    margin: 0;
    color: var(--primary-color);
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
}

.modal .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    padding: 20px;
}

.modal .form-group-full-width {
    grid-column: 1 / -1;
}

.modal input,
.modal select,
.modal textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-family: 'Tajawal', sans-serif;
}

.modal input:focus,
.modal select:focus,
.modal textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
}

.required {
    color: var(--danger-color);
}

.modal .form-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    padding: 0 20px 20px;
}

.submit-btn, .cancel-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    min-width: 120px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.submit-btn {
    background-color: var(--primary-color);
    color: white;
}

.submit-btn:hover {
    background-color: var(--primary-dark);
}

.cancel-btn {
    background-color: #f0f0f0;
    color: var(--text-dark);
}

.cancel-btn:hover {
    background-color: #e0e0e0;
}

/* رسالة التحميل */
.loading-message {
    text-align: center;
    color: var(--text-muted);
    padding: 15px;
    font-style: italic;
}

/* تنسيقات تذييل الصفحة */
.main-footer {
    text-align: center;
    padding: 15px;
    background-color: var(--card-bg);
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.main-footer p {
    margin: 0;
}

.home-btn {
    background-color: var(--secondary-color);
    color: var(--text-light);
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.home-btn:hover {
    background-color: var(--primary-dark);
}

/* تحسينات للجوال والأجهزة اللوحية */
@media (max-width: 1200px) {
    .filter-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

@media (max-width: 768px) {
    .summary-cards {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }
    
    .dashboard-main {
        padding: 15px;
    }
    
    .filter-grid {
        grid-template-columns: 1fr;
    }
    
    .search-group {
        grid-column: 1;
    }
    
    .modal-content {
        width: 95%;
    }
    
    .modal .form-grid {
        grid-template-columns: 1fr;
    }
    
    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .card-header > div {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }
    
    #expenses-table {
        font-size: 0.9rem;
    }
    
    #expenses-table th,
    #expenses-table td {
        padding: 8px 10px;
    }
    
    .main-footer {
        flex-direction: column-reverse;
        gap: 10px;
    }
}

@media (max-width: 576px) {
    .filter-actions {
        flex-direction: column;
    }
    
    .filter-actions button {
        width: 100%;
    }
    
    .modal .form-actions {
        flex-direction: column;
    }
    
    .submit-btn, .cancel-btn {
        width: 100%;
    }
    
    .pagination button {
        min-width: 35px;
        height: 35px;
        padding: 0 8px;
        font-size: 0.9rem;
    }
}

/* أنماط صفحة مصاريف الحافلات */

/* الألوان المتغيرة */
:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --accent-color: #f39c12;
    --light-color: #f5f7fa;
    --border-color: #e1e8ed;
    --text-dark: #2c3e50;
    --text-muted: #7f8c8d;
    --text-light: #ffffff;
    --danger-color: #e74c3c;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --card-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    --hover-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
    --card-bg: #ffffff;
    --border-radius: 8px;
}

/* تنسيقات الحاوية مع القائمة الجانبية */
.container.with-sidebar {
    display: flex;
    min-height: calc(100vh - 60px);
}

/* تنسيقات المحتوى الرئيسي */
.dashboard-main {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

/* بطاقات الإحصائيات */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.summary-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 20px;
    display: flex;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    position: relative;
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.card-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
    color: var(--primary-color);
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: 1rem;
    color: var(--text-muted);
    margin: 0 0 8px 0;
    font-weight: 500;
}

.card-content p {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    color: var(--text-dark);
}

.card-content span {
    font-size: 0.9rem;
    color: var(--text-muted);
}

/* ألوان مخصصة لكل نوع بطاقة */
.summary-card.maintenance .card-icon {
    color: var(--info-color);
}

.summary-card.fuel .card-icon {
    color: var(--warning-color);
}

.summary-card.other .card-icon {
    color: var(--accent-color);
}

.summary-card.total .card-icon {
    color: var(--success-color);
}

/* نمط رسائل التحميل */
.loading-message {
    text-align: center;
    color: var(--text-muted);
    padding: 15px;
    font-style: italic;
}

/* نمط النافذة المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 600px;
    animation: modalFadeIn 0.3s ease-out;
    max-height: 90vh;
    overflow-y: auto;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.close-modal-btn {
    position: absolute;
    left: 20px;
    top: 15px;
    font-size: 24px;
    color: var(--text-muted);
    background: none;
    border: none;
    cursor: pointer;
}

.close-modal-btn:hover {
    color: var(--danger-color);
}

.modal-content h2 {
    margin: 0;
    color: var(--primary-color);
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
}

.modal .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    padding: 20px;
}

.modal .form-group-full-width {
    grid-column: 1 / -1;
}

.modal-subtitle {
    padding: 10px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.95rem;
}

.form-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    padding: 0 20px 20px;
}

.submit-btn, .cancel-btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    min-width: 120px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.submit-btn {
    background-color: var(--primary-color);
    color: white;
}

.submit-btn:hover {
    background-color: var(--primary-dark);
}

.cancel-btn {
    background-color: #f0f0f0;
    color: var(--text-dark);
}

.cancel-btn:hover {
    background-color: #e0e0e0;
}

.form-group {
    margin-bottom: 10px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--secondary-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    color: var(--text-dark);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
}

.form-group input[readonly],
.form-group input:disabled {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

.required {
    color: var(--danger-color);
}

/* تنسيقات الجدول */
.table-responsive {
    overflow-x: auto;
}

#expenses-table {
    width: 100%;
    border-collapse: collapse;
}

#expenses-table th,
#expenses-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

#expenses-table th {
    background-color: rgba(52, 152, 219, 0.08);
    color: var(--primary-color);
    font-weight: 600;
}

#expenses-table tr:last-child td {
    border-bottom: none;
}

#expenses-table tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

/* أزرار إجراءات الجدول */
.action-btn {
    background-color: transparent;
    border: none;
    font-size: 1rem;
    padding: 5px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.edit-btn {
    color: var(--primary-color);
}

.edit-btn:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

.delete-btn {
    color: var(--danger-color);
}

.delete-btn:hover {
    background-color: rgba(231, 76, 60, 0.1);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 992px) {
    .container.with-sidebar {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        border-left: none;
        border-bottom: 1px solid var(--border-color);
        max-height: 200px;
    }
    
    .dashboard-main {
        padding: 15px;
    }
}

@media (max-width: 768px) {
    .summary-cards {
        grid-template-columns: repeat(auto-fit, minmax(100%, 1fr));
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .submit-btn, .cancel-btn {
        width: 100%;
    }
    
    .card-header {
        flex-direction: column;
        align-items: start;
    }
    
    .card-header div {
        margin-top: 10px;
        width: 100%;
        display: flex;
        justify-content: space-between;
    }
}

/* تنسيقات للرسائل */
.message {
    padding: 12px 15px;
    margin: 15px 0;
    border-radius: 8px;
    font-weight: 500;
    display: none;
}

.message.info {
    background-color: rgba(52, 152, 219, 0.1);
    color: var (--info-color);
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.message.success {
    background-color: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(46, 204, 113, 0.2);
}

.message.error {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.message.warning {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(243, 156, 18, 0.2);
}

/* الترقيم الصفحي */
.pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
}

.pagination button {
    min-width: 40px;
    height: 40px;
    padding: 0 12px;
    border: none;
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination button:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Styles for the centered bus info header */
.bus-info-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background-color: var(--card-bg, #fff);
    border-radius: var(--border-radius, 8px);
    box-shadow: var(--card-shadow, 0 2px 10px rgba(0,0,0,0.07));
    display: flex; /* Use flexbox for alignment */
    flex-direction: column; /* Stack items vertically */
    align-items: center; /* Center items horizontally */
}

.bus-info-header .bus-icon-large {
    font-size: 4rem;
    margin-bottom: 15px;
    display: inline-block; /* Allows centering */
}

.bus-info-header .bus-icon-large.yellow {
    color: #f1c40f; /* Yellow color for the icon */
}

/* Container for Bus Number and Selector */
.bus-selector-container {
    display: flex;
    align-items: center; /* Align items vertically */
    gap: 15px; /* Space between number and dropdown */
    margin-top: 10px; /* Space below icon */
    margin-bottom: 5px; /* Space above subtitle */
}

.bus-info-header h1 {
    font-size: 1.8rem;
    color: var(--secondary-color, #2c3e50);
    margin-bottom: 0; /* Remove bottom margin as gap is handled by container */
}

.bus-selector-dropdown {
    padding: 6px 10px;
    border: 1px solid var(--border-color, #ccc);
    border-radius: var(--border-radius, 4px);
    font-size: 1rem;
    background-color: #fff;
    cursor: pointer;
    min-width: 150px; /* Give it some minimum width */
    transition: border-color 0.3s ease;
}

.bus-selector-dropdown:hover {
    border-color: var(--primary-color, #3498db);
}

.bus-selector-dropdown:focus {
    outline: none;
    border-color: var(--primary-color, #3498db);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.bus-selector-dropdown:disabled {
    background-color: #f0f0f0;
    cursor: not-allowed;
    opacity: 0.7;
}

.bus-info-header p {
    font-size: 1rem;
    color: var(--text-muted, #7f8c8d);
    margin-bottom: 15px;
}

/* Styles for the expense cards grid */
.expense-cards-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr); /* 5 columns */
    gap: 20px;
    margin-bottom: 30px;
}

.expense-card {
    background-color: var(--card-bg, #fff);
    border-radius: var(--border-radius, 8px);
    padding: 20px 15px;
    text-align: center;
    box-shadow: var(--card-shadow, 0 2px 10px rgba(0,0,0,0.07));
    border-top: 4px solid transparent; /* For color indication */
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    min-height: 120px; /* Give cards some minimum height */
    display: flex;
    flex-direction: column;
    justify-content: center; /* Center content vertically */
}

.expense-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--hover-shadow, 0 5px 15px rgba(0,0,0,0.1));
}

.expense-card .card-icon {
    font-size: 2rem;
    margin-bottom: 10px;
    opacity: 0.8;
}

.expense-card h3 {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 5px;
    color: var(--secondary-color, #2c3e50);
}

.expense-card p {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-dark, #34495e);
    margin: 0;
}

/* Central Total Card */
.total-card {
    grid-column: 3 / 4; /* Place in the 3rd column */
    grid-row: 1 / 4; /* Span 3 rows */
    background-color: var(--primary-color, #3498db); /* Blue background */
    color: var(--text-light, #fff);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-top: none; /* Remove top border */
    border-radius: var(--border-radius, 8px);
    padding: 20px;
    box-shadow: var(--card-shadow, 0 2px 10px rgba(0,0,0,0.07));
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}
.total-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--hover-shadow, 0 5px 15px rgba(0,0,0,0.1));
}

.total-card .card-icon {
    font-size: 3rem;
    opacity: 1;
    color: var(--text-light, #fff);
    margin-bottom: 15px;
}

.total-card h3 {
    font-size: 1.2rem;
    color: var(--text-light, #fff);
    margin-bottom: 10px;
}

.total-card p {
    font-size: 2rem;
    color: var(--text-light, #fff);
    margin-bottom: 10px;
}
.total-card span {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Color definitions based on image */
.expense-card.color-blue { border-top-color: #3498db; }
.expense-card.color-blue .card-icon { color: #3498db; }

.expense-card.color-purple { border-top-color: #9b59b6; }
.expense-card.color-purple .card-icon { color: #9b59b6; }

.expense-card.color-green { border-top-color: #2ecc71; }
.expense-card.color-green .card-icon { color: #2ecc71; }

.expense-card.color-lightblue { border-top-color: #5dade2; } /* Adjusted light blue */
.expense-card.color-lightblue .card-icon { color: #5dade2; }

.expense-card.color-yellow { border-top-color: #f1c40f; }
.expense-card.color-yellow .card-icon { color: #f1c40f; }

.expense-card.color-orange { border-top-color: #e67e22; }
.expense-card.color-orange .card-icon { color: #e67e22; }

.expense-card.color-teal { border-top-color: #1abc9c; } /* Teal for spare parts & rent */
.expense-card.color-teal .card-icon { color: #1abc9c; }

.expense-card.color-red { border-top-color: #e74c3c; } /* Red for fuel & violations */
.expense-card.color-red .card-icon { color: #e74c3c; }

.expense-card.color-grey { border-top-color: #95a5a6; }
.expense-card.color-grey .card-icon { color: #95a5a6; }


/* Responsive adjustments for the grid */
@media (max-width: 1200px) {
    .expense-cards-grid {
        grid-template-columns: repeat(4, 1fr); /* 4 columns */
    }
    .total-card {
        grid-column: span 2; /* Make total card span 2 columns */
        grid-row: auto; /* Reset row span */
        /* You might need to use 'order' property to place it correctly */
        order: 1; /* Example: try to place it after first few items */
    }
    /* Adjust order of other items if needed */
    .expense-cards-grid > div:not(.total-card) {
        order: 2;
    }
}

@media (max-width: 992px) {
    .expense-cards-grid {
        grid-template-columns: repeat(3, 1fr); /* 3 columns */
    }
     .total-card {
        grid-column: span 1; /* Back to 1 column span */
        order: 0; /* Reset order or adjust as needed */
    }
     .expense-cards-grid > div:not(.total-card) {
        order: 0; /* Reset order */
    }
}

@media (max-width: 768px) {
    .expense-cards-grid {
        grid-template-columns: repeat(2, 1fr); /* 2 columns */
    }
    .total-card {
        grid-column: span 2; /* Span full width */
        order: -1; /* Move total card to the top */
    }
    .expense-card p {
        font-size: 1.1rem;
    }
    .total-card p {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .expense-cards-grid {
        grid-template-columns: 1fr; /* 1 column */
    }
     .total-card {
        grid-column: span 1; /* Span full width */
        order: -1; /* Keep at top */
    }
}

/* Inherit base styles from shared_styles.css */
/* Define fallback variables if needed */
:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #34495e;
    --text-color: #333;
    --text-muted: #777;
    --bg-color: #f4f7f9;
    --card-bg: #ffffff;
    --border-color: #e0e0e0;
    --hover-bg: #e9ecef;
    --border-radius: 6px;
    --card-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

/* General Enhancements */
body {
    font-family: 'Tajawal', sans-serif; /* Ensure correct font */
    background-color: var(--bg-color);
    color: var(--text-color);
    margin: 0;
    line-height: 1.6;
}

/* تعديل الحاوية الرئيسية لتناسب عرض الصفحة الواحدة */
.dashboard-container.single-view {
    display: block; /* No flex needed */
}

/* تعديل محتوى الصفحة الرئيسي */
.dashboard-content {
    /* إزالة flex-grow */
    padding: 20px; /* تقليل الحشو قليلاً */
    max-width: 1400px; /* زيادة العرض الأقصى قليلاً */
    margin: 0 auto; /* توسيط المحتوى */
}

/* Header */
.dashboard-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: #fff;
    padding: 25px 30px; /* زيادة الحشو */
    margin-bottom: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    position: relative; /* For back button positioning */
    text-align: center; /* Center header text */
}
.dashboard-header h1 {
    margin: 0 0 8px 0;
    font-size: 2rem; /* تكبير الخط */
    font-weight: 700;
}
.dashboard-header h1 i {
    margin-left: 12px;
}
.dashboard-header p {
    margin: 0;
    font-size: 1.1rem; /* تكبير الخط */
    opacity: 0.9;
}
.dashboard-header .back-btn {
    position: absolute;
    top: 20px;
    left: 20px;
    background-color: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #fff;
}
.dashboard-header .back-btn:hover {
    background-color: rgba(255, 255, 255, 0.25);
}

/* --- إضافة: أنماط قسم الإحصائيات المفصل الجديد --- */
.detailed-stats-section {
    margin-bottom: 30px;
    background-color: var(--card-bg, #fff);
    border-radius: var(--border-radius, 6px);
    box-shadow: var(--card-shadow, 0 2px 5px rgba(0,0,0,0.1));
    padding: 20px;
}

.detailed-stats-container {
    display: flex;
    flex-wrap: wrap; /* السماح بالالتفاف في الشاشات الصغيرة */
    justify-content: center; /* توسيط العناصر */
    align-items: center;
    gap: 15px; /* مسافة بين العناصر */
}

.stat-item {
    background-color: #f8f9fa; /* خلفية فاتحة للعناصر */
    border: 1px solid var(--border-color, #e0e0e0);
    border-radius: var(--border-radius, 6px);
    padding: 10px 15px;
    text-align: center;
    min-width: 130px; /* عرض أدنى للعنصر */
    flex: 1; /* السماح للعناصر بالتمدد */
    transition: transform 0.2s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
}

.stat-label {
    display: block;
    font-size: 0.85rem;
    color: var(--text-muted, #777);
    margin-bottom: 5px;
    font-weight: 500;
}

.stat-value {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--secondary-color, #2c3e50);
}

/* تمييز عنصر المجموع الإجمالي */
.stat-item.total-stat {
    background-color: var(--primary-color, #3498db);
    color: #fff;
    border-color: var(--primary-dark, #2980b9);
    min-width: 180px; /* عرض أكبر للمجموع */
    padding: 15px 20px; /* حشو أكبر */
    order: 0; /* محاولة وضعه في الوسط (قد يحتاج لتعديل حسب عدد العناصر) */
    transform: scale(1.05); /* تكبير طفيف */
}

.stat-item.total-stat .stat-label {
    color: rgba(255, 255, 255, 0.85);
    font-size: 0.9rem;
}

.stat-item.total-stat .stat-value {
    color: #fff;
    font-size: 1.4rem;
    font-weight: 700;
}

/* تعديلات للشاشات الصغيرة */
@media (max-width: 992px) {
    .detailed-stats-container {
        justify-content: space-around; /* توزيع أفضل في الشاشات المتوسطة */
    }
    .stat-item {
        min-width: 110px;
    }
    .stat-item.total-stat {
        order: -1; /* وضعه في البداية في الشاشات الصغيرة */
        width: 100%; /* يأخذ عرض كامل */
        margin-bottom: 15px; /* إضافة مسافة سفلية */
        transform: scale(1); /* إزالة التكبير */
    }
}

@media (max-width: 576px) {
    .stat-item {
        min-width: 90px;
        padding: 8px 10px;
    }
    .stat-label {
        font-size: 0.75rem;
    }
    .stat-value {
        font-size: 0.95rem;
    }
    .stat-item.total-stat .stat-value {
        font-size: 1.2rem;
    }
}
/* --- نهاية أنماط القسم الجديد --- */

/* Controls Section */
.control-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 30px;
}
.control-card .card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}
.control-card .card-header h2 {
    margin: 0;
    font-size: 1.2rem;
    color: var(--secondary-color);
}
.control-card .card-body {
    padding: 20px;
}

/* --- إضافة: أنماط شبكة الفلاتر --- */
.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); /* أعمدة مرنة */
    gap: 15px 20px; /* مسافات بين العناصر */
    align-items: end; /* محاذاة العناصر للأسفل */
}

.filters-grid .form-group {
    margin-bottom: 0; /* إزالة الهامش السفلي الافتراضي */
}

.filters-grid label {
    font-size: 0.9rem;
    margin-bottom: 4px;
}

.filters-grid input,
.filters-grid select {
    padding: 8px 10px; /* تقليل الحشو */
    font-size: 0.95rem;
}

.filter-actions {
    display: flex;
    gap: 10px;
    grid-column: 1 / -1; /* جعل الأزرار تمتد على عرض الشبكة */
    justify-content: flex-start; /* محاذاة الأزرار لليمين */
    margin-top: 10px; /* هامش علوي للأزرار */
}

.filter-actions .control-btn {
    padding: 8px 15px; /* تقليل حشو الأزرار */
    font-size: 0.9rem;
}
/* --- نهاية أنماط الفلاتر --- */


/* Table Section */
.table-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 30px;
    overflow: hidden; /* لإخفاء أي تجاوز */
}
.table-card .card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.table-card .card-header h2 {
    margin: 0;
    font-size: 1.2rem;
    color: var(--secondary-color);
}
.table-card .card-body {
    padding: 0; /* إزالة الحشو للسماح للجدول بأخذ العرض الكامل */
}
.table-responsive {
    overflow-x: auto;
}
#expenses-table {
    width: 100%;
    border-collapse: collapse;
    /* margin-top: 0; */ /* إزالة الهامش العلوي */
}
#expenses-table th, #expenses-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
    white-space: nowrap;
}
#expenses-table th {
    background-color: #f8f9fa; /* لون خلفية أفتح للترويسة */
    font-weight: 600;
    color: var(--secondary-color);
    position: sticky; /* تثبيت الترويسة عند التمرير */
    top: 0;
    z-index: 10;
}
#expenses-table tbody tr:hover {
    background-color: var(--hover-bg);
}
#expenses-table tbody tr:last-child td {
    border-bottom: none; /* إزالة الخط السفلي لآخر صف */
}
#expenses-table .loading-message, #list-message {
    text-align: center;
    padding: 30px 20px; /* زيادة الحشو */
    color: var(--text-muted);
    font-size: 1.1rem;
}
#list-message {
    margin: 0;
    border-radius: 0;
    border-bottom: 1px solid var(--border-color);
}

/* Table Action Buttons */
.action-btn {
    /* ... (نفس الأنماط السابقة) ... */
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px 8px;
    margin: 0 2px;
    border-radius: var(--border-radius);
    font-size: 0.9em;
    transition: background-color 0.2s, color 0.2s;
}
.action-btn i { font-size: 1.1em; }
.action-btn.edit-btn { color: var(--warning-color); }
.action-btn.edit-btn:hover { background-color: rgba(243, 156, 18, 0.1); }
.action-btn.delete-btn { color: var(--danger-color); }
.action-btn.delete-btn:hover { background-color: rgba(231, 76, 60, 0.1); }

/* Pagination */
.pagination {
    padding: 15px 20px;
    text-align: center;
    border-top: 1px solid var(--border-color);
    background-color: #fdfdfd;
}
.pagination button, .pagination span {
    margin: 0 5px;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.2s, color 0.2s;
    color: var(--primary-color);
}
.pagination button:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}
.pagination button:disabled {
    background-color: var(--light-color);
    color: var(--text-muted);
    cursor: not-allowed;
    border-color: var(--border-color);
}
.pagination span.page-info {
    border: none;
    background: none;
    cursor: default;
    color: var(--text-muted);
    padding: 8px 10px;
}


/* Modal Styles */
.form-section {
    /* ... (نفس الأنماط السابقة) ... */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}
.form-section.show { opacity: 1; visibility: visible; }
.form-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
    width: 90%;
    max-width: 750px; /* زيادة العرض قليلاً */
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}
.form-card .card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    position: relative; /* For close button */
}
.form-card .card-header h2 { margin: 0; font-size: 1.3rem; }
.form-card .card-body {
    padding: 25px 30px; /* زيادة الحشو */
    overflow-y: auto; /* السماح بالتمرير للمحتوى الطويل */
    flex-grow: 1;
}
.close-btn {
    position: absolute;
    top: 10px;
    left: 15px;
    background: none;
    border: none;
    font-size: 1.8rem; /* تكبير زر الإغلاق */
    line-height: 1;
    cursor: pointer;
    color: var(--text-muted);
    padding: 5px;
}
.close-btn:hover { color: var(--danger-color); }

/* Modal Form Grid */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px 20px;
}
.form-group { margin-bottom: 15px; }
.form-group label { display: block; margin-bottom: 6px; font-weight: 500; color: var(--secondary-color); font-size: 0.9rem; }
.form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-sizing: border-box;
    font-size: 0.95rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}
.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}
.form-group input[readonly] { background-color: #f0f0f0; cursor: not-allowed; }
.form-group textarea { resize: vertical; min-height: 80px; }
.required { color: var(--danger-color); margin-right: 3px; }

/* Modal Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}
.form-actions .control-btn { padding: 10px 20px; font-size: 1rem; }
.submit-btn { background-color: var(--primary-color); color: #fff; }
.submit-btn:hover:not(:disabled) { background-color: var(--primary-dark); }
.cancel-btn { background-color: var(--text-muted); color: #fff; }
.cancel-btn:hover:not(:disabled) { background-color: var(--secondary-color); }

/* Messages */
.message {
    padding: 12px 15px;
    margin-bottom: 20px; /* زيادة الهامش */
    border-radius: var(--border-radius);
    text-align: center;
    font-weight: 500;
    border: 1px solid transparent;
    display: none; /* Hidden by default */
}
.message.show { display: block; }
.message.info { background-color: #eaf5fd; border-color: #b8daff; color: #0c5460; }
.message.success { background-color: #eafaf1; border-color: #b1dfbb; color: #155724; }
.message.warning { background-color: #fef5e7; border-color: #ffeeba; color: #856404; }
.message.error { background-color: #fdedec; border-color: #f5c6cb; color: #721c24; }

/* Footer */
.dashboard-footer {
    text-align: center;
    margin-top: 40px; /* زيادة الهامش */
    padding: 20px;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
    font-size: 0.9rem;
    background-color: var(--card-bg);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .dashboard-content { padding: 15px; }
    .dashboard-header { padding: 20px; text-align: center; }
    .dashboard-header .back-btn { top: 15px; left: 15px; padding: 6px 10px; }
    .dashboard-header h1 { font-size: 1.6rem; }
    .dashboard-header p { font-size: 0.95rem; }
    .stats-grid { grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 15px; }
    .dash-card { flex-direction: column; text-align: center; padding: 15px; }
    .dash-card .card-icon { margin-left: 0; margin-bottom: 10px; font-size: 2rem; }
    .dash-card .card-content p { font-size: 1.3rem; }
    .filters-grid { grid-template-columns: 1fr 1fr; } /* عمودين للفلاتر */
    .filter-actions { grid-column: 1 / -1; justify-content: center; flex-wrap: wrap; }
    #expenses-table th, #expenses-table td { padding: 10px 8px; font-size: 0.9rem; }
    .form-card { width: 95%; max-height: 85vh; }
    .form-card .card-body { padding: 20px; }
    .form-grid { grid-template-columns: 1fr; } /* عمود واحد للنموذج */
    .form-group[style*="grid-column"] { grid-column: auto !important; } /* إلغاء امتداد الأعمدة */
    .form-actions { justify-content: center; }
}
