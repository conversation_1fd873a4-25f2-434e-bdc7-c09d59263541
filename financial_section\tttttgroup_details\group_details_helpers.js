// --- Helper Functions ---
const showMessage = (element, message, type = 'info', duration = 3000) => {
    if (!element) return;
    element.textContent = message;
    element.className = `message ${type}`;
    element.style.display = 'block';
    if (duration > 0) {
        setTimeout(() => {
            if (element.textContent === message) {
                element.style.display = 'none';
            }
        }, duration);
    }
};

const formatCurrency = (amount) => {
    const value = parseFloat(amount);
    return isNaN(value) ? '0.00' : value.toFixed(2);
};

const getMonthName = (monthNumber) => {
    const names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"];
    const index = parseInt(monthNumber, 10) - 1;
    return names[index] || `شهر ${monthNumber}`;
};

// --- Export Functions ---
export { showMessage, formatCurrency, getMonthName };
