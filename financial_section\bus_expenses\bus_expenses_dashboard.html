<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة معلومات مصاريف الحافلات</title>
    <!-- Shared Styles -->
    <link rel="stylesheet" href="../../shared_styles.css">
    <!-- Page Specific Styles -->
    <link rel="stylesheet" href="bus_expenses_dashboard.css">
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Config -->
    <script src="../../config.js"></script>
    <!-- Auth Script -->
    <script src="../../auth.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- إضافة مكتبة Choices.js للـ multi-select (اختياري لكن يحسن التجربة) -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js/public/assets/styles/choices.min.css"/>
    <script src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js"></script>
</head>
<body>
    <!-- Navbar -->
    <nav class="main-navbar">
        <div class="navbar-brand">
            <i class="fas fa-bus"></i>
            لوحة معلومات مصاريف الحافلات
        </div>
        <div class="navbar-user">
            <span id="navbar-username"></span>
            <button id="logout-btn" class="logout-btn" style="display: none;">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </button>
        </div>
    </nav>

    <div class="dashboard-container">
        <div class="dashboard-content">
            <main class="dashboard-main">
                <!-- Page Header -->
                <header class="dashboard-page-header">
                    <h1><i class="fas fa-tachometer-alt"></i> نظرة عامة على مصاريف الحافلات</h1>
                    <p>عرض وتحليل مصاريف الحافلات حسب النوع <span id="period-display">للشهر الحالي</span></p>
                    <p>ملخص المصاريف لـ <span id="current-month-year">--</span></p>
                    <button id="open-report-modal-btn" class="control-btn report-btn">
                        <i class="fas fa-file-alt"></i> إنشاء تقرير
                    </button>
                    <div id="dashboard-message" class="message" style="display: none;"></div>
                </header>

                <!-- Expense Type Dashboard -->
                <section class="expenses-dashboard">
                    <div class="expense-boxes right-boxes">
                        <div class="expense-box" data-type="صيانة">
                            <div class="expense-icon"><i class="fas fa-tools"></i></div>
                            <h3>صيانة</h3>
                            <p class="expense-amount">0.00 ريال</p>
                        </div>
                        <div class="expense-box" data-type="تأمين">
                            <div class="expense-icon"><i class="fas fa-shield-alt"></i></div>
                            <h3>تأمين</h3>
                            <p class="expense-amount">0.00 ريال</p>
                        </div>
                        <div class="expense-box" data-type="تغير زيت">
                            <div class="expense-icon"><i class="fas fa-oil-can"></i></div>
                            <h3>تغير زيت</h3>
                            <p class="expense-amount">0.00 ريال</p>
                        </div>
                        <div class="expense-box" data-type="تغير كفرات">
                            <div class="expense-icon"><i class="fas fa-ring"></i></div>
                            <h3>تغير كفرات</h3>
                            <p class="expense-amount">0.00 ريال</p>
                        </div>
                        <div class="expense-box" data-type="ايجار">
                            <div class="expense-icon"><i class="fas fa-key"></i></div>
                            <h3>إيجار</h3>
                            <p class="expense-amount">0.00 ريال</p>
                        </div>
                        <div class="expense-box" data-type="وقود">
                            <div class="expense-icon"><i class="fas fa-gas-pump"></i></div>
                            <h3>وقود</h3>
                            <p class="expense-amount">0.00 ريال</p>
                        </div>
                    </div>

                    <div class="total-expense">
                        <div class="total-icon"><i class="fas fa-money-bill-wave"></i></div>
                        <h2>إجمالي المصاريف</h2>
                        <!-- تعديل: إضافة تفاصيل الإجمالي -->
                        <p id="total-base-amount" class="total-detail"><span>الأساسي:</span> 0.00 ريال</p>
                        <p id="total-tax-amount" class="total-detail"><span>الضريبة:</span> 0.00 ريال</p>
                        <p id="grand-total-amount" class="grand-total-value"><span>الكلي:</span> 0.00 ريال</p>
                        <!-- نهاية التعديل -->
                        <div class="period-selector">
                            <button class="period-btn active" data-period="month">الشهر الحالي</button>
                        </div>
                    </div>

                    <div class="expense-boxes left-boxes">
                        <div class="expense-box" data-type="تراخيص">
                            <div class="expense-icon"><i class="fas fa-id-card"></i></div>
                            <h3>تراخيص</h3>
                            <p class="expense-amount">0.00 ريال</p>
                        </div>
                        <div class="expense-box" data-type="غسيل">
                            <div class="expense-icon"><i class="fas fa-shower"></i></div>
                            <h3>غسيل</h3>
                            <p class="expense-amount">0.00 ريال</p>
                        </div>
                        <div class="expense-box" data-type="فحص دوري">
                            <div class="expense-icon"><i class="fas fa-clipboard-check"></i></div>
                            <h3>فحص دوري</h3>
                            <p class="expense-amount">0.00 ريال</p>
                        </div>
                        <div class="expense-box" data-type="قطع غيار">
                            <div class="expense-icon"><i class="fas fa-cogs"></i></div>
                            <h3>قطع غيار</h3>
                            <p class="expense-amount">0.00 ريال</p>
                        </div>
                        <div class="expense-box" data-type="مخالفات">
                            <div class="expense-icon"><i class="fas fa-exclamation-triangle"></i></div>
                            <h3>مخالفات</h3>
                            <p class="expense-amount">0.00 ريال</p>
                        </div>
                        <div class="expense-box" data-type="أخرى">
                            <div class="expense-icon"><i class="fas fa-ellipsis-h"></i></div>
                            <h3>أخرى</h3>
                            <p class="expense-amount">0.00 ريال</p>
                        </div>
                    </div>
                </section>

                <!-- Buses Section -->
                <section class="buses-section">
                    <h2><i class="fas fa-bus"></i> قائمة الحافلات</h2>
                    <div class="buses-container" id="buses-list">
                        <!-- Buses will be loaded dynamically here -->
                        <div class="loading-message">جاري تحميل بيانات الحافلات...</div>
                    </div>
                </section>
            </main>
        </div><!-- نهاية dashboard-content -->
    </div><!-- نهاية dashboard-container -->

    <!-- Footer -->
    <footer class="main-footer">
        <button id="back-to-finance" class="home-btn">
            <i class="fas fa-arrow-left"></i> العودة للوحة المالية
        </button>
        <p>&copy; 2024 نظام إدارة النقل المدرسي</p>
    </footer>

    <!-- تعديل: نافذة منبثقة لاختيار معايير التقرير -->
    <div id="report-modal" class="modal">
        <!-- تقليل حجم النافذة قليلاً -->
        <div class="modal-content">
            <span id="close-report-modal-btn" class="close-modal-btn">&times;</span>
            <!-- تعديل العنوان -->
            <h2><i class="fas fa-file-alt"></i> تحديد معايير التقرير</h2>
            <div id="report-modal-message" class="message" style="display: none;"></div>

            <div class="report-options">
                <div class="form-group">
                    <label for="report-bus-select">اختر الحافلات (واحدة أو أكثر):</label>
                    <select id="report-bus-select" multiple>
                        <!-- سيتم ملء الخيارات بواسطة JavaScript -->
                    </select>
                </div>
                <div class="form-group">
                    <label>فترة التقرير:</label>
                    <span id="report-period-display" style="font-weight: bold;">الشهر المحدد حالياً</span>
                </div>
                <div class="form-actions">
                    <!-- تعديل الزر -->
                    <button id="launch-report-btn" class="control-btn submit-btn">
                        <i class="fas fa-external-link-alt"></i> فتح التقرير في صفحة جديدة
                    </button>
                    <button type="button" class="control-btn cancel-btn" onclick="closeReportModal()">إلغاء</button>
                </div>
            </div>

            <!-- إزالة: قسم عرض التقرير والتنبيهات -->
            <!--
            <hr style="margin: 20px 0;">
            <div class="report-output-section"> ... </div>
            -->
        </div>
    </div>
    <!-- نهاية نافذة التقرير -->

    <!-- Page Scripts -->
    <script defer src="bus_expenses_dashboard.js"></script>
</body>
</html>
