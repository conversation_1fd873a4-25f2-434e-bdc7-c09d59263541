// Supabase Initialization
let supabaseUrl, supabaseAnonKey;
if (typeof SUPABASE_URL !== 'undefined') supabaseUrl = SUPABASE_URL;
else console.error('SUPABASE_URL not found.');
if (typeof SUPABASE_ANON_KEY !== 'undefined') supabaseAnonKey = SUPABASE_ANON_KEY;
else console.error('SUPABASE_ANON_KEY not found.');

const { createClient } = supabase;
const _supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('Supabase Initialized for Month Selection:', _supabase);

// DOM Elements
const yearSelect = document.getElementById('select-year');
const monthSelect = document.getElementById('select-month');
const messageArea = document.getElementById('message-area');
const actionsCard = document.getElementById('actions-card');
const selectedMonthNameEl = document.getElementById('selected-month-name');
const selectedYearNumberEl = document.getElementById('selected-year-number');
const monthStatusDisplay = document.getElementById('month-status-display');
const manageSubscriptionsBtn = document.getElementById('manage-subscriptions-btn');
const viewTransactionsBtn = document.getElementById('view-transactions-btn');

// State
let availableYears = [];
let availableMonths = [];
let selectedYearId = null;
let selectedMonthId = null;
let selectedMonthData = null;

// Function to display messages
const showMessage = (message, type = 'info') => {
    messageArea.textContent = message;
    messageArea.className = `message ${type} show`;
    messageArea.style.display = 'block';
};

// Fetch available budget years
const fetchYears = async () => {
    try {
        const { data, error } = await _supabase
            .from('budget_years')
            .select('id, year_number')
            .order('year_number', { ascending: false });
        if (error) throw error;
        availableYears = data || [];
        populateYearDropdown();
    } catch (error) {
        console.error('Error fetching years:', error);
        showMessage(`خطأ في جلب السنوات: ${error.message}`, 'error');
    }
};

// Populate year dropdown
const populateYearDropdown = () => {
    yearSelect.innerHTML = '<option value="">اختر السنة...</option>';
    availableYears.forEach(year => {
        const option = document.createElement('option');
        option.value = year.id;
        option.textContent = year.year_number;
        yearSelect.appendChild(option);
    });
};

// Fetch months for a selected year
const fetchMonthsForYear = async (yearId) => {
    monthSelect.innerHTML = '<option value="">جاري التحميل...</option>';
    monthSelect.disabled = true;
    actionsCard.style.display = 'none'; // Hide actions when year changes
    selectedMonthData = null;
    selectedMonthId = null;

    if (!yearId) {
        monthSelect.innerHTML = '<option value="">اختر السنة أولاً...</option>';
        return;
    }
    try {
        const { data, error } = await _supabase
            .from('budget_months')
            .select('*') // Fetch all details including is_closed
            .eq('budget_year_id', yearId)
            .order('month_number', { ascending: true });
        if (error) throw error;
        availableMonths = data || [];
        populateMonthDropdown();
    } catch (error) {
        console.error('Error fetching months:', error);
        monthSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
        showMessage(`خطأ في جلب الشهور: ${error.message}`, 'error');
    } finally {
        monthSelect.disabled = false;
    }
};

// Populate month dropdown
const populateMonthDropdown = () => {
    monthSelect.innerHTML = '<option value="">اختر الشهر...</option>';
    availableMonths.forEach(month => {
        const option = document.createElement('option');
        option.value = month.id;
        option.textContent = `${month.month_name || `شهر ${month.month_number}`} (${month.is_closed ? 'مغلق' : 'مفتوح'})`;
        monthSelect.appendChild(option);
    });
    monthSelect.disabled = false;
};

// Show actions for the selected month
const showMonthActions = () => {
    if (!selectedMonthId || !selectedYearId) {
        actionsCard.style.display = 'none';
        return;
    }

    selectedMonthData = availableMonths.find(m => m.id == selectedMonthId);
    const selectedYear = availableYears.find(y => y.id == selectedYearId);

    if (!selectedMonthData || !selectedYear) {
        actionsCard.style.display = 'none';
        showMessage('خطأ في العثور على بيانات الشهر أو السنة المختارة.', 'error');
        return;
    }

    selectedMonthNameEl.textContent = selectedMonthData.month_name || `شهر ${selectedMonthData.month_number}`;
    selectedYearNumberEl.textContent = selectedYear.year_number;

    // Display month status
    if (selectedMonthData.is_closed) {
        monthStatusDisplay.textContent = 'الحالة: مغلق';
        monthStatusDisplay.className = 'month-status closed';
    } else {
        monthStatusDisplay.textContent = 'الحالة: مفتوح';
        monthStatusDisplay.className = 'month-status open';
    }
    monthStatusDisplay.style.display = 'block';


    actionsCard.style.display = 'block';
    messageArea.style.display = 'none'; // Hide any previous messages
};

// Event Listeners
yearSelect.addEventListener('change', (e) => {
    selectedYearId = e.target.value;
    fetchMonthsForYear(selectedYearId);
});

monthSelect.addEventListener('change', (e) => {
    selectedMonthId = e.target.value;
    showMonthActions();
});

manageSubscriptionsBtn.addEventListener('click', () => {
    if (selectedYearId && selectedMonthId) {
        // Navigate to subscriptions page with parameters
        window.location.href = `../subscriptions/subscriptions.html?yearId=${selectedYearId}&monthId=${selectedMonthId}`;
    }
});

viewTransactionsBtn.addEventListener('click', () => {
    if (selectedYearId && selectedMonthId) {
        // Navigate to transactions page with parameters
        window.location.href = `../transactions/transactions.html?yearId=${selectedYearId}&monthId=${selectedMonthId}`;
    }
});

// Initial Load
document.addEventListener('DOMContentLoaded', async () => {
    await fetchYears();
});
