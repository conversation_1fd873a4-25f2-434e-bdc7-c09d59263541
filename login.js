document.addEventListener('DOMContentLoaded', () => {
    const loginForm = document.getElementById('login-form');
    const phoneNumberInput = document.getElementById('phone-number');
    const loginMessage = document.getElementById('login-message');
    const submitButton = loginForm.querySelector('button[type="submit"]');

    // Hardcoded phone number for login
    const validPhoneNumber = '0506561089';

    // Check if already logged in (e.g., user pressed back button)
    if (sessionStorage.getItem('isLoggedIn') === 'true') {
        console.log('User already logged in, redirecting...');
        // Redirect to the next step (budget year selection)
        // Ensure the target path is correct relative to login.html
        window.location.href = 'financial_section/select_budget_year.html'; // Adjust path if needed
        return; // Stop further execution
    }

    const showLoginMessage = (message, type = 'error') => {
        if (!loginMessage) return;
        loginMessage.textContent = message;
        loginMessage.className = `message ${type} show`;
        loginMessage.style.display = 'block';
    };

    loginForm.addEventListener('submit', (event) => {
        event.preventDefault();
        showLoginMessage('جاري التحقق...', 'info');
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الدخول...';

        const enteredPhoneNumber = phoneNumberInput.value.trim();

        // Basic validation
        if (!enteredPhoneNumber) {
            showLoginMessage('الرجاء إدخال رقم الهاتف.');
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="fas fa-lock"></i> دخول';
            return;
        }

        // Simulate a short delay for checking
        setTimeout(() => {
            if (enteredPhoneNumber === validPhoneNumber) {
                showLoginMessage('تم التحقق بنجاح! جاري التوجيه...', 'success');
                // Store login state in sessionStorage (cleared when browser tab closes)
                sessionStorage.setItem('isLoggedIn', 'true');
                sessionStorage.setItem('loggedInUserPhone', enteredPhoneNumber); // Optional: store user identifier

                // Redirect to the next step (budget year selection page)
                // Ensure the target path is correct relative to login.html
                window.location.href = 'financial_section/select_budget_year.html'; // Adjust path if needed

            } else {
                showLoginMessage('رقم الهاتف غير صحيح. الرجاء المحاولة مرة أخرى.', 'error');
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="fas fa-lock"></i> دخول';
                phoneNumberInput.focus(); // Focus back on input
                phoneNumberInput.select(); // Select text for easy correction
            }
        }, 500); // 0.5 second delay
    });
});
