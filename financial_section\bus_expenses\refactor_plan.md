# خطة إعادة هيكلة قسم مصاريف الحافلات

## الهدف

1.  تحويل صفحة `bus_expenses.html` لعرض تفاصيل ومصاريف **حافلة واحدة محددة** يتم تمرير معرفها (`bus_id`) عبر عنوان URL.
2.  إنشاء صفحة لوحة معلومات جديدة (`bus_expenses_dashboard.html`) تعرض ملخصًا إجماليًا للمصاريف حسب النوع لجميع الحافلات، وتعرض بطاقات مرئية لكل حافلة للانتقال إلى صفحة تفاصيلها.
3.  تحديث حقل "نوع المصروف" ليصبح قائمة منسدلة بالخيارات المحددة.

## المراحل

### المرحلة الأولى: تعديل صفحة تفاصيل الحافلة الحالية

*   **الملفات المستهدفة:**
    *   `c:\Users\<USER>\Desktop\مشروعي\financial_section\bus_expenses\bus_expenses.html`
    *   `c:\Users\<USER>\Desktop\مشروعي\financial_section\bus_expenses\bus_expenses.js`
    *   `c:\Users\<USER>\Desktop\مشروعي\financial_section\bus_expenses\bus_expenses.css`
*   **التغييرات:**
    1.  **HTML:**
        *   إزالة القائمة الجانبية لعرض الباصات.
        *   تعديل الحاوية الرئيسية لإزالة الاعتماد على القائمة الجانبية.
        *   تحويل حقل إدخال "نوع المصروف" في النافذة المنبثقة إلى قائمة منسدلة (`select`) بالخيارات التالية:
            *   صيانة
            *   قطع غيار <!-- إضافة جديدة -->
            *   تأمين
            *   تغير زيت
            *   تغير كفرات
            *   ايجار
            *   وقود
            *   تراخيص
            *   غسيل
            *   فحص دوري
            *   أخرى
        *   إزالة قائمة اختيار الباص من النافذة المنبثقة واستبدالها بحقل مخفي `bus_id_hidden`.
        *   تحديث العناوين لتناسب عرض بيانات حافلة واحدة.
        *   إضافة فلاتر لنوع المصروف (قائمة منسدلة)، نطاق التاريخ، والبحث بالوصف.
    2.  **JavaScript:**
        *   إزالة الشيفرة المتعلقة بالقائمة الجانبية.
        *   إضافة وظيفة لجلب `bus_id` من عنوان URL.
        *   تعديل وظائف جلب البيانات (`fetchBusExpenses`, `calculateAndDisplayBusStats`) لجلب وعرض بيانات الحافلة المحددة فقط.
        *   تعديل `fetchBuses` لجلب بيانات الحافلة المحددة فقط (لعرض رقمها في العنوان).
        *   تحديث `openExpenseModal` لتعيين قيمة الحقل المخفي `bus_id_hidden`.
        *   تحديث `handleExpenseSubmit` لاستخدام الحقل المخفي.
        *   تحديث منطق الفلترة.
        *   تحديث التعامل مع حقل "نوع المصروف" الجديد (القائمة المنسدلة).
    3.  **CSS:**
        *   إزالة الأنماط المتعلقة بالقائمة الجانبية.
        *   تعديل تنسيقات التخطيط العام إذا لزم الأمر.

### المرحلة الثانية: إنشاء صفحة لوحة معلومات مصاريف الحافلات

*   **الملفات الجديدة:**
    *   `c:\Users\<USER>\Desktop\مشروعي\financial_section\bus_expenses\bus_expenses_dashboard.html`
    *   `c:\Users\<USER>\Desktop\مشروعي\financial_section\bus_expenses\bus_expenses_dashboard.js`
    *   `c:\Users\<USER>\Desktop\مشروعي\financial_section\bus_expenses\bus_expenses_dashboard.css`
*   **الميزات:**
    1.  **HTML:**
        *   هيكل أساسي مع شريط تنقل وتذييل.
        *   قسم لعرض بطاقات ملخص لكل نوع مصروف رئيسي مع إجمالي المبالغ.
        *   قسم لعرض بطاقات مرئية لكل حافلة (أيقونة + رقم الحافلة).
    2.  **JavaScript:**
        *   جلب بيانات جميع الحافلات.
        *   جلب بيانات جميع المصروفات (أو لفترة محددة).
        *   حساب وعرض إجمالي المصروفات لكل نوع في بطاقات الملخص.
        *   عرض بطاقات الحافلات مع إضافة مستمع حدث للنقر للانتقال إلى `bus_expenses.html?bus_id={id}`.
    3.  **CSS:**
        *   تنسيق بطاقات الملخص وبطاقات الحافلات.

---

**التنفيذ (المرحلة الأولى):**
