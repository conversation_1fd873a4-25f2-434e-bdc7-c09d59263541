<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة السائقين</title>
    <!-- Ensure favicon.ico exists at c:\Users\<USER>\Desktop\مشروعي\favicon.ico -->
    <link rel="icon" href="../../favicon.ico" type="image/x-icon"> <!-- Corrected path -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <!-- Link to the local style.css file -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-content">
                <h1><i class="fas fa-users-cog"></i> إدارة السائقين</h1>
                <p>نظرة شاملة على ماليات السائقين للشهر الحالي.</p>
            </div>
            <!-- Navbar User Info/Logout -->
            <div class="navbar-user" style="position: absolute; top: 10px; left: 10px; color: white;">
                <span id="navbar-username" style="margin-left: 10px;"></span>
                <button id="logout-btn" class="control-btn" style="display: none; background-color: var(--danger-color); padding: 5px 10px; font-size: 0.8rem;">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </button>
            </div>
        </header>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Summary Section -->
            <section class="summary-section">
                <div class="date-display">
                    <h2 id="current-month-year">الشهر والسنة الحالية</h2>
                </div>
                <div class="summary-grid">
                    <div class="summary-card">
                        <h3><i class="fas fa-users"></i> عدد السائقين</h3>
                        <p id="total-drivers">...</p>
                    </div>
                    <div class="summary-card">
                        <h3><i class="fas fa-money-bill-wave"></i> إجمالي الرواتب الافتراضية</h3>
                        <p id="total-default-salaries">... ريال</p>
                    </div>
                    <div class="summary-card">
                        <h3><i class="fas fa-hand-holding-usd"></i> إجمالي المدفوع</h3>
                        <p id="total-paid">... ريال</p>
                    </div>
                    <div class="summary-card">
                        <h3><i class="fas fa-balance-scale-left"></i> مجموع المتبقي للشهر</h3> <!-- Changed Label -->
                        <p id="total-remaining">... ريال</p>
                    </div>
                    <div class="summary-card">
                        <h3><i class="fas fa-university"></i> مصاريف بنكية</h3>
                        <p id="total-bank-expenses">... ريال</p>
                    </div>
                    <div class="summary-card">
                        <h3><i class="fas fa-file-invoice-dollar"></i> مصاريف مخصومة</h3>
                        <p id="total-deducted-expenses">... ريال</p>
                    </div>
                     <div class="summary-card">
                        <h3><i class="fas fa-minus-circle"></i> إجمالي الخصومات</h3>
                        <p id="total-deductions">... ريال</p>
                    </div>
                </div>
            </section>

            <!-- Driver List Section -->
            <section class="driver-list-section">
                <div class="section-header"> <!-- Added wrapper for title and button -->
                    <h2><i class="fas fa-id-card"></i> ملفات السائقين</h2>
                    <button id="comprehensive-report-btn" class="control-btn report-btn">
                        <i class="fas fa-file-alt"></i> إنشاء تقرير شامل
                    </button>
                </div>
                 <div id="list-message" class="message" style="display: none;"></div>
                <div id="driver-files-container" class="driver-files-grid">
                    <!-- Driver cards will be loaded here by script.js -->
                    <p class="loading-message">جاري تحميل قائمة السائقين...</p>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="dashboard-footer">
             <button id="back-to-main-dashboard-btn" class="control-btn">
                <i class="fas fa-tachometer-alt"></i> العودة إلى لوحة التحكم المالية
            </button>
        </footer>
    </div>

    <!-- Comprehensive Report Modal -->
    <div id="comprehensive-report-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('comprehensive-report-modal')">&times;</span>
            <h2><i class="fas fa-file-alt"></i> إنشاء تقرير شامل للسائقين</h2>
            <p>حدد السائقين لتضمينهم في التقرير الشامل للشهر المحدد.</p>
            <div id="comprehensive-report-message" class="message" style="display: none;"></div>

            <div class="modal-controls">
                 <label style="margin-right: 20px;"> <!-- Added margin -->
                    <input type="checkbox" id="select-all-comprehensive-drivers"> تحديد الكل
                 </label>
                 <label> <!-- Added Comparison Checkbox -->
                    <input type="checkbox" id="compare-with-previous-month"> مقارنة مع الشهر السابق
                 </label>
            </div>

            <div id="comprehensive-report-driver-list" class="modal-list">
                <!-- Driver list with checkboxes will be loaded here -->
                <p class="loading-message">جاري تحميل السائقين...</p>
            </div>

            <div class="modal-actions">
                <button id="generate-comprehensive-report-submit" class="control-btn success-btn">
                    <i class="fas fa-cogs"></i> توليد التقرير
                </button>
                <button type="button" class="control-btn cancel-btn" onclick="closeModal('comprehensive-report-modal')">إلغاء</button>
            </div>
        </div>
    </div>
    <!-- End Comprehensive Report Modal -->

    <!-- Scripts -->
    <!-- Supabase Client -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Config File -->
    <script src="../../config.js"></script>
    <!-- Auth Check (Loaded BEFORE script.js) -->
    <script src="../../auth.js"></script>
    <!-- Page Specific Script (Loaded AFTER auth.js) -->
    <script src="script.js"></script>
</body>
</html>
