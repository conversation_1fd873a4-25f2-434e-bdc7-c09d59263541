/* Dashboard Layout */
.dashboard-layout {
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    overflow-x: hidden;
}

/* Top Navigation Bar */
.top-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-toggle-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.sidebar-toggle-btn:hover {
    background-color: rgba(255,255,255,0.1);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 600;
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.navbar-user {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logout-btn {
    background: rgba(255,255,255,0.1);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: rgba(255,255,255,0.2);
}

/* Main Content */
.main-content {
    margin-top: 70px;
    padding: 0;
    min-height: calc(100vh - 70px);
    transition: margin-right 0.3s ease;
}

.content-wrapper {
    padding: 30px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Dashboard Header */
.dashboard-header {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    border-top: 4px solid #667eea;
}

.header-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.dashboard-header h1 {
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.dashboard-header h1 i {
    color: #667eea;
}

.header-description {
    color: #6c757d;
    font-size: 1rem;
    margin: 0;
    opacity: 0.9;
}

/* Statistics Section */
.stats-section {
    margin-bottom: 40px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border-top: 4px solid transparent;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card:nth-child(1) {
    border-top-color: #667eea;
}

.stat-card:nth-child(2) {
    border-top-color: #28a745;
}

.stat-card:nth-child(3) {
    border-top-color: #17a2b8;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #17a2b8, #3498db);
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 5px 0;
}

.stat-info p {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

/* Cards */
.card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    overflow: hidden;
    margin-bottom: 30px;
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h2 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header h2 i {
    color: #667eea;
}

.card-body {
    padding: 25px;
}

/* Filters Section */
.filters-section {
    margin-bottom: 30px;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    color: #495057;
    font-weight: 500;
    font-size: 0.9rem;
}

.search-input-container {
    position: relative;
}

.search-input-container input {
    padding-right: 45px;
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
}

.search-input-container input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-input-container i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

select {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background-color: white;
    color: #495057;
    font-family: inherit;
    font-size: 0.95rem;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Table Responsive */
.table-responsive {
    overflow-x: auto;
    border-radius: 12px;
}

/* Modern Table */
.modern-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.modern-table thead {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.modern-table th {
    padding: 15px;
    text-align: right;
    font-weight: 600;
    color: #2c3e50;
    border-bottom: 2px solid #e9ecef;
    font-size: 0.9rem;
}

.modern-table td {
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.modern-table tbody tr {
    transition: background-color 0.2s ease;
}

.modern-table tbody tr:hover {
    background-color: #f8f9fa;
}

.loading-message {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 40px 20px;
}

.loading-message i {
    margin-left: 10px;
}

/* Table Inputs and Selects */
.modern-table select,
.modern-table input[type="number"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.modern-table select:focus,
.modern-table input[type="number"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.modern-table input[readonly] {
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

/* Buttons */
.btn {
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-warning {
    background: linear-gradient(135deg, #ed8936 0%, #dd7324 100%);
    color: white;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(237, 137, 54, 0.3);
}

.btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.8rem;
}

.btn-edit {
    background: #ed8936;
    color: white;
}

.btn-edit:hover {
    background: #dd7324;
    transform: scale(1.1);
}

.btn-assign {
    background: #4299e1;
    color: white;
}

.btn-assign:hover {
    background: #3182ce;
    transform: scale(1.1);
}

.btn-save {
    background: #48bb78;
    color: white;
}

.btn-save:hover {
    background: #38a169;
    transform: scale(1.1);
}

.btn-save:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    transform: none;
}

.btn-delete {
    background: #f56565;
    color: white;
}

.btn-delete:hover {
    background: #e53e3e;
    transform: scale(1.1);
}

.btn-fetch-settings {
    background: #28a745;
    color: white;
}

.btn-fetch-settings:hover {
    background: #218838;
    transform: scale(1.1);
}

.btn-add-defaults {
    background: #007bff;
    color: white;
}

.btn-add-defaults:hover {
    background: #0056b3;
    transform: scale(1.1);
}

/* Balance Display */
.balance-display {
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 6px;
    text-align: center;
    font-size: 0.9rem;
}

.balance-positive {
    background: rgba(245, 101, 101, 0.1);
    color: #f56565;
}

.balance-negative {
    background: rgba(66, 153, 225, 0.1);
    color: #4299e1;
}

.balance-zero {
    background: rgba(113, 128, 150, 0.1);
    color: #6c757d;
}

/* Group Badge */
.group-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    display: inline-block;
}

.group-badge.assigned {
    background: rgba(72, 187, 120, 0.1);
    color: #48bb78;
    border: 1px solid rgba(72, 187, 120, 0.2);
}

.group-badge.unassigned {
    background: rgba(113, 128, 150, 0.1);
    color: #6c757d;
    border: 1px solid rgba(113, 128, 150, 0.2);
}

/* Badge and Counter Styles */
.badge {
    background: #667eea;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.badge-count {
    background: #f56565;
    color: white;
    padding: 4px 8px;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 700;
    margin-right: 8px;
    min-width: 20px;
    text-align: center;
}

/* Header Actions in Navbar */
.navbar-right .header-actions {
    gap: 10px;
}

.new-students-btn,
.hidden-students-btn {
    position: relative;
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    font-size: 0.85rem;
}

.new-students-btn {
    background: rgba(255,255,255,0.1);
}

.new-students-btn:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-1px);
}

.hidden-students-btn {
    background: rgba(255,255,255,0.1);
}

.hidden-students-btn:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .top-navbar {
        padding: 0 15px;
    }

    .navbar-left {
        gap: 10px;
    }

    .navbar-brand span {
        display: none;
    }

    .navbar-right {
        gap: 10px;
    }

    .header-actions {
        gap: 5px;
    }

    .new-students-btn,
    .hidden-students-btn {
        padding: 6px 10px;
        font-size: 0.8rem;
    }

    .new-students-btn span:not(.badge-count),
    .hidden-students-btn span:not(.badge-count) {
        display: none;
    }

    .content-wrapper {
        padding: 20px 15px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .stat-card {
        padding: 20px;
    }

    .filters-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .modern-table {
        font-size: 0.8rem;
    }

    .modern-table th,
    .modern-table td {
        padding: 10px 8px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 5px;
    }

    .btn-icon {
        width: 28px;
        height: 28px;
        font-size: 0.7rem;
    }
}

@media (max-width: 480px) {
    .dashboard-header h1 {
        font-size: 1.4rem;
    }

    .header-description {
        font-size: 0.9rem;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .stat-icon {
        align-self: center;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

.large-modal {
    max-width: 900px;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2,
.modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-modal-btn,
.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.close-modal-btn:hover,
.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 25px;
    max-height: 60vh;
    overflow-y: auto;
}

/* Form Styles */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input[readonly] {
    background-color: #f8f9fa;
    color: #6c757d;
}

.form-help {
    display: block;
    margin-top: 5px;
    font-size: 0.8rem;
    color: #6c757d;
}

.student-info {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.student-info i {
    color: #667eea;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
    margin-top: 20px;
}

/* Message Styles */
.message {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-weight: 500;
}

.message.success {
    background: rgba(72, 187, 120, 0.1);
    color: #48bb78;
    border: 1px solid rgba(72, 187, 120, 0.2);
}

.message.error {
    background: rgba(245, 101, 101, 0.1);
    color: #f56565;
    border: 1px solid rgba(245, 101, 101, 0.2);
}

.message.warning {
    background: rgba(237, 137, 54, 0.1);
    color: #ed8936;
    border: 1px solid rgba(237, 137, 54, 0.2);
}

.message.info {
    background: rgba(66, 153, 225, 0.1);
    color: #4299e1;
    border: 1px solid rgba(66, 153, 225, 0.2);
}

/* Balance Display Styles */
.balance-display {
    font-weight: 600;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: inline-block;
    min-width: 80px;
    text-align: center;
}

.balance-display.clickable {
    cursor: pointer;
    border: 2px solid transparent;
}

.balance-display.clickable:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-color: rgba(102, 126, 234, 0.3);
}

.balance-display.balance-positive {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.balance-display.balance-negative {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.balance-display.balance-zero {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

/* Balance Info Modal */
.balance-info-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
}

.balance-info-modal.show {
    display: flex;
}

.balance-info-modal .modal-content {
    background: white;
    border-radius: 12px;
    max-width: 600px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.balance-info-modal .modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px 12px 0 0;
}

.balance-info-modal .modal-header h2 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.balance-info-modal .close-modal-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.balance-info-modal .close-modal-btn:hover {
    background-color: #f8f9fa;
    color: #dc3545;
}

.balance-info-modal .modal-body {
    padding: 25px;
}

.balance-breakdown-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.balance-breakdown-table th {
    background: #f8f9fa;
    padding: 12px 15px;
    text-align: right;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #e9ecef;
}

.balance-breakdown-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.balance-breakdown-table .amount {
    font-weight: 600;
    text-align: center;
}

.balance-breakdown-table .amount.positive {
    color: #dc3545;
}

.balance-breakdown-table .amount.negative {
    color: #28a745;
}

.balance-breakdown-table .amount.zero {
    color: #6c757d;
}

.balance-breakdown-table .total-row {
    background: #f8f9fa;
    font-weight: 600;
}

.balance-breakdown-table .total-row td {
    border-top: 2px solid #dee2e6;
    border-bottom: none;
}

/* Responsive Design for Modal */
@media (max-width: 768px) {
    .balance-info-modal .modal-content {
        margin: 10px;
        max-height: 90vh;
    }

    .balance-info-modal .modal-header {
        padding: 15px 20px;
    }

    .balance-info-modal .modal-body {
        padding: 20px;
    }

    .balance-breakdown-table th,
    .balance-breakdown-table td {
        padding: 8px 10px;
        font-size: 0.9rem;
    }
}


