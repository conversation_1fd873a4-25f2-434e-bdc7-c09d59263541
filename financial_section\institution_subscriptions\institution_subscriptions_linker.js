// هذا الملف سيحتوي على الدوال لربط اشتراكات المؤسسات بالمعاملات البنكية

console.log("institution_subscriptions_linker.js loaded.");

/**
 * @namespace InstitutionSubscriptionsLinker
 * @description يحتوي على الدوال المتعلقة بربط اشتراكات المؤسسات بالمعاملات البنكية.
 */
const InstitutionSubscriptionsLinker = (() => {
    let _supabaseLinker; // سيتم تهيئته لاحقًا
    let availableBanksForPayment = []; // لتخزين البنوك المتاحة للدفع

    /**
     * تهيئة الوحدة مع عميل Supabase.
     * @param {object} supabaseClient - عميل Supabase.
     */
    function initialize(supabaseClient) {
        if (!supabaseClient) {
            console.error("Supabase client is required for InstitutionSubscriptionsLinker.");
            return;
        }
        _supabaseLinker = supabaseClient;
        console.log("InstitutionSubscriptionsLinker initialized with Supabase client.");
    }

    /**
     * جلب البنوك المتاحة (مثلاً، النوع 'مركزي') لتعبئة قائمة اختيار البنك في نافذة الدفع.
     * @returns {Promise<Array<object>>} - قائمة بالبنوك.
     */
    async function fetchBanksForPaymentModal() {
        if (!_supabaseLinker) {
            console.error("Supabase client not initialized in Linker.");
            return [];
        }
        try {
            const { data, error } = await _supabaseLinker
                .from('banks')
                .select('id, name')
                // .eq('bank_type', 'مركزي') //  تم إزالة هذا السطر لجلب كل البنوك
                .order('name', { ascending: true });

            if (error) throw error;
            availableBanksForPayment = data || [];
            console.log("All banks fetched for payment modal:", availableBanksForPayment);
            return availableBanksForPayment;
        } catch (error) {
            console.error('Error fetching all banks for payment modal:', error);
            return [];
        }
    }

    /**
     * تعبئة القائمة المنسدلة للبنوك في نافذة الدفع.
     * @param {string} selectElementId - معرّف عنصر الـ select الخاص بالبنوك.
     */
    function populateBankSelectInPaymentModal(selectElementId = 'group-payment-bank') {
        const bankSelect = document.getElementById(selectElementId);
        if (!bankSelect) {
            console.error(`Bank select element with ID '${selectElementId}' not found.`);
            return;
        }

        const firstOption = bankSelect.options[0];
        bankSelect.innerHTML = '';
        if (firstOption) bankSelect.appendChild(firstOption);

        if (availableBanksForPayment.length === 0) {
            const option = document.createElement('option');
            option.value = "";
            option.textContent = "لا توجد بنوك متاحة";
            option.disabled = true;
            bankSelect.appendChild(option);
            return;
        }

        availableBanksForPayment.forEach(bank => {
            const option = document.createElement('option');
            option.value = bank.id;
            option.textContent = bank.name;
            bankSelect.appendChild(option);
        });
    }


    /**
     * تم حذف دالة linkSubscriptionPaymentToBankTransaction لأنها تم استبدالها بالتريجرات في قاعدة البيانات
     */

    /**
     * حذف جميع المعاملات البنكية المرتبطة باشتراك مؤسسة معين.
     * @param {number|string} subscriptionId - معرّف الاشتراك.
     * @returns {Promise<boolean>}
     */
    async function deleteAllBankTransactionsForSubscription(subscriptionId) {
        if (!_supabaseLinker) {
            console.error("Supabase client not initialized in Linker for deleteAllBankTransactionsForSubscription.");
            return false;
        }
        if (!subscriptionId) {
            console.error("Subscription ID is required to delete related bank transactions.");
            return false;
        }

        try {
            console.log(`Attempting to delete ALL bank transactions linked to subscription ID: ${subscriptionId}`);
            const { error } = await _supabaseLinker
                .from('bank_transactions')
                .delete()
                .eq('enterprise_subscription_id', subscriptionId);

            if (error) throw error;
            console.log(`All bank transactions for subscription ID ${subscriptionId} deleted (or no matching transactions found).`);
            return true;
        } catch (error) {
            console.error('Error deleting all bank transactions for subscription:', error);
            return false;
        }
    }

    /**
     * معالجة التغييرات على اشتراك لمزامنة المعاملات البنكية.
     * @param {'INSERT_PAYMENT' | 'UPDATE_SUBSCRIPTION' | 'DELETE_SUBSCRIPTION'} changeType
     * @param {object} subscriptionData - بيانات الاشتراك (الجديدة أو القديمة قبل الحذف).
     * @param {string | null} paymentBankId - معرّف البنك المختار للدفع (فقط لـ INSERT_PAYMENT).
     * @param {number | null} paymentAmountForThisTransaction - المبلغ المدفوع في هذه العملية المحددة (فقط لـ INSERT_PAYMENT).
     */
    async function handleSubscriptionChangeForBankTransactions(changeType, subscriptionData, paymentBankId = null, paymentAmountForThisTransaction = null) {
        if (!_supabaseLinker) {
            console.error("Supabase client not initialized in Linker for handleSubscriptionChange.");
            return;
        }

        console.log(`Handling subscription change for bank transactions: ${changeType}`, { subscriptionData, paymentBankId, paymentAmountForThisTransaction });

        const currentSubscriptionId = subscriptionData.id;

        if (changeType === 'INSERT_PAYMENT') {
            // تم حذف إنشاء المعاملة البنكية يدوياً - التريجر يقوم بهذا تلقائياً
            console.log(`Payment processed for subscription ${subscriptionData.id}. Bank transaction will be created automatically by database trigger.`);
        } else if (changeType === 'UPDATE_SUBSCRIPTION') {
            // هذا يعني أن الاشتراك الكلي تم تحديثه (مثلاً، المبلغ الإجمالي أو ملاحظات عامة)
            // إذا كان paid_amount الكلي للاشتراك الآن صفرًا، نحذف كل المعاملات المرتبطة.
            // وإذا كان هناك paid_amount وبنك مسجل، قد نرغب في التأكد من وجود معاملة (هذا يعتمد على المنطق المطلوب)
            // السيناريو الحالي: التحديثات العامة للاشتراك لا تؤثر مباشرة على المعاملات البنكية الفردية المسجلة لكل دفعة.
            // المعاملات البنكية تُدار فقط عند تسجيل دفعة ('INSERT_PAYMENT') أو حذف الاشتراك ('DELETE_SUBSCRIPTION').
            // إذا تم تعديل paid_amount للاشتراك يدويًا إلى الصفر من نموذج تعديل الاشتراك الرئيسي:
            if (parseFloat(subscriptionData.paid_amount) <= 0) {
                await deleteAllBankTransactionsForSubscription(currentSubscriptionId);
            }
        } else if (changeType === 'DELETE_SUBSCRIPTION') {
            await deleteAllBankTransactionsForSubscription(currentSubscriptionId);
        }
    }

    return {
        initialize,
        fetchBanksForPaymentModal,
        populateBankSelectInPaymentModal,
        handleSubscriptionChangeForBankTransactions
    };

})();
