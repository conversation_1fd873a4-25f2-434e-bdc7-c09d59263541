.selection-main {
    padding-top: 20px;
}

.selection-card .card-header {
    position: relative; /* Needed for absolute positioning of back button */
}

.selection-card .card-header h2 i {
    margin-left: 10px;
}

#selected-year-display {
    font-weight: bold;
    color: var(--accent-color);
}

.back-btn {
    /* Position button within the header */
    position: absolute;
    left: 20px; /* Adjust as needed */
    top: 50%;
    transform: translateY(-50%);
    padding: 8px 15px;
    font-size: 0.9rem;
}

.cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); /* Smaller cards for months */
    gap: 20px;
    padding: 10px 0;
}

.month-card {
    background-color: #fff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    aspect-ratio: 1 / 1; /* Make cards square-ish */
}

.month-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
    border-color: var(--accent-color);
}

.month-card .month-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
    line-height: 1;
}

.month-card .month-name {
    font-size: 1rem;
    font-weight: 500;
    color: var(--secondary-color);
}

.month-card.closed {
    background-color: #f8f9fa;
    cursor: not-allowed;
    opacity: 0.7;
}

.month-card.closed:hover {
    transform: none;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    border-color: var(--border-color);
}

.month-card.closed .month-number,
.month-card.closed .month-name {
    color: var(--text-muted);
}

.month-card.closed::after {
    content: "مغلق";
    position: absolute;
    bottom: 10px;
    font-size: 0.8em;
    color: var(--danger-color);
    font-weight: bold;
}


.loading-placeholder {
    grid-column: 1 / -1; /* Span full width */
    text-align: center;
    padding: 40px 20px;
    font-size: 1.2rem;
    color: var(--text-muted);
}

.loading-placeholder i {
    margin-left: 10px;
    font-size: 1.5rem;
}
