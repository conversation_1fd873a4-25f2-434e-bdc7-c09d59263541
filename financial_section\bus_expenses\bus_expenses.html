<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- تعديل العنوان -->
    <title>تفاصيل مصاريف الحافلة</title>
    <!-- Shared Styles -->
    <link rel="stylesheet" href="../../shared_styles.css">
    <!-- Page Specific Styles -->
    <link rel="stylesheet" href="bus_expenses.css"> <!-- استخدام اسم الملف الصحيح -->
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Config -->
    <script src="../../config.js"></script>
    <!-- Auth Script -->
    <script src="../../auth.js"></script>
    <!-- Page Script -->
    <!-- استخدام اسم الملف الصحيح -->
    <script defer src="bus_expenses.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts (Example: Tajawal) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- إزالة القائمة الجانبية -->
    <!-- <aside class="dashboard-sidebar"> ... </aside> -->

    <!-- تعديل الحاوية الرئيسية -->
    <div class="dashboard-container single-view"> <!-- إضافة كلاس لتمييز العرض -->
        <!-- المحتوى الرئيسي -->
        <div class="dashboard-content">
            <!-- ترويسة الصفحة -->
            <header class="dashboard-header">
                <!-- تعديل العنوان الرئيسي والفرعي -->
                <h1 id="main-header-title"><i class="fas fa-bus"></i> تفاصيل مصاريف الحافلة</h1>
                <p id="main-header-subtitle">عرض وإدارة مصاريف الحافلة المحددة.</p>
                <!-- زر العودة إلى لوحة المعلومات -->
                <button id="back-to-dashboard-btn" class="control-btn back-btn" style="position: absolute; top: 15px; left: 15px;">
                    <i class="fas fa-arrow-left"></i> العودة للوحة المعلومات
                </button>
                <!-- منطقة الرسائل العامة -->
                <div id="dashboard-message" class="message" style="display: none; margin-top: 15px;"></div>
            </header>

            <!-- إزالة: قسم الإحصائيات القديم (البطاقات) -->
            <!--
            <section class="stats-section">
                <div class="stats-grid">
                    ... (old cards) ...
                </div>
            </section>
            -->

            <!-- إضافة: قسم الإحصائيات المفصل الجديد -->
            <section class="detailed-stats-section">
                <div class="detailed-stats-container">
                    <!-- عناصر المصروفات -->
                    <div class="stat-item">
                        <span class="stat-label">صيانة</span>
                        <span class="stat-value" id="stat-maintenance">0.00 ريال</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">قطع غيار</span>
                        <span class="stat-value" id="stat-parts">0.00 ريال</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">تأمين</span>
                        <span class="stat-value" id="stat-insurance">0.00 ريال</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">تغير زيت</span>
                        <span class="stat-value" id="stat-oil">0.00 ريال</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">تغير كفرات</span>
                        <span class="stat-value" id="stat-tires">0.00 ريال</span>
                    </div>

                    <!-- المجموع الإجمالي في الوسط -->
                    <div class="stat-item total-stat">
                        <span class="stat-label">إجمالي المصروفات</span>
                        <span class="stat-value" id="stat-grand-total">0.00 ريال</span>
                    </div>

                    <!-- عناصر المصروفات (الجزء الثاني) -->
                    <div class="stat-item">
                        <span class="stat-label">ايجار</span>
                        <span class="stat-value" id="stat-rent">0.00 ريال</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">وقود</span>
                        <span class="stat-value" id="stat-fuel">0.00 ريال</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">تراخيص</span>
                        <span class="stat-value" id="stat-licenses">0.00 ريال</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">غسيل</span>
                        <span class="stat-value" id="stat-washing">0.00 ريال</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">فحص دوري</span>
                        <span class="stat-value" id="stat-inspection">0.00 ريال</span>
                    </div>
                     <div class="stat-item">
                        <span class="stat-label">أخرى</span>
                        <span class="stat-value" id="stat-other">0.00 ريال</span>
                    </div>
                </div>
            </section>
            <!-- نهاية قسم الإحصائيات المفصل -->


            <!-- قسم الفلاتر والتحكم -->
            <section class="controls-section">
                <div class="control-card">
                    <div class="card-header">
                        <h2><i class="fas fa-filter"></i> تصفية وبحث</h2>
                    </div>
                    <div class="card-body">
                        <div class="filters-grid">
                            <!-- فلتر نوع المصروف -->
                            <div class="form-group">
                                <label for="filter-expense-type">نوع المصروف:</label>
                                <select id="filter-expense-type">
                                    <option value="">الكل</option>
                                    <option value="صيانة">صيانة</option>
                                    <option value="قطع غيار">قطع غيار</option> <!-- إضافة جديدة -->
                                    <option value="تأمين">تأمين</option>
                                    <option value="تغير زيت">تغير زيت</option>
                                    <option value="تغير كفرات">تغير كفرات</option>
                                    <option value="ايجار">ايجار</option>
                                    <option value="وقود">وقود</option>
                                    <option value="تراخيص">تراخيص</option>
                                    <option value="غسيل">غسيل</option>
                                    <option value="فحص دوري">فحص دوري</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            <!-- فلتر نطاق التاريخ -->
                            <div class="form-group">
                                <label for="filter-start-date">من تاريخ:</label>
                                <input type="date" id="filter-start-date">
                            </div>
                            <div class="form-group">
                                <label for="filter-end-date">إلى تاريخ:</label>
                                <input type="date" id="filter-end-date">
                            </div>
                            <!-- بحث بالوصف -->
                            <div class="form-group">
                                <label for="search-input">بحث بالوصف:</label>
                                <input type="text" id="search-input" placeholder="أدخل كلمة للبحث...">
                            </div>
                            <!-- أزرار التحكم -->
                            <div class="filter-actions">
                                <button id="filter-btn" class="control-btn filter-btn"><i class="fas fa-search"></i> تطبيق</button>
                                <button id="reset-filter-btn" class="control-btn reset-btn"><i class="fas fa-undo"></i> إلغاء</button>
                                <button id="open-add-expense-modal-btn" class="control-btn add-btn"><i class="fas fa-plus"></i> إضافة مصروف</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- قسم جدول المصروفات -->
            <section class="table-section">
                <div class="table-card">
                    <div class="card-header">
                        <!-- تعديل عنوان الجدول -->
                        <h2 id="table-header-title"><i class="fas fa-list-ul"></i> قائمة المصروفات</h2>
                        <span class="badge" id="expenses-count">0</span>
                    </div>
                    <div class="card-body">
                        <div id="list-message" class="message" style="display: none;"></div>
                        <div class="table-responsive">
                            <table id="expenses-table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>نوع المصروف</th>
                                        <th>المبلغ الأساسي</th>
                                        <th>الضريبة</th>
                                        <th>الإجمالي</th>
                                        <th>التاريخ</th>
                                        <th>طريقة الدفع</th>
                                        <th>البنك</th>
                                        <th>التفاصيل</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="expenses-tbody">
                                    <!-- Loading message or data rows -->
                                    <tr><td colspan="10" class="loading-message">جاري تحميل المصروفات...</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- Pagination -->
                        <div class="pagination" id="pagination-controls"></div>
                    </div>
                </div>
            </section>

            <!-- تذييل الصفحة -->
            <footer class="dashboard-footer">
                <p>&copy; 2024 نظام الإدارة المالية</p>
            </footer>
        </div><!-- نهاية dashboard-content -->

        <!-- قسم نموذج إضافة/تعديل المصروف (Modal) -->
        <div id="expense-form-section" class="form-section">
            <div class="form-card large-form"> <!-- استخدام كلاس لتكبير النموذج -->
                <div class="card-header">
                    <h2 id="expense-form-title">إضافة مصروف جديد</h2>
                    <button id="close-expense-form-btn" class="close-btn" title="إغلاق">&times;</button>
                </div>
                <div class="card-body">
                    <div id="expense-form-message" class="message" style="display: none;"></div>
                    <form id="expense-form">
                        <!-- حقول مخفية -->
                        <input type="hidden" id="expense_id">
                        <input type="hidden" id="modal_bus_id">
                        <input type="hidden" id="modal_month_id">

                        <!-- إزالة خطوات اختيار الشهر -->
                        <!-- <div id="modal-step-month"> ... </div> -->

                        <!-- تفاصيل المصروف (كانت الخطوة الثانية) -->
                        <div id="modal-step-details">
                            <!-- عرض الشهر المحدد (للتأكيد فقط) -->
                            <p style="text-align: center; margin-bottom: 15px; font-weight: 500;">
                                الشهر المحدد: <strong id="active-month-display">--</strong>
                            </p>

                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="expense_date">تاريخ المصروف <span class="required">*</span></label>
                                    <input type="date" id="expense_date" required>
                                </div>
                                <div class="form-group">
                                    <label for="expense_type">نوع المصروف <span class="required">*</span></label>
                                    <!-- تغيير إلى قائمة منسدلة -->
                                    <select id="expense_type" required>
                                        <option value="">-- اختر النوع --</option>
                                        <option value="صيانة">صيانة</option>
                                        <option value="قطع غيار">قطع غيار</option> <!-- إضافة جديدة -->
                                        <option value="تأمين">تأمين</option>
                                        <option value="تغير زيت">تغير زيت</option>
                                        <option value="تغير كفرات">تغير كفرات</option>
                                        <option value="ايجار">ايجار</option>
                                        <option value="وقود">وقود</option>
                                        <option value="تراخيص">تراخيص</option>
                                        <option value="غسيل">غسيل</option>
                                        <option value="فحص دوري">فحص دوري</option>
                                        <option value="أخرى">أخرى</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="amount_numeric">المبلغ الأساسي <span class="required">*</span></label>
                                    <input type="number" id="amount_numeric" min="0.01" step="0.01" placeholder="0.00" required>
                                </div>
                                <div class="form-group">
                                    <label for="include_tax">يشمل ضريبة؟</label>
                                    <select id="include_tax">
                                        <option value="false">لا</option>
                                        <option value="true">نعم</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="tax_amount">مبلغ الضريبة</label>
                                    <input type="number" id="tax_amount" placeholder="0.00" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="total_with_tax">الإجمالي شامل الضريبة</label>
                                    <input type="number" id="total_with_tax" placeholder="0.00" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="bank_id">البنك <span class="required">*</span></label>
                                    <select id="bank_id" required>
                                        <option value="">-- اختر البنك --</option>
                                        <!-- Options populated by JS -->
                                    </select>
                                </div>
                                <div class="form-group" style="grid-column: span 2;"> <!-- جعل حقل التفاصيل يأخذ عمودين -->
                                    <label for="details">التفاصيل</label>
                                    <textarea id="details" rows="3" placeholder="أدخل تفاصيل إضافية..."></textarea>
                                </div>
                            </div>
                            <div class="form-actions">
                                <!-- إزالة زر العودة لخطوة الشهر -->
                                <!-- <button type="button" id="modal-back-to-month-btn" class="control-btn cancel-btn">السابق</button> -->
                                <button type="submit" class="control-btn submit-btn">حفظ المصروف</button>
                                <button type="button" class="control-btn cancel-btn" onclick="closeExpenseModal()">إلغاء</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div><!-- نهاية form-section -->

    </div><!-- نهاية dashboard-container -->
</body>
</html>
