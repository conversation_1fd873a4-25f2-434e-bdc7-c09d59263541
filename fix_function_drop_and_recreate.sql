-- إصلاح شامل لمشكلة الدالة المتضاربة
-- الخطوة 1: حذف جميع إصدارات الدالة المحتملة

-- حذف بجميع التوقيعات المحتملة
DROP FUNCTION IF EXISTS public.check_payment_triggers_status();
DROP FUNCTION IF EXISTS public.check_payment_triggers_status(OUT table_name text, OUT trigger_exists boolean, OUT trigger_name text, OUT last_test_result text);
DROP FUNCTION IF EXISTS public.check_payment_triggers_status(OUT tbl_name text, OUT trigger_exists boolean, OUT trigger_name text, OUT table_status text);

-- حذف أي دوال أخرى قد تكون متضاربة
DROP FUNCTION IF EXISTS public.quick_trigger_check();

-- الخطوة 2: إنشاء دالة جديدة بتوقيع مختلف تماماً
CREATE OR REPLACE FUNCTION public.verify_payment_triggers()
RETURNS TABLE(
    table_name text,
    has_trigger boolean,
    trigger_name text,
    table_exists boolean
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        tables_list.tbl_name::text as table_name,
        EXISTS(
            SELECT 1 FROM information_schema.triggers tr 
            WHERE tr.event_object_table = tables_list.tbl_name 
              AND tr.trigger_name LIKE '%financial_log_trigger'
        ) as has_trigger,
        COALESCE(
            (SELECT tr.trigger_name FROM information_schema.triggers tr 
             WHERE tr.event_object_table = tables_list.tbl_name 
               AND tr.trigger_name LIKE '%financial_log_trigger' 
             LIMIT 1), 
            'غير موجود'
        )::text as trigger_name,
        EXISTS(
            SELECT 1 FROM information_schema.tables ist 
            WHERE ist.table_name = tables_list.tbl_name 
              AND ist.table_schema = 'public'
        ) as table_exists
    FROM (
        SELECT unnest(ARRAY[
            'student_payments',
            'enterprise_group_payments',
            'enterprise_subscriptions',
            'nathriyat_transactions',
            'bus_expenses',
            'driver_expenses'
        ]) as tbl_name
    ) tables_list;
END;
$$;

-- الخطوة 3: دالة بسيطة للتحقق السريع
CREATE OR REPLACE FUNCTION public.list_financial_triggers()
RETURNS TABLE(
    table_name text,
    trigger_name text,
    trigger_timing text,
    trigger_events text
)
LANGUAGE sql
AS $$
    SELECT 
        tr.event_object_table::text as table_name,
        tr.trigger_name::text as trigger_name,
        tr.action_timing::text as trigger_timing,
        string_agg(tr.event_manipulation, ', ' ORDER BY tr.event_manipulation)::text as trigger_events
    FROM information_schema.triggers tr
    WHERE tr.trigger_name LIKE '%financial_log_trigger'
       OR tr.trigger_name LIKE '%payment%trigger'
       OR tr.trigger_name LIKE '%student_payments%trigger'
    GROUP BY tr.event_object_table, tr.trigger_name, tr.action_timing
    ORDER BY tr.event_object_table;
$$;

-- الخطوة 4: دالة لاختبار التريجر
CREATE OR REPLACE FUNCTION public.test_trigger_functionality(
    p_table_name text DEFAULT 'student_payments'
)
RETURNS text
LANGUAGE plpgsql
AS $$
DECLARE
    v_result text;
    v_trigger_count integer;
BEGIN
    -- التحقق من وجود التريجر
    SELECT COUNT(*) INTO v_trigger_count
    FROM information_schema.triggers 
    WHERE event_object_table = p_table_name 
      AND trigger_name LIKE '%financial_log_trigger';
    
    IF v_trigger_count > 0 THEN
        v_result := 'التريجر موجود للجدول: ' || p_table_name;
    ELSE
        v_result := 'التريجر غير موجود للجدول: ' || p_table_name;
    END IF;
    
    RETURN v_result;
END;
$$;

-- الخطوة 5: اختبار الدوال الجديدة
SELECT 'تم إنشاء الدوال الجديدة بنجاح!' as status;

-- عرض حالة التريجرات
SELECT 'حالة التريجرات:' as info;
SELECT * FROM public.verify_payment_triggers();

-- عرض قائمة التريجرات المالية
SELECT 'قائمة التريجرات المالية:' as info;
SELECT * FROM public.list_financial_triggers();

-- اختبار وظيفة التريجر
SELECT 'اختبار وظيفة التريجر:' as info;
SELECT public.test_trigger_functionality('student_payments') as student_payments_test;
SELECT public.test_trigger_functionality('bus_expenses') as bus_expenses_test;
SELECT public.test_trigger_functionality('nathriyat_transactions') as nathriyat_test;

-- تعليمات الاستخدام
SELECT 'تعليمات الاستخدام:' as instructions
UNION ALL
SELECT '1. للتحقق من التريجرات: SELECT * FROM public.verify_payment_triggers();'
UNION ALL
SELECT '2. لعرض التريجرات: SELECT * FROM public.list_financial_triggers();'
UNION ALL
SELECT '3. لاختبار تريجر: SELECT public.test_trigger_functionality(''table_name'');';
