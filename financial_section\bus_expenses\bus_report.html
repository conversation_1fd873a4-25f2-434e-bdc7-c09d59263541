<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير مصاريف الحافلات</title>
    <!-- Shared Styles -->
    <link rel="stylesheet" href="../../shared_styles.css">
    <!-- Report Specific Styles -->
    <link rel="stylesheet" href="bus_report.css">
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns"></script> <!-- Adapter for time scale if needed -->
    <!-- Config -->
    <script src="../../config.js"></script>
    <!-- Report Script -->
    <script defer src="bus_report.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="report-container">
        <header class="report-header no-print">
            <h1><i class="fas fa-file-invoice-dollar"></i> تقرير مصاريف الحافلات</h1>
            <button id="print-report-btn" class="control-btn print-btn">
                <i class="fas fa-print"></i> طباعة التقرير
            </button>
        </header>

        <div id="report-content" class="report-content">
            <!-- سيتم ملء المحتوى بواسطة JavaScript -->
            <div class="loading-placeholder">
                <i class="fas fa-spinner fa-spin"></i> جاري تحميل بيانات التقرير...
            </div>
        </div>

        <!-- قسم الرسوم البيانية -->
        <section class="charts-section">
            <h2><i class="fas fa-chart-pie"></i> الرسوم البيانية</h2>
            <div class="charts-grid">
                <div class="chart-container">
                    <h3>المصاريف حسب نوع المصروف</h3>
                    <canvas id="expenses-by-type-chart"></canvas>
                </div>
                <div class="chart-container">
                    <h3>إجمالي المصاريف لكل حافلة</h3>
                    <canvas id="expenses-by-bus-chart"></canvas>
                </div>
            </div>
             <div id="chart-message" class="message info" style="display: none;"></div>
        </section>

        <!-- تعديل: قسم التحليل التفاعلي -->
        <section class="analysis-section no-print">
            <h2><i class="fas fa-lightbulb"></i> هل لاحظت؟ (تحليل البيانات)</h2>
            <p>سيقوم النظام بطرح سؤال بناءً على بيانات التقرير، حاول الإجابة بـ "نعم" أو "لا".</p>
            <div id="analysis-question-area" class="analysis-question-display">
                <p id="system-question-text">جاري إنشاء السؤال...</p>
            </div>
            <div id="analysis-answer-buttons" class="analysis-buttons" style="display: none;">
                <button id="answer-yes-btn" class="control-btn success-btn">
                    <i class="fas fa-check"></i> نعم
                </button>
                <button id="answer-no-btn" class="control-btn danger-btn">
                    <i class="fas fa-times"></i> لا
                </button>
            </div>
            <div id="analysis-feedback-area" class="analysis-feedback" style="display: none;">
                <p><strong>التقييم:</strong> <span id="feedback-correctness"></span></p>
                <p><strong>التوضيح:</strong> <span id="feedback-reason"></span></p>
                <button id="next-question-btn" class="control-btn primary-btn" style="margin-top: 10px;">
                    <i class="fas fa-redo"></i> سؤال تالي
                </button>
            </div>
             <div id="analysis-message" class="message info" style="display: none;"></div>
        </section>
        <!-- نهاية قسم التحليل -->

    </div>
</body>
</html>
