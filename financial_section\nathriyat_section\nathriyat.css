@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --accent-color: #f39c12;
    --success-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --text-dark: #2c3e50;
    --text-muted: #7f8c8d;
    --text-light: #ffffff;
    --border-color: #e1e8ed;
    --border-radius: 6px;
    --card-shadow: 0 2px 15px rgba(0,0,0,0.08);
    --hover-shadow: 0 5px 25px rgba(0,0,0,0.15);
    --card-bg: #ffffff;
}

/* تنسيقات أساسية */
body {
    font-family: 'Tajawal', sans-serif;
    background-color: #f4f7f9;
    color: var(--text-dark);
    line-height: 1.6;
    margin: 0;
}

/* تنسيق الحاوية الرئيسية */
.dashboard-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* تنسيق الرأس */
.dashboard-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-light);
    padding: 25px 20px;
    text-align: center;
    position: relative;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.dashboard-header h1 {
    margin: 0 0 10px 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.dashboard-header p {
    margin: 0;
    opacity: 0.9;
    font-weight: 300;
    font-size: 1rem;
}

.dashboard-header h1 i {
    margin-left: 10px;
}

/* إعادة إضافة: تنسيق عرض الشهر النشط */
.active-month-info {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.9rem;
    margin-top: 10px;
    display: inline-flex; /* Changed to inline-flex */
    align-items: center; /* Align items vertically */
    gap: 10px; /* Add gap between elements */
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.active-month-info.loaded {
    background-color: rgba(46, 204, 113, 0.15); /* Light green when loaded */
    border-color: rgba(46, 204, 113, 0.4);
    color: #e8f8f1;
}

#active-month-name {
    font-weight: 600;
}

/* إضافة: تنسيق قائمة تبديل الشهر */
.month-switcher {
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 3px 8px;
    font-size: 0.85rem;
    font-family: 'Tajawal', sans-serif;
    cursor: pointer;
    max-width: 150px; /* Limit width */
    margin-right: 5px; /* Space from the name */
    transition: border-color 0.2s ease;
}

.month-switcher:hover {
    border-color: var(--primary-color);
}

.month-switcher:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* تنسيق زر تبديل القائمة الجانبية */
.sidebar-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--text-light);
    padding: 8px 12px;
    font-size: 1.1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1020;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-toggle:hover {
    background-color: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.8);
}

.sidebar-toggle:active {
    transform: translateY(-50%) scale(0.95);
}

/* تنسيق زر العودة */
.back-btn {
    background-color: rgba(255, 255, 255, 0.15);
    color: var(--text-light);
    border: none;
    padding: 8px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s ease;
    font-weight: 500;
    font-size: 0.9rem;
}

.back-btn:hover {
    background-color: rgba(255, 255, 255, 0.25);
}

/* تنسيق المنطقة الرئيسية */
.dashboard-main {
    flex: 1;
    padding: 20px;
    max-width: 1100px;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
}

/* تنسيق مساحة الرسائل */
.message {
    padding: 12px 15px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    font-weight: 500;
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.message.show {
    opacity: 1;
    transform: translateY(0);
}

.message.info {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.message.success {
    background-color: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(46, 204, 113, 0.2);
}

.message.error {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.message.warning {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(243, 156, 18, 0.2);
}

/* تنسيق البطاقات */
.form-section, .table-section {
    margin-bottom: 25px;
}

.form-card, .table-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    margin: 0;
    font-size: 1.3rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-header .badge {
    background-color: var(--primary-color);
    color: white;
    padding: 3px 10px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

.card-body {
    padding: 20px;
}

/* تنسيق النموذج */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    grid-gap: 15px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group-full-width {
    grid-column: 1 / -1;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--secondary-color);
}

.form-group input[type="text"],
.form-group input[type="date"],
.form-group input[type="number"],
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    font-family: 'Tajawal', sans-serif;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
}

/* تنسيق حقل اختيار البنك في النموذج */
#bank_id {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    font-family: 'Tajawal', sans-serif;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background-color: #fff; /* Ensure background color */
}

#bank_id:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
}

.required {
    color: var(--danger-color);
}

.form-actions {
    display: flex;
    justify-content: flex-start;
    gap: 15px;
    margin-top: 15px;
}

/* تنسيق الأزرار */
.control-btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-family: 'Tajawal', sans-serif;
    font-size: 0.95rem;
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
}

.primary-btn:hover {
    background-color: var(--primary-dark);
}

.save-btn {
    background-color: var(--success-color);
    color: white;
}

.save-btn:hover:not(:disabled) {
    background-color: #27ae60;
}

.cancel-btn {
    background-color: #f0f0f0;
    color: var(--text-dark);
}

.cancel-btn:hover {
    background-color: #e0e0e0;
}

/* تنسيق الجدول */
.table-responsive {
    overflow-x: auto;
}

#nathriyat-table {
    width: 100%;
    border-collapse: collapse;
}

#nathriyat-table th,
#nathriyat-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

#nathriyat-table th {
    background-color: rgba(52, 152, 219, 0.05);
    color: var(--primary-color);
    font-weight: 600;
}

#nathriyat-table tr:last-child td {
    border-bottom: none;
}

#nathriyat-table tr:hover {
    background-color: rgba(52, 152, 219, 0.03);
}

/* تنسيق أزرار الإجراءات في الجدول */
.action-btn {
    background-color: transparent;
    border: none;
    font-size: 1rem;
    padding: 5px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.edit-btn {
    color: var(--primary-color);
}

.edit-btn:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

.delete-btn {
    color: var(--danger-color);
}

.delete-btn:hover {
    background-color: rgba(231, 76, 60, 0.1);
}

/* تنسيق زر عرض التفاصيل في جدول الأنواع */
.details-btn {
    color: var(--info-color); /* Use info color */
    margin-left: 5px; /* Space between buttons */
}

.details-btn:hover {
    background-color: rgba(52, 152, 219, 0.1); /* Light blue background on hover */
}

/* تنسيق رسالة التحميل */
.loading-message {
    text-align: center;
    color: var(--text-muted);
    padding: 30px;
    font-style: italic;
}

/* تنسيق التذييل */
.dashboard-footer {
    text-align: center;
    padding: 20px;
    background-color: var(--card-bg);
    color: var(--text-muted);
    border-top: 1px solid var(--border-color);
    margin-top: auto;
}

/* تنسيق الترقيم الصفحي */
.pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
}

.pagination button {
    min-width: 40px;
    height: 40px;
    padding: 0 12px;
    border: none;
    background-color: #fff;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination button:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination button.active {
    background-color: var(--primary-color);
    color: white;
    font-weight: bold;
}

/* تنسيق النموذج عندما يكون معطّلاً  */
#nathriyat-form[style*="opacity: 0.5"] {
    pointer-events: none;
    cursor: not-allowed;
}

/* تحسينات للأجهزة اللوحية والهواتف */
@media (max-width: 768px) {
    .dashboard-main {
        padding: 15px;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .card-header .badge {
        align-self: flex-start;
    }
    
    #nathriyat-table {
        font-size: 0.9rem;
    }
    
    #nathriyat-table th,
    #nathriyat-table td {
        padding: 10px 8px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions button {
        width: 100%;
    }
    
    .table-responsive {
        margin: 0 -10px;
        width: calc(100% + 20px);
    }
}

/* تنسيقات الطباعة */
@media print {
    body {
        background-color: #fff;
        font-size: 12pt;
    }
    
    .dashboard-header {
        background: none !important;
        color: #000;
        padding: 0 0 10px 0;
        margin-bottom: 20px;
        border-bottom: 1px solid #999;
        box-shadow: none;
    }
    
    .dashboard-header h1 {
        color: #000;
        font-size: 18pt;
    }
    
    .dashboard-header p {
        color: #333;
    }
    
    .active-month-info {
        background: none !important;
        color: #000;
        border: 1px solid #999;
        padding: 3px 10px;
    }
    
    .form-section,
    .no-print,
    .back-btn,
    .form-actions,
    .action-btn,
    .pagination {
        display: none !important;
    }
    
    .dashboard-main {
        padding: 0;
    }
    
    .card-header {
        border-bottom: 2px solid #999;
        background: none !important;
    }
    
    .card-header h2 {
        color: #000;
        font-size: 16pt;
    }
    
    .card-body {
        padding: 10px 0;
    }
    
    #nathriyat-table th {
        background: none !important;
        color: #000;
        border-bottom: 2px solid #999;
    }
    
    #nathriyat-table td {
        border-bottom: 1px solid #ddd;
    }
    
    .table-card,
    .form-card {
        box-shadow: none;
        border: none;
    }
    
    .dashboard-footer {
        border-top: 1px solid #999;
        padding: 10px 0;
        margin-top: 30px;
        background: none !important;
    }
}

/* تنسيقات زر إضافة نوع نثرية */
.action-buttons {
    display: flex;
    justify-content: flex-end; /* تعديل: توزيع الأزرار */
    gap: 15px; /* إضافة مسافة بين الأزرار */
    margin-bottom: 20px;
    padding: 0 10px;
}

/* تنسيقات قسم أنواع النثريات */
.types-section {
    margin-bottom: 25px;
}

#nathriyat-types-table {
    width: 100%;
    border-collapse: collapse;
}

#nathriyat-types-table th,
#nathriyat-types-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

#nathriyat-types-table th {
    background-color: rgba(52, 152, 219, 0.05);
    color: var(--primary-color);
    font-weight: 600;
}

#nathriyat-types-table tr:last-child td {
    border-bottom: none;
}

#nathriyat-types-table tr:hover {
    background-color: rgba(52, 152, 219, 0.03);
}

/* تنسيق عمود الأيقونة */
.type-icon {
    text-align: center;
    font-size: 1.2rem;
    color: var(--primary-color);
}

/* تنسيقات خاصة بالنافذة المنبثقة */
#type-form-message {
    margin-bottom: 15px;
}

/* تنسيق للأيقونة في الجدول */
.icon-preview {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background-color: #f5f5f5;
    border-radius: 50%;
    margin: 0 auto;
}

/* تنسيقات إضافية للجوال */
@media (max-width: 768px) {
    .action-buttons {
        justify-content: center;
    }
    
    #nathriyat-types-table th,
    #nathriyat-types-table td {
        padding: 10px 8px;
    }
}

/* تنسيقات النافذة المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    overflow: auto;
}

/* تنسيقات وضع النافذة المنبثقة في المركز */
.centered-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    overflow: auto;
    align-items: center;
    justify-content: center;
}

.centered-modal.show {
    display: flex;
}

.modal-content {
    background-color: var(--card-bg);
    margin: 5% auto;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 500px;
    position: relative;
    transition: transform 0.3s ease, opacity 0.3s ease;
    transform: scale(0.95);
    opacity: 0;
}

.centered-modal .modal-content {
    margin: 0;
    transform: scale(0.9);
    animation: modalFadeIn 0.3s forwards;
}

@keyframes modalFadeIn {
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.modal.show .modal-content {
    transform: scale(1);
    opacity: 1;
}

.close-modal-btn {
    position: absolute;
    left: 20px;
    top: 20px;
    font-size: 24px;
    color: var(--text-muted);
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-modal-btn:hover {
    color: var(--danger-color);
}

/* تنسيقات منتقي الأيقونات */
.icon-selector {
    margin-top: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid var(--border-color);
}

.icon-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 10px;
}

.icon-option {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background-color: white;
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.icon-option i {
    font-size: 1.2rem;
    color: var(--secondary-color);
    transition: color 0.2s ease;
}

.icon-option:hover {
    background-color: rgba(52, 152, 219, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.icon-option.selected {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.icon-option.selected i {
    color: white;
}

.selected-icon-display {
    display: flex;
    align-items: center;
    margin-top: 15px;
    padding: 8px 15px;
    border-radius: 6px;
    background-color: #fff;
    border: 1px dashed var(--border-color);
}

.selected-icon-display span {
    font-weight: 500;
    color: var(--text-dark);
    margin-left: 10px;
}

.icon-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--primary-color);
}

.icon-preview i {
    font-size: 1.2rem;
    color: white;
}

/* تحسينات للشاشات المختلفة */
@media (max-width: 768px) {
    .icon-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}

/* تنسيقات النافذة المنبثقة المحسنة */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(3px);
    align-items: center;
    justify-content: center;
}

.centered-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(3px);
}

.centered-modal.show {
    display: flex;
}

.modal-content {
    background-color: var(--card-bg);
    border-radius: 15px; /* زيادة انحناء الزوايا */
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25); /* ظل أقوى */
    width: 90%;
    max-width: 500px;
    position: relative;
    animation: modalFadeIn 0.3s forwards;
    padding: 0; /* إزالة الهامش الداخلي العام */
}

.modal-content h2 {
    background-color: #f7f7f7; /* خلفية رمادية فاتحة للعنوان */
    color: var(--primary-color);
    padding: 20px;
    margin: 0;
    border-radius: 15px 15px 0 0; /* انحناء للزوايا العلوية فقط */
    font-size: 1.4rem;
    text-align: center;
    border-bottom: 1px solid #eee;
}

.modal-content form {
    padding: 20px;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}

.close-modal-btn {
    position: absolute;
    left: 15px;
    top: 15px;
    font-size: 26px;
    color: #aaa;
    cursor: pointer;
    transition: color 0.3s ease;
    background: none;
    border: none;
    line-height: 1;
    padding: 0;
}

.close-modal-btn:hover {
    color: var(--danger-color);
}

/* تحسين تنسيقات منتقي الأيقونات */
.icon-selector {
    background-color: #fff;
    border-radius: 10px;
    border: 1px solid #e0e0e0;
    padding: 15px;
    margin-top: 10px;
}

.icon-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 10px;
}

.icon-option {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 5px;
    background-color: #f9f9f9;
    border: 1px solid #e8e8e8;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 0 auto; /* توسيط العناصر */
}

.icon-option i {
    font-size: 1.2rem;
    color: #555;
    transition: all 0.2s ease;
}

.icon-option:hover {
    background-color: #f0f7ff; /* خلفية زرقاء فاتحة عند التحويم */
    border-color: #c5d8f1;
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
}

.icon-option.selected {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.4); /* ظل أزرق خفيف */
    transform: translateY(-3px); /* رفع الأيقونة المختارة قليلاً */
}

.icon-option.selected i {
    color: #fff;
}

.selected-icon-display {
    display: flex;
    align-items: center;
    margin-top: 15px;
    padding: 10px 15px;
    border-radius: 8px;
    background-color: #f8f8f8;
    border: 1px dashed #ddd;
}

.selected-icon-display span {
    font-weight: 500;
    color: var(--text-dark);
    margin-left: 10px;
}

.icon-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--primary-color);
}

.icon-preview i {
    font-size: 1.2rem;
    color: white;
}

/* تنسيقات أزرار النموذج */
.modal-content .form-actions {
    background-color: #f7f7f7; /* خلفية رمادية فاتحة للأزرار */
    border-top: 1px solid #eee;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between; /* توزيع الأزرار على الجوانب */
    border-radius: 0 0 15px 15px; /* انحناء للزوايا السفلية */
    margin: 20px -20px -20px -20px; /* توسيع العنصر ليملأ عرض النافذة */
}

.modal-content .save-btn {
    background-color: #4CAF50; /* أخضر أكثر حيوية */
    color: white;
    padding: 10px 25px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s ease;
}

.modal-content .save-btn:hover {
    background-color: #3d8b40;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.modal-content .cancel-btn {
    background-color: #f1f1f1;
    color: #555;
    padding: 10px 25px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modal-content .cancel-btn:hover {
    background-color: #e0e0e0;
}

/* تنسيق حقول الإدخال */
.modal-content .form-group {
    margin-bottom: 20px;
}

.modal-content .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.modal-content .form-group input[type="text"],
.modal-content .form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

.modal-content .form-group input[type="text"]:focus,
.modal-content .form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
}

.required {
    color: #e74c3c;
    margin-right: 3px;
}

/* تحسين تصميم النافذة المنبثقة للشاشات الصغيرة */
@media (max-width: 768px) {
    .icon-grid {
        grid-template-columns: repeat(6, 1fr);
    }
    
    .modal-content {
        width: 95%;
    }
    
    .modal-content .form-actions {
        flex-direction: column-reverse; /* وضع زر الحفظ في الأعلى */
        gap: 10px;
    }
    
    .modal-content .save-btn,
    .modal-content .cancel-btn {
        width: 100%;
        padding: 12px;
    }
}

@media (max-width: 576px) {
    .icon-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* تنسيقات نافذة تفاصيل النوع */
#type-details-modal .modal-content.large {
    max-width: 800px; /* عرض أكبر للنافذة */
}

#type-details-modal h2 span {
    color: var(--accent-color); /* لون مميز لاسم النوع */
}

.modal-table-container {
    max-height: 400px; /* تحديد ارتفاع أقصى للجدول داخل النافذة */
    overflow-y: auto; /* إضافة شريط تمرير عمودي عند الحاجة */
    margin-top: 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
}

#type-details-table {
    width: 100%;
    border-collapse: collapse;
}

#type-details-table th,
#type-details-table td {
    padding: 10px 12px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

#type-details-table th {
    background-color: #f8f9fa;
    font-weight: 500;
    position: sticky; /* تثبيت العناوين عند التمرير */
    top: 0;
    z-index: 1;
}

#type-details-table tbody tr:hover {
    background-color: #f5f5f5;
}

#type-details-table tfoot td {
    border-top: 2px solid var(--secondary-color);
    background-color: #f8f9fa;
    font-size: 1.05rem;
}

#type-details-message {
    margin-bottom: 15px;
}

/* تنسيق زر إضافة معاملة جديدة */
.save-btn { /* استخدام نفس تنسيق زر الحفظ */
    background-color: var(--success-color);
    color: white;
}

.save-btn:hover:not(:disabled) {
    background-color: #27ae60;
}

/* تنسيقات مخصصة لنافذة إضافة نثرية جديدة */
#add-nathriya-modal .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

#add-nathriya-modal .form-group-full-width {
    grid-column: 1 / -1;
}

#add-nathriya-modal .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--secondary-color);
}

#add-nathriya-modal .form-group input,
#add-nathriya-modal .form-group select,
#add-nathriya-modal .form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-family: 'Tajawal', sans-serif;
    font-size: 0.95rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

#add-nathriya-modal .form-group input:focus,
#add-nathriya-modal .form-group select:focus,
#add-nathriya-modal .form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
}

#add-nathriya-modal .form-actions {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin-top: 20px;
    padding: 15px;
    background-color: #f7f9fa;
    border-top: 1px solid #eee;
    border-radius: 0 0 8px 8px;
    margin: 0 -20px -20px -20px;
}

#add-nathriya-modal .save-btn,
#add-nathriya-modal .cancel-btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-family: 'Tajawal', sans-serif;
}

#add-nathriya-modal .save-btn {
    background-color: var(--success-color);
    color: white;
}

#add-nathriya-modal .save-btn:hover:not(:disabled) {
    background-color: #27ae60;
}

#add-nathriya-modal .cancel-btn {
    background-color: #f0f0f0;
    color: #555;
}

#add-nathriya-modal .cancel-btn:hover {
    background-color: #e0e0e0;
}

/* تحسين تلميحات الحقول المطلوبة */
.required {
    color: #e74c3c;
    margin-right: 3px;
}

/* مؤشرات التحميل */
.select-loading {
    opacity: 0.6;
    pointer-events: none;
}

.select-loading::after {
    content: "... جاري التحميل";
    font-size: 0.9em;
    font-style: italic;
    color: #777;
}

/* تنسيقات متجاوبة للشاشات الصغيرة */
@media (max-width: 768px) {
    #add-nathriya-modal .form-grid {
        grid-template-columns: 1fr;
    }
    
    #add-nathriya-modal .form-actions {
        flex-direction: column;
    }
    
    #add-nathriya-modal .save-btn,
    #add-nathriya-modal .cancel-btn {
        width: 100%;
    }
}
