<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإعدادات الافتراضية للسائقين</title>
    <!-- Shared Styles -->
    <link rel="stylesheet" href="../../shared_styles.css">
    <!-- Page Specific Styles -->
    <link rel="stylesheet" href="driver_settings.css">
    <!-- Sidebar Component Styles -->
    <link rel="stylesheet" href="../../shared_components/sidebar.css">
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Config -->
    <script src="../../config.js"></script>
    <!-- Auth Script -->
    <script src="../../auth.js"></script>
    <!-- Sidebar Component Script -->
    <script src="../../shared_components/sidebar.js"></script>
    <!-- Page Script -->
    <script defer src="driver_settings.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body class="dashboard-layout" data-load-sidebar="true">
    <!-- Top Navigation Bar -->
    <nav class="top-navbar">
        <div class="navbar-left">
            <button id="sidebar-toggle" class="sidebar-toggle-btn">
                <i class="fas fa-bars"></i>
            </button>
            <div class="navbar-brand">
                <i class="fas fa-user-tie"></i>
                <span>إدارة الإعدادات الافتراضية للسائقين</span>
            </div>
        </div>
        <div class="navbar-right">
            <div class="navbar-user">
                <span id="navbar-username"></span>
                <button id="logout-btn" class="logout-btn" style="display: none;">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Sidebar Container (will be populated by sidebar.js) -->
    <div id="sidebar-container"></div>

    <!-- Main Content Area -->
    <main class="main-content">
        <div class="content-wrapper">
            <!-- Dashboard Header -->
            <header class="dashboard-header">
                <div class="header-content">
                    <h1>
                        <i class="fas fa-user-tie"></i>
                        إعدادات السائقين
                    </h1>
                    <p class="header-description">إدارة الرواتب الأساسية والأرصدة الافتراضية لجميع السائقين</p>
                </div>
                <div class="header-actions">
                    <button id="refresh-btn" class="btn btn-secondary">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
                <div id="dashboard-message" class="message" style="display: none;"></div>
            </header>

            <!-- Statistics Cards -->
                <section class="stats-section">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="total-drivers">0</h3>
                                <p>إجمالي السائقين</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="configured-drivers">0</h3>
                                <p>سائقين مُعدين</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="total-balance">0</h3>
                                <p>إجمالي الأرصدة</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Search and Filters -->
                <section class="filters-section">
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-search"></i> البحث والتصفية</h2>
                        </div>
                        <div class="card-body">
                            <div class="filters-grid">
                                <div class="form-group">
                                    <label for="search-input">البحث عن سائق</label>
                                    <div class="search-input-container">
                                        <input type="text" id="search-input" placeholder="ابحث بالاسم أو رقم الهاتف...">
                                        <i class="fas fa-search"></i>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="shift-filter">تصفية حسب الوردية</label>
                                    <select id="shift-filter">
                                        <option value="">جميع الورديات</option>
                                        <option value="صباحي">صباحي</option>
                                        <option value="مسائي">مسائي</option>
                                        <option value="مختلط">مختلط</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="balance-filter">تصفية حسب الرصيد</label>
                                    <select id="balance-filter">
                                        <option value="">جميع الأرصدة</option>
                                        <option value="positive">رصيد موجب</option>
                                        <option value="negative">رصيد سالب</option>
                                        <option value="zero">رصيد صفر</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Drivers Table -->
                <section class="table-section">
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-table"></i> قائمة السائقين</h2>
                            <div class="header-actions">
                                <span class="badge" id="drivers-count">0 سائق</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Message Area -->
                            <div id="list-message" class="message" style="display: none;"></div>

                            <!-- Table Container -->
                            <div class="table-responsive">
                                <table id="defaults-table" class="modern-table">
                                    <thead>
                                        <tr>
                                            <th>اسم السائق</th>
                                            <th>الوردية</th>
                                            <th>الراتب الأساسي</th>
                                            <th>رصيد الافتتاح</th>
                                            <th>الرصيد الحالي</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="defaults-tbody">
                                        <tr>
                                            <td colspan="7" class="loading-message">
                                                <i class="fas fa-spinner fa-spin"></i>
                                                جاري تحميل بيانات السائقين...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <div class="pagination-container">
                                <div class="pagination-info">
                                    <span id="pagination-info">عرض 0 من 0 سائق</span>
                                </div>
                                <div class="pagination" id="pagination-controls">
                                    <!-- Pagination buttons will be added by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
        </div>
    </main>

    <!-- Edit Driver Modal -->
    <div id="edit-driver-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-edit"></i> تعديل إعدادات السائق</h2>
                <button class="close-modal-btn" id="close-edit-modal-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="edit-modal-message-area" class="message" style="display: none;"></div>
                <form id="edit-driver-form">
                    <input type="hidden" id="edit-driver-id">
                    <div class="form-row">
                        <div class="form-group">
                            <label>اسم السائق:</label>
                            <div class="driver-info">
                                <i class="fas fa-user-tie"></i>
                                <span id="edit-driver-name"></span>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-work-shift">الوردية:</label>
                            <select id="edit-work-shift" required>
                                <option value="">-- اختر الوردية --</option>
                                <option value="صباحي">صباحي</option>
                                <option value="مسائي">مسائي</option>
                                <option value="مختلط">مختلط</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="edit-default-salary">الراتب الأساسي (ريال):</label>
                            <input type="number" id="edit-default-salary" step="0.01" min="0" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-opening-balance">رصيد الافتتاح (ريال):</label>
                            <input type="number" id="edit-opening-balance" step="0.01">
                            <small class="form-help">رصيد الافتتاح للسائق (اختياري)</small>
                        </div>
                        <div class="form-group">
                            <label for="edit-current-balance">الرصيد الحالي (ريال):</label>
                            <input type="number" id="edit-current-balance" step="0.01" readonly>
                            <small class="form-help">للعرض فقط - يتم حسابه تلقائياً</small>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ التغييرات
                        </button>
                        <button type="button" class="btn btn-secondary" id="cancel-edit-btn">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Balance Info Modal -->
    <div id="balance-info-modal" class="balance-info-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تفاصيل الرصيد - <span id="balance-info-driver-name">...</span></h2>
                <button type="button" class="close-modal-btn" id="close-balance-info-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div id="balance-info-message" class="message" style="display: none;"></div>
                <table class="balance-breakdown-table">
                    <thead>
                        <tr>
                            <th>البيان</th>
                            <th>المبلغ</th>
                            <th>الملاحظات</th>
                        </tr>
                    </thead>
                    <tbody id="balance-breakdown-tbody">
                        <!-- Balance breakdown will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>



</body>
</html>
