/* Dashboard Layout */
.dashboard-layout {
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    overflow-x: hidden;
}

/* Top Navigation Bar */
.top-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-toggle-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.sidebar-toggle-btn:hover {
    background-color: rgba(255,255,255,0.1);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 600;
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.month-display {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: rgba(255,255,255,0.1);
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 500;
}

.navbar-user {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logout-btn {
    background: rgba(255,255,255,0.1);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: rgba(255,255,255,0.2);
}

/* Sidebar styles moved to shared_components/sidebar.css */

/* Main Content */
.main-content {
    margin-top: 70px;
    padding: 0;
    min-height: calc(100vh - 70px);
    transition: margin-right 0.3s ease;
}

.content-wrapper {
    padding: 30px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Dashboard Header */
.dashboard-header {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    border-top: 4px solid #667eea;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 20px;
}

.dashboard-header h1 {
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.dashboard-header h1 i {
    color: #667eea;
}

.month-selector-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.month-selector-container label {
    color: #6c757d;
    font-weight: 500;
}

.month-selector {
    padding: 8px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background-color: white;
    color: #495057;
    font-family: inherit;
    font-size: 0.95rem;
    cursor: pointer;
    min-width: 180px;
    transition: border-color 0.3s ease;
}

.month-selector:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Financial Summary */
.financial-summary {
    margin-bottom: 40px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

.summary-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border-top: 4px solid transparent;
    position: relative;
    overflow: hidden;
}

.summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.summary-card.revenue {
    border-top-color: #28a745;
}

.summary-card.expenses {
    border-top-color: #dc3545;
}

.summary-card.tax {
    border-top-color: #ffc107;
}

.summary-card.profit {
    border-top-color: #17a2b8;
}

.card-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.card-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.revenue .card-icon {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.expenses .card-icon {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
}

.tax .card-icon {
    background: linear-gradient(135deg, #ffc107, #f39c12);
}

.profit .card-icon {
    background: linear-gradient(135deg, #17a2b8, #3498db);
}

.card-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
}

.card-body {
    text-align: center;
}

.amount {
    font-size: 2.2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
    direction: ltr;
    text-align: center;
}

.currency {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.description {
    color: #6c757d;
    font-size: 0.85rem;
    line-height: 1.4;
}

/* Charts Section */
.charts-section {
    margin-bottom: 40px;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 30px;
}

.chart-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.chart-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.chart-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f8f9fa;
}

.chart-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-header h3 i {
    color: #667eea;
}

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

.chart-container canvas {
    max-height: 100%;
    width: 100% !important;
    height: 100% !important;
}

/* Notifications Section */
.notifications-section {
    margin-bottom: 40px;
}

.notifications-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    overflow: hidden;
}

.notifications-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.notifications-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.notifications-header h3 i {
    color: #667eea;
}

.notifications-count {
    background: #667eea;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.notifications-body {
    padding: 0;
}

.notifications-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    padding: 15px 25px;
    border-bottom: 1px solid #f8f9fa;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: background-color 0.3s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.loading {
    justify-content: center;
    color: #6c757d;
    font-style: italic;
}

.notification-item i {
    color: #667eea;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.notification-item.warning i {
    color: #ffc107;
}

.notification-item.error i {
    color: #dc3545;
}

.notification-item.success i {
    color: #28a745;
}

.notification-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.notification-message {
    font-weight: 500;
    line-height: 1.4;
}

.notification-time {
    font-size: 0.75rem;
    color: #6c757d;
    font-weight: 400;
}

/* Section modal styles moved to shared_components/sidebar.css */

/* Footer */
.dashboard-footer {
    background: white;
    border-top: 1px solid #e9ecef;
    padding: 20px 30px;
    margin-top: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 15px;
}

.home-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.95rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.home-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.dashboard-footer p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Message Styles */
.message {
    padding: 12px 20px;
    border-radius: 8px;
    margin: 15px 0;
    display: none;
    font-weight: 500;
}

.message.show {
    display: block;
}

.message.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Bank Type Modal (existing styles) */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(4px);
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal .modal-content {
    background-color: #fff;
    margin: auto;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    width: 90%;
    max-width: 450px;
    text-align: center;
    position: relative;
    animation: fadeInModal 0.3s ease-out;
}

@keyframes fadeInModal {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.close-modal-btn {
    color: #aaa;
    position: absolute;
    left: 15px;
    top: 10px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-modal-btn:hover,
.close-modal-btn:focus {
    color: #333;
    text-decoration: none;
}

.modal .modal-content h2 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #2c3e50;
}

.modal .modal-content p {
    margin-bottom: 25px;
    color: #6c757d;
    font-size: 1rem;
}

.modal-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.modal-btn {
    padding: 12px 25px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.modal-btn.primary {
    background-color: #667eea;
    color: white;
}

.modal-btn.primary:hover {
    background-color: #5a6fd8;
}

.modal-btn.secondary {
    background-color: #6c757d;
    color: white;
}

.modal-btn.secondary:hover {
    background-color: #5a6268;
}

.modal-btn i {
    font-size: 1.1em;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .content-wrapper {
        padding: 20px;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .top-navbar {
        padding: 0 15px;
    }

    .navbar-brand span {
        display: none;
    }

    .month-display {
        display: none;
    }

    .sidebar {
        width: 280px;
        right: -280px;
    }

    .content-wrapper {
        padding: 15px;
    }

    .header-content {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .dashboard-header h1 {
        font-size: 1.5rem;
        justify-content: center;
    }

    .month-selector-container {
        justify-content: center;
    }

    .summary-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .summary-card {
        padding: 20px;
    }

    .amount {
        font-size: 1.8rem;
    }

    .charts-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .chart-container {
        height: 250px;
    }

    .dashboard-footer {
        flex-direction: column;
        text-align: center;
    }

    .modal-actions {
        flex-direction: column;
    }

    .section-modal .modal-content {
        width: 95%;
        margin: 10px;
    }

    .modal-body {
        padding: 20px;
    }
}

@media (max-width: 576px) {
    .summary-card {
        padding: 15px;
    }

    .card-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .amount {
        font-size: 1.6rem;
    }

    .chart-container {
        height: 200px;
    }

    .notifications-list {
        max-height: 200px;
    }

    .notification-item {
        padding: 12px 15px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}
