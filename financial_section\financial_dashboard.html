<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة المعلومات المالية</title>
    <!-- Shared Styles -->
    <link rel="stylesheet" href="../shared_styles.css">
    <!-- Page Specific Styles -->
    <link rel="stylesheet" href="financial_dashboard.css">
    <!-- Sidebar Component Styles -->
    <link rel="stylesheet" href="../shared_components/sidebar.css">
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Config -->
    <script src="../config.js"></script>
    <!-- Auth Script -->
    <script src="../auth.js"></script>
    <!-- Sidebar Component Script -->
    <script src="../shared_components/sidebar.js"></script>
    <!-- Page Script -->
    <script defer src="financial_dashboard.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body class="dashboard-layout" data-load-sidebar="true">
    <!-- Top Navigation Bar -->
    <nav class="top-navbar">
        <div class="navbar-left">
            <button id="sidebar-toggle" class="sidebar-toggle-btn">
                <i class="fas fa-bars"></i>
            </button>
            <div class="navbar-brand">
                <i class="fas fa-chart-line"></i>
                <span>لوحة المعلومات المالية</span>
            </div>
        </div>
        <div class="navbar-right">
            <div class="month-display">
                <i class="fas fa-calendar-alt"></i>
                <span id="dashboard-month-year">جاري التحميل...</span>
            </div>
            <div class="navbar-user">
                <span id="navbar-username"></span>
                <button id="logout-btn" class="logout-btn" style="display: none;">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Sidebar Container (will be populated by sidebar.js) -->
    <div id="sidebar-container"></div>

    <!-- Main Content Area -->
    <main class="main-content">
        <div class="content-wrapper">
            <!-- Dashboard Header -->
            <header class="dashboard-header">
                <div class="header-content">
                    <h1>
                        <i class="fas fa-chart-pie"></i>
                        التقرير المالي الشهري
                    </h1>
                    <div class="month-selector-container">
                        <label for="month-selector">عرض شهر:</label>
                        <select id="month-selector" class="month-selector">
                            <option value="">اختر شهر...</option>
                        </select>
                    </div>
                </div>
                <div id="dashboard-message" class="message" style="display: none;"></div>
            </header>

            <!-- Financial Summary Cards -->
            <section class="financial-summary">
                <div class="summary-grid">
                    <!-- إجمالي الإيرادات -->
                    <div class="summary-card revenue">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <h3>إجمالي الإيرادات</h3>
                        </div>
                        <div class="card-body">
                            <div class="amount" id="total-revenue">0.00</div>
                            <div class="currency">ريال سعودي</div>
                            <div class="description">اشتراكات الطلاب والمؤسسات (بدون ضريبة)</div>
                        </div>
                    </div>

                    <!-- إجمالي المصروفات -->
                    <div class="summary-card expenses">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <h3>إجمالي المصروفات</h3>
                        </div>
                        <div class="card-body">
                            <div class="amount" id="total-expenses">0.00</div>
                            <div class="currency">ريال سعودي</div>
                            <div class="description">السائقين والباصات والنثريات (بدون ضريبة)</div>
                        </div>
                    </div>

                    <!-- إجمالي الضريبة المضافة -->
                    <div class="summary-card tax">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <h3>إجمالي الضريبة المضافة</h3>
                        </div>
                        <div class="card-body">
                            <div class="amount" id="total-tax">0.00</div>
                            <div class="currency">ريال سعودي</div>
                            <div class="description">من جميع الأقسام التي تحمل ضريبة</div>
                        </div>
                    </div>

                    <!-- صافي الربح -->
                    <div class="summary-card profit">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <h3>صافي الربح</h3>
                        </div>
                        <div class="card-body">
                            <div class="amount" id="net-profit">0.00</div>
                            <div class="currency">ريال سعودي</div>
                            <div class="description">الإيرادات - المصروفات + الضريبة</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Charts Section -->
            <section class="charts-section">
                <div class="charts-grid">
                    <!-- Revenue Chart -->
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3><i class="fas fa-chart-column"></i> الإيرادات الشهرية</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="revenueChart"></canvas>
                        </div>
                    </div>

                    <!-- Expenses Chart -->
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3><i class="fas fa-chart-bar"></i> المصروفات الشهرية بالتفصيل</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="expensesChart"></canvas>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Notifications Section -->
            <section class="notifications-section">
                <div class="notifications-card">
                    <div class="notifications-header">
                        <h3><i class="fas fa-bell"></i> الإشعارات والتنبيهات</h3>
                        <span class="notifications-count" id="notifications-count">0</span>
                    </div>
                    <div class="notifications-body">
                        <div id="notifications-list" class="notifications-list">
                            <div class="notification-item loading">
                                <i class="fas fa-spinner fa-spin"></i>
                                <span>جاري تحميل الإشعارات...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Bank Type Selection Modal -->
    <div id="bank-type-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal-btn" id="close-bank-type-modal">&times;</span>
            <h2>اختر نوع البنك</h2>
            <p>الرجاء تحديد نوع حسابات البنوك التي تريد عرض معاملاتها:</p>
            <div class="modal-actions">
                <button id="select-bank-type-markazi" data-type="مركزي" class="modal-btn primary">
                    <i class="fas fa-building-columns"></i> مركزي
                </button>
                <button id="select-bank-type-ajel" data-type="آجل" class="modal-btn secondary">
                    <i class="fas fa-calendar-alt"></i> آجل
                </button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="dashboard-footer">
        <button id="home-btn" class="home-btn">
            <i class="fas fa-calendar-day"></i> العودة لاختيار الشهر
        </button>
        <p>&copy; 2024 نظام الإدارة المالية</p>
    </footer>
</body>
</html>
