<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل المجموعة</title>
    <!-- Shared Styles -->
    <link rel="stylesheet" href="../../shared_styles.css"> <!-- Adjusted path -->
    <!-- Sidebar Styles -->
    <link rel="stylesheet" href="../../shared_components/sidebar.css">
    <!-- Page Specific Styles -->
    <link rel="stylesheet" href="group_details.css"> <!-- Added page specific CSS -->
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Config -->
    <script src="../../config.js"></script> <!-- Adjusted path -->
    <!-- Auth Script -->
    <script src="../../auth.js"></script> <!-- Adjusted path -->
    <!-- Page Script -->
    <script defer src="group_details.js"></script>
    <!-- إضافة سكريبت تعديل تكلفة الشهر -->
    <script defer src="edit_month_cost.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts (Example: Tajawal) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>


    <!-- Main Navbar -->
    <nav class="main-navbar">
        <div class="navbar-content">
            <div class="navbar-right">
                <button id="sidebar-toggle" class="sidebar-toggle-btn">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="navbar-title">تفاصيل المجموعة</span>
            </div>
            <div class="navbar-center">
                <span id="navbar-group-info" class="navbar-group-info">
                    مجموعة: <span id="navbar-group-name">...</span> -
                    الشهر: <span id="navbar-month-year">...</span>
                </span>
            </div>
            <div class="navbar-left">
                <span class="navbar-phone">فودرا (0506510089)</span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container">
        <main class="group-details-main">
            <!-- Page Header -->
            <header class="page-header">
                <h1>
                    مجموعة: <span id="group-name-display" class="highlight">...</span>
                    <span class="month-year-info">(الشهر: <span id="month-year-display">...</span>)</span>
                </h1>
                <button id="back-to-groups-btn" class="btn btn-secondary back-btn">
                    <i class="fas fa-arrow-right"></i> العودة للمجموعات
                </button>
                <button id="edit-month-cost-btn" class="btn btn-primary" style="margin-top: 10px;">
                    تعديل تكلفة الشهر
                </button>
                <!-- ملاحظة توضيحية -->
                <span style="display:block;font-size:0.95em;color:#856404;background:#fff3cd;border:1px solid #ffeeba;padding:6px 12px;border-radius:4px;margin-top:8px;">
                    سيتم تعديل تكلفة الشهر الحالي فقط ولن يؤثر على الشهور الأخرى.
                </span>
                <div id="page-message" class="message" style="display: none;"></div>
            </header>

            <!-- Student Cards Section -->
            <section id="student-cards-container" class="student-cards-container">
                <!-- Student cards will be loaded here by JS -->
                <div class="loading-placeholder">
                    <i class="fas fa-spinner fa-spin"></i> جاري تحميل بيانات الطلاب...
                </div>
            </section>

            <!-- Bulk Actions Container -->
            <div id="bulk-actions-container" class="bulk-actions-container">
                <span class="selected-count">تم تحديد <span id="selected-count">0</span> طالب</span>
                <button type="button" id="bulk-delete-btn" class="btn">
                    <i class="fas fa-trash"></i> حذف المحدد
                </button>
            </div>
                <!-- Student card structure (inside renderStudentCards JS function) will be updated to include: -->
                <!-- Example structure within the card's action area: -->
                <!--
                <div class="student-card-actions">
                    <button class="btn btn-primary pay-action-btn" data-student-id="${student.id}" data-student-name="${student.name}" data-default-amount="${student.defaultAmount || 0}" ${isLocked ? 'disabled' : ''}>
                        <i class="fas fa-money-check-alt"></i> تسجيل دفعة
                    </button>
                    <button class="btn btn-secondary edit-payment-btn" data-student-id="${student.id}" data-student-name="${student.name}" data-payment-id="${student.latestPaymentId}" data-default-amount="${student.defaultAmount || 0}" ${!student.latestPaymentId || isLocked ? 'disabled' : ''}>
                        <i class="fas fa-edit"></i> تعديل الدفعة
                    </button>
                    <button class="btn btn-info print-invoice-btn" data-student-id="${student.id}" data-student-name="${student.name}" ${!hasAnyPayment || isLocked ? 'disabled' : ''}>
                        <i class="fas fa-print"></i> طباعة إيصال
                    </button>
                    <button class="btn ${isLocked ? 'btn-warning unlock-month-btn' : 'btn-danger lock-month-btn'}" data-student-id="${student.id}" ${!canLockUnlock ? 'disabled' : ''}>
                        <i class="fas ${isLocked ? 'fa-unlock' : 'fa-lock'}"></i> ${isLocked ? 'فك الإقفال' : 'إقفال الشهر'}
                    </button>
                </div>
                -->
            </section>

            <!-- Edit Payment Modal -->
            <div id="edit-payment-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <!-- Changed title -->
                        <h2 id="edit-payment-title">تعديل دفعة لـ: <span id="edit-payment-student-name">...</span></h2>
                        <button type="button" class="close-modal-btn" id="close-edit-payment-modal-btn">&times;</button> <!-- Added ID -->
                    </div>
                    <div class="modal-body">
                        <!-- Removed old <p> tag -->
                        <div id="edit-payment-message" class="message" style="display: none;"></div> <!-- Moved message area up -->

                        <!-- Edit Payment Calculation Box -->
                        <div id="edit-payment-calculation-box" class="payment-calculation-box collapsed">
                            <div class="calculation-header" onclick="toggleEditCalculationBox()">
                                <span>حساب المبلغ المطلوب</span>
                                <i class="fas fa-chevron-down" id="edit-calculation-toggle-icon"></i>
                            </div>
                            <div class="calculation-details" id="edit-calculation-details">
                                <div class="calculation-row">
                                    <span>تكلفة الشهر:</span>
                                    <span class="amount month-cost" id="edit-calc-month-cost">0.00 ريال</span>
                                </div>
                                <div class="calculation-row">
                                    <span>الرصيد الحالي:</span>
                                    <span class="amount" id="edit-calc-current-balance">0.00 ريال</span>
                                </div>

                                <!-- قسم الدفعات المسجلة سابقاً -->
                                <div id="edit-calc-payments-section">
                                    <!-- سيتم ملء هذا القسم بالدفعات المسجلة -->
                                </div>

                                <div class="calculation-row total">
                                    <span>المطلوب دفعه:</span>
                                    <span class="amount required" id="edit-calc-required-amount">0.00 ريال</span>
                                </div>
                                <div class="calculation-row">
                                    <span>المبلغ المدخل:</span>
                                    <span class="amount entered" id="edit-calc-entered-amount">0.00 ريال</span>
                                </div>
                                <div class="calculation-row total">
                                    <span>المتبقي بعد التعديل:</span>
                                    <span class="amount remaining" id="edit-calc-remaining-after">0.00 ريال</span>
                                </div>
                            </div>
                        </div>

                        <!-- Added Month/Year display (read-only) -->
                        <div class="form-row">
                             <div class="form-group">
                                <label for="edit-payment-month-year">الشهر المالي:</label>
                                <input type="text" id="edit-payment-month-year" name="month_year" readonly disabled style="background-color: #e9ecef; cursor: default;">
                            </div>
                            <div class="form-group">
                                <label for="edit-payment-status">حالة الدفع <span class="required">*</span></label>
                                <select id="edit-payment-status" name="status" required>
                                    <option value="">اختر الحالة...</option>
                                    <option value="paid">مدفوع</option>
                                    <option value="partial">جزئي</option>
                                    <option value="not_paid">غير مدفوع</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="edit-payment-amount">المبلغ <span class="required">*</span></label>
                                <input type="number" id="edit-payment-amount" name="amount" step="0.01" min="0" placeholder="0.00" required>
                            </div>
                            <div class="form-group">
                                <label for="edit-payment-date">التاريخ <span class="required">*</span></label>
                                <input type="date" id="edit-payment-date" name="date" required>
                            </div>
                        </div>

                        <div class="form-row">
                             <div class="form-group form-group-full">
                                <label for="edit-payment-bank">البنك <span class="required">*</span></label>
                                <select id="edit-payment-bank" name="bank_id" required>
                                    <!-- Bank options will be populated by JS -->
                                </select>
                            </div>
                        </div>
                        <!-- Removed old amount input and message area location -->
                    </div>
                    <div class="modal-footer">
                         <button type="button" id="cancel-edit-payment-btn" class="btn btn-secondary">إلغاء</button> <!-- Added Cancel Button -->
                        <button type="button" id="save-edit-payment-btn" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ التعديلات
                        </button>
                    </div>
                </div>
            </div>

            <!-- Edit Month Cost Modal -->
            <div id="edit-month-cost-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>تعديل تكلفة الشهر</h2>
                        <button type="button" class="close-modal-btn" id="close-edit-month-cost-modal-btn">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-row">
                            <div class="form-group form-group-full">
                                <label for="new-month-cost">التكلفة الجديدة</label>
                                <input type="number" id="new-month-cost" min="0" step="0.01" value="0.00">
                            </div>
                        </div>
                        <div id="edit-month-cost-message" class="message" style="display: none;"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" id="cancel-edit-month-cost-btn" class="btn btn-secondary">إلغاء</button>
                        <button type="button" id="save-edit-month-cost-btn" class="btn btn-primary">حفظ</button>
                    </div>
                </div>
            </div>

            <!-- Payment History Modal -->
            <div id="payment-history-modal" class="payment-history-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>سجل الدفعات - <span id="payment-history-student-name">...</span></h2>
                        <button type="button" class="close-modal-btn" id="close-payment-history-modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div id="payment-history-message" class="message" style="display: none;"></div>

                        <!-- Bulk Actions for Payment History -->
                        <div id="payment-history-bulk-actions" class="bulk-actions-container" style="display: none;">
                            <span id="payment-history-selected-count">0 مدفوعات محددة</span>
                            <button type="button" class="btn btn-danger" onclick="deleteSelectedPayments()">
                                <i class="fas fa-trash"></i> حذف المحدد
                            </button>
                        </div>

                        <table class="payment-history-table">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="select-all-payments" onchange="toggleAllPaymentSelection()">
                                    </th>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>البنك</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="payment-history-tbody">
                                <!-- Payment records will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Balance Info Modal -->
            <div id="balance-info-modal" class="balance-info-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>تفاصيل الرصيد - <span id="balance-info-student-name">...</span></h2>
                        <button type="button" class="close-modal-btn" id="close-balance-info-modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div id="balance-info-message" class="message" style="display: none;"></div>
                        <table class="balance-breakdown-table">
                            <thead>
                                <tr>
                                    <th>البيان</th>
                                    <th>المبلغ</th>
                                    <th>الملاحظات</th>
                                </tr>
                            </thead>
                            <tbody id="balance-breakdown-tbody">
                                <!-- Balance breakdown will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        </main>
    </div>

    <!-- Footer -->
    <footer class="main-footer">
    </footer>

    <!-- Load Sidebar Component -->
    <script src="../../shared_components/sidebar.js"></script>
    <script>
        // Initialize sidebar after page load
        document.addEventListener('DOMContentLoaded', function() {
            // Load sidebar component
            if (typeof loadSidebar === 'function') {
                loadSidebar();
            }
        });
    </script>

</body>
</html>
