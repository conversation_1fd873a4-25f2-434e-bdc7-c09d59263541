/* Define fallback variables in case main style.css is missing */
:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #34495e;
    --text-color: #333;
    --text-muted: #777;
    --bg-color: #f4f7f9;
    --card-bg: #ffffff;
    --border-color: #e0e0e0;
    --hover-bg: #e9ecef;
    --border-radius: 6px;
    --card-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* General Enhancements */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-color); /* Light background for the page */
    color: var(--text-color); /* Default text color */
    margin: 0;
    line-height: 1.6;
}

.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styling */
.dashboard-sidebar {
    width: 260px;
    background-color: var(--card-bg);
    padding: 20px;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1); /* Shadow on the left */
    display: flex;
    flex-direction: column;
    border-left: 1px solid var(--border-color);
}

.dashboard-sidebar h2 {
    color: var(--primary-dark);
    margin-top: 0;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.4rem;
}
.dashboard-sidebar h2 i {
    margin-left: 8px;
}

#buses-sidebar-list {
    list-style: none;
    padding: 0;
    margin: 15px 0;
    flex-grow: 1; /* Allow list to take available space */
    overflow-y: auto; /* Add scroll if list is long */
}

#buses-sidebar-list li a {
    display: block;
    padding: 12px 15px;
    color: var(--text-color); /* Use variable from main style.css */
    text-decoration: none;
    border-radius: var(--border-radius); /* Use variable */
    margin-bottom: 5px;
    transition: background-color 0.3s, color 0.3s;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; /* Prevent long names from breaking layout */
}

#buses-sidebar-list li a:hover {
    background-color: var(--hover-bg); /* Use variable */
    color: var(--primary-color); /* Use variable */
}

#buses-sidebar-list li a.active {
    background-color: var(--primary-color); /* Use variable */
    color: #fff;
    font-weight: 600;
}

#buses-sidebar-list .loading-message,
#buses-sidebar-list .info-message,
#buses-sidebar-list .error-message {
    padding: 15px;
    text-align: center;
    color: var(--text-muted); /* Use variable */
    font-style: italic;
}
#buses-sidebar-list .error-message {
    color: var(--danger-color); /* Use variable */
}

.sidebar-btn.back-btn {
    margin-top: auto; /* Push button to the bottom */
    background-color: var(--secondary-color);
    color: #fff;
    border: none;
    padding: 12px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.3s;
    text-align: center;
    font-size: 1rem;
}
.sidebar-btn.back-btn:hover {
    background-color: var(--dark-color);
}
.sidebar-btn.back-btn i {
    margin-left: 8px;
}


/* Main Content Area */
.dashboard-content {
    flex-grow: 1;
    padding: 30px;
    overflow-y: auto; /* Allow content scrolling */
}

/* Header */
.dashboard-header {
    background-color: var(--card-bg);
    padding: 20px 30px;
    margin-bottom: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}
.dashboard-header h1 {
    margin: 0 0 5px 0;
    color: var(--primary-dark);
    font-size: 1.8rem;
}
.dashboard-header p {
    margin: 0;
    color: var(--text-muted);
    font-size: 1rem;
}

/* Cards */
.dash-card, .control-card, .table-card, .form-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 30px;
}
.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.card-header h2 {
    margin: 0;
    font-size: 1.2rem;
    color: var(--secondary-color);
}
.card-header h2 i {
    margin-left: 8px;
}
.card-body {
    padding: 20px;
}
.badge {
    background-color: var(--primary-color);
    color: #fff;
    padding: 5px 10px;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: bold;
}

/* Controls Section */
.controls-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
    align-items: end; /* Align items to the bottom */
}
.control-btn {
    padding: 10px 15px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.95rem;
    transition: background-color 0.3s, color 0.3s;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}
.control-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}
.add-btn { background-color: var(--success-color); }
.add-btn:hover:not(:disabled) { background-color: #27ae60; }
.filter-btn { background-color: var(--primary-color); }
.filter-btn:hover:not(:disabled) { background-color: var(--primary-dark); }
.reset-btn { background-color: var(--secondary-color); }
.reset-btn:hover:not(:disabled) { background-color: var(--dark-color); }

/* Table */
.table-responsive {
    overflow-x: auto;
}
#expenses-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}
#expenses-table th, #expenses-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
    white-space: nowrap; /* Prevent text wrapping */
}
#expenses-table th {
    background-color: var(--light-color);
    font-weight: 600;
    color: var(--secondary-color);
}
#expenses-table tbody tr:hover {
    background-color: var(--hover-bg);
}
#expenses-table .loading-message, #list-message {
    text-align: center;
    padding: 20px;
    color: var(--text-muted);
}

/* Table Action Buttons */
.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px 8px;
    margin: 0 2px;
    border-radius: var(--border-radius);
    font-size: 0.9em;
    transition: background-color 0.2s, color 0.2s;
}

.action-btn i {
    font-size: 1.1em; /* Slightly larger icons */
}

.action-btn.edit-btn {
    color: var(--warning-color); /* Use variable */
}
.action-btn.edit-btn:hover {
    background-color: #fff3cd; /* Light yellow background on hover */
}

.action-btn.delete-btn {
    color: var(--danger-color); /* Use variable */
}
.action-btn.delete-btn:hover {
    background-color: #f8d7da; /* Light red background on hover */
}

/* Forms */
.form-group {
    margin-bottom: 15px;
}
.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--secondary-color);
}
.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="date"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-sizing: border-box; /* Include padding and border in element's total width and height */
    font-size: 1rem;
}
.form-group input:read-only,
.form-group input:disabled,
.form-group select:disabled,
.form-group textarea:disabled {
    background-color: #eee;
    cursor: not-allowed;
}
.required {
    color: var(--danger-color);
    margin-right: 3px;
}

/* Form Modal */
.form-section {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}
.form-section.show {
    opacity: 1;
    visibility: visible;
}
.form-card {
    width: 90%;
    max-width: 700px; /* Default max width */
    max-height: 90vh;
    overflow-y: auto;
}
.form-card.large-form {
    max-width: 900px;
}
.form-card .card-header {
    position: relative;
}
.close-btn {
    position: absolute;
    top: 10px;
    left: 15px; /* Adjusted for LTR */
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-muted);
    padding: 5px;
}
.close-btn:hover {
    color: var(--danger-color);
}
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}
.submit-btn { background-color: var(--primary-color); color: #fff; }
.submit-btn:hover:not(:disabled) { background-color: var(--primary-dark); }
.cancel-btn { background-color: var(--text-muted); color: #fff; }
.cancel-btn:hover:not(:disabled) { background-color: var(--secondary-color); }

.form-fieldset {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px 20px;
    margin-bottom: 20px;
}
.form-fieldset legend {
    font-weight: 600;
    color: var(--primary-dark);
    padding: 0 10px;
    margin-right: 10px;
    font-size: 1.05rem;
}
.form-fieldset legend i {
    margin-left: 8px;
}
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px 20px;
}

/* Messages */
.message {
    padding: 10px 15px;
    margin-top: 15px;
    border-radius: var(--border-radius);
    text-align: center;
    font-weight: 500;
}
.message.info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
.message.success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
.message.warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeeba; }
.message.error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }

/* Ensure pagination controls have some spacing */
.pagination {
    margin-top: 20px;
    text-align: center;
}
.pagination button, .pagination span {
    margin: 0 5px;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    cursor: pointer;
}
.pagination button:disabled {
    background-color: var(--light-color);
    color: var(--text-muted);
    cursor: not-allowed;
}
.pagination span {
    border: none;
    background: none;
    cursor: default;
}

/* Footer */
.dashboard-footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
    font-size: 0.9rem;
}
