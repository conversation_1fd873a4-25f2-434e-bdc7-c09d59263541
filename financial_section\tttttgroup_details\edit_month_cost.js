// --- منطق تعديل تكلفة الشهر ---

// عناصر المودال
const editMonthCostModal = document.getElementById('edit-month-cost-modal');
const openEditMonthCostBtn = document.getElementById('edit-month-cost-btn');
const closeEditMonthCostBtn = document.getElementById('close-edit-month-cost-modal-btn');
const cancelEditMonthCostBtn = document.getElementById('cancel-edit-month-cost-btn');
const saveEditMonthCostBtn = document.getElementById('save-edit-month-cost-btn');
const newMonthCostInput = document.getElementById('new-month-cost');
const editMonthCostMessage = document.getElementById('edit-month-cost-message');

// إضافة عنصر اختيار الطالب
const studentSelectContainer = document.createElement('div');
studentSelectContainer.className = 'form-row';
studentSelectContainer.innerHTML = `
    <div class="form-group form-group-full">
        <label for="edit-month-cost-student">اختر الطالب</label>
        <select id="edit-month-cost-student" required>
            <option value="">اختر الطالب...</option>
        </select>
    </div>
`;
// إدراج القائمة المنسدلة أعلى حقل التكلفة
const modalBody = editMonthCostModal?.querySelector('.modal-body');
if (modalBody && !modalBody.querySelector('#edit-month-cost-student')) {
    modalBody.insertBefore(studentSelectContainer, modalBody.firstChild);
}
const studentSelect = document.getElementById('edit-month-cost-student');

// فتح المودال
if (openEditMonthCostBtn) {
    openEditMonthCostBtn.addEventListener('click', async () => {
        if (editMonthCostModal) editMonthCostModal.classList.add('active');
        if (editMonthCostMessage) editMonthCostMessage.style.display = 'none';
        // جلب الطلاب من الكاش أو قاعدة البيانات
        let students = [];
        if (typeof studentDataCache !== 'undefined' && Array.isArray(studentDataCache) && studentDataCache.length > 0) {
            students = studentDataCache;
        } else if (typeof _supabase !== 'undefined' && typeof groupId !== 'undefined') {
            // جلب الطلاب من قاعدة البيانات إذا لم يكن الكاش متاحاً
            const { data: members } = await _supabase
                .from('student_group_members')
                .select('student_id, students (id, name)')
                .eq('group_id', groupId);
            students = (members || []).map(m => m.students);
        }
        // تعبئة القائمة
        if (studentSelect) {
            studentSelect.innerHTML = '<option value="">اختر الطالب...</option>';
            students.forEach(s => {
                studentSelect.innerHTML += `<option value="${s.id}">${s.name}</option>`;
            });
        }
        // إعادة تعيين الحقول
        newMonthCostInput.value = '';
    });
}

// عند اختيار طالب: جلب تكلفة الشهر لهذا الطالب والشهر الحالي
if (studentSelect) {
    studentSelect.addEventListener('change', async () => {
        const studentId = studentSelect.value;
        if (!studentId) {
            newMonthCostInput.value = '';
            return;
        }
        if (typeof _supabase !== 'undefined' && typeof selectedMonthId !== 'undefined') {
            // جلب من جدول student_month_costs أولاً
            const { data: monthCostData, error: monthCostError } = await _supabase
                .from('student_month_costs')
                .select('month_cost')
                .eq('student_id', studentId)
                .eq('budget_month_id', selectedMonthId)
                .single();
            if (!monthCostError && monthCostData && typeof monthCostData.month_cost !== 'undefined' && monthCostData.month_cost !== null) {
                // إذا وُجدت قيمة محددة لهذا الشهر، استخدمها
                newMonthCostInput.value = parseFloat(monthCostData.month_cost).toFixed(2);
            } else {
                // إذا لم يوجد سجل أو month_cost فارغ، جلب القيمة الافتراضية من student_subscription_defaults
                const { data: defaultData, error: defaultError } = await _supabase
                    .from('student_subscription_defaults')
                    .select('default_amount')
                    .eq('student_id', studentId)
                    .single();
                if (!defaultError && defaultData && typeof defaultData.default_amount !== 'undefined' && defaultData.default_amount !== null) {
                    newMonthCostInput.value = parseFloat(defaultData.default_amount).toFixed(2);
                } else {
                    newMonthCostInput.value = '';
                }
            }
        }
    });
}

// إغلاق المودال
function closeEditMonthCostModal() {
    if (editMonthCostModal) editMonthCostModal.classList.remove('active');
    if (editMonthCostMessage) editMonthCostMessage.style.display = 'none';
}
if (closeEditMonthCostBtn) closeEditMonthCostBtn.addEventListener('click', closeEditMonthCostModal);
if (cancelEditMonthCostBtn) cancelEditMonthCostBtn.addEventListener('click', closeEditMonthCostModal);
if (editMonthCostModal) {
    editMonthCostModal.addEventListener('click', (e) => {
        if (e.target === editMonthCostModal) closeEditMonthCostModal();
    });
}

// حفظ التكلفة الجديدة لطالب محدد (تحديث أو إدراج في student_month_costs لهذا الشهر فقط)
if (saveEditMonthCostBtn) {
    saveEditMonthCostBtn.addEventListener('click', async () => {
        const studentId = studentSelect.value;
        const newCost = parseFloat(newMonthCostInput.value);
        if (!studentId) {
            if (editMonthCostMessage) {
                editMonthCostMessage.textContent = 'الرجاء اختيار الطالب.';
                editMonthCostMessage.className = 'message error';
                editMonthCostMessage.style.display = 'block';
            }
            return;
        }
        if (isNaN(newCost) || newCost < 0) {
            if (editMonthCostMessage) {
                editMonthCostMessage.textContent = 'الرجاء إدخال تكلفة صحيحة.';
                editMonthCostMessage.className = 'message error';
                editMonthCostMessage.style.display = 'block';
            }
            return;
        }
        if (typeof _supabase === 'undefined' || typeof selectedMonthId === 'undefined') {
            if (editMonthCostMessage) {
                editMonthCostMessage.textContent = 'خطأ في الاتصال بقاعدة البيانات.';
                editMonthCostMessage.className = 'message error';
                editMonthCostMessage.style.display = 'block';
            }
            return;
        }
        saveEditMonthCostBtn.disabled = true;
        saveEditMonthCostBtn.textContent = '...جاري الحفظ';
        try {
            // upsert: إذا كان السجل موجود يحدثه، إذا لم يوجد ينشئه
            const { error } = await _supabase
                .from('student_month_costs')
                .upsert({
                    student_id: studentId,
                    budget_month_id: selectedMonthId,
                    month_cost: newCost
                }, { onConflict: ['student_id', 'budget_month_id'] });
            if (error) throw error;
            if (editMonthCostMessage) {
                editMonthCostMessage.textContent = 'تم تحديث تكلفة الشهر للطالب لهذا الشهر فقط.';
                editMonthCostMessage.className = 'message success';
                editMonthCostMessage.style.display = 'block';
            }
            setTimeout(() => {
                closeEditMonthCostModal();
                saveEditMonthCostBtn.disabled = false;
                saveEditMonthCostBtn.textContent = 'حفظ';
            }, 1200);
        } catch (err) {
            if (editMonthCostMessage) {
                editMonthCostMessage.textContent = 'حدث خطأ أثناء تحديث التكلفة.';
                editMonthCostMessage.className = 'message error';
                editMonthCostMessage.style.display = 'block';
            }
            saveEditMonthCostBtn.disabled = false;
            saveEditMonthCostBtn.textContent = 'حفظ';
        }
    });
}
