/* Content copied from c:\Users\<USER>\OneDrive\سطح المكتب\مشروعي\institutions_section\style.css */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

:root {
    /* Main Colors */
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;

    /* Backgrounds */
    --body-bg: #f5f7fa;
    --card-bg: #ffffff;

    /* Borders */
    --border-color: #e1e8ed;
    --border-radius: 8px;

    /* Typography */
    --text-dark: #34495e;
    --text-muted: #7f8c8d;
    --text-light: #ffffff;

    /* Shadow */
    --card-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    --hover-shadow: 0 5px 25px rgba(0, 0, 0, 0.12);
    --button-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--body-bg);
    color: var(--text-dark);
    line-height: 1.6;
}

/* Dashboard Container */
.dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.dashboard-header {
    background: linear-gradient(135deg, var(--secondary-color), #34495e); /* Adjusted color */
    color: var(--text-light);
    padding: 20px;
    text-align: center;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
}

.header-content h1 {
    font-size: 1.8rem;
    margin-bottom: 5px;
    font-weight: 700;
}

.header-content p {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 300;
}

.header-content h1 i {
    margin-left: 10px;
}

/* Main Content */
.dashboard-main {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    width: 100%;
}

/* Stats Cards (Keep if needed, otherwise remove) */
.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}
/* ... (rest of stat card styles if needed) ... */

/* Controls Section */
.controls-section {
    margin-bottom: 20px;
}

.control-card, .table-card, .form-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa; /* Light header background */
}

.card-header h2 {
    font-size: 1.2rem;
    font-weight: 500;
    margin: 0;
}

.card-body {
    padding: 20px 30px;
    overflow-y: auto;
}

.controls-grid {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 15px;
    align-items: center;
}

/* Specific grid for year selector */
.year-selector-grid {
    grid-template-columns: 1fr auto; /* Input takes available space, button takes auto */
    align-items: end; /* Align items to the bottom */
}
.year-selector-grid .form-group {
    margin-bottom: 0; /* Remove default bottom margin */
}


.control-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var(--text-light);
    transition: all 0.3s ease;
    box-shadow: var(--button-shadow);
}

.control-btn i {
    font-size: 1rem;
}

.add-btn {
    background-color: var(--success-color);
}

.add-btn:hover {
    background-color: #27ae60;
}

.print-btn { /* Keep if needed */
    background-color: var(--primary-color);
}
.print-btn:hover {
    background-color: var(--primary-dark);
}

/* Search container (Keep if needed) */
.search-container {
    display: flex;
    max-width: 100%;
    position: relative;
}
/* ... (rest of search styles if needed) ... */

/* Table */
.table-section {
    margin-bottom: 20px;
}

.badge {
    background-color: var(--primary-color);
    color: var(--text-light);
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.table-responsive {
    overflow-x: auto;
}

/* Updated ID for months table */
#months-table {
    width: 100%;
    border-collapse: collapse;
}

#months-table th,
#months-table td {
    padding: 12px 15px;
    text-align: right;
    vertical-align: middle;
}

#months-table th {
    background-color: rgba(44, 62, 80, 0.05); /* Adjusted color */
    color: var(--secondary-color);
    font-weight: 500;
    border-bottom: 1px solid var(--border-color);
    white-space: nowrap;
}

#months-table tbody tr {
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.3s ease;
}

#months-table tbody tr:last-child {
    border-bottom: none;
}

#months-table tbody tr:hover {
    background-color: rgba(44, 62, 80, 0.03); /* Adjusted color */
}

#months-table .loading-message {
    text-align: center;
    color: var(--text-muted);
    padding: 20px;
}

/* Action buttons */
.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px 8px;
    margin: 0 3px;
    font-size: 1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.action-btn.close-month-btn {
    color: var(--danger-color);
}
.action-btn.close-month-btn:hover:not(:disabled) {
    background-color: rgba(231, 76, 60, 0.1);
}
.action-btn.close-month-btn:disabled {
    color: var(--text-muted);
    cursor: not-allowed;
    opacity: 0.6;
}

.action-btn.view-details-btn { /* Example for future use */
    color: var(--primary-color);
}
.action-btn.view-details-btn:hover {
    background-color: rgba(52, 152, 219, 0.1);
}


/* Pagination (Keep if needed) */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    gap: 5px;
}
/* ... (rest of pagination styles if needed) ... */


/* Form Section - Modal Styling (Keep for Add Year) */
.form-section {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    justify-content: center;
    align-items: center;
    padding: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.form-section.show {
    display: flex;
    opacity: 1;
}

/* Use mini-form class for smaller modals */
.mini-form {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    width: 100%;
    max-width: 450px; /* Adjust as needed */
    margin: auto;
    position: relative;
    transform: translateY(30px);
    transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    overflow: hidden;
}

.form-section.show .mini-form {
    transform: translateY(0);
}

/* Re-use card-header style */
.mini-form .card-header {
    padding: 15px 25px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.mini-form .card-header h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}
.close-btn:hover {
    color: var(--danger-color);
    background-color: rgba(231, 76, 60, 0.1);
}

.mini-form .card-body {
    padding: 25px 30px;
}

/* Form group styles */
.form-group {
    margin-bottom: 20px; /* Increased margin */
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.25);
}

.required {
    color: var(--danger-color);
    margin-right: 3px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 25px; /* Increased margin */
}

.submit-btn,
.cancel-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.submit-btn {
    background-color: var(--success-color);
    color: var(--text-light);
}
.submit-btn:hover {
    background-color: #27ae60;
}

.cancel-btn {
    background-color: var(--light-color);
    color: var(--text-dark);
}
.cancel-btn:hover {
    background-color: #bdc3c7;
}

/* Message Styles */
.message {
    margin-top: 15px;
    padding: 12px;
    border-radius: var(--border-radius);
    text-align: center;
    font-weight: 500;
    display: none; /* Hide by default */
}

.message.show {
    display: block;
}

.message.info {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.message.success {
    background-color: rgba(46, 204, 113, 0.1);
    color: var(--success-color); /* Corrected variable name */
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.message.error {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

/* Footer */
.dashboard-footer {
    background-color: var(--card-bg);
    text-align: center;
    padding: 15px;
    color: var(--text-muted);
    border-top: 1px solid var(--border-color);
    margin-top: auto;
}

/* Status indicator styles */
.status-closed { color: var(--danger-color); font-weight: bold; }
.status-open { color: var(--success-color); font-weight: bold; }

/* Responsive */
@media (max-width: 768px) {
    .controls-grid {
        grid-template-columns: 1fr;
    }
    .year-selector-grid {
        grid-template-columns: 1fr; /* Stack on smaller screens */
        gap: 10px;
    }
    .year-selector-grid .control-btn {
        width: 100%; /* Make button full width */
    }
}

@media (max-width: 576px) {
    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }
    .badge {
        margin-top: 5px;
        align-self: flex-start;
    }
    .form-actions {
        flex-direction: column;
    }
    .submit-btn,
    .cancel-btn {
        width: 100%;
    }
    .header-content h1 {
        font-size: 1.5rem;
    }
}
