/* Content copied from c:\Users\<USER>\OneDrive\سطح المكتب\مشروعي\financial_section\monthly_budget\style.css */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

:root {
    /* Main Colors */
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;

    /* Backgrounds */
    --body-bg: #f5f7fa;
    --card-bg: #ffffff;

    /* Borders */
    --border-color: #e1e8ed;
    --border-radius: 8px;

    /* Typography */
    --text-dark: #34495e;
    --text-muted: #7f8c8d;
    --text-light: #ffffff;

    /* Shadow */
    --card-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    --hover-shadow: 0 5px 25px rgba(0, 0, 0, 0.12);
    --button-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--body-bg);
    color: var(--text-dark);
    line-height: 1.6;
}

/* Dashboard Container */
.dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.dashboard-header {
    background: linear-gradient(135deg, var(--secondary-color), #34495e); /* Adjusted color */
    color: var(--text-light);
    padding: 20px;
    text-align: center;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
}

.header-content h1 {
    font-size: 1.8rem;
    margin-bottom: 5px;
    font-weight: 700;
}

.header-content p {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 300;
}

.header-content h1 i {
    margin-left: 10px;
}

/* Main Content */
.dashboard-main {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    width: 100%;
}

/* Controls Section */
.controls-section {
    margin-bottom: 20px;
}

.control-card, .table-card, .form-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa; /* Light header background */
}

.card-header h2 {
    font-size: 1.2rem;
    font-weight: 500;
    margin: 0;
}

.card-body {
    padding: 20px 30px;
    overflow-y: auto;
}

.controls-grid {
    display: grid;
    /* grid-template-columns: auto 1fr auto; */ /* Default, override below */
    gap: 15px;
    align-items: center;
}

/* Specific grid for filters */
.filters-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); /* Flexible columns */
    gap: 15px 20px; /* Row and column gap */
    align-items: end;
}
.filters-grid .form-group {
    margin-bottom: 0;
}
.filters-grid .control-btn {
    padding: 8px 15px; /* Slightly smaller buttons */
    font-size: 0.9em;
    height: 40px; /* Match input height */
    white-space: nowrap; /* Prevent button text wrapping */
}
.filter-btn { background-color: var(--primary-color); }
.filter-btn:hover { background-color: var(--primary-dark); }
.reset-btn { background-color: var(--secondary-color); }
.reset-btn:hover { background-color: #34495e; }


.control-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var(--text-light);
    transition: all 0.3s ease;
    box-shadow: var(--button-shadow);
}

.control-btn i {
    font-size: 1rem;
}

.add-btn {
    background-color: var(--success-color);
}

.add-btn:hover {
    background-color: #27ae60;
}

/* Table */
.table-section {
    margin-bottom: 20px;
}

.badge {
    background-color: var(--primary-color);
    color: var(--text-light);
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.table-responsive {
    overflow-x: auto;
}

/* Updated ID for transactions table */
#transactions-table {
    width: 100%;
    border-collapse: collapse;
}

#transactions-table th,
#transactions-table td {
    padding: 12px 15px;
    text-align: right;
    vertical-align: middle;
    white-space: nowrap; /* Prevent wrapping in cells */
}

#transactions-table th {
    background-color: rgba(44, 62, 80, 0.05);
    color: var(--secondary-color);
    font-weight: 500;
    border-bottom: 1px solid var(--border-color);
}

#transactions-table tbody tr {
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.3s ease;
}

#transactions-table tbody tr:last-child {
    border-bottom: none;
}

#transactions-table tbody tr:hover {
    background-color: rgba(44, 62, 80, 0.03);
}

#transactions-table .loading-message {
    text-align: center;
    color: var(--text-muted);
    padding: 20px;
}

/* Action buttons */
.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px 8px;
    margin: 0 3px;
    font-size: 1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.action-btn.edit-btn {
    color: var(--warning-color);
}
.action-btn.edit-btn:hover:not(:disabled) {
    background-color: rgba(243, 156, 18, 0.1);
}

.action-btn.delete-btn {
    color: var(--danger-color);
}
.action-btn.delete-btn:hover:not(:disabled) {
    background-color: rgba(231, 76, 60, 0.1);
}

/* Disable buttons for non-manual transactions */
.action-btn:disabled {
    color: var(--text-muted);
    cursor: not-allowed;
    opacity: 0.5;
}


/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    gap: 5px;
}
.pagination button {
    background-color: var(--light-color);
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.pagination button:hover:not(.disabled) {
    background-color: var(--primary-color);
    color: var(--text-light);
}
.pagination button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
.pagination button.active {
    background-color: var(--primary-color);
    color: var(--text-light);
    font-weight: bold;
}
.pagination-ellipsis {
    padding: 8px 5px;
    color: var(--text-muted);
}


/* Form Section - Modal Styling */
.form-section {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    justify-content: center;
    align-items: center;
    padding: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.form-section.show {
    display: flex;
    opacity: 1;
}

.form-card { /* Re-using form-card, adjust max-width if needed */
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    width: 100%;
    max-width: 700px; /* Default width, adjust if needed */
    margin: auto;
    position: relative;
    transform: translateY(30px);
    transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    overflow: hidden; /* Prevent content overflow */
}

.form-section.show .form-card {
    transform: translateY(0);
}

.form-card .card-header { /* Style header within modal */
    padding: 15px 25px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.form-card .card-header h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}
.close-btn:hover {
    color: var(--danger-color);
    background-color: rgba(231, 76, 60, 0.1);
}

.form-card .card-body { /* Style body within modal */
    padding: 25px 30px;
    max-height: 75vh; /* Limit height */
    overflow-y: auto; /* Allow scrolling */
}

/* Form row */
.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 15px;
}

/* Form group styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: all 0.3s ease;
    font-family: inherit;
}
/* Specific padding for date input */
.form-group input[type="date"] {
    padding: 9px 15px;
}


.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.25);
}

.required {
    color: var(--danger-color);
    margin-right: 3px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 25px;
}

.submit-btn,
.cancel-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.submit-btn {
    background-color: var(--success-color);
    color: var(--text-light);
}
.submit-btn:hover {
    background-color: #27ae60;
}

.cancel-btn {
    background-color: var(--light-color);
    color: var(--text-dark);
}
.cancel-btn:hover {
    background-color: #bdc3c7;
}

/* Message Styles */
.message {
    margin-top: 15px;
    padding: 12px;
    border-radius: var(--border-radius);
    text-align: center;
    font-weight: 500;
    display: none; /* Hide by default */
}

.message.show {
    display: block;
}

.message.info {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.message.success {
    background-color: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.message.error {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

/* Footer */
.dashboard-footer {
    background-color: var(--card-bg);
    text-align: center;
    padding: 15px;
    color: var(--text-muted);
    border-top: 1px solid var(--border-color);
    margin-top: auto;
}

/* Transaction type styles */
.transaction-deposit { color: var(--success-color); font-weight: bold; }
.transaction-withdrawal { color: var(--danger-color); font-weight: bold; }

/* Responsive */
@media (max-width: 992px) {
    .filters-grid {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr)); /* Adjust min width */
    }
}

@media (max-width: 768px) {
    .controls-grid {
        grid-template-columns: 1fr; /* Stack controls */
    }
    .filters-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); /* Further adjust */
    }
    .form-row {
        grid-template-columns: 1fr; /* Stack form elements */
    }
}

@media (max-width: 576px) {
    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }
    .badge {
        margin-top: 5px;
        align-self: flex-start;
    }
    .form-actions {
        flex-direction: column;
    }
    .submit-btn,
    .cancel-btn {
        width: 100%;
    }
    .header-content h1 {
        font-size: 1.5rem;
    }
    #transactions-table th,
    #transactions-table td {
        padding: 10px 8px; /* Reduce padding */
        font-size: 0.9em;
    }
}
