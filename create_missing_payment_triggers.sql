-- إنشاء التريجرات المفقودة للجداول الأخرى
-- بناءً على تحليل قاعدة البيانات الحالية وتريجر student_payments الذي يعمل

-- المشكلة المكتشفة:
-- 1. تريجر student_payments يعمل بدالة handle_student_payment_changes_v2
-- 2. الجداول الأخرى لا تملك تريجرات مالية
-- 3. نحتاج إنشاء دوال وتريجرات مشابهة لكل جدول

-- الخطوة 1: إنشاء دالة عامة للتعامل مع مصروفات الحافلات
CREATE OR REPLACE FUNCTION public.handle_bus_expense_changes()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_description text;
    v_log_id uuid;
    v_old_log_id uuid;
    v_amount numeric;
BEGIN
    -- تحديد المبلغ (استخدام total_with_tax إذا كان موجوداً، وإلا amount)
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        v_amount := COALESCE(NEW.total_with_tax, NEW.amount);
    ELSE
        v_amount := COALESCE(OLD.total_with_tax, OLD.amount);
    END IF;

    IF TG_OP = 'INSERT' THEN
        -- التحقق من وجود bank_id قبل إنشاء المعاملة
        IF NEW.bank_id IS NOT NULL AND v_amount > 0 THEN
            v_description := 'Bus expense: ' || COALESCE(NEW.expense_type, 'unknown') || 
                           CASE WHEN NEW.details IS NOT NULL THEN ' - ' || NEW.details ELSE '' END;
            
            -- إنشاء سجل في financial_transactions_log
            INSERT INTO public.financial_transactions_log (
                transaction_date, amount, transaction_type, description,
                source_table, source_record_id, bank_id, budget_month_id, is_reversal
            )
            VALUES (
                NEW.expense_date, v_amount, 'withdrawal', v_description,
                'bus_expenses', NEW.id::text, NEW.bank_id, NEW.budget_month_id, false
            )
            RETURNING id INTO v_log_id;
            
            -- إنشاء معاملة بنكية
            INSERT INTO public.bank_transactions (
                bank_id, amount, transaction_type, transaction_date, description,
                transaction_source, reference_table, reference_id,
                budget_month_id, financial_transaction_log_id
            )
            VALUES (
                NEW.bank_id, v_amount, 'withdrawal', NEW.expense_date, v_description,
                'bus_expenses-' || NEW.id::text, 'bus_expenses', NEW.id,
                NEW.budget_month_id, v_log_id
            );
            
            RAISE NOTICE 'Created bus expense log % and bank transaction for expense %', v_log_id, NEW.id;
        END IF;
        
    ELSIF TG_OP = 'UPDATE' THEN
        -- التحقق من وجود تغييرات مهمة
        IF (OLD.amount IS DISTINCT FROM NEW.amount OR 
            OLD.total_with_tax IS DISTINCT FROM NEW.total_with_tax OR
            OLD.bank_id IS DISTINCT FROM NEW.bank_id OR 
            OLD.expense_date IS DISTINCT FROM NEW.expense_date) THEN
            
            -- العثور على السجل القديم
            SELECT id INTO v_old_log_id
            FROM public.financial_transactions_log
            WHERE source_table = 'bus_expenses' 
              AND source_record_id = OLD.id::text
              AND is_reversal = false
            ORDER BY created_at DESC
            LIMIT 1;
            
            -- إنشاء سجل عكسي للمعاملة القديمة
            IF v_old_log_id IS NOT NULL THEN
                v_description := 'Reversal for bus expense: ' || COALESCE(OLD.expense_type, 'unknown');
                
                INSERT INTO public.financial_transactions_log (
                    transaction_date, amount, transaction_type, description,
                    source_table, source_record_id, bank_id, budget_month_id, 
                    is_reversal, original_log_entry_id
                )
                VALUES (
                    OLD.expense_date, COALESCE(OLD.total_with_tax, OLD.amount), 'deposit', v_description,
                    'bus_expenses', OLD.id::text, OLD.bank_id, OLD.budget_month_id, 
                    true, v_old_log_id
                );
                
                -- حذف المعاملة البنكية القديمة
                DELETE FROM public.bank_transactions 
                WHERE financial_transaction_log_id = v_old_log_id;
                
                RAISE NOTICE 'Created reversal entry and deleted old bank transaction for bus expense %', OLD.id;
            END IF;
            
            -- إنشاء سجل جديد للمعاملة المحدثة
            IF NEW.bank_id IS NOT NULL AND v_amount > 0 THEN
                v_description := 'Bus expense (updated): ' || COALESCE(NEW.expense_type, 'unknown');
                
                INSERT INTO public.financial_transactions_log (
                    transaction_date, amount, transaction_type, description,
                    source_table, source_record_id, bank_id, budget_month_id, is_reversal
                )
                VALUES (
                    NEW.expense_date, v_amount, 'withdrawal', v_description,
                    'bus_expenses', NEW.id::text, NEW.bank_id, NEW.budget_month_id, false
                )
                RETURNING id INTO v_log_id;
                
                -- إنشاء معاملة بنكية جديدة
                INSERT INTO public.bank_transactions (
                    bank_id, amount, transaction_type, transaction_date, description,
                    transaction_source, reference_table, reference_id,
                    budget_month_id, financial_transaction_log_id
                )
                VALUES (
                    NEW.bank_id, v_amount, 'withdrawal', NEW.expense_date, v_description,
                    'bus_expenses-' || NEW.id::text, 'bus_expenses', NEW.id,
                    NEW.budget_month_id, v_log_id
                );
                
                RAISE NOTICE 'Created new bus expense log % and bank transaction for updated expense %', v_log_id, NEW.id;
            END IF;
        END IF;
        
    ELSIF TG_OP = 'DELETE' THEN
        -- العثور على السجل في financial_transactions_log
        SELECT id INTO v_old_log_id
        FROM public.financial_transactions_log
        WHERE source_table = 'bus_expenses' 
          AND source_record_id = OLD.id::text
          AND is_reversal = false
        ORDER BY created_at DESC
        LIMIT 1;
        
        -- إنشاء سجل عكسي للحذف
        IF v_old_log_id IS NOT NULL THEN
            v_description := 'Deletion reversal for bus expense: ' || COALESCE(OLD.expense_type, 'unknown');
            
            INSERT INTO public.financial_transactions_log (
                transaction_date, amount, transaction_type, description,
                source_table, source_record_id, bank_id, budget_month_id, 
                is_reversal, original_log_entry_id
            )
            VALUES (
                OLD.expense_date, COALESCE(OLD.total_with_tax, OLD.amount), 'deposit', v_description,
                'bus_expenses', OLD.id::text, OLD.bank_id, OLD.budget_month_id, 
                true, v_old_log_id
            );
            
            -- حذف المعاملة البنكية
            DELETE FROM public.bank_transactions 
            WHERE financial_transaction_log_id = v_old_log_id;
            
            RAISE NOTICE 'Created deletion reversal and deleted bank transaction for deleted bus expense %', OLD.id;
        END IF;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$;

-- الخطوة 2: إنشاء دالة للتعامل مع معاملات النثريات
CREATE OR REPLACE FUNCTION public.handle_nathriyat_transaction_changes()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_description text;
    v_log_id uuid;
    v_old_log_id uuid;
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- التحقق من وجود bank_id قبل إنشاء المعاملة
        IF NEW.bank_id IS NOT NULL AND NEW.amount > 0 THEN
            v_description := 'Nathriyat transaction: ' || COALESCE(NEW.nathriyat_type_id::text, 'unknown');
            
            -- إنشاء سجل في financial_transactions_log
            INSERT INTO public.financial_transactions_log (
                transaction_date, amount, transaction_type, description,
                source_table, source_record_id, bank_id, budget_month_id, is_reversal
            )
            VALUES (
                NEW.transaction_date, NEW.amount, 'withdrawal', v_description,
                'nathriyat_transactions', NEW.id::text, NEW.bank_id, NEW.budget_month_id, false
            )
            RETURNING id INTO v_log_id;
            
            -- إنشاء معاملة بنكية
            INSERT INTO public.bank_transactions (
                bank_id, amount, transaction_type, transaction_date, description,
                transaction_source, reference_table, reference_id,
                budget_month_id, financial_transaction_log_id
            )
            VALUES (
                NEW.bank_id, NEW.amount, 'withdrawal', NEW.transaction_date, v_description,
                'nathriyat_transactions-' || NEW.id::text, 'nathriyat_transactions', NEW.id,
                NEW.budget_month_id, v_log_id
            );
            
            RAISE NOTICE 'Created nathriyat log % and bank transaction for transaction %', v_log_id, NEW.id;
        END IF;
        
    ELSIF TG_OP = 'UPDATE' THEN
        -- التحقق من وجود تغييرات مهمة
        IF (OLD.amount IS DISTINCT FROM NEW.amount OR 
            OLD.bank_id IS DISTINCT FROM NEW.bank_id OR 
            OLD.transaction_date IS DISTINCT FROM NEW.transaction_date) THEN
            
            -- العثور على السجل القديم
            SELECT id INTO v_old_log_id
            FROM public.financial_transactions_log
            WHERE source_table = 'nathriyat_transactions' 
              AND source_record_id = OLD.id::text
              AND is_reversal = false
            ORDER BY created_at DESC
            LIMIT 1;
            
            -- إنشاء سجل عكسي للمعاملة القديمة
            IF v_old_log_id IS NOT NULL THEN
                v_description := 'Reversal for nathriyat transaction: ' || COALESCE(OLD.nathriyat_type_id::text, 'unknown');
                
                INSERT INTO public.financial_transactions_log (
                    transaction_date, amount, transaction_type, description,
                    source_table, source_record_id, bank_id, budget_month_id, 
                    is_reversal, original_log_entry_id
                )
                VALUES (
                    OLD.transaction_date, OLD.amount, 'deposit', v_description,
                    'nathriyat_transactions', OLD.id::text, OLD.bank_id, OLD.budget_month_id, 
                    true, v_old_log_id
                );
                
                -- حذف المعاملة البنكية القديمة
                DELETE FROM public.bank_transactions 
                WHERE financial_transaction_log_id = v_old_log_id;
                
                RAISE NOTICE 'Created reversal entry and deleted old bank transaction for nathriyat %', OLD.id;
            END IF;
            
            -- إنشاء سجل جديد للمعاملة المحدثة
            IF NEW.bank_id IS NOT NULL AND NEW.amount > 0 THEN
                v_description := 'Nathriyat transaction (updated): ' || COALESCE(NEW.nathriyat_type_id::text, 'unknown');
                
                INSERT INTO public.financial_transactions_log (
                    transaction_date, amount, transaction_type, description,
                    source_table, source_record_id, bank_id, budget_month_id, is_reversal
                )
                VALUES (
                    NEW.transaction_date, NEW.amount, 'withdrawal', v_description,
                    'nathriyat_transactions', NEW.id::text, NEW.bank_id, NEW.budget_month_id, false
                )
                RETURNING id INTO v_log_id;
                
                -- إنشاء معاملة بنكية جديدة
                INSERT INTO public.bank_transactions (
                    bank_id, amount, transaction_type, transaction_date, description,
                    transaction_source, reference_table, reference_id,
                    budget_month_id, financial_transaction_log_id
                )
                VALUES (
                    NEW.bank_id, NEW.amount, 'withdrawal', NEW.transaction_date, v_description,
                    'nathriyat_transactions-' || NEW.id::text, 'nathriyat_transactions', NEW.id,
                    NEW.budget_month_id, v_log_id
                );
                
                RAISE NOTICE 'Created new nathriyat log % and bank transaction for updated transaction %', v_log_id, NEW.id;
            END IF;
        END IF;
        
    ELSIF TG_OP = 'DELETE' THEN
        -- العثور على السجل في financial_transactions_log
        SELECT id INTO v_old_log_id
        FROM public.financial_transactions_log
        WHERE source_table = 'nathriyat_transactions' 
          AND source_record_id = OLD.id::text
          AND is_reversal = false
        ORDER BY created_at DESC
        LIMIT 1;
        
        -- إنشاء سجل عكسي للحذف
        IF v_old_log_id IS NOT NULL THEN
            v_description := 'Deletion reversal for nathriyat transaction: ' || COALESCE(OLD.nathriyat_type_id::text, 'unknown');
            
            INSERT INTO public.financial_transactions_log (
                transaction_date, amount, transaction_type, description,
                source_table, source_record_id, bank_id, budget_month_id, 
                is_reversal, original_log_entry_id
            )
            VALUES (
                OLD.transaction_date, OLD.amount, 'deposit', v_description,
                'nathriyat_transactions', OLD.id::text, OLD.bank_id, OLD.budget_month_id, 
                true, v_old_log_id
            );
            
            -- حذف المعاملة البنكية
            DELETE FROM public.bank_transactions 
            WHERE financial_transaction_log_id = v_old_log_id;
            
            RAISE NOTICE 'Created deletion reversal and deleted bank transaction for deleted nathriyat %', OLD.id;
        END IF;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$;

-- الخطوة 3: إنشاء دالة للتعامل مع مصروفات السائقين (فقط المدفوعة من البنك)
CREATE OR REPLACE FUNCTION public.handle_driver_expense_changes()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_description text;
    v_log_id uuid;
    v_old_log_id uuid;
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- التحقق من وجود bank_id و bank_paid_amount قبل إنشاء المعاملة
        IF NEW.bank_id IS NOT NULL AND COALESCE(NEW.bank_paid_amount, 0) > 0 THEN
            v_description := 'Driver expense (bank paid): ' || COALESCE(NEW.expense_type, 'unknown');
            
            -- إنشاء سجل في financial_transactions_log
            INSERT INTO public.financial_transactions_log (
                transaction_date, amount, transaction_type, description,
                source_table, source_record_id, bank_id, budget_month_id, is_reversal
            )
            VALUES (
                NEW.expense_date, NEW.bank_paid_amount, 'withdrawal', v_description,
                'driver_expenses', NEW.id::text, NEW.bank_id, NEW.budget_month_id, false
            )
            RETURNING id INTO v_log_id;
            
            -- إنشاء معاملة بنكية
            INSERT INTO public.bank_transactions (
                bank_id, amount, transaction_type, transaction_date, description,
                transaction_source, reference_table, reference_id,
                budget_month_id, financial_transaction_log_id
            )
            VALUES (
                NEW.bank_id, NEW.bank_paid_amount, 'withdrawal', NEW.expense_date, v_description,
                'driver_expenses-' || NEW.id::text, 'driver_expenses', NEW.id,
                NEW.budget_month_id, v_log_id
            );
            
            RAISE NOTICE 'Created driver expense log % and bank transaction for expense %', v_log_id, NEW.id;
        END IF;
        
    ELSIF TG_OP = 'UPDATE' THEN
        -- التحقق من وجود تغييرات مهمة في المدفوعات البنكية
        IF (OLD.bank_paid_amount IS DISTINCT FROM NEW.bank_paid_amount OR 
            OLD.bank_id IS DISTINCT FROM NEW.bank_id OR 
            OLD.expense_date IS DISTINCT FROM NEW.expense_date) THEN
            
            -- العثور على السجل القديم
            SELECT id INTO v_old_log_id
            FROM public.financial_transactions_log
            WHERE source_table = 'driver_expenses' 
              AND source_record_id = OLD.id::text
              AND is_reversal = false
            ORDER BY created_at DESC
            LIMIT 1;
            
            -- إنشاء سجل عكسي للمعاملة القديمة
            IF v_old_log_id IS NOT NULL THEN
                v_description := 'Reversal for driver expense: ' || COALESCE(OLD.expense_type, 'unknown');
                
                INSERT INTO public.financial_transactions_log (
                    transaction_date, amount, transaction_type, description,
                    source_table, source_record_id, bank_id, budget_month_id, 
                    is_reversal, original_log_entry_id
                )
                VALUES (
                    OLD.expense_date, COALESCE(OLD.bank_paid_amount, 0), 'deposit', v_description,
                    'driver_expenses', OLD.id::text, OLD.bank_id, OLD.budget_month_id, 
                    true, v_old_log_id
                );
                
                -- حذف المعاملة البنكية القديمة
                DELETE FROM public.bank_transactions 
                WHERE financial_transaction_log_id = v_old_log_id;
                
                RAISE NOTICE 'Created reversal entry and deleted old bank transaction for driver expense %', OLD.id;
            END IF;
            
            -- إنشاء سجل جديد للمعاملة المحدثة
            IF NEW.bank_id IS NOT NULL AND COALESCE(NEW.bank_paid_amount, 0) > 0 THEN
                v_description := 'Driver expense (updated, bank paid): ' || COALESCE(NEW.expense_type, 'unknown');
                
                INSERT INTO public.financial_transactions_log (
                    transaction_date, amount, transaction_type, description,
                    source_table, source_record_id, bank_id, budget_month_id, is_reversal
                )
                VALUES (
                    NEW.expense_date, NEW.bank_paid_amount, 'withdrawal', v_description,
                    'driver_expenses', NEW.id::text, NEW.bank_id, NEW.budget_month_id, false
                )
                RETURNING id INTO v_log_id;
                
                -- إنشاء معاملة بنكية جديدة
                INSERT INTO public.bank_transactions (
                    bank_id, amount, transaction_type, transaction_date, description,
                    transaction_source, reference_table, reference_id,
                    budget_month_id, financial_transaction_log_id
                )
                VALUES (
                    NEW.bank_id, NEW.bank_paid_amount, 'withdrawal', NEW.expense_date, v_description,
                    'driver_expenses-' || NEW.id::text, 'driver_expenses', NEW.id,
                    NEW.budget_month_id, v_log_id
                );
                
                RAISE NOTICE 'Created new driver expense log % and bank transaction for updated expense %', v_log_id, NEW.id;
            END IF;
        END IF;
        
    ELSIF TG_OP = 'DELETE' THEN
        -- العثور على السجل في financial_transactions_log
        SELECT id INTO v_old_log_id
        FROM public.financial_transactions_log
        WHERE source_table = 'driver_expenses' 
          AND source_record_id = OLD.id::text
          AND is_reversal = false
        ORDER BY created_at DESC
        LIMIT 1;
        
        -- إنشاء سجل عكسي للحذف
        IF v_old_log_id IS NOT NULL THEN
            v_description := 'Deletion reversal for driver expense: ' || COALESCE(OLD.expense_type, 'unknown');
            
            INSERT INTO public.financial_transactions_log (
                transaction_date, amount, transaction_type, description,
                source_table, source_record_id, bank_id, budget_month_id, 
                is_reversal, original_log_entry_id
            )
            VALUES (
                OLD.expense_date, COALESCE(OLD.bank_paid_amount, 0), 'deposit', v_description,
                'driver_expenses', OLD.id::text, OLD.bank_id, OLD.budget_month_id, 
                true, v_old_log_id
            );
            
            -- حذف المعاملة البنكية
            DELETE FROM public.bank_transactions 
            WHERE financial_transaction_log_id = v_old_log_id;
            
            RAISE NOTICE 'Created deletion reversal and deleted bank transaction for deleted driver expense %', OLD.id;
        END IF;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$;

-- الخطوة 4: إنشاء التريجرات
-- حذف التريجرات القديمة إن وجدت
DROP TRIGGER IF EXISTS bus_expenses_financial_log_trigger ON public.bus_expenses;
DROP TRIGGER IF EXISTS nathriyat_transactions_financial_log_trigger ON public.nathriyat_transactions;
DROP TRIGGER IF EXISTS driver_expenses_financial_log_trigger ON public.driver_expenses;

-- إنشاء التريجرات الجديدة
CREATE TRIGGER bus_expenses_financial_log_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.bus_expenses
FOR EACH ROW EXECUTE FUNCTION public.handle_bus_expense_changes();

CREATE TRIGGER nathriyat_transactions_financial_log_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.nathriyat_transactions
FOR EACH ROW EXECUTE FUNCTION public.handle_nathriyat_transaction_changes();

CREATE TRIGGER driver_expenses_financial_log_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.driver_expenses
FOR EACH ROW EXECUTE FUNCTION public.handle_driver_expense_changes();

-- الخطوة 5: دالة للتحقق من حالة جميع التريجرات
CREATE OR REPLACE FUNCTION public.check_all_payment_triggers()
RETURNS TABLE(
    table_name text,
    trigger_exists boolean,
    trigger_function text
)
LANGUAGE sql
AS $$
    SELECT 
        tables.tbl_name::text as table_name,
        EXISTS(
            SELECT 1 FROM information_schema.triggers tr
            WHERE tr.event_object_table = tables.tbl_name 
              AND (tr.trigger_name LIKE '%financial_log_trigger' OR tr.trigger_name LIKE '%payment%trigger')
        ) as trigger_exists,
        COALESCE(
            (SELECT tr.action_statement FROM information_schema.triggers tr
             WHERE tr.event_object_table = tables.tbl_name 
               AND (tr.trigger_name LIKE '%financial_log_trigger' OR tr.trigger_name LIKE '%payment%trigger')
             LIMIT 1),
            'لا يوجد'
        )::text as trigger_function
    FROM (
        SELECT unnest(ARRAY[
            'student_payments',
            'bus_expenses',
            'nathriyat_transactions',
            'driver_expenses',
            'enterprise_subscriptions',
            'enterprise_group_payments'
        ]) as tbl_name
    ) tables
    ORDER BY tables.tbl_name;
$$;

-- الخطوة 6: عرض النتائج
SELECT 'تم إنشاء جميع التريجرات المفقودة بنجاح!' as status;

-- عرض حالة جميع التريجرات
SELECT * FROM public.check_all_payment_triggers();

-- تعليمات الاختبار
SELECT 'تعليمات الاختبار:' as instructions
UNION ALL
SELECT '1. لاختبار bus_expenses: أدرج مصروف حافلة مع bank_id'
UNION ALL
SELECT '2. لاختبار nathriyat_transactions: أدرج معاملة نثريات مع bank_id'
UNION ALL
SELECT '3. لاختبار driver_expenses: أدرج مصروف سائق مع bank_id و bank_paid_amount'
UNION ALL
SELECT '4. تحقق من إنشاء السجلات في financial_transactions_log و bank_transactions';
