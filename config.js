// Ideally, load these from environment variables in a real application
const SUPABASE_URL = 'https://jalwmdtsmfalyfvvseoi.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImphbHdtZHRzbWZhbHlmdnZzZW9pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxNDAxNDksImV4cCI6MjA2MzcxNjE0OX0.OBMKqeQmMwG1vY5HUMkt9rQqzm3yrnZUqO9PPrtaJEM';

// Export for use in other scripts (if using modules)
// If not using modules, you might include this script directly before others
// or attach these variables to the window object (less recommended).
// For simplicity now, script.js will assume these variables are globally available
// after including this file, or it will redefine them.
