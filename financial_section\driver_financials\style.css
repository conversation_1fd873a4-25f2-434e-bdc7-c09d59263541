/* Inherit shared styles */
/* Add specific styles for driver_financials page */

/* Top Navigation Bar */
.top-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-toggle-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.sidebar-toggle-btn:hover {
    background-color: rgba(255,255,255,0.1);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 600;
}

body {
    background-color: #f4f7f6; /* Light background */
    margin-top: 70px; /* Add margin for fixed navbar */
}

.dashboard-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.dashboard-main {
    flex-grow: 1;
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.page-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 15px;
    background-color: var(--card-bg, #fff);
    border-radius: var(--border-radius, 8px);
    box-shadow: var(--card-shadow, 0 4px 15px rgba(0, 0, 0, 0.08));
}

.page-header h1 {
    color: var(--primary-color, #2c3e50);
    margin-bottom: 5px;
}

#driver-name-header {
    color: var(--accent-color, #1abc9c);
}

.page-header p {
    color: var(--text-muted, #7f8c8d);
}

.summary-section {
    margin-bottom: 30px;
}

.summary-section h2 {
    text-align: center;
    margin-bottom: 20px;
    color: var(--primary-color, #2c3e50);
}

.summary-grid {
    display: grid;
    /* Use auto-fit to allow wrapping and maintain minimum size */
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px; /* Adjust gap as needed */
    margin-top: 15px;
}

.summary-card {
    background-color: var(--card-bg, #fff);
    border-radius: var(--border-radius, 8px);
    box-shadow: var(--card-shadow, 0 4px 15px rgba(0, 0, 0, 0.08));
    padding: 15px;
    text-align: center;
    border-top: 4px solid var(--primary-color, #2c3e50);
}

.summary-card h3 {
    font-size: 0.9rem;
    color: var(--text-muted, #7f8c8d);
    margin-bottom: 8px;
    font-weight: 500;
}
.summary-card h3 i {
    margin-left: 5px;
}

.summary-card p {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--secondary-color, #34495e);
}

.details-section {
    background-color: var(--card-bg, #fff);
    border-radius: var(--border-radius, 8px);
    box-shadow: var(--card-shadow, 0 4px 15px rgba(0, 0, 0, 0.08));
    padding: 20px;
    margin-top: 30px; /* Add space above the details section */
}

.details-section h2 {
    margin-bottom: 15px;
    color: var(--primary-color, #2c3e50);
    border-bottom: 1px solid var(--border-color, #dfe4ea);
    padding-bottom: 10px;
}

/* Header for Details Section to align title and button */
.details-section .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid var(--border-color, #dfe4ea);
    padding-bottom: 10px;
}

.details-section .card-header h2 {
    margin-bottom: 0; /* Remove bottom margin from h2 */
    border-bottom: none; /* Remove border from h2 */
    padding-bottom: 0; /* Remove padding from h2 */
}

/* Payment button in header */
.payment-btn {
    background-color: var(--success-color, #28a745);
    color: white;
}

.payment-btn:hover {
    background-color: #218838;
}

/* Group buttons in the header */
.details-section .header-buttons {
    /* Restore flex layout for buttons */
    display: flex;
    gap: 10px;
}

/* New Grid Layout for Details Section */
.details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr; /* Two equal columns */
    gap: 0; /* No gap, border will act as separator */
    border: 1px solid var(--border-color, #dfe4ea); /* Optional outer border */
    border-radius: var(--border-radius, 8px); /* Optional */
    overflow: hidden; /* Ensure border radius applies */
}

.details-column {
    padding: 15px;
}

/* Add vertical separator */
.commissions-column {
    border-left: 1px solid var(--border-color, #dfe4ea); /* Vertical line on the left of the right column (RTL) */
}

.column-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color, #dfe4ea);
}

.column-header h2 {
    margin: 0;
    font-size: 1.2rem;
    color: var(--primary-color, #2c3e50);
}
.column-header h2 i {
    margin-left: 8px;
}

.column-header .control-btn {
    padding: 6px 12px; /* Slightly smaller buttons */
    font-size: 0.9rem;
}

.transaction-list {
    /* Placeholder styles for the list area */
    min-height: 150px;
    color: var(--text-muted, #7f8c8d);
    font-size: 0.95rem;
}
.transaction-list p {
    text-align: center;
    padding-top: 20px;
}

/* Responsive adjustments for the grid */
@media (max-width: 768px) {
    .details-grid {
        grid-template-columns: 1fr; /* Stack columns on smaller screens */
    }
    .commissions-column {
        border-left: none; /* Remove vertical border */
        border-bottom: 1px solid var(--border-color, #dfe4ea); /* Add horizontal border */
    }
}

.table-container {
    /* Styles for table if added later */
    min-height: 100px; /* Placeholder height */
    text-align: center;
    color: var(--text-muted, #7f8c8d);
}

/* Footer button */
.main-footer {
    display: flex;
    justify-content: space-between; /* Align items */
    align-items: center;
    padding: 10px 5%;
}

.control-btn {
    background-color: var(--secondary-color, #34495e);
    color: var(--light-color, #ecf0f1);
    padding: 8px 15px;
    border: none;
    border-radius: var(--border-radius, 8px);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.control-btn:hover {
    background-color: var(--primary-color, #2c3e50);
}

.control-btn i {
    margin-left: 5px;
}

/* Style for the add deduction button */
.control-btn.accent-btn {
    background-color: var(--accent-color, #1abc9c);
    color: white;
}

.control-btn.accent-btn:hover {
    background-color: #16a085; /* Darker accent color */
}

/* Style for the add commission button */
.control-btn.success-btn {
    background-color: var(--success-color, #28a745);
    color: white;
}
.control-btn.success-btn:hover {
    background-color: #218838; /* Darker success color */
}

/* Message styles */
.message {
    padding: 12px 15px;
    margin: 15px 0;
    border-radius: var(--border-radius, 8px);
    border: 1px solid transparent;
    display: none; /* Hidden by default */
}
.message.show { display: block; }
.message.info { background-color: #eaf5fd; border-color: var(--info-color, #3498db); color: #2980b9; }
.message.success { background-color: #eafaf1; border-color: var (--success-color, #2ecc71); color: #27ae60; }
.message.warning { background-color: #fef5e7; border-color: var(--warning-color, #f39c12); color: #d35400; }
.message.error { background-color: #fdedec; border-color: var(--danger-color, #e74c3c); color: #c0392b; }

/* --- Modal Styles --- */
.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1000; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed FOR THE MODAL BACKGROUND, NOT CONTENT */
    background-color: rgba(0,0,0,0.6); /* Black w/ opacity */
    /* padding-top: 60px; /* Removed padding-top to allow flex centering */
    /* Add flex centering for the modal content */
    display: flex; /* Use flexbox */
    align-items: center; /* Vertical center */
    justify-content: center; /* Horizontal center */
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    background-color: #fefefe;
    margin: 20px; /* Add some margin for spacing from screen edges */
    padding: 0; /* Padding is handled by form-card */
    border: 1px solid #888;
    width: 450px; /* Fixed width for square-like appearance */
    min-height: 400px; /* Minimum height for square-like appearance */
    border-radius: 12px; /* Slightly more rounded corners */
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    animation: slideIn 0.3s ease-out;
    overflow-y: auto; /* Allow vertical scroll within the modal content if needed */
    max-height: calc(100vh - 40px); /* Limit height to prevent overflow */
}

/* Inherit form-card styles from shared_styles.css if available */
/* Or define basic form-card styles here */
.form-card .card-header {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;
    border-bottom: 1px solid var(--border-color, #dee2e6);
    border-top-left-radius: var(--border-radius, 8px);
    border-top-right-radius: var(--border-radius, 8px);
}
.form-card .card-header h2 {
    font-size: 1.2rem;
    margin: 0;
    color: var(--primary-color, #2c3e50);
}
.form-card .card-header h2 i { margin-left: 8px; }

.form-card .close-btn {
    color: #aaa;
    float: left; /* Position to the left in RTL */
    font-size: 28px;
    font-weight: bold;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0 5px;
    line-height: 1;
}
.form-card .close-btn:hover,
.form-card .close-btn:focus {
    color: black;
    text-decoration: none;
}

.form-card .card-body {
    padding: 20px 25px;
    /* Ensure box-sizing is applied, although global rule should cover it */
    box-sizing: border-box;
}

/* Form Group Styles - Enhanced for better layout */
.form-group {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--secondary-color, #34495e);
    font-size: 0.95rem;
}

/* Enhanced form elements with better styling */
.modal .form-group input[type="text"],
.modal .form-group input[type="number"],
.modal .form-group input[type="date"],
.modal .form-group select,
.modal .form-group textarea {
    width: 100%;
    max-width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1rem;
    font-family: inherit;
    transition: all 0.3s ease;
    background-color: #fafbfc;
}

.modal .form-group input:focus,
.modal .form-group select:focus,
.modal .form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    background-color: #fff;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Textarea specific styling */
.modal .form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Form Actions (can inherit from shared_styles.css) */
.form-actions {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end; /* Align buttons to the end (left in RTL) */
    gap: 10px;
}

/* Enhanced Button Styles */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-transform: none;
    min-width: 120px;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

/* Responsive modal for mobile */
@media (max-width: 768px) {
    .modal-content {
        width: calc(100% - 40px);
        min-height: auto;
        margin: 20px;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn {
        width: 100%;
        min-width: auto;
    }
}

/* Modal Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
@keyframes slideIn {
    from { transform: translateY(-30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Type Selection View */
#type-selection-view p {
    margin-bottom: 15px;
    font-size: 1.1rem;
    text-align: center;
    color: var(--secondary-color, #34495e);
}
.type-selection-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap; /* Allow wrapping on smaller screens */
}
.type-select-btn {
    padding: 12px 25px;
    font-size: 1.1rem;
    min-width: 120px;
}
.type-select-btn i {
    margin-left: 8px;
}
/* Specific colors for type buttons */
.type-select-btn[data-form="advance"] { background-color: var(--primary-color, #2c3e50); }
.type-select-btn[data-form="advance"]:hover { background-color: var(--secondary-color, #34495e); }
.type-select-btn[data-form="fine"] { background-color: var(--warning-color, #f39c12); color: #fff; }
.type-select-btn[data-form="fine"]:hover { background-color: #e67e22; }
.type-select-btn[data-form="accident"] { background-color: var(--danger-color, #e74c3c); }
.type-select-btn[data-form="accident"]:hover { background-color: #c0392b; }


/* VAT Section Styles */
.vat-section {
    background-color: #f8f9fa;
    padding: 10px 15px;
    border-radius: var(--border-radius, 8px);
    border: 1px solid var(--border-color, #dee2e6);
    margin-top: 15px;
}
.vat-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 500;
}
.vat-label input[type="checkbox"] {
    margin-left: 8px;
    width: 16px;
    height: 16px;
}
#fine-vat-details p,
#accident-vat-details p {
    margin: 5px 0;
    font-size: 0.95rem;
    color: var(--text-muted, #7f8c8d);
}
#fine-vat-details span,
#accident-vat-details span {
    font-weight: bold;
    color: var(--secondary-color, #34495e);
}

/* Payment Options Styles */
.payment-options {
    display: flex;
    gap: 15px;
    margin-top: 5px;
    flex-wrap: wrap;
}
.payment-options label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal; /* Override default label weight */
}
.payment-options input[type="radio"] {
    margin-left: 5px;
}

/* Bank Details Section */
#fine-bank-details,
#accident-bank-details {
    border: 1px dashed var(--border-color, #dee2e6);
    padding: 15px;
    margin-top: 10px;
    border-radius: var(--border-radius, 8px);
    background-color: #fdfdfe;
}

/* Ensure form actions are correctly aligned */
.form-actions {
    margin-top: 20px;
    display: flex;
    justify-content: space-between; /* Space out back/cancel/save */
    gap: 10px;
    flex-wrap: wrap; /* Allow wrapping */
}
.form-actions .back-to-type-select {
    margin-right: auto; /* Push back button to the start (right in RTL) */
}

/* Action buttons in table */
.edit-btn, .delete-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    margin: 0 2px;
}

.edit-btn {
    color: var(--info-color, #3498db);
}

.edit-btn:hover {
    background-color: rgba(52, 152, 219, 0.1);
    color: #2980b9;
}

.delete-btn {
    color: var(--danger-color, #e74c3c);
}

.delete-btn:hover {
    background-color: rgba(231, 76, 60, 0.1);
    color: #c0392b;
}

.edit-btn i, .delete-btn i {
    pointer-events: none;
}

/* Footer button adjustments */
.main-footer {
    display: flex;
    justify-content: space-between; /* Space out back button and copyright */
    align-items: center;
    padding: 10px 5%;
    position: relative; /* Needed for absolute positioning of center button */
    flex-wrap: wrap; /* Allow wrapping */
}

.main-footer p {
    margin: 5px 0; /* Add some margin for wrapping */
    width: 100%; /* Take full width on wrap */
    text-align: center; /* Center copyright when wrapped */
    order: 3; /* Ensure copyright is last */
}

.main-footer .control-btn {
    margin: 5px 0; /* Add margin for wrapping */
}

#back-to-drivers-list-btn {
    order: 1; /* Keep back button first */
}

/* Style and Center the Add Payment Button */
.payment-btn {
    background-color: var(--info-color, #3498db); /* Blue color for payment */
    color: white;
    position: absolute; /* Position relative to footer */
    left: 50%;
    transform: translateX(-50%); /* Center horizontally */
    order: 2; /* Place between back button and copyright */
}

.payment-btn:hover {
    background-color: #2980b9; /* Darker blue */
}

/* Ensure payment modal styles are covered */
/* ... (existing modal, form-card, form-group, payment-options styles) ... */

/* Conditional Bank Details for Payment Modal */
#payment-bank-details {
    border: 1px dashed var(--border-color, #dee2e6);
    padding: 15px;
    margin-top: 10px;
    border-radius: var(--border-radius, 8px);
    background-color: #fdfdfe;
}

/* Responsive adjustments for footer buttons */
@media (max-width: 600px) {
    .main-footer {
        flex-direction: column; /* Stack items vertically */
        align-items: center; /* Center items */
    }
    .payment-btn {
        position: static; /* Remove absolute positioning */
        transform: none; /* Remove transform */
        width: 80%; /* Make button wider */
        max-width: 300px;
        margin-top: 10px;
        margin-bottom: 10px;
    }
    #back-to-drivers-list-btn {
        width: 80%;
        max-width: 300px;
    }
    .main-footer p {
        margin-top: 10px;
    }
}

/* Details Section */
.details-section .card-header {
    background-color: #f8f9fa; /* Light background for header */
    border-bottom: 1px solid var(--border-color);
}

.details-section .card-header h2 {
    font-size: 1.3rem;
    color: var(--primary-color);
    margin: 0;
}

.details-section .card-header h2 i {
    margin-left: 10px;
}

.details-section .card-body {
    padding: 0; /* Remove padding if table has its own */
}

/* Transactions Table */
#transactions-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0; /* Remove bottom margin if inside card-body */
}

#transactions-table th,
#transactions-table td {
    padding: 12px 15px;
    text-align: right;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.95rem;
}

#transactions-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: var(--text-dark);
    white-space: nowrap;
}

#transactions-table tbody tr:hover {
    background-color: rgba(105, 130, 100, 0.04); /* Subtle hover effect */
}

#transactions-table tbody tr:last-child td {
    border-bottom: none; /* Remove border from last row */
}

#transactions-table .loading-placeholder,
#transactions-table .no-data-placeholder {
    text-align: center;
    padding: 30px;
    color: var(--text-muted);
    font-style: italic;
}

#transactions-table .amount-deduction {
    color: var(--danger-color);
    font-weight: 500;
}
#transactions-table .amount-deduction::before {
    content: '- ';
}

#transactions-table .amount-addition {
    color: var(--success-color);
    font-weight: 500;
}
#transactions-table .amount-addition::before {
    content: '+ ';
}

#transactions-table .amount-neutral {
    color: var(--text-muted);
}

/* Responsive Table */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

/* Ensure messages inside card body have margin */
.details-section .card-body .message {
    margin: 15px;
}

/* Details Section Grid Layout */
.details-section {
    margin-top: 30px;
    /* Remove card styling if using grid directly */
    /* background-color: var(--card-bg); */
    /* border-radius: var(--border-radius); */
    /* box-shadow: var(--card-shadow); */
    /* padding: 20px; */
}

.details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr; /* Two equal columns */
    gap: 25px;
    margin-bottom: 30px; /* Space before the main table */
}

.details-column {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden; /* Contain elements */
    display: flex;
    flex-direction: column;
    height: 400px; /* Fixed height for consistency */
}

.column-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
}

.column-header h2 {
    font-size: 1.2rem;
    margin: 0;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.column-header .control-btn {
    padding: 6px 12px;
    font-size: 0.85rem;
}

.transaction-list {
    padding: 15px 20px;
    overflow-y: auto;
    flex-grow: 1; /* Allow list to take available space */
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px dashed var(--border-color);
}

.transaction-item:last-child {
    border-bottom: none;
}

.item-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.item-date {
    font-size: 0.85rem;
    color: var(--text-muted);
}

.item-description {
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--text-dark);
}

.item-amount {
    font-size: 1.1rem;
    font-weight: 600;
    white-space: nowrap;
    margin-right: 10px; /* Space between details and amount */
}

.item-amount.positive {
    color: var(--success-color);
}

.item-amount.negative {
    color: var(--danger-color);
}

.list-placeholder {
    text-align: center;
    padding: 30px 15px;
    color: var(--text-muted);
    font-style: italic;
}

.list-placeholder.error {
    color: var(--danger-color);
    font-style: normal;
    font-weight: 500;
}

.list-placeholder i {
    margin-left: 5px;
}

/* Styles for the main transactions table (already added) */
.details-section.card { /* Keep styles for the bottom table card */
    margin-top: 30px;
}

/* Table Styles */
#transactions-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

#transactions-table th,
#transactions-table td {
    border: 1px solid var(--border-color, #dfe4ea);
    padding: 10px 12px;
    text-align: right;
    vertical-align: middle;
    font-size: 0.9rem; /* Slightly smaller font for table */
}

#transactions-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: var(--secondary-color, #34495e);
}

/* Specific column styling */
#transactions-table td:nth-child(4), /* Total Amount */
#transactions-table td:nth-child(5), /* VAT */
#transactions-table td:nth-child(6), /* Bank Paid */
#transactions-table td:nth-child(7) { /* Salary Impact */
    text-align: left;
    direction: ltr;
    font-weight: 500;
    min-width: 100px; /* Ensure minimum width */
}

/* Style for positive/negative amounts in salary impact */
.amount-addition { color: var(--success-color, #28a745); font-weight: bold; }
.amount-deduction { color: var(--danger-color, #dc3545); font-weight: bold; }
.amount-neutral { color: var(--text-muted, #7f8c8d); }

/* Placeholder styles */
.loading-placeholder td,
.no-data-placeholder td {
    text-align: center;
    color: var(--text-muted, #7f8c8d);
    padding: 20px;
    font-style: italic;
}
.loading-placeholder i { margin-left: 8px; }

/* Actions Column Styling */
#transactions-table th:last-child,
#transactions-table td:last-child {
    text-align: center; /* Center align actions */
    width: 80px; /* Fixed width for actions */
}

.delete-btn {
    background-color: transparent;
    border: none;
    color: var(--danger-color, #e74c3c);
    cursor: pointer;
    font-size: 1.1rem;
    padding: 5px;
    transition: color 0.2s;
}

.delete-btn:hover {
    color: #c0392b; /* Darker red on hover */
}

.delete-btn i {
    pointer-events: none; /* Ensure icon doesn't interfere with click */
}

/* Footer button */
.main-footer {
    display: flex;
    justify-content: space-between; /* Align items */
