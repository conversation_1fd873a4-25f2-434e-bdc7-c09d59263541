// --- Auth Check ---
checkAuth('../../login.html'); // Adjusted path

// --- Supabase Initialization ---
let _supabase;
try {
    // Check if the global supabase object and its createClient method exist
    if (typeof window.supabase !== 'undefined' && typeof window.supabase.createClient === 'function') {
        _supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('Supabase client initialized for group details.');
    } else {
        console.error('Supabase client not found.');
        alert('خطأ في تهيئة الاتصال بقاعدة البيانات.');
    }
} catch (error) {
    console.error('Error initializing Supabase client:', error);
    alert('خطأ في تهيئة الاتصال بقاعدة البيانات.');
}

// --- DOM Elements ---
const groupNameDisplay = document.getElementById('group-name-display');
const monthYearDisplay = document.getElementById('month-year-display');
const pageMessage = document.getElementById('page-message');
const studentCardsContainer = document.getElementById('student-cards-container');
const backToGroupsBtn = document.getElementById('back-to-groups-btn');
const paymentModalElement = document.createElement('div'); // Create ADD modal element once
const editPaymentModalElement = document.getElementById('edit-payment-modal'); // Get EDIT modal element

// --- State ---
let groupId = null;
let groupName = null;
let selectedMonthId = null;
let selectedMonthNumber = null;
let selectedYearNumber = null;
let studentDataCache = [];
let banksList = [];
let currentModalStudentId = null;
let currentModalDefaultAmount = 0;
let currentEditPaymentId = null;
let selectedStudents = new Set(); // For bulk selection
let paymentHistoryModal = null;
let balanceInfoModal = null;
let selectedPayments = new Set(); // For payment history bulk selection
let currentPaymentHistoryStudentId = null;
let editPaymentOriginalData = null;

// --- Helper Functions ---
const showMessage = (element, message, type = 'info', duration = 3000) => {
    if (!element) return;
    element.textContent = message;
    element.className = `message ${type}`;
    element.style.display = 'block';
    if (duration > 0) {
        setTimeout(() => {
            // Only hide if the message hasn't changed
            if (element.textContent === message) {
                element.style.display = 'none';
            }
        }, duration);
    }
};

const formatCurrency = (amount) => {
    // This function already handles negative numbers correctly with toFixed(2)
    const value = parseFloat(amount);
    return isNaN(value) ? '0.00' : value.toFixed(2);
};

const getMonthName = (monthNumber) => {
    const names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"];
    const index = parseInt(monthNumber, 10) - 1;
    return names[index] || `شهر ${monthNumber}`;
};

// --- Bulk Selection Functions ---
const updateBulkActionsVisibility = () => {
    const bulkContainer = document.getElementById('bulk-actions-container');
    const selectedCountSpan = document.getElementById('selected-count');

    if (selectedStudents.size > 0) {
        bulkContainer.classList.add('show');
        selectedCountSpan.textContent = selectedStudents.size;
    } else {
        bulkContainer.classList.remove('show');
    }
};

const handleStudentSelection = (studentId, isSelected) => {
    if (isSelected) {
        selectedStudents.add(studentId);
    } else {
        selectedStudents.delete(studentId);
    }

    // Update card visual state
    const card = document.querySelector(`.student-card[data-student-id="${studentId}"]`);
    if (card) {
        if (isSelected) {
            card.classList.add('selected');
        } else {
            card.classList.remove('selected');
        }
    }

    updateBulkActionsVisibility();
};

const handleBulkDelete = async () => {
    if (selectedStudents.size === 0) return;

    const studentNames = Array.from(selectedStudents).map(id => {
        const student = studentDataCache.find(s => s.id === id);
        return student ? student.name : 'طالب غير معروف';
    }).join('، ');

    const confirmation = confirm(`هل أنت متأكد من رغبتك في حذف الطلاب التالية من المجموعة؟\n\n${studentNames}\n\nلا يمكن التراجع عن هذه العملية.`);
    if (!confirmation) return;

    const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
    if (bulkDeleteBtn) {
        bulkDeleteBtn.disabled = true;
        bulkDeleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحذف...';
    }

    try {
        const studentIdsArray = Array.from(selectedStudents);

        // Check for locked students
        const lockedStudents = studentIdsArray.filter(id => {
            const student = studentDataCache.find(s => s.id === id);
            return student && student.isLocked;
        });

        if (lockedStudents.length > 0) {
            const lockedNames = lockedStudents.map(id => {
                const student = studentDataCache.find(s => s.id === id);
                return student ? student.name : 'طالب غير معروف';
            }).join('، ');

            showMessage(pageMessage, `لا يمكن حذف الطلاب التالية لأن حساباتهم مقفلة: ${lockedNames}`, 'warning', 7000);
            return;
        }

        // Delete from database
        const { error } = await _supabase
            .from('student_group_members')
            .delete()
            .in('student_id', studentIdsArray)
            .eq('group_id', groupId);

        if (error) throw error;

        // Remove cards from UI
        studentIdsArray.forEach(studentId => {
            const cardElement = document.querySelector(`.student-card[data-student-id="${studentId}"]`);
            if (cardElement) cardElement.remove();
        });

        // Update cache
        studentDataCache = studentDataCache.filter(s => !selectedStudents.has(s.id));

        // Clear selection
        selectedStudents.clear();
        updateBulkActionsVisibility();

        showMessage(pageMessage, `تم حذف ${studentIdsArray.length} طالب من المجموعة بنجاح.`, 'success');

    } catch (error) {
        console.error('Error in bulk delete:', error);
        showMessage(pageMessage, `فشل في الحذف الجماعي: ${error.message}`, 'error');
    } finally {
        if (bulkDeleteBtn) {
            bulkDeleteBtn.disabled = false;
            bulkDeleteBtn.innerHTML = '<i class="fas fa-trash"></i> حذف المحدد';
        }
    }
};

// --- Invoice Generation ---
const generatePaymentInvoiceHTML = (invoiceData) => {
    if (!invoiceData || !invoiceData.studentName || !invoiceData.payments) {
        console.error("Invoice generation failed: Missing essential data.");
        return null;
    }

    const orgName = "اسم المؤسسة التعليمية";
    const orgAddress = "العنوان، المدينة، الدولة";
    const orgLogoUrl = "../../logo.png";
    const invoiceTitle = "إيصال استلام دفعات اشتراك";
    const invoiceNumber = `RCPT-${invoiceData.studentId.substring(0, 6).toUpperCase()}-${invoiceData.yearNumber}${String(invoiceData.monthNumber).padStart(2, '0')}`;
    const issueDate = new Date().toLocaleDateString('ar-SA');
    const formattedTotalPaid = formatCurrency(invoiceData.totalPaid);
    const monthYearText = `${getMonthName(invoiceData.monthNumber)} ${invoiceData.yearNumber}`;

    let paymentRowsHTML = '';
    if (invoiceData.payments.length > 0) {
        invoiceData.payments.forEach((payment, index) => {
            paymentRowsHTML += `
                <tr>
                    <td>دفعة ${index + 1}</td>
                    <td>${new Date(payment.payment_date).toLocaleDateString('ar-SA')}</td>
                    <td>${formatCurrency(payment.amount)} ريال</td>
                </tr>
            `;
        });
    } else {
        paymentRowsHTML = '<tr><td colspan="3">لا توجد دفعات مسجلة لهذا الشهر.</td></tr>';
    }

    return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>${invoiceTitle} - ${invoiceData.studentName}</title>
    <style>
        body { font-family: 'Tajawal', sans-serif; direction: rtl; margin: 0; padding: 20px; background-color: #f4f7f6; }
        .invoice-container { background-color: #fff; max-width: 800px; margin: 20px auto; padding: 30px; border: 1px solid #dee2e6; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .invoice-header { display: flex; justify-content: space-between; align-items: flex-start; border-bottom: 2px solid #3498db; padding-bottom: 15px; margin-bottom: 20px; }
        .invoice-header .org-details h1 { margin: 0 0 5px 0; color: #3498db; font-size: 1.8em; }
        .invoice-header .org-details p { margin: 2px 0; font-size: 0.9em; color: #555; }
        .invoice-header .org-logo img { max-height: 70px; max-width: 150px; }
        .invoice-title { text-align: center; margin-bottom: 25px; }
        .invoice-title h2 { margin: 0; font-size: 1.6em; color: #333; }
        .invoice-meta { display: flex; justify-content: space-between; margin-bottom: 20px; font-size: 0.9em; color: #555; }
        .invoice-meta div { flex: 1; padding: 0 5px; }
        .invoice-meta strong { color: #333; }
        .student-details { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; border: 1px solid #eee; }
        .student-details h3 { margin-top: 0; margin-bottom: 10px; font-size: 1.2em; color: #3498db; }
        .student-details p { margin: 5px 0; font-size: 1em; }
        .payment-details table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .payment-details th, .payment-details td { border: 1px solid #dee2e6; padding: 10px 12px; text-align: right; }
        .payment-details th { background-color: #f2f2f2; font-weight: 600; }
        .payment-details .total-row td { font-weight: bold; font-size: 1.1em; background-color: #eaf5fd; }
        .invoice-footer { text-align: center; margin-top: 30px; font-size: 0.85em; color: #777; border-top: 1px solid #eee; padding-top: 15px; }
        @media print {
            body { background-color: #fff; padding: 0; margin: 0; -webkit-print-color-adjust: exact; print-color-adjust: exact; }
            .invoice-container { margin: 0; padding: 20px; border: none; box-shadow: none; max-width: 100%; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <header class="invoice-header">
            <div class="org-details">
                <h1>${orgName}</h1>
                <p>${orgAddress}</p>
            </div>
            ${orgLogoUrl ? `<div class="org-logo"><img src="${orgLogoUrl}" alt="Logo"></div>` : ''}
        </header>
        <section class="invoice-title"><h2>${invoiceTitle}</h2></section>
        <section class="invoice-meta">
            <div><strong>رقم الإيصال:</strong> ${invoiceNumber}</div>
            <div><strong>تاريخ الإصدار:</strong> ${issueDate}</div>
            <div><strong>الشهر المالي:</strong> ${monthYearText}</div>
        </section>
        <section class="student-details">
            <h3>بيانات الطالب</h3>
            <p><strong>اسم الطالب:</strong> ${invoiceData.studentName}</p>
            <p><strong>المجموعة:</strong> ${invoiceData.groupName || 'غير محددة'}</p>
            ${invoiceData.defaultAmount ? `<p><strong>المبلغ المطلوب للشهر:</strong> ${formatCurrency(invoiceData.defaultAmount)} ريال</p>` : ''}
        </section>
        <section class="payment-details">
            <h3>تفاصيل الدفعات المستلمة للشهر</h3>
            <table>
                <thead><tr><th>البيان</th><th>تاريخ الدفعة</th><th>المبلغ</th></tr></thead>
                <tbody>
                    ${paymentRowsHTML}
                    <tr class="total-row"><td colspan="2">إجمالي المبلغ المستلم</td><td>${formattedTotalPaid} ريال</td></tr>
                </tbody>
            </table>
        </section>
        <footer class="invoice-footer">
            هذا إيصال تم إنشاؤه بواسطة النظام. للاستفسارات، يرجى التواصل مع الإدارة.
            <br>
            <button class="no-print" onclick="window.print()" style="margin-top: 10px; padding: 8px 15px; cursor: pointer;">طباعة</button>
        </footer>
    </div>
</body>
</html>`;
};

// --- Payment History Functions ---
const openPaymentHistoryModal = async (studentId, studentName) => {
    if (!paymentHistoryModal) {
        paymentHistoryModal = document.getElementById('payment-history-modal');
    }

    currentPaymentHistoryStudentId = studentId;
    selectedPayments.clear();
    updatePaymentHistoryBulkActions();

    const studentNameSpan = document.getElementById('payment-history-student-name');
    const tbody = document.getElementById('payment-history-tbody');
    const messageEl = document.getElementById('payment-history-message');

    if (studentNameSpan) studentNameSpan.textContent = studentName;
    if (tbody) tbody.innerHTML = '<tr><td colspan="6"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</td></tr>';
    if (messageEl) messageEl.style.display = 'none';

    paymentHistoryModal.classList.add('show');

    try {
        // Fetch payments for this student in this month
        const { data: payments, error } = await _supabase
            .from('student_payments')
            .select(`
                id,
                amount,
                payment_date,
                payment_status,
                banks (name)
            `)
            .eq('student_id', studentId)
            .eq('budget_month_id', selectedMonthId)
            .order('payment_date', { ascending: false });

        if (error) throw error;

        if (!payments || payments.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6">لا توجد دفعات مسجلة لهذا الطالب في هذا الشهر</td></tr>';
            return;
        }

        // Render payments table with checkboxes
        tbody.innerHTML = payments.map(payment => `
            <tr>
                <td>
                    <input type="checkbox" class="payment-checkbox"
                           data-payment-id="${payment.id}"
                           onchange="handlePaymentSelection('${payment.id}', this.checked)">
                </td>
                <td>${new Date(payment.payment_date).toLocaleDateString('ar-SA')}</td>
                <td>${formatCurrency(payment.amount)} ريال</td>
                <td>${getStatusText(payment.payment_status)}</td>
                <td>${payment.banks?.name || 'غير محدد'}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn edit-btn" onclick="editPaymentFromHistory('${payment.id}', '${studentId}', '${studentName}')">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn delete-btn" onclick="deletePaymentFromHistory('${payment.id}', '${studentId}')">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

    } catch (error) {
        console.error('Error fetching payment history:', error);
        if (messageEl) {
            showMessage(messageEl, `خطأ في تحميل سجل الدفعات: ${error.message}`, 'error', 0);
        }
        if (tbody) {
            tbody.innerHTML = '<tr><td colspan="6">فشل في تحميل البيانات</td></tr>';
        }
    }
};

const getStatusText = (status) => {
    switch (status) {
        case 'paid': return 'مدفوع';
        case 'partial': return 'جزئي';
        case 'not_paid': return 'غير مدفوع';
        default: return status || 'غير محدد';
    }
};

const editPaymentFromHistory = (paymentId, studentId, studentName) => {
    // Close payment history modal first
    if (paymentHistoryModal) {
        paymentHistoryModal.classList.remove('show');
    }

    // Find student data for default amount
    const studentData = studentDataCache.find(s => s.id === studentId);
    const defaultAmount = studentData ? studentData.defaultAmount : 0;

    // Open edit payment modal
    openEditPaymentModal(studentId, studentName, paymentId, defaultAmount);
};

const deletePaymentFromHistory = async (paymentId, studentId) => {
    const confirmation = confirm('هل أنت متأكد من رغبتك في حذف هذه الدفعة؟\n\nلا يمكن التراجع عن هذه العملية.');
    if (!confirmation) return;

    try {
        const { error } = await _supabase
            .from('student_payments')
            .delete()
            .eq('id', paymentId);

        if (error) throw error;

        // Refresh payment history
        const studentData = studentDataCache.find(s => s.id === studentId);
        if (studentData) {
            await openPaymentHistoryModal(studentId, studentData.name);
        }

        // Update student card
        await updateStudentCardUI(studentId);

        showMessage(pageMessage, 'تم حذف الدفعة بنجاح.', 'success');

    } catch (error) {
        console.error('Error deleting payment:', error);
        showMessage(pageMessage, `فشل في حذف الدفعة: ${error.message}`, 'error');
    }
};

const closePaymentHistoryModal = () => {
    if (paymentHistoryModal) {
        paymentHistoryModal.classList.remove('show');
    }
    selectedPayments.clear();
    updatePaymentHistoryBulkActions();
};

// --- Payment History Bulk Selection Functions ---
const handlePaymentSelection = (paymentId, isSelected) => {
    if (isSelected) {
        selectedPayments.add(paymentId);
    } else {
        selectedPayments.delete(paymentId);
    }
    updatePaymentHistoryBulkActions();
};

const toggleAllPaymentSelection = () => {
    const selectAllCheckbox = document.getElementById('select-all-payments');
    const paymentCheckboxes = document.querySelectorAll('.payment-checkbox');

    paymentCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
        handlePaymentSelection(checkbox.dataset.paymentId, checkbox.checked);
    });
};

const updatePaymentHistoryBulkActions = () => {
    const bulkContainer = document.getElementById('payment-history-bulk-actions');
    const selectedCountSpan = document.getElementById('payment-history-selected-count');

    if (selectedPayments.size > 0) {
        bulkContainer.style.display = 'block';
        selectedCountSpan.textContent = `${selectedPayments.size} مدفوعات محددة`;
    } else {
        bulkContainer.style.display = 'none';
    }
};

const deleteSelectedPayments = async () => {
    if (selectedPayments.size === 0) return;

    const confirmation = confirm(`هل أنت متأكد من رغبتك في حذف ${selectedPayments.size} دفعة محددة؟\n\nلا يمكن التراجع عن هذه العملية.`);
    if (!confirmation) return;

    try {
        const paymentIds = Array.from(selectedPayments);

        const { error } = await _supabase
            .from('student_payments')
            .delete()
            .in('id', paymentIds);

        if (error) throw error;

        // Refresh payment history
        if (currentPaymentHistoryStudentId) {
            const studentData = studentDataCache.find(s => s.id === currentPaymentHistoryStudentId);
            if (studentData) {
                await openPaymentHistoryModal(currentPaymentHistoryStudentId, studentData.name);
            }
        }

        // Update student card
        await updateStudentCardUI(currentPaymentHistoryStudentId);

        showMessage(pageMessage, `تم حذف ${paymentIds.length} دفعة بنجاح.`, 'success');

    } catch (error) {
        console.error('Error deleting selected payments:', error);
        showMessage(pageMessage, `فشل في حذف الدفعات المحددة: ${error.message}`, 'error');
    }
};

// --- Edit Payment Calculation Functions ---
const toggleEditCalculationBox = () => {
    const calculationBox = document.getElementById('edit-payment-calculation-box');
    const toggleIcon = document.getElementById('edit-calculation-toggle-icon');

    if (calculationBox.classList.contains('collapsed')) {
        calculationBox.classList.remove('collapsed');
        toggleIcon.classList.remove('fa-chevron-down');
        toggleIcon.classList.add('fa-chevron-up');
    } else {
        calculationBox.classList.add('collapsed');
        toggleIcon.classList.remove('fa-chevron-up');
        toggleIcon.classList.add('fa-chevron-down');
    }
};

const updateEditPaymentCalculations = async () => {
    if (!editPaymentOriginalData || !currentEditingStudentId) return;

    const amountInput = document.getElementById('edit-payment-amount');
    const enteredAmount = parseFloat(amountInput.value) || 0;

    try {
        // Get student data
        const studentData = studentDataCache.find(s => s.id === currentEditingStudentId);
        if (!studentData) return;

        const monthCost = parseFloat(studentData.defaultAmount) || 0;
        const currentBalance = parseFloat(studentData.currentBalance) || 0;

        // Fetch all payments excluding this payment to show details
        const { data: payments, error } = await _supabase
            .from('student_payments')
            .select('id, amount, payment_date, payment_status')
            .eq('student_id', currentEditingStudentId)
            .eq('budget_month_id', selectedMonthId)
            .neq('id', currentEditPaymentId)
            .order('payment_date', { ascending: true });

        if (error) throw error;

        // Calculate total paid from other payments
        const totalPaidBefore = payments.reduce((sum, p) => sum + parseFloat(p.amount || 0), 0);

        // Calculate required amount and remaining
        const requiredAmount = Math.max(0, monthCost + currentBalance - totalPaidBefore);
        const remainingAfter = monthCost + currentBalance - totalPaidBefore - enteredAmount;

        // Update basic calculation display with proper colors
        const monthCostEl = document.getElementById('edit-calc-month-cost');
        const currentBalanceEl = document.getElementById('edit-calc-current-balance');
        const requiredAmountEl = document.getElementById('edit-calc-required-amount');
        const enteredAmountEl = document.getElementById('edit-calc-entered-amount');
        const remainingAfterEl = document.getElementById('edit-calc-remaining-after');

        if (monthCostEl) {
            monthCostEl.textContent = `${formatCurrency(monthCost)} ريال`;
            monthCostEl.className = 'amount month-cost';
        }

        if (currentBalanceEl) {
            currentBalanceEl.textContent = `${formatCurrency(currentBalance)} ريال`;
            if (currentBalance > 0) {
                currentBalanceEl.className = 'amount balance-positive';
            } else if (currentBalance < 0) {
                currentBalanceEl.className = 'amount balance-negative';
            } else {
                currentBalanceEl.className = 'amount balance-zero';
            }
        }

        if (requiredAmountEl) {
            requiredAmountEl.textContent = `${formatCurrency(requiredAmount)} ريال`;
            requiredAmountEl.className = 'amount required';
        }

        if (enteredAmountEl) {
            enteredAmountEl.textContent = `${formatCurrency(enteredAmount)} ريال`;
            enteredAmountEl.className = 'amount entered';
        }

        if (remainingAfterEl) {
            remainingAfterEl.textContent = `${formatCurrency(remainingAfter)} ريال`;
            remainingAfterEl.className = 'amount remaining';
        }

        // Update payments section with detailed breakdown
        const paymentsSection = document.getElementById('edit-calc-payments-section');
        if (paymentsSection) {
            if (payments.length === 0) {
                paymentsSection.innerHTML = `
                    <div class="calculation-row">
                        <span>لا توجد دفعات أخرى مسجلة:</span>
                        <span class="amount payments">0.00 ريال</span>
                    </div>
                `;
            } else {
                let paymentsHTML = '';
                let runningTotal = 0;

                payments.forEach((payment, index) => {
                    const amount = parseFloat(payment.amount || 0);
                    runningTotal += amount;
                    const paymentDate = new Date(payment.payment_date).toLocaleDateString('ar-SA');

                    paymentsHTML += `
                        <div class="calculation-row">
                            <span>دفعة ${index + 1} (${paymentDate}):</span>
                            <span class="amount payments">${formatCurrency(amount)} ريال</span>
                        </div>
                    `;
                });

                // Add total row
                paymentsHTML += `
                    <div class="calculation-row" style="border-top: 1px solid #dee2e6; margin-top: 5px; padding-top: 8px;">
                        <span><strong>إجمالي الدفعات الأخرى:</strong></span>
                        <span class="amount payments"><strong>${formatCurrency(runningTotal)} ريال</strong></span>
                    </div>
                `;

                paymentsSection.innerHTML = paymentsHTML;
            }
        }

    } catch (error) {
        console.error('Error updating edit payment calculations:', error);
    }
};

// --- Balance Info Functions ---
const openBalanceInfoModal = async (studentId, studentName) => {
    if (!balanceInfoModal) {
        balanceInfoModal = document.getElementById('balance-info-modal');
    }

    const studentNameSpan = document.getElementById('balance-info-student-name');
    const tbody = document.getElementById('balance-breakdown-tbody');
    const messageEl = document.getElementById('balance-info-message');

    if (studentNameSpan) studentNameSpan.textContent = studentName;
    if (tbody) tbody.innerHTML = '<tr><td colspan="3"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</td></tr>';
    if (messageEl) messageEl.style.display = 'none';

    balanceInfoModal.classList.add('show');

    try {
        // Get student's current data
        const studentData = studentDataCache.find(s => s.id === studentId);
        if (!studentData) throw new Error('بيانات الطالب غير متوفرة');

        // Fetch opening balance and current balance
        const { data: defaultsData, error: defaultsError } = await _supabase
            .from('student_subscription_defaults')
            .select('opening_balance, current_balance')
            .eq('student_id', studentId)
            .single();

        if (defaultsError) throw defaultsError;

        // Fetch locked amounts from previous months
        const { data: lockedAmounts, error: lockedError } = await _supabase
            .from('subscription_locks')
            .select(`
                locked_remaining_amount,
                budget_month_id,
                budget_months (
                    month_number,
                    month_name,
                    budget_years (year_number)
                )
            `)
            .eq('student_id', studentId);

        if (lockedError) throw lockedError;

        // Build breakdown table
        let breakdown = [];
        let runningTotal = 0;

        // Opening balance from student_subscription_defaults
        const openingBalance = parseFloat(defaultsData.opening_balance || 0);
        if (openingBalance !== 0) {
            breakdown.push({
                description: 'الرصيد الافتتاحي',
                amount: openingBalance,
                notes: 'رصيد مُدخل يدوياً عند إنشاء الحساب'
            });
            runningTotal += openingBalance;
        }

        // Locked amounts from previous months
        if (lockedAmounts && lockedAmounts.length > 0) {
            lockedAmounts.forEach(lock => {
                const amount = parseFloat(lock.locked_remaining_amount || 0);
                if (amount !== 0 && lock.budget_months && lock.budget_months.budget_years) {
                    const monthName = getMonthName(lock.budget_months.month_number);
                    const year = lock.budget_months.budget_years.year_number;
                    breakdown.push({
                        description: `متبقي شهر ${monthName} ${year}`,
                        amount: amount,
                        notes: amount > 0 ? 'مبلغ مستحق من شهر سابق' : 'دفعة زائدة من شهر سابق'
                    });
                    runningTotal += amount;
                }
            });
        }

        // Add current month balance if different from running total
        const currentBalance = parseFloat(defaultsData.current_balance || 0);
        const currentMonthName = selectedMonthNumber && selectedYearNumber ?
            `${getMonthName(selectedMonthNumber)} ${selectedYearNumber}` : 'الشهر الحالي';

        // Show current month balance only if it's different from the calculated running total
        // This happens when there are transactions in the current month
        if (Math.abs(currentBalance - runningTotal) > 0.01) {
            const currentMonthAmount = currentBalance - runningTotal;
            breakdown.push({
                description: `متبقي ${currentMonthName}`,
                amount: currentMonthAmount,
                notes: currentMonthAmount > 0 ? 'مبلغ مستحق من شهر حالي' : 'دفعة زائدة من شهر حالي'
            });
            runningTotal = currentBalance;
        }

        // Current balance total
        breakdown.push({
            description: 'إجمالي الرصيد الحالي',
            amount: runningTotal,
            notes: 'المجموع النهائي للرصيد',
            isTotal: true
        });

        // Render breakdown table
        if (breakdown.length === 0) {
            tbody.innerHTML = '<tr><td colspan="3">لا توجد تفاصيل رصيد لهذا الطالب</td></tr>';
        } else {
            tbody.innerHTML = breakdown.map(item => {
                const amountClass = item.amount > 0 ? 'positive-amount' :
                                  item.amount < 0 ? 'negative-amount' : 'neutral-amount';
                const rowClass = item.isTotal ? 'total-row' : '';

                return `
                    <tr class="${rowClass}">
                        <td>${item.description}</td>
                        <td class="${amountClass}">${formatCurrency(item.amount)} ريال</td>
                        <td>${item.notes}</td>
                    </tr>
                `;
            }).join('');
        }

    } catch (error) {
        console.error('Error fetching balance info:', error);
        if (messageEl) {
            showMessage(messageEl, `خطأ في تحميل تفاصيل الرصيد: ${error.message}`, 'error', 0);
        }
        if (tbody) {
            tbody.innerHTML = '<tr><td colspan="3">فشل في تحميل البيانات</td></tr>';
        }
    }
};

const closeBalanceInfoModal = () => {
    if (balanceInfoModal) {
        balanceInfoModal.classList.remove('show');
    }
};

// --- Bank Transaction Logic ---
// تم حذف دالة createBankTransactionForStudentPayment لأنها تم استبدالها بالتريجرات في قاعدة البيانات

// --- Add Payment Modal Logic ---
const setupPaymentModal = () => {
    paymentModalElement.id = 'payment-modal';
    paymentModalElement.className = 'modal';
    paymentModalElement.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-student-name">تسجيل دفعة لـ: ...</h2>
                <button type="button" class="close-modal-btn" id="close-payment-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div id="modal-payment-message" class="message" style="display: none;"></div>

                <!-- Payment Calculation Box -->
                <div id="payment-calculation-box" class="payment-calculation-box collapsed">
                    <div class="calculation-header" onclick="toggleCalculationBox()">
                        <span>حساب المبلغ المطلوب</span>
                        <i class="fas fa-chevron-down" id="calculation-toggle-icon"></i>
                    </div>
                    <div class="calculation-details" id="calculation-details">
                        <div class="calculation-row">
                            <span>تكلفة الشهر:</span>
                            <span class="amount" id="calc-month-cost">0.00 ريال</span>
                        </div>
                        <div class="calculation-row">
                            <span>الرصيد الحالي:</span>
                            <span class="amount" id="calc-current-balance">0.00 ريال</span>
                        </div>
                        <div class="calculation-row">
                            <span>المدفوع سابقاً هذا الشهر:</span>
                            <span class="amount" id="calc-paid-before">0.00 ريال</span>
                        </div>
                        <div class="calculation-row total">
                            <span>المطلوب دفعه:</span>
                            <span class="amount" id="calc-required-amount">0.00 ريال</span>
                        </div>
                        <div class="calculation-row">
                            <span>المبلغ المدخل:</span>
                            <span class="amount" id="calc-entered-amount">0.00 ريال</span>
                        </div>
                        <div class="calculation-row total">
                            <span>المتبقي بعد الدفع:</span>
                            <span class="amount" id="calc-remaining-after">0.00 ريال</span>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="modal-month-year">الشهر المالي:</label>
                        <input type="text" id="modal-month-year" name="month_year" readonly disabled style="background-color: #e9ecef; cursor: default;">
                    </div>
                    <div class="form-group">
                        <label for="modal-status">حالة الدفع:</label>
                        <select id="modal-status" name="status" required>
                            <option value="">اختر الحالة...</option>
                            <option value="paid">مدفوع</option>
                            <option value="partial">جزئي</option>
                            <option value="not_paid">غير مدفوع</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="modal-amount">المبلغ:</label>
                        <input type="number" id="modal-amount" name="amount" step="0.01" min="0" placeholder="0.00" required>
                    </div>
                    <div class="form-group">
                        <label for="modal-date">التاريخ:</label>
                        <input type="date" id="modal-date" name="date" required value="${new Date().toISOString().slice(0, 10)}">
                    </div>
                </div>
                 <div class="form-row">
                    <div class="form-group form-group-full">
                        <label for="modal-bank">البنك:</label>
                        <select id="modal-bank" name="bank_id" required>
                            <!-- Bank options will be populated here -->
                        </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" id="confirm-payment-btn" class="btn btn-primary">
                    <i class="fas fa-check"></i> تأكيد الدفع
                </button>
            </div>
        </div>
    `;
    document.body.appendChild(paymentModalElement);

    // Add event listeners for modal elements
    const closeModalBtn = document.getElementById('close-payment-modal');
    const confirmPaymentBtn = document.getElementById('confirm-payment-btn');
    const modalStatusSelect = document.getElementById('modal-status');
    const modalAmountInput = document.getElementById('modal-amount');

    if (closeModalBtn) closeModalBtn.addEventListener('click', closePaymentModal);
    if (confirmPaymentBtn) confirmPaymentBtn.addEventListener('click', handleConfirmPayment);
    if (modalStatusSelect) modalStatusSelect.addEventListener('change', toggleModalPaymentFields);
    if (modalAmountInput) {
        modalAmountInput.addEventListener('input', updateModalStatusBasedOnAmount);
        modalAmountInput.addEventListener('input', updatePaymentCalculations);
    }

    // Close modal on outside click
    paymentModalElement.addEventListener('click', (event) => {
        if (event.target === paymentModalElement) {
            closePaymentModal();
        }
    });
};

// --- Payment Calculation Functions ---
const toggleCalculationBox = () => {
    const box = document.getElementById('payment-calculation-box');
    const icon = document.getElementById('calculation-toggle-icon');

    if (box.classList.contains('collapsed')) {
        box.classList.remove('collapsed');
        box.classList.add('expanded');
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
    } else {
        box.classList.remove('expanded');
        box.classList.add('collapsed');
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
    }
};

const updatePaymentCalculations = () => {
    const studentCacheData = studentDataCache.find(s => s.id === currentModalStudentId);
    if (!studentCacheData) return;

    const monthCost = currentModalDefaultAmount;
    const currentBalance = studentCacheData.overallStudentBalance;
    const paidBefore = studentCacheData.paidAmountThisMonth;
    const enteredAmount = parseFloat(document.getElementById('modal-amount')?.value || 0);

    // Calculate required amount (cost + balance - paid before)
    const requiredAmount = Math.max(0, (monthCost + currentBalance) - paidBefore);

    // Calculate remaining after this payment
    const remainingAfter = requiredAmount - enteredAmount;

    // Update calculation display with proper colors
    const updateCalcElement = (id, amount, colorClass) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = `${formatCurrency(amount)} ريال`;
            element.className = `amount ${colorClass}`;
        }
    };

    updateCalcElement('calc-month-cost', monthCost, 'month-cost');

    // Balance color logic: positive = red, negative = green, zero = gray
    let balanceColorClass = 'balance-zero';
    if (currentBalance > 0) balanceColorClass = 'balance-positive';
    else if (currentBalance < 0) balanceColorClass = 'balance-negative';
    updateCalcElement('calc-current-balance', currentBalance, balanceColorClass);

    updateCalcElement('calc-paid-before', paidBefore, 'payments');
    updateCalcElement('calc-required-amount', requiredAmount, 'required');
    updateCalcElement('calc-entered-amount', enteredAmount, 'entered');
    updateCalcElement('calc-remaining-after', remainingAfter, 'remaining');
};

const updateModalStatusBasedOnAmount = () => {
    const amountInput = document.getElementById('modal-amount');
    const statusSelect = document.getElementById('modal-status');
    if (!amountInput || !statusSelect) return;

    const enteredAmount = parseFloat(amountInput.value) || 0;
    // Find the student's data in the cache to get the current paid amount
    const studentCacheData = studentDataCache.find(s => s.id === currentModalStudentId);
    const paidSoFar = studentCacheData ? studentCacheData.paidAmountThisMonth : 0;
    const subscriptionAmount = currentModalDefaultAmount; // Month's base cost
    const overallBalance = studentCacheData ? studentCacheData.overallStudentBalance : 0; // Get overall balance

    // Calculate the projected total paid for the month if this payment is added
    const projectedTotalPaidThisMonth = paidSoFar + enteredAmount;

    // Calculate the projected remaining amount for the month, considering the overall balance
    // Remaining = (Cost + Balance) - ProjectedPaid
    const projectedRemainingForMonth = (subscriptionAmount + overallBalance) - projectedTotalPaidThisMonth;

    // Determine status based on projected remaining amount for the month
    if (projectedRemainingForMonth <= 0) {
        statusSelect.value = 'paid'; // Covered by payments and/or balance
    } else if (projectedTotalPaidThisMonth > 0) {
        statusSelect.value = 'partial'; // Some payment made, but still owes for the month
    } else {
        statusSelect.value = 'not_paid'; // No payment made and still owes
    }

    toggleModalPaymentFields();
};


const toggleModalPaymentFields = () => {
    const statusSelect = document.getElementById('modal-status');
    const amountInput = document.getElementById('modal-amount');
    const dateInput = document.getElementById('modal-date');
    const bankSelect = document.getElementById('modal-bank');
    const confirmBtn = document.getElementById('confirm-payment-btn');

    const status = statusSelect ? statusSelect.value : '';
    const disable = status === 'not_paid';

    if (amountInput) amountInput.disabled = disable;
    if (dateInput) dateInput.disabled = disable;
    if (bankSelect) bankSelect.disabled = disable;
    if (confirmBtn) confirmBtn.disabled = disable;

    // Clear amount if disabled
    if (disable && amountInput) {
        amountInput.value = '0.00';
    }
};

const openPaymentModal = (studentId, studentName, defaultAmount) => {
    const studentCacheData = studentDataCache.find(s => s.id === studentId);
    if (!studentCacheData) {
        showMessage(pageMessage, `خطأ: لم يتم العثور على بيانات الطالب ${studentName}.`, 'error', 5000);
        return;
    }
    if (studentCacheData.isLocked) {
        showMessage(pageMessage, `حساب الطالب ${studentName} مقفل لهذا الشهر ولا يمكن تسجيل دفعات جديدة.`, 'warning', 5000);
        return;
    }

    currentModalStudentId = studentId;
    currentModalDefaultAmount = parseFloat(defaultAmount) || 0; // Store the month's base cost

    const modalTitle = document.getElementById('modal-student-name');
    const modalBankSelect = document.getElementById('modal-bank');
    const modalMessageEl = document.getElementById('modal-payment-message');
    const amountInput = document.getElementById('modal-amount');
    const modalMonthYearInput = document.getElementById('modal-month-year');
    const statusSelect = document.getElementById('modal-status');
    const dateInput = document.getElementById('modal-date');

    if (modalTitle) modalTitle.textContent = `تسجيل دفعة لـ: ${studentName}`;
    if (modalMonthYearInput) {
        modalMonthYearInput.value = (selectedMonthNumber && selectedYearNumber)
            ? `${getMonthName(selectedMonthNumber)} ${selectedYearNumber}`
            : 'غير محدد';
    }

    // Populate bank options
    if (modalBankSelect) {
        let bankOptionsHtml = '<option value="">اختر البنك...</option>';
        banksList.forEach(bank => {
            bankOptionsHtml += `<option value="${bank.id}">${bank.name}</option>`;
        });
        modalBankSelect.innerHTML = bankOptionsHtml;
    }

    // Reset fields and set initial values
    if (statusSelect) {
        // Set initial status based on the overall month status from cache
        switch (studentCacheData.overallMonthStatus) {
            case 'مدفوع': statusSelect.value = 'paid'; break;
            case 'جزئي': statusSelect.value = 'partial'; break;
            case 'غير مدفوع': statusSelect.value = 'not_paid'; break;
            default: statusSelect.value = ''; break;
        }
    }
    if (amountInput) {
        // Pre-fill amount with the remaining cost for the month, unless already paid/covered or cost is 0
        // Remaining cost = Cost - (Paid + Balance) -> but we only want to prefill the payment amount
        // Let's prefill with the positive part of the remaining amount for the month
        const remainingForMonth = studentCacheData.remainingForMonth;
        if (studentCacheData.overallMonthStatus === 'مدفوع' || currentModalDefaultAmount <= 0) {
            amountInput.value = '0.00'; // Already covered or no cost, default new payment to 0
        } else {
            // Prefill with the amount still needed for the month, capped at the month's cost
            const amountNeeded = Math.max(0, remainingForMonth);
            amountInput.value = amountNeeded > 0 ? amountNeeded.toFixed(2) : '0.00';
        }
    }
    if (dateInput) dateInput.value = new Date().toISOString().slice(0, 10);
    if (modalBankSelect) modalBankSelect.value = '';
    if (modalMessageEl) modalMessageEl.style.display = 'none';

    updateModalStatusBasedOnAmount(); // Set initial status based on pre-filled amount and paidSoFar
    updatePaymentCalculations(); // Update calculation box
    paymentModalElement.classList.add('show');
};


const closePaymentModal = () => {
    paymentModalElement.classList.remove('show');
    currentModalStudentId = null;
    currentModalDefaultAmount = 0;
};

const handleConfirmPayment = async () => {
    const studentId = currentModalStudentId;
    const statusSelect = document.getElementById('modal-status');
    const amountInput = document.getElementById('modal-amount');
    const dateInput = document.getElementById('modal-date');
    const bankSelect = document.getElementById('modal-bank');
    const messageEl = document.getElementById('modal-payment-message');
    const confirmBtn = document.getElementById('confirm-payment-btn');

    if (!statusSelect || !amountInput || !dateInput || !bankSelect || !messageEl || !confirmBtn) {
        console.error("Add Payment Modal elements not found!");
        showMessage(pageMessage, 'خطأ في واجهة الدفع المنبثقة.', 'error');
        return;
    }

    const status = statusSelect.value;
    const amount = parseFloat(amountInput.value);
    const paymentDate = dateInput.value;
    const bankId = bankSelect.value;

    // Validation
    if (!status) {
        showMessage(messageEl, 'الرجاء اختيار حالة الدفع.', 'error'); return;
    }
    if (status === 'paid' || status === 'partial') {
        if (isNaN(amount) || amount <= 0) {
            showMessage(messageEl, 'الرجاء إدخال مبلغ صحيح أكبر من صفر.', 'error'); return;
        }
        if (!paymentDate) {
            showMessage(messageEl, 'الرجاء تحديد تاريخ الدفعة.', 'error'); return;
        }
        if (!bankId) {
            showMessage(messageEl, 'الرجاء اختيار البنك.', 'error'); return;
        }
    }
    if (status === 'not_paid') {
        // Allow confirming 'not_paid' without amount/bank/date?
        // For now, let's assume 'not_paid' means no transaction is recorded via this modal.
        // If the goal is to *update* the status to not_paid, that should happen elsewhere or via edit.
        showMessage(messageEl, 'تم تحديد "غير مدفوع". لن يتم تسجيل دفعة جديدة.', 'info', 5000);
        // closePaymentModal(); // Optionally close
        return; // Don't proceed with insertion
    }


    confirmBtn.disabled = true;
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    showMessage(messageEl, 'جاري الحفظ...', 'info', 0);

    try {
        // Insert Payment
        const { data: paymentData, error: paymentError } = await _supabase
            .from('student_payments')
            .insert({
                student_id: studentId,
                budget_month_id: selectedMonthId,
                amount: amount, // Use the entered amount
                payment_date: paymentDate,
                bank_id: bankId,
                payment_status: status // Save the status selected in the modal
            })
            .select('id')
            .single();

        if (paymentError) throw paymentError;
        console.log('[Payment Success] Payment saved. Returned data:', paymentData);

        // تم حذف إنشاء المعاملة البنكية يدوياً لأن التريجر في قاعدة البيانات يقوم بهذا تلقائياً
        console.log(`[Payment Success] Payment saved with ID: ${paymentData?.id}. Bank transaction will be created automatically by database trigger.`);

        showMessage(messageEl, 'تم حفظ الدفعة بنجاح!', 'success', 3000);
        setTimeout(closePaymentModal, 1500);

        // Update UI Card (Refetch details for accuracy)
        await updateStudentCardUI(studentId);

    } catch (error) {
        console.error('Error saving payment:', error);
        showMessage(messageEl, `خطأ في حفظ الدفعة: ${error.message}`, 'error', 0);
    } finally {
        if (paymentModalElement.classList.contains('show')) { // Check if modal is still open
             confirmBtn.disabled = false;
             confirmBtn.innerHTML = '<i class="fas fa-check"></i> تأكيد الدفع';
        }
    }
};

// --- Edit Payment Modal Logic ---
const setupEditPaymentModalListeners = () => {
    const closeEditBtn = document.getElementById('close-edit-payment-modal-btn');
    const cancelEditBtn = document.getElementById('cancel-edit-payment-btn');
    const modalStatusSelect = document.getElementById('edit-payment-status');
    const modalAmountInput = document.getElementById('edit-payment-amount');

    if (closeEditBtn) closeEditBtn.addEventListener('click', closeEditPaymentModal);
    if (cancelEditBtn) cancelEditBtn.addEventListener('click', closeEditPaymentModal);
    if (modalStatusSelect) modalStatusSelect.addEventListener('change', toggleEditModalPaymentFields);
    if (modalAmountInput) modalAmountInput.addEventListener('input', updateEditModalStatusBasedOnAmount);

    if (editPaymentModalElement) {
        editPaymentModalElement.addEventListener('click', (event) => {
            if (event.target === editPaymentModalElement) {
                closeEditPaymentModal();
            }
        });
        // Add Escape listener
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && editPaymentModalElement.classList.contains('active')) {
                closeEditPaymentModal();
            }
        });
    }
};

const toggleEditModalPaymentFields = () => {
    const statusSelect = document.getElementById('edit-payment-status');
    const amountInput = document.getElementById('edit-payment-amount');
    const dateInput = document.getElementById('edit-payment-date');
    const bankSelect = document.getElementById('edit-payment-bank');
    const confirmBtn = document.getElementById('save-edit-payment-btn');

    const status = statusSelect ? statusSelect.value : '';
    const disableFields = status === 'not_paid';

    if (amountInput) amountInput.disabled = disableFields;
    if (dateInput) dateInput.disabled = disableFields;
    if (bankSelect) bankSelect.disabled = disableFields;

    // Keep confirm button enabled unless explicitly disabled elsewhere
    if (confirmBtn) confirmBtn.disabled = false;

    // Set amount to 0.00 if disabling
    if (disableFields && amountInput) {
        amountInput.value = '0.00';
    }
};

const updateEditModalStatusBasedOnAmount = () => {
    const amountInput = document.getElementById('edit-payment-amount');
    const statusSelect = document.getElementById('edit-payment-status');
    if (!amountInput || !statusSelect || !currentEditPaymentId) return;

    // Don't auto-update status if user explicitly set it to 'not_paid'
    if (statusSelect.value === 'not_paid') {
        toggleEditModalPaymentFields();
        return;
    }

    const editedAmount = parseFloat(amountInput.value) || 0;
    const subscriptionAmount = currentModalDefaultAmount; // Month's base cost
    const studentCacheData = studentDataCache.find(s => s.id === currentModalStudentId);

    if (studentCacheData) {
        const originalAmount = parseFloat(amountInput.dataset.originalAmount || 0); // Get stored original amount
        const paidSoFar = studentCacheData.paidAmountThisMonth;
        const overallBalance = studentCacheData.overallStudentBalance;

        // Calculate projected total paid for the month if this edit is saved
        const projectedTotalPaidThisMonth = (paidSoFar - originalAmount) + editedAmount;

        // Calculate projected remaining for the month, considering balance
        const projectedRemainingForMonth = (subscriptionAmount + overallBalance) - projectedTotalPaidThisMonth;

        // Determine status based on projected remaining for the month
        if (projectedRemainingForMonth <= 0) {
            statusSelect.value = 'paid';
        } else if (projectedTotalPaidThisMonth > 0) {
            statusSelect.value = 'partial';
        } else {
            statusSelect.value = 'not_paid';
        }
    } else {
         // Fallback if cache data isn't available (less accurate, doesn't consider balance)
         if (subscriptionAmount > 0) {
            if (editedAmount >= subscriptionAmount) {
                statusSelect.value = 'paid';
            } else if (editedAmount > 0) {
                statusSelect.value = 'partial';
            } else {
                statusSelect.value = 'not_paid';
            }
        } else {
            statusSelect.value = 'paid'; // Cost is 0
        }
    }

    toggleEditModalPaymentFields();
};


const openEditPaymentModal = async (studentId, studentName, paymentId, defaultAmount) => {
    const modalStudentName = document.getElementById('edit-payment-student-name');
    const modalAmountInput = document.getElementById('edit-payment-amount');
    const modalSaveButton = document.getElementById('save-edit-payment-btn');
    const modalMessage = document.getElementById('edit-payment-message');
    const modalStatusSelect = document.getElementById('edit-payment-status');
    const modalDateInput = document.getElementById('edit-payment-date');
    const modalBankSelect = document.getElementById('edit-payment-bank');
    const modalMonthYearInput = document.getElementById('edit-payment-month-year');

    if (!editPaymentModalElement || !modalStudentName || !modalAmountInput || !modalSaveButton || !modalMessage || !modalStatusSelect || !modalDateInput || !modalBankSelect || !modalMonthYearInput) {
        console.error('One or more Edit Payment Modal elements not found.');
        showMessage(pageMessage, 'خطأ في تحميل واجهة تعديل الدفعة.', 'error');
        return;
    }

    currentEditPaymentId = paymentId;
    currentEditingStudentId = studentId; // Store studentId for calculations
    currentModalStudentId = studentId; // Store studentId for status calculation
    currentModalDefaultAmount = parseFloat(defaultAmount) || 0; // Store month's base cost

    modalMessage.style.display = 'none';
    modalSaveButton.disabled = true;
    modalSaveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> تحميل...';
    modalStudentName.textContent = studentName;
    modalMonthYearInput.value = (selectedMonthNumber && selectedYearNumber)
        ? `${getMonthName(selectedMonthNumber)} ${selectedYearNumber}`
        : 'غير محدد';

    // Populate bank options
    if (modalBankSelect) {
        let bankOptionsHtml = '<option value="">اختر البنك...</option>';
        banksList.forEach(bank => {
            bankOptionsHtml += `<option value="${bank.id}">${bank.name}</option>`;
        });
        modalBankSelect.innerHTML = bankOptionsHtml;
    }

    try {
        console.log(`[Edit Payment] Fetching details for payment ID: ${paymentId}`);
        const { data: paymentData, error: fetchError } = await _supabase
            .from('student_payments')
            .select('amount, payment_date, payment_status, bank_id')
            .eq('id', paymentId)
            .single();

        if (fetchError) throw fetchError;
        if (!paymentData) throw new Error('لم يتم العثور على بيانات الدفعة المحددة.');

        console.log('[Edit Payment] Fetched payment data:', paymentData);

        // Store original payment data
        editPaymentOriginalData = {
            amount: parseFloat(paymentData.amount || 0),
            payment_date: paymentData.payment_date,
            payment_status: paymentData.payment_status,
            bank_id: paymentData.bank_id
        };

        const originalAmount = parseFloat(paymentData.amount || 0);
        modalAmountInput.value = originalAmount.toFixed(2);
        modalAmountInput.dataset.originalAmount = originalAmount; // Store original amount
        modalDateInput.value = paymentData.payment_date;
        modalStatusSelect.value = paymentData.payment_status;
        modalBankSelect.value = paymentData.bank_id || '';

        // Add event listener for amount input to update calculations
        modalAmountInput.addEventListener('input', updateEditPaymentCalculations);

        // Initialize calculations
        await updateEditPaymentCalculations();

        updateEditModalStatusBasedOnAmount(); // Set initial status based on fetched data and context

        // Setup save button listener *after* data is loaded
        modalSaveButton.onclick = () => handleSaveEditPayment(paymentId, studentId); // Pass paymentId and studentId

        modalSaveButton.disabled = false;
        modalSaveButton.innerHTML = '<i class="fas fa-save"></i> حفظ التعديلات';

        editPaymentModalElement.classList.add('active');
        document.body.style.overflow = 'hidden';

    } catch (error) {
        console.error('Error fetching payment details for editing:', error);
        showMessage(modalMessage, `خطأ في تحميل بيانات الدفعة: ${error.message}`, 'error', 0);
        modalSaveButton.innerHTML = '<i class="fas fa-exclamation-triangle"></i> خطأ';
    }
};

const closeEditPaymentModal = () => {
    if (editPaymentModalElement) {
        editPaymentModalElement.classList.remove('active');
        document.body.style.overflow = '';
        currentEditPaymentId = null;
        currentEditingStudentId = null;
        currentModalStudentId = null;
        currentModalDefaultAmount = 0;
        editPaymentOriginalData = null;

        const modalSaveButton = document.getElementById('save-edit-payment-btn');
        const modalAmountInput = document.getElementById('edit-payment-amount');

        if (modalSaveButton) modalSaveButton.onclick = null; // Remove listener
        if (modalAmountInput) {
            delete modalAmountInput.dataset.originalAmount; // Clear stored amount
            modalAmountInput.removeEventListener('input', updateEditPaymentCalculations);
        }

        // Reset calculation box to collapsed state
        const calculationBox = document.getElementById('edit-payment-calculation-box');
        const toggleIcon = document.getElementById('edit-calculation-toggle-icon');
        if (calculationBox) calculationBox.classList.add('collapsed');
        if (toggleIcon) {
            toggleIcon.classList.remove('fa-chevron-up');
            toggleIcon.classList.add('fa-chevron-down');
        }
    }
};

const handleSaveEditPayment = async (paymentId, studentId) => { // Added studentId parameter
    if (!paymentId || !studentId) {
        console.error("handleSaveEditPayment called without paymentId or studentId.");
        showMessage(pageMessage, 'خطأ داخلي: معرف الدفعة أو الطالب مفقود.', 'error');
        return;
    }

    const statusSelect = document.getElementById('edit-payment-status');
    const amountInput = document.getElementById('edit-payment-amount');
    const dateInput = document.getElementById('edit-payment-date');
    const bankSelect = document.getElementById('edit-payment-bank');
    const messageEl = document.getElementById('edit-payment-message');
    const confirmBtn = document.getElementById('save-edit-payment-btn');

    if (!statusSelect || !amountInput || !dateInput || !bankSelect || !messageEl || !confirmBtn) {
        console.error("Edit modal elements not found during save!");
        showMessage(pageMessage, 'خطأ في واجهة تعديل الدفع.', 'error');
        return;
    }

    let status = statusSelect.value;
    let amount = parseFloat(amountInput.value);
    let paymentDate = dateInput.value;
    let bankId = bankSelect.value;

    // Validation
    if (!status) {
        showMessage(messageEl, 'الرجاء اختيار حالة الدفع.', 'error'); return;
    }
    if (status === 'paid' || status === 'partial') {
        if (isNaN(amount) || amount <= 0) {
            showMessage(messageEl, 'الرجاء إدخال مبلغ صحيح أكبر من صفر لحالة الدفع هذه.', 'error'); return;
        }
        if (!paymentDate) {
            showMessage(messageEl, 'الرجاء تحديد تاريخ الدفعة.', 'error'); return;
        }
        if (!bankId) {
            showMessage(messageEl, 'الرجاء اختيار البنك.', 'error'); return;
        }
    } else if (status === 'not_paid') {
        amount = 0; // Force amount to 0 if status is 'not_paid'
        // Date and Bank might still be relevant for the record, even if amount is 0
        if (!paymentDate) {
            // Use original date if available? Or require a date? Let's require it.
            showMessage(messageEl, 'الرجاء تحديد تاريخ (حتى لو كانت الحالة غير مدفوع).', 'error'); return;
        }
        if (!bankId) {
            // If bank is required by DB, show error. Otherwise, allow null/empty.
            // Let's require it for consistency, assuming it might be needed for tracking.
             showMessage(messageEl, 'الرجاء اختيار البنك (مطلوب حتى لحالة غير مدفوع).', 'error'); return;
             // Alternative: Set bankId to null if allowed by DB: bankId = null;
        }
    }


    confirmBtn.disabled = true;
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    showMessage(messageEl, 'جاري حفظ التعديلات...', 'info', 0);

    try {
        let updateData = {
            payment_status: status,
            payment_date: paymentDate,
            amount: amount,
            bank_id: bankId // Include bankId for all cases based on validation above
        };

        console.log(`[Edit Payment] Updating payment ID ${paymentId} with data:`, updateData);

        const { data: updatedPayment, error: updateError } = await _supabase
            .from('student_payments')
            .update(updateData)
            .eq('id', paymentId)
            .select()
            .single();

        if (updateError) throw updateError;

        console.log('[Edit Payment] Payment updated successfully:', updatedPayment);
        console.log('[Edit Payment] Bank transactions and financial logs will be handled automatically by database triggers.');


        showMessage(messageEl, 'تم حفظ التعديلات بنجاح!', 'success', 3000);
        setTimeout(closeEditPaymentModal, 1500);

        // Update UI Card
        await updateStudentCardUI(studentId); // Use the passed studentId

    } catch (error) {
        console.error('Error saving edited payment:', error);
        showMessage(messageEl, `خطأ في حفظ التعديلات: ${error.message}`, 'error', 0);
    } finally {
        if (editPaymentModalElement?.classList.contains('active')) {
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = '<i class="fas fa-save"></i> حفظ التعديلات';
        }
    }
};

// --- Month Lock/Unlock Logic ---
const handleLockMonth = async (studentId) => {
    const studentCacheData = studentDataCache.find(s => s.id === studentId);
    if (!studentCacheData) {
         showMessage(pageMessage, `خطأ: لم يتم العثور على بيانات الطالب ${studentId}.`, 'error');
         return;
    }
    if (studentCacheData.isLocked) return; // Already locked

    // Calculate the amount to transfer: Cost - Paid for the month.
    // This can be negative if the student overpaid for the month.
    const monthCost = studentCacheData.defaultAmount;
    const paidThisMonth = studentCacheData.paidAmountThisMonth;
    // Allow negative transfer (overpayment adjustment)
    const amountToTransfer = monthCost - paidThisMonth;

    const studentName = studentCacheData.name;

    // Adjust confirmation message based on whether amount is positive or negative
    let confirmationMessage = `هل أنت متأكد من رغبتك في إقفال حساب شهر ${getMonthName(selectedMonthNumber)} ${selectedYearNumber} للطالب ${studentName}؟\n\n`;
    if (amountToTransfer >= 0) {
        confirmationMessage += `سيتم ترحيل المبلغ المتبقي من تكلفة الشهر (${formatCurrency(amountToTransfer)} ريال) إلى رصيده العام.\n`;
    } else {
        // If amountToTransfer is negative, it means they overpaid for the month.
        // Adding this negative amount to the balance effectively reduces the balance.
        confirmationMessage += `سيتم تعديل رصيده العام بمقدار (${formatCurrency(amountToTransfer)} ريال) ليعكس الدفعة الزائدة لهذا الشهر.\n`;
    }
    confirmationMessage += `لا يمكن التراجع عن هذه العملية بسهولة.`;

    const confirmation = confirm(confirmationMessage);
    if (!confirmation) return;


    const lockButton = document.querySelector(`.lock-month-btn[data-student-id="${studentId}"]`);
    if (lockButton) {
        lockButton.disabled = true;
        lockButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإقفال...';
    }

    try {
        // Fetch current balance for accuracy
        const { data: currentDefault, error: fetchError } = await _supabase
            .from('student_subscription_defaults')
            .select('current_balance')
            .eq('student_id', studentId)
            .single();
        if (fetchError || !currentDefault) throw new Error(`خطأ في جلب الرصيد الحالي: ${fetchError?.message || 'لم يتم العثور على بيانات الاشتراك.'}`);

        // Use parseFloat, allowing negative balance. Default to 0 if null/undefined.
        const currentDbBalance = parseFloat(currentDefault.current_balance || 0);
        // The new balance is the current balance PLUS the amount transferred (which is the unpaid portion)
        const newOverallBalance = currentDbBalance + amountToTransfer;

        // Insert lock record
        const { error: lockInsertError } = await _supabase
            .from('subscription_locks')
            .insert({
                student_id: studentId,
                budget_month_id: selectedMonthId,
                locked_remaining_amount: amountToTransfer // Store the transferred amount
            });
        // Handle potential duplicate lock error gracefully
        if (lockInsertError && lockInsertError.code !== '23505') { // 23505 is unique_violation
             throw new Error(`خطأ في تسجيل الإقفال: ${lockInsertError.message}`);
        } else if (lockInsertError?.code === '23505') {
             console.warn(`Month already locked for student ${studentId}. UI might be slightly delayed.`);
             // If already locked, maybe skip balance update? Or ensure it's idempotent.
             // For now, we proceed, assuming the balance update might have failed before.
        }


        // Update overall balance
        const { error: balanceUpdateError } = await _supabase
            .from('student_subscription_defaults')
            .update({ current_balance: newOverallBalance })
            .eq('student_id', studentId);
        if (balanceUpdateError) throw new Error(`خطأ في تحديث الرصيد العام: ${balanceUpdateError.message}`);

        console.log(`Month locked successfully for student ${studentId}. Amount transferred: ${amountToTransfer}. New balance: ${newOverallBalance}`);

        // Update UI and Cache - Refetch to ensure consistency
        await updateStudentCardUI(studentId);

        showMessage(pageMessage, `تم إقفال حساب الشهر للطالب ${studentName} بنجاح.`, 'success');

    } catch (error) {
        console.error('Error locking month:', error);
        showMessage(pageMessage, `فشل إقفال الشهر: ${error.message}`, 'error');
        // Re-enable button only on failure
        if (lockButton) {
            lockButton.disabled = false;
            lockButton.innerHTML = '<i class="fas fa-lock-open"></i> إقفال الشهر';
        }
    }
};

const handleUnlockMonth = async (studentId) => {
    const studentCacheData = studentDataCache.find(s => s.id === studentId);
     if (!studentCacheData) {
         showMessage(pageMessage, `خطأ: لم يتم العثور على بيانات الطالب ${studentId}.`, 'error');
         return;
    }
    if (!studentCacheData.isLocked) return; // Already unlocked

    const studentName = studentCacheData.name;

    // Confirmation message needs to be clear about reversing the *transferred* amount
    // We need to fetch the amount that *was* transferred when locking.
    const confirmation = confirm(`هل أنت متأكد من رغبتك في فك إقفال حساب شهر ${getMonthName(selectedMonthNumber)} ${selectedYearNumber} للطالب ${studentName}؟\n\nسيتم خصم المبلغ الذي تم ترحيله عند الإقفال من رصيده العام.`);
    if (!confirmation) return;


    const unlockButton = document.querySelector(`.unlock-month-btn[data-student-id="${studentId}"]`);
    if (unlockButton) {
        unlockButton.disabled = true;
        unlockButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الفك...';
    }

    try {
        // Find lock record to get amount and ID
        const { data: lockRecord, error: lockFetchError } = await _supabase
            .from('subscription_locks')
            .select('id, locked_remaining_amount')
            .eq('student_id', studentId)
            .eq('budget_month_id', selectedMonthId)
            .single();
        // If no lock record found, maybe it was already unlocked?
        if (lockFetchError && lockFetchError.code === 'PGRST116') { // PGRST116: Row not found
             console.warn(`Lock record not found for student ${studentId}, month ${selectedMonthId}. Assuming already unlocked.`);
             showMessage(pageMessage, `لم يتم العثور على سجل إقفال للطالب ${studentName}. ربما تم فكه بالفعل.`, 'warning');
             await updateStudentCardUI(studentId); // Refresh UI just in case
             return;
        }
        if (lockFetchError || !lockRecord) throw new Error(`خطأ في العثور على سجل الإقفال: ${lockFetchError?.message || 'لم يتم العثور على السجل.'}`);


        const lockedAmountToReverse = parseFloat(lockRecord.locked_remaining_amount || 0);
        const lockRecordId = lockRecord.id;

        // Fetch current balance
        const { data: currentDefault, error: fetchError } = await _supabase
            .from('student_subscription_defaults')
            .select('current_balance')
            .eq('student_id', studentId)
            .single();
        if (fetchError || !currentDefault) throw new Error(`خطأ في جلب الرصيد الحالي: ${fetchError?.message || 'لم يتم العثور على بيانات الاشتراك.'}`);

        // Use parseFloat, allowing negative balance. Default to 0 if null/undefined.
        const currentDbBalance = parseFloat(currentDefault.current_balance || 0);
        // Reverse the transfer: subtract the locked amount from the current balance
        const newOverallBalance = currentDbBalance - lockedAmountToReverse;

        // Update overall balance FIRST
        const { error: balanceUpdateError } = await _supabase
            .from('student_subscription_defaults')
            .update({ current_balance: newOverallBalance })
            .eq('student_id', studentId);
        if (balanceUpdateError) throw new Error(`خطأ في تحديث الرصيد العام: ${balanceUpdateError.message}`);

        // THEN Delete lock record
        const { error: lockDeleteError } = await _supabase
            .from('subscription_locks')
            .delete()
            .eq('id', lockRecordId);
        if (lockDeleteError) {
            // Log error but proceed, balance was updated. Manual cleanup might be needed for the lock record.
            console.error(`Failed to delete lock record ${lockRecordId} after balance update. Manual check might be needed. Error: ${lockDeleteError.message}`);
            showMessage(pageMessage, `تم تحديث الرصيد ولكن فشل حذف سجل الإقفال (قد يتطلب تدخلاً يدوياً).`, 'warning');
            // throw new Error(`خطأ في حذف سجل الإقفال: ${lockDeleteError.message}`); // Don't throw, let UI update
        }


        console.log(`Month unlocked successfully for student ${studentId}. Amount reversed: ${lockedAmountToReverse}. New balance: ${newOverallBalance}`);

        // Update UI and Cache - Refetch to ensure consistency
        await updateStudentCardUI(studentId);

        showMessage(pageMessage, `تم فك إقفال حساب الشهر للطالب ${studentName} بنجاح.`, 'success');

    } catch (error) {
        console.error('Error unlocking month:', error);
        showMessage(pageMessage, `فشل فك إقفال الشهر: ${error.message}`, 'error');
        // Re-enable button only on failure (or consider refresh)
        if (unlockButton) {
            unlockButton.disabled = false;
            unlockButton.innerHTML = '<i class="fas fa-unlock"></i> فك القفل';
        }
    }
};

// --- Student Removal Logic ---
const handleRemoveStudentFromGroup = async (studentId) => {
    const studentCacheData = studentDataCache.find(s => s.id === studentId);
    if (!studentCacheData) return;
    if (studentCacheData.isLocked) {
        showMessage(pageMessage, `لا يمكن إزالة الطالب ${studentCacheData.name} لأن حسابه مقفل لهذا الشهر.`, 'warning');
        return;
    }

    const studentName = studentCacheData.name;
    const confirmation = confirm(`هل أنت متأكد من رغبتك في إزالة الطالب ${studentName} من هذه المجموعة؟`);
    if (!confirmation) return;

    const removeButton = document.querySelector(`.remove-student-corner-btn[data-student-id="${studentId}"]`);
    if (removeButton) {
        removeButton.disabled = true;
        // Optional: Change icon to spinner
        // removeButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    }

    try {
        if (!groupId) throw new Error('معرف المجموعة غير متوفر لإتمام عملية الحذف.');

        const { error } = await _supabase
            .from('student_group_members')
            .delete()
            .eq('student_id', studentId)
            .eq('group_id', groupId);
        if (error) throw error;

        console.log(`[Remove Student] Student ${studentId} removed from group ${groupId} successfully.`);

        // Remove card from UI
        const cardElement = document.querySelector(`.student-card[data-student-id="${studentId}"]`);
        if (cardElement) cardElement.remove();

        // Remove from cache
        studentDataCache = studentDataCache.filter(s => s.id !== studentId);

        showMessage(pageMessage, `تمت إزالة الطالب ${studentName} من المجموعة بنجاح.`, 'success');

    } catch (error) {
        console.error('[Remove Student] Error removing student from group:', error);
        showMessage(pageMessage, `فشل في إزالة الطالب: ${error.message}`, 'error');
        if (removeButton) removeButton.disabled = false; // Re-enable on failure
    }
};

// --- Print Invoice Logic ---
const handlePrintInvoiceClick = async (studentId, studentName, defaultAmount) => {
    console.log(`[Invoice] Print button clicked for student: ${studentName} (ID: ${studentId})`);
    if (!studentId || !selectedMonthId) {
        showMessage(pageMessage, 'خطأ: بيانات الطالب أو الشهر غير متوفرة لطباعة الإيصال.', 'error');
        return;
    }

    showMessage(pageMessage, `جاري تجهيز إيصال الطالب ${studentName}...`, 'info', 0);

    try {
        const { data: payments, error: paymentsError } = await _supabase
            .from('student_payments')
            .select('id, payment_date, amount')
            .eq('student_id', studentId)
            .eq('budget_month_id', selectedMonthId)
            .order('payment_date', { ascending: true });
        if (paymentsError) throw paymentsError;

        const totalPaid = payments.reduce((sum, payment) => sum + parseFloat(payment.amount || 0), 0);

        const invoiceData = {
            studentId: studentId,
            studentName: studentName,
            groupName: groupName,
            monthNumber: selectedMonthNumber,
            yearNumber: selectedYearNumber,
            payments: payments,
            totalPaid: totalPaid,
            defaultAmount: defaultAmount // Pass the month's cost
        };

        const invoiceHTML = generatePaymentInvoiceHTML(invoiceData);
        if (!invoiceHTML) throw new Error('فشل في إنشاء محتوى الإيصال.');
        const invoiceWindow = window.open('', '_blank');
        if (invoiceWindow) {
            // Use innerHTML instead of document.write
            invoiceWindow.document.open(); // Still needed to start writing
            invoiceWindow.document.close(); // Close to allow modification
            invoiceWindow.document.body.innerHTML = invoiceHTML; // Inject the actual content
            // The print button inside the HTML will handle printing
            showMessage(pageMessage, `تم فتح إيصال الطالب ${studentName} في نافذة جديدة.`, 'success', 3000);
        } else {
            throw new Error('فشل فتح نافذة جديدة. قد يكون تم حظرها بواسطة المتصفح.');
        }

    } catch (error) {
        console.error('[Invoice] Error generating or fetching data for invoice:', error);
        showMessage(pageMessage, `خطأ في طباعة الإيصال: ${error.message}`, 'error');
    } finally {
        // Ensure loading message is cleared even if window opening failed but before error shown
        if (pageMessage.textContent.startsWith('جاري تجهيز إيصال')) {
             setTimeout(() => { // Give a slight delay for potential error message to show
                 if (pageMessage.textContent.startsWith('جاري تجهيز إيصال')) {
                    pageMessage.style.display = 'none';
                 }
             }, 100);
        }
    }
};


// --- Data Fetching and Processing ---
const fetchBanks = async () => {
    if (!_supabase) return [];
    try {
        const { data, error } = await _supabase
            .from('banks')
            .select('id, name')
            .order('name');
        if (error) throw error;
        console.log('Fetched banks:', data);
        return data || [];
    } catch (error) {
        console.error('Error fetching banks:', error);
        showMessage(pageMessage, 'خطأ في جلب قائمة البنوك.', 'error');
        return [];
    }
};

const fetchAndProcessStudentData = async () => {
    if (!_supabase || !groupId || !selectedMonthId) {
        throw new Error('بيانات المجموعة أو الشهر غير متوفرة لجلب بيانات الطلاب.');
    }

    // 1. Fetch student IDs in the group
    const { data: members, error: membersError } = await _supabase
        .from('student_group_members')
        .select('student_id')
        .eq('group_id', groupId);
    if (membersError) throw membersError;
    const studentIds = members.map(m => m.student_id);
    if (studentIds.length === 0) return []; // No students in group

    // 2. Fetch student details and defaults (including current_balance)
    const { data: studentsWithDefaults, error: studentsError } = await _supabase
        .from('students')
        .select(`
            id,
            name,
            student_subscription_defaults ( default_service_type, default_amount, current_balance )
        `)
        .in('id', studentIds);
    if (studentsError) throw studentsError;

    // 3. Fetch custom month costs
    let monthCostMap = {};
    const { data: monthCosts, error: monthCostsError } = await _supabase
        .from('student_month_costs')
        .select('student_id, month_cost')
        .eq('budget_month_id', selectedMonthId)
        .in('student_id', studentIds);
    if (monthCostsError) {
        console.error('Error fetching custom month costs:', monthCostsError);
        showMessage(pageMessage, 'حدث خطأ أثناء جلب التكاليف الشهرية المخصصة، سيتم استخدام التكاليف الافتراضية.', 'warning', 5000);
    } else if (monthCosts) {
        monthCostMap = monthCosts.reduce((map, cost) => {
            const costValue = parseFloat(cost.month_cost);
            if (!isNaN(costValue)) map[cost.student_id] = costValue;
            return map;
        }, {});
    }

    // 4. Fetch payments for the month
    let paymentsSumMap = {};
    let latestPaymentMap = {};
    const { data: payments, error: paymentsError } = await _supabase
        .from('student_payments')
        .select('id, student_id, amount, payment_date, payment_status, bank_id')
        .eq('budget_month_id', selectedMonthId)
        .in('student_id', studentIds)
        .order('payment_date', { ascending: false }); // Get latest first
    if (paymentsError) throw paymentsError;
    payments.forEach(payment => {
        const studentId = payment.student_id;
        const currentAmount = parseFloat(payment.amount || 0);
        paymentsSumMap[studentId] = (paymentsSumMap[studentId] || 0) + currentAmount;
        // Capture the absolute latest payment details
        if (!latestPaymentMap[studentId]) {
            latestPaymentMap[studentId] = {
                id: payment.id, date: payment.payment_date, status: payment.payment_status,
                bankId: payment.bank_id, amount: currentAmount
            };
        }
    });

    // 5. Fetch lock status
    let lockedStudentsMap = new Set();
    const { data: locks, error: locksError } = await _supabase
        .from('subscription_locks')
        .select('student_id')
        .eq('budget_month_id', selectedMonthId)
        .in('student_id', studentIds);
    if (locksError) throw locksError;
    if (locks) locks.forEach(lock => lockedStudentsMap.add(lock.student_id));

    // 6. Combine data
    return studentsWithDefaults.map(student => {
        const defaultsData = student.student_subscription_defaults;
        let defaults = {};
        // Handle cases where relation might be null or an empty array
        if (Array.isArray(defaultsData)) defaults = defaultsData[0] || {};
        else if (typeof defaultsData === 'object' && defaultsData !== null) defaults = defaultsData;

        const fallbackDefaultAmount = parseFloat(defaults.default_amount || 0);
        const customMonthCost = monthCostMap[student.id];
        // Determine the actual cost for *this* month
        const defaultAmount = (customMonthCost !== undefined && !isNaN(customMonthCost)) ? customMonthCost : fallbackDefaultAmount;

        const paidAmountThisMonth = paymentsSumMap[student.id] || 0;
        const latestPayment = latestPaymentMap[student.id];
        let latestPaymentBankName = 'لا يوجد';
        if (latestPayment && latestPayment.bankId) {
            const bank = banksList.find(b => b.id === latestPayment.bankId);
            latestPaymentBankName = bank ? bank.name : 'بنك غير معروف';
        }

        // Use parseFloat, allowing negative balance. Default to 0 if null/undefined.
        const overallStudentBalance = parseFloat(defaults.current_balance || 0);
        const defaultServiceType = defaults.default_service_type || 'غير محدد';

        // Calculate remaining for the month (Cost + Balance - Paid)
        // This value can be negative if the student has overpaid or has a large positive balance.
        const remainingForMonth = (defaultAmount + overallStudentBalance) - paidAmountThisMonth;

        // Determine overall status for the month based on the remaining amount for the month
        let overallMonthStatus = 'غير مدفوع';
        if (remainingForMonth <= 0) {
            // If remaining is zero or negative, it's considered paid (covered by payments or balance)
            overallMonthStatus = 'مدفوع';
        } else if (paidAmountThisMonth > 0) {
            // If remaining is positive BUT something was paid this month, it's partial
            overallMonthStatus = 'جزئي';
        }
        // else it remains 'غير مدفوع' (remaining > 0 and paidAmountThisMonth == 0)

        const isLocked = lockedStudentsMap.has(student.id);

        return {
            id: student.id, name: student.name, defaultServiceType, defaultAmount,
            paidAmountThisMonth,
            remainingForMonth, // The calculated value based on cost, balance, paid
            overallStudentBalance, // Can be negative now
            latestPaymentId: latestPayment ? latestPayment.id : null,
            latestPaymentDate: latestPayment ? latestPayment.date : null,
            overallMonthStatus, // Status based on remainingForMonth
            latestPaymentBankName, isLocked
        };
    });
};


// --- UI Rendering ---
const renderStudentCard = (student) => {
    const card = document.createElement('div');
    card.className = student.isLocked ? 'student-card locked' : 'student-card';
    card.dataset.studentId = student.id;
    card.dataset.studentName = student.name; // Store name for modal title

    // Color for the "Remaining for Month" field - based on its value
    let remainingColorClass = 'amount-remaining'; // Default red-ish (owes money)
    if (student.remainingForMonth <= 0) {
        remainingColorClass = 'amount-paid'; // Green-ish if remaining is zero or negative (covered)
    }

    // Color for the "Overall Balance" field
    let balanceColorClass = 'amount-neutral'; // Default color
    if (student.overallStudentBalance > 0) {
        balanceColorClass = 'amount-remaining'; // Red color if balance is positive (student has credit)
    } else if (student.overallStudentBalance < 0) {
        balanceColorClass = 'amount-paid'; // Blue color if balance is negative (student owes overall)
    }


    const isLocked = student.isLocked;
    // Disable buttons if locked
    const actionButtonsDisabledAttr = isLocked ? 'disabled' : '';

    // Determine Pay button class and disabled state based on *overallMonthStatus* (paid/partial/not_paid)
    let payButtonClass = 'btn pay-action-btn';
    let payButtonDisabled = isLocked; // Start with locked status

    if (!isLocked) {
        switch (student.overallMonthStatus) {
            case 'مدفوع':
                payButtonClass += ' btn-secondary'; // Grey if fully paid/covered for the month
                payButtonDisabled = true;
                break;
            case 'جزئي':
                payButtonClass += ' btn-warning'; // Yellow if partially paid (still owes for the month)
                break;
            case 'غير مدفوع':
            default:
                 // Enable pay button if not paid and month cost > 0
                 if (student.defaultAmount > 0) {
                    payButtonClass += ' btn-success'; // Green if not paid yet and cost > 0
                 } else {
                    // If cost is 0, it should be 'مدفوع' anyway, but handle just in case
                    payButtonClass += ' btn-secondary';
                    payButtonDisabled = true;
                 }
                break;
        }
    } else {
        payButtonClass += ' btn-secondary'; // Ensure grey if locked
    }
    const payButtonDisabledAttr = payButtonDisabled ? 'disabled' : '';

    // Lock/Unlock button
    let lockUnlockButtonHtml = '';
    if (isLocked) {
        lockUnlockButtonHtml = `
            <button type="button" class="btn btn-danger unlock-month-btn" data-student-id="${student.id}">
                <i class="fas fa-unlock"></i> فك القفل
            </button>`;
    } else {
        // Disable lock button if already paid for the month? Or allow locking anyway?
        // Let's allow locking even if paid, as it finalizes the month.
        lockUnlockButtonHtml = `
            <button type="button" class="btn btn-warning lock-month-btn" data-student-id="${student.id}">
                <i class="fas fa-lock-open"></i> إقفال الشهر
            </button>`;
    }

    // Edit button logic: disabled if locked or no latest payment
    const editButtonDisabled = isLocked || !student.latestPaymentId;
    const editButtonDisabledAttr = editButtonDisabled ? 'disabled' : '';
    const editButtonTitle = !student.latestPaymentId ? 'title="لا توجد دفعة لتعديلها"' : '';

    // Print button logic: disabled if nothing paid this month
    const printButtonDisabled = student.paidAmountThisMonth <= 0;
    const printButtonDisabledAttr = printButtonDisabled ? 'disabled' : '';


    card.innerHTML = `
        <input type="checkbox" class="student-card-checkbox" data-student-id="${student.id}" ${isLocked ? 'disabled' : ''}>
        <button type="button" class="btn-icon remove-student-corner-btn" data-student-id="${student.id}" title="إزالة من المجموعة" ${actionButtonsDisabledAttr}>
            <i class="fas fa-times"></i>
        </button>
        <div class="student-card-header">
            <i class="fas fa-user-graduate"></i>
            <span class="student-name">${student.name}</span>
            ${isLocked ? '<span class="lock-indicator"><i class="fas fa-lock"></i> مقفل</span>' : ''}
        </div>
        <div class="student-card-info">
            <div class="info-item"><span class="label">نوع الخدمة:</span><span class="value">${student.defaultServiceType}</span></div>
            <div class="info-item"><span class="label">تكلفة الشهر:</span><span class="value amount-due">${formatCurrency(student.defaultAmount)}</span></div>
            <div class="info-item"><span class="label">المدفوع هذا الشهر:</span><span class="value amount-paid" id="paid-${student.id}">${formatCurrency(student.paidAmountThisMonth)}</span></div>
            <div class="info-item"><span class="label">المتبقي لهذا الشهر:</span><span class="value ${remainingColorClass}" id="remaining-month-${student.id}">${formatCurrency(student.remainingForMonth)}</span></div>
            <div class="info-item">
                <span class="label">الرصيد الحالي:</span>
                <span class="value ${balanceColorClass}" id="balance-overall-${student.id}">${formatCurrency(student.overallStudentBalance)}</span>
                <button type="button" class="balance-info-btn" data-student-id="${student.id}" data-student-name="${student.name}" title="تفاصيل الرصيد">
                    <i class="fas fa-question"></i>
                </button>
            </div>
            <div class="info-item"><span class="label">حالة الشهر:</span><span class="value status-${student.overallMonthStatus.replace(' ', '-').toLowerCase()}" id="status-month-${student.id}">${student.overallMonthStatus}</span></div>
            <div class="info-item"><span class="label">تاريخ آخر دفعة:</span><span class="value" id="last-payment-date-${student.id}">${student.latestPaymentDate ? student.latestPaymentDate : 'لا يوجد'}</span></div>
            <div class="info-item"><span class="label">بنك آخر دفعة:</span><span class="value" id="last-payment-bank-${student.id}">${student.latestPaymentBankName}</span></div>
        </div>
        ${isLocked ? `
            <div class="locked-amount-display">
                <div class="locked-label">المبلغ المقفول لهذا الشهر</div>
                <div class="locked-value">${formatCurrency(student.remainingForMonth)} ريال</div>
            </div>
        ` : ''}
        <div class="payment-form">
             <button type="button" class="${payButtonClass}" ${payButtonDisabledAttr} data-student-id="${student.id}" data-student-name="${student.name}" data-default-amount="${student.defaultAmount}">
                <i class="fas fa-dollar-sign"></i> دفع
             </button>
             <button type="button" class="btn btn-secondary edit-cost-btn" data-student-id="${student.id}" data-student-name="${student.name}" data-current-cost="${student.defaultAmount}">
                <i class="fas fa-edit"></i> تعديل التكلفة
             </button>
             <button type="button" class="btn btn-info payment-history-btn" data-student-id="${student.id}" data-student-name="${student.name}">
                <i class="fas fa-history"></i> سجل الدفعات
             </button>
             ${lockUnlockButtonHtml}
             <button class="btn btn-secondary print-invoice-btn" data-student-id="${student.id}" data-student-name="${student.name}" data-default-amount="${student.defaultAmount}" ${printButtonDisabledAttr}>
                <i class="fas fa-print"></i> طباعة إيصال
            </button>
        </div>
    `;

    // Add event listeners for the card's buttons
    const payButton = card.querySelector('.pay-action-btn');
    const lockButton = card.querySelector('.lock-month-btn');
    const unlockButton = card.querySelector('.unlock-month-btn');
    const removeStudentButton = card.querySelector('.remove-student-corner-btn');
    const paymentHistoryButton = card.querySelector('.payment-history-btn');
    const printInvoiceButton = card.querySelector('.print-invoice-btn');
    const balanceInfoButton = card.querySelector('.balance-info-btn');
    const editCostButton = card.querySelector('.edit-cost-btn');
    const checkbox = card.querySelector('.student-card-checkbox');

    if (payButton && !payButtonDisabled) {
        payButton.addEventListener('click', (e) => {
            const targetStudentId = e.currentTarget.dataset.studentId;
            const targetStudentName = e.currentTarget.dataset.studentName;
            const targetDefaultAmount = e.currentTarget.dataset.defaultAmount; // Pass month's cost
            openPaymentModal(targetStudentId, targetStudentName, targetDefaultAmount);
        });
    }
    if (lockButton) lockButton.addEventListener('click', () => handleLockMonth(student.id));
    if (unlockButton) unlockButton.addEventListener('click', () => handleUnlockMonth(student.id));
    if (removeStudentButton && !isLocked) removeStudentButton.addEventListener('click', () => handleRemoveStudentFromGroup(student.id));

    if (paymentHistoryButton) {
        paymentHistoryButton.addEventListener('click', () => {
            openPaymentHistoryModal(student.id, student.name);
        });
    }

    if (balanceInfoButton) {
        balanceInfoButton.addEventListener('click', () => {
            openBalanceInfoModal(student.id, student.name);
        });
    }

    if (checkbox && !isLocked) {
        checkbox.addEventListener('change', (e) => {
            handleStudentSelection(student.id, e.target.checked);
        });
    }

    if (printInvoiceButton && !printButtonDisabled) {
        printInvoiceButton.addEventListener('click', () => {
            const studentCache = studentDataCache.find(s => s.id === student.id);
            const correctDefaultAmount = studentCache ? studentCache.defaultAmount : student.defaultAmount; // Fallback
            handlePrintInvoiceClick(student.id, student.name, correctDefaultAmount);
        });
    }

    if (editCostButton) {
        editCostButton.addEventListener('click', (e) => {
            const targetStudentId = e.currentTarget.dataset.studentId;
            const targetStudentName = e.currentTarget.dataset.studentName;
            const currentCost = e.currentTarget.dataset.currentCost;
            openEditMonthCostModal(targetStudentId, targetStudentName, currentCost);
        });
    }

    return card;
};


const renderStudentCards = (students) => {
    studentCardsContainer.innerHTML = ''; // Clear previous content

    if (!students || students.length === 0) {
        studentCardsContainer.innerHTML = '<p class="loading-placeholder">لا يوجد طلاب في هذه المجموعة.</p>';
        return;
    }

    students.forEach(student => {
        const cardElement = renderStudentCard(student);
        studentCardsContainer.appendChild(cardElement);
    });
};

// --- UI Update Function ---
const updateStudentCardUI = async (studentId, updatedStudentData = null) => {
    console.log(`[UI Update] Updating card for student ID: ${studentId}`);
    let studentData = updatedStudentData;

    // If full data isn't provided, refetch it to ensure accuracy
    if (!studentData) {
        try {
            // Refetch data for ALL students in the group to update cache correctly
            // This ensures consistency, especially after lock/unlock affecting balances
            const allStudentsData = await fetchAndProcessStudentData();
            studentDataCache = allStudentsData; // Update the entire cache
            studentData = studentDataCache.find(s => s.id === studentId);

            if (!studentData) {
                console.warn(`[UI Update] Student ${studentId} not found after refetch. Card might be removed or group changed.`);
                // Remove the card if the student is no longer found in the processed data
                const cardElement = document.querySelector(`.student-card[data-student-id="${studentId}"]`);
                if (cardElement) cardElement.remove();
                return;
            }

        } catch (error) {
            console.error(`[UI Update] Error refetching data for student ${studentId}:`, error);
            showMessage(pageMessage, `خطأ في تحديث بيانات الطالب ${studentId}. قد تحتاج لتحديث الصفحة.`, 'warning', 5000);
            return; // Stop update if refetch fails
        }
    } else {
         // If data *was* provided (e.g., after lock/unlock before refetch was implemented), update cache
         const cacheIndex = studentDataCache.findIndex(s => s.id === studentId);
         if (cacheIndex > -1) {
             studentDataCache[cacheIndex] = updatedStudentData;
         } else {
             studentDataCache.push(updatedStudentData); // Add if somehow missing
         }
         // Even if data was provided, a full refetch might be safer after complex operations
         // Consider calling updateStudentCardUI(studentId) without the second arg after operations like lock/unlock
    }


    // Find the existing card
    const cardElement = document.querySelector(`.student-card[data-student-id="${studentId}"]`);
    if (!cardElement) {
        console.warn(`[UI Update] Card element not found for student ${studentId}. Cannot update.`);
        // If card doesn't exist but data does, maybe render it anew?
        // This could happen if the initial render failed or was incomplete.
        // renderStudentCards(studentDataCache); // Re-render all might be simpler
        return;
    }

    // Re-render the specific card content with the new data
    const newCardElement = renderStudentCard(studentData); // renderStudentCard returns the full card element
    // Replace the old card with the new one to ensure listeners are correct
    cardElement.replaceWith(newCardElement);

    console.log(`[UI Update] Card updated successfully for student ID: ${studentId}`);
};


// --- Initialization ---
const initializePage = async () => {
    console.log('Group Details page initializing...');

    const urlParams = new URLSearchParams(window.location.search);
    groupId = urlParams.get('groupId');
    groupName = urlParams.get('groupName');
    selectedMonthId = urlParams.get('monthId');
    selectedMonthNumber = urlParams.get('monthNumber');
    selectedYearNumber = urlParams.get('yearNumber');

    console.log('URL Params:', { groupId, groupName, selectedMonthId, selectedMonthNumber, selectedYearNumber });

    if (groupNameDisplay) groupNameDisplay.textContent = groupName || 'غير محدد';
    if (monthYearDisplay) {
        monthYearDisplay.textContent = (selectedMonthNumber && selectedYearNumber)
            ? `${getMonthName(selectedMonthNumber)} ${selectedYearNumber}`
            : 'غير محدد';
    }

    // Update navbar info (not needed in unified header)

    if (!groupId || !selectedMonthId) {
        showMessage(pageMessage, 'خطأ: معرف المجموعة أو الشهر مفقود. لا يمكن تحميل البيانات.', 'error');
        studentCardsContainer.innerHTML = '<p class="loading-placeholder error">بيانات غير كافية لتحميل الصفحة.</p>';
        return;
    }

    studentCardsContainer.innerHTML = '<div class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i> جاري تحميل البيانات...</div>';

    try {
        // Fetch banks first as they are needed for processing student data
        banksList = await fetchBanks();

        // Setup modals (needs banksList populated for dropdowns)
        setupPaymentModal(); // Add modal structure to DOM
        setupEditPaymentModalListeners(); // Edit modal listeners
        setupModalListeners(); // Setup listeners for new modals
        setupEditMonthCostModalListeners(); // Setup edit month cost modal listeners
        setupBulkActionsListeners(); // Setup bulk actions listeners

        // Fetch and process student data (now includes bank names)
        studentDataCache = await fetchAndProcessStudentData();
        renderStudentCards(studentDataCache); // Render cards with processed data

    } catch (error) {
        console.error('Error initializing page:', error);
        showMessage(pageMessage, `خطأ في تحميل الصفحة: ${error.message}`, 'error');
        studentCardsContainer.innerHTML = '<p class="loading-placeholder error">فشل تحميل البيانات.</p>';
    }

    // Setup back button listener
    if (backToGroupsBtn) {
        backToGroupsBtn.addEventListener('click', () => {
            // Navigate back to the subscriptions page
            window.location.href = '../student_subscriptions/student_subscriptions.html';
        });
    }
};

// --- Modal Setup Functions ---
const setupModalListeners = () => {
    // Payment History Modal
    const paymentHistoryModal = document.getElementById('payment-history-modal');
    const closePaymentHistoryBtn = document.getElementById('close-payment-history-modal');

    if (closePaymentHistoryBtn) {
        closePaymentHistoryBtn.addEventListener('click', closePaymentHistoryModal);
    }

    if (paymentHistoryModal) {
        paymentHistoryModal.addEventListener('click', (e) => {
            if (e.target === paymentHistoryModal) {
                closePaymentHistoryModal();
            }
        });
    }

    // Balance Info Modal
    const balanceInfoModal = document.getElementById('balance-info-modal');
    const closeBalanceInfoBtn = document.getElementById('close-balance-info-modal');

    if (closeBalanceInfoBtn) {
        closeBalanceInfoBtn.addEventListener('click', closeBalanceInfoModal);
    }

    if (balanceInfoModal) {
        balanceInfoModal.addEventListener('click', (e) => {
            if (e.target === balanceInfoModal) {
                closeBalanceInfoModal();
            }
        });
    }

    // Escape key listener for all modals
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            if (paymentHistoryModal?.classList.contains('show')) {
                closePaymentHistoryModal();
            }
            if (balanceInfoModal?.classList.contains('show')) {
                closeBalanceInfoModal();
            }
        }
    });
};

const setupEditMonthCostModalListeners = () => {
    const editMonthCostModal = document.getElementById('edit-month-cost-modal');
    const closeEditMonthCostBtn = document.getElementById('close-edit-month-cost-modal-btn');
    const cancelEditMonthCostBtn = document.getElementById('cancel-edit-month-cost-btn');

    if (closeEditMonthCostBtn) {
        closeEditMonthCostBtn.addEventListener('click', closeEditMonthCostModal);
    }

    if (cancelEditMonthCostBtn) {
        cancelEditMonthCostBtn.addEventListener('click', closeEditMonthCostModal);
    }

    if (editMonthCostModal) {
        editMonthCostModal.addEventListener('click', (e) => {
            if (e.target === editMonthCostModal) {
                closeEditMonthCostModal();
            }
        });
    }

    // Escape key listener
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && editMonthCostModal?.classList.contains('active')) {
            closeEditMonthCostModal();
        }
    });
};

const setupBulkActionsListeners = () => {
    const bulkDeleteBtn = document.getElementById('bulk-delete-btn');

    if (bulkDeleteBtn) {
        bulkDeleteBtn.addEventListener('click', handleBulkDelete);
    }
};

// --- Edit Month Cost Modal Logic ---
const openEditMonthCostModal = async (studentId, studentName, currentCost) => {
    const editMonthCostModal = document.getElementById('edit-month-cost-modal');
    const newMonthCostInput = document.getElementById('new-month-cost');
    const editMonthCostMessage = document.getElementById('edit-month-cost-message');
    const modalTitle = editMonthCostModal?.querySelector('.modal-header h2');

    if (!editMonthCostModal) {
        console.error('Edit month cost modal not found');
        return;
    }

    // Update modal title to include student name
    if (modalTitle) {
        modalTitle.textContent = `تعديل تكلفة الشهر - ${studentName}`;
    }

    // Set current cost as default value
    if (newMonthCostInput) {
        newMonthCostInput.value = parseFloat(currentCost || 0).toFixed(2);
    }

    // Hide any previous messages
    if (editMonthCostMessage) {
        editMonthCostMessage.style.display = 'none';
    }

    // Store student info for saving
    editMonthCostModal.dataset.studentId = studentId;
    editMonthCostModal.dataset.studentName = studentName;

    // Show modal
    editMonthCostModal.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Setup save button listener for this specific student
    const saveBtn = document.getElementById('save-edit-month-cost-btn');
    if (saveBtn) {
        // Remove any existing listeners
        const newSaveBtn = saveBtn.cloneNode(true);
        saveBtn.parentNode.replaceChild(newSaveBtn, saveBtn);

        // Add new listener for this student
        newSaveBtn.addEventListener('click', () => handleSaveStudentMonthCost(studentId, studentName));
    }
};

const handleSaveStudentMonthCost = async (studentId, studentName) => {
    const newMonthCostInput = document.getElementById('new-month-cost');
    const editMonthCostMessage = document.getElementById('edit-month-cost-message');
    const saveBtn = document.getElementById('save-edit-month-cost-btn');

    const newCost = parseFloat(newMonthCostInput?.value || 0);

    if (isNaN(newCost) || newCost < 0) {
        showMessage(editMonthCostMessage, 'الرجاء إدخال تكلفة صحيحة.', 'error');
        return;
    }

    if (!selectedMonthId) {
        showMessage(editMonthCostMessage, 'خطأ: لم يتم تحديد الشهر المالي.', 'error');
        return;
    }

    // Disable save button during processing
    if (saveBtn) {
        saveBtn.disabled = true;
        saveBtn.textContent = 'جاري الحفظ...';
    }

    try {
        // Update or insert student month cost
        const { error } = await _supabase
            .from('student_month_costs')
            .upsert({
                student_id: studentId,
                budget_month_id: selectedMonthId,
                month_cost: newCost
            }, { onConflict: ['student_id', 'budget_month_id'] });

        if (error) throw error;

        showMessage(editMonthCostMessage, `تم تحديث تكلفة الشهر للطالب ${studentName} بنجاح.`, 'success');

        // Update the student card UI
        await updateStudentCardUI(studentId);

        // Close modal after short delay
        setTimeout(() => {
            closeEditMonthCostModal();
        }, 1500);

    } catch (error) {
        console.error('Error updating student month cost:', error);
        showMessage(editMonthCostMessage, `خطأ في تحديث التكلفة: ${error.message}`, 'error');
    } finally {
        // Re-enable save button
        if (saveBtn) {
            saveBtn.disabled = false;
            saveBtn.textContent = 'حفظ';
        }
    }
};

const closeEditMonthCostModal = () => {
    const editMonthCostModal = document.getElementById('edit-month-cost-modal');
    const editMonthCostMessage = document.getElementById('edit-month-cost-message');

    if (editMonthCostModal) {
        editMonthCostModal.classList.remove('active');
        editMonthCostModal.removeAttribute('data-student-id');
        editMonthCostModal.removeAttribute('data-student-name');
    }

    if (editMonthCostMessage) {
        editMonthCostMessage.style.display = 'none';
    }

    document.body.style.overflow = '';
};

// Make functions globally available for onclick handlers
window.editPaymentFromHistory = editPaymentFromHistory;
window.deletePaymentFromHistory = deletePaymentFromHistory;
window.toggleCalculationBox = toggleCalculationBox;
window.toggleEditCalculationBox = toggleEditCalculationBox;
window.handlePaymentSelection = handlePaymentSelection;
window.toggleAllPaymentSelection = toggleAllPaymentSelection;
window.deleteSelectedPayments = deleteSelectedPayments;
window.openEditMonthCostModal = openEditMonthCostModal;

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', () => {
    console.log('Page loaded.');

    // لا تضيف أي كود متعلق بالـ sidebar هنا!
    // الـ sidebar يتم التعامل معه بواسطة shared_components/sidebar.js

    initializePage();
});
