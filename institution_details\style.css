/* Content copied from c:\Users\<USER>\OneDrive\سطح المكتب\مشروعي\institutions_section\style.css */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

:root {
    /* Main Colors */
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;

    /* Backgrounds */
    --body-bg: #f5f7fa;
    --card-bg: #ffffff;

    /* Borders */
    --border-color: #e1e8ed;
    --border-radius: 8px;

    /* Typography */
    --text-dark: #34495e;
    --text-muted: #7f8c8d;
    --text-light: #ffffff;

    /* Shadow */
    --card-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    --hover-shadow: 0 5px 25px rgba(0, 0, 0, 0.12);
    --button-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--body-bg);
    color: var(--text-dark);
    line-height: 1.6;
}

/* Dashboard Container */
.dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.dashboard-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-light);
    padding: 20px;
    text-align: center;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
}

.header-content h1 {
    font-size: 1.8rem;
    margin-bottom: 5px;
    font-weight: 700;
}

.header-content p {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 300;
}

.header-content h1 i {
    margin-left: 10px;
}

/* Main Content */
.dashboard-main {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    width: 100%;
}

/* Stats Cards */
.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.stat-icon {
    font-size: 2rem;
    margin-left: 15px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-card:nth-child(1) .stat-icon {
    color: var(--primary-color);
    background-color: rgba(52, 152, 219, 0.1);
}

.stat-card:nth-child(2) .stat-icon {
    color: var(--success-color);
    background-color: rgba(46, 204, 113, 0.1);
}

.stat-card:nth-child(3) .stat-icon {
    color: var(--warning-color);
    background-color: rgba(243, 156, 18, 0.1);
}

.stat-info h3 {
    font-size: 1.8rem;
    margin-bottom: 5px;
    font-weight: 700;
}

.stat-info p {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Controls Section */
.controls-section {
    margin-bottom: 20px;
}

.control-card, .table-card, .form-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa; /* Light header background */
}

.card-header h2 {
    font-size: 1.2rem;
    font-weight: 500;
    margin: 0;
}

.card-body {
    padding: 20px 30px; /* Adjust padding */
    max-height: 75vh; /* Limit height and allow scrolling */
    overflow-y: auto;
}

.controls-grid {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 15px;
    align-items: center;
}

.control-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var(--text-light);
    transition: all 0.3s ease;
    box-shadow: var(--button-shadow);
}

.control-btn i {
    font-size: 1rem;
}

.add-btn {
    background-color: var(--success-color);
}

.add-btn:hover {
    background-color: #27ae60;
}

.print-btn {
    background-color: var(--primary-color);
}

.print-btn:hover {
    background-color: var(--primary-dark);
}

.search-container {
    display: flex;
    max-width: 100%;
    position: relative;
}

#search-input {
    width: 100%;
    padding: 10px 45px 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

#search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.25);
}

.search-btn {
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 1rem;
    padding: 5px;
    cursor: pointer;
}

/* Table */
.table-section {
    margin-bottom: 20px;
}

.badge {
    background-color: var(--primary-color);
    color: var(--text-light);
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.table-responsive {
    overflow-x: auto;
}

/* Updated ID for groups table */
#groups-table {
    width: 100%;
    border-collapse: collapse;
}

#groups-table th,
#groups-table td {
    padding: 12px 15px;
    text-align: right;
    vertical-align: middle; /* Align content vertically */
}

#groups-table th {
    background-color: rgba(52, 152, 219, 0.05);
    color: var(--secondary-color);
    font-weight: 500;
    border-bottom: 1px solid var(--border-color);
    white-space: nowrap; /* Prevent header text wrapping */
}

#groups-table tbody tr {
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.3s ease;
}

#groups-table tbody tr:last-child {
    border-bottom: none;
}

#groups-table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

#groups-table .loading-message {
    text-align: center;
    color: var(--text-muted);
    padding: 20px;
}

/* Style for links in table */
#groups-table td a {
    color: var(--primary-color);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}
#groups-table td a:hover {
    text-decoration: underline;
    color: var(--primary-dark);
}
#groups-table td a i {
    font-size: 0.9em;
}


.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px 8px;
    margin: 0 3px;
    font-size: 1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.action-btn.edit-btn {
    color: var(--warning-color);
}

.action-btn.edit-btn:hover {
    background-color: rgba(243, 156, 18, 0.1);
}

.action-btn.delete-btn {
    color: var(--danger-color);
}

.action-btn.delete-btn:hover {
    background-color: rgba(231, 76, 60, 0.1);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    gap: 5px;
}

.pagination button {
    background-color: var(--light-color);
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination button:hover:not(.disabled) {
    background-color: var(--primary-color);
    color: var(--text-light);
}

.pagination-ellipsis {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 5px;
    color: var(--text-muted);
}
.pagination button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
.pagination button.active {
    background-color: var(--primary-color);
    color: var(--text-light);
    font-weight: bold;
}


/* Enhanced Form Section - Modal Styling */
.form-section {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px); /* Add blur effect to background */
    -webkit-backdrop-filter: blur(4px); /* For Safari */
    justify-content: center;
    align-items: center;
    padding: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.form-section.show {
    display: flex;
    opacity: 1;
}

.form-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    width: 100%;
    max-width: 800px; /* Increased width for group form */
    margin: auto;
    position: relative;
    transform: translateY(30px);
    transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    overflow: hidden;
}

.form-section.show .form-card {
    transform: translateY(0);
}

.card-header {
    padding: 15px 25px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-btn:hover {
    color: var(--danger-color);
    background-color: rgba(231, 76, 60, 0.1);
}

.card-body {
    padding: 20px 30px; /* Adjust padding */
    max-height: 75vh; /* Limit height and allow scrolling */
    overflow-y: auto;
}

/* Ensure this .form-row style is used inside the modal */
.form-row {
    display: grid;
    /* Adjust grid columns based on content, allow flexibility */
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 15px;
}

/* Style for fieldsets */
.form-fieldset {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px 20px 20px 20px; /* Add padding */
    margin-bottom: 25px; /* Space between fieldsets */
    position: relative;
}

.form-fieldset legend {
    font-weight: 600;
    color: var(--primary-dark);
    padding: 0 10px; /* Padding around legend text */
    margin-right: 10px; /* Position legend correctly in RTL */
    font-size: 1.1rem;
}

/* Style for required fields */
.required {
    color: var(--danger-color);
    margin-right: 3px;
}

/* VAT Details Container Styling */
.vat-details {
    display: none; /* Hide by default */
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px dashed var(--border-color);
}

/* Show VAT details when the fieldset has the 'show-vat' class */
.form-fieldset.show-vat .vat-details {
    display: block;
}

/* Read-only input styling */
input[readonly] {
    background-color: #f8f9fa; /* Light grey background */
    cursor: not-allowed; /* Indicate non-editable */
    opacity: 0.8;
}

/* Checkbox styling */
.form-group-checkbox {
    display: flex;
    align-items: center; /* Align checkbox and label vertically */
    padding-top: 25px; /* Align with input fields */
}

.form-group-checkbox label {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 0; /* Remove default margin */
}

.form-group-checkbox input[type="checkbox"] {
    width: auto; /* Override default width */
    margin-left: 8px; /* Space between checkbox and text */
    accent-color: var(--primary-color); /* Style checkbox color */
    height: 18px;
    width: 18px;
}

/* File input styling */
.form-group input[type="file"] {
    padding: 8px; /* Adjust padding */
    border: 1px dashed var(--border-color); /* Dashed border */
    background-color: #f9f9f9;
}

.form-group input[type="file"]::file-selector-button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    background-color: var(--primary-color);
    color: var(--text-light);
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-left: 10px; /* Space in RTL */
}

.form-group input[type="file"]::file-selector-button:hover {
    background-color: var(--primary-dark);
}

/* Small text hint */
.form-group small {
    display: block;
    margin-top: 5px;
    font-size: 0.85rem;
    color: var(--text-muted);
}

/* Responsive adjustments for modal */
@media (max-width: 768px) {
    /* ... existing responsive styles ... */
    .form-row {
        grid-template-columns: 1fr; /* Stack form elements on smaller screens */
        gap: 15px; /* Adjusted gap for stacked */
    }
    .form-group-checkbox {
        padding-top: 0; /* Adjust alignment when stacked */
        margin-top: 10px;
    }
     .form-card {
        max-width: 95%; /* Allow wider modal on smaller screens */
    }
}

/* Trip Type Selection Styling */
.trip-type-options {
    display: flex;
    flex-wrap: wrap; /* Allow wrapping on smaller screens */
    gap: 15px 25px; /* Row and column gap */
    justify-content: center; /* Center options */
    padding: 10px 0;
}

.form-group-radio {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.form-group-radio input[type="radio"] {
    cursor: pointer;
    /* Optional: Add custom radio button styles if desired */
}

.form-group-radio label {
    cursor: pointer;
    font-weight: 500;
    margin: 0; /* Remove default label margin */
}

/* Ensure hidden fieldsets don't affect layout */
fieldset[style*="display: none"] {
    margin: 0;
    padding: 0;
    border: none;
}

/* Read-only input styling */
input[readonly] {
    background-color: #f8f9fa; /* Light grey background */
    cursor: not-allowed; /* Indicate non-editable */
    opacity: 0.8;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea { /* Added textarea */
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: all 0.3s ease;
    font-family: inherit; /* Ensure textarea uses the same font */
}

/* Specific style for time input */
.form-group input[type="time"] {
    /* Add specific styling if needed, e.g., padding */
    padding: 10px;
}


.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus { /* Added textarea */
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.25);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.submit-btn,
.cancel-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.submit-btn {
    background-color: var (--success-color);
    color: var(--text-light);
}

.submit-btn:hover {
    background-color: #27ae60;
}

.cancel-btn {
    background-color: var(--light-color);
    color: var(--text-dark);
}

.cancel-btn:hover {
    background-color: #bdc3c7;
}

/* Message Styles */
.message {
    margin-top: 15px;
    padding: 12px;
    border-radius: var(--border-radius);
    text-align: center;
    font-weight: 500;
    display: none;
}

.message.show {
    display: block;
}

.message.info {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.message.success {
    background-color: rgba(46, 204, 113, 0.1);
    color: var (--success-color);
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.message.error {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

/* Footer */
.dashboard-footer {
    background-color: var(--card-bg);
    text-align: center;
    padding: 15px;
    color: var(--text-muted);
    border-top: 1px solid var(--border-color);
    margin-top: auto;
}

/* Responsive */
@media (max-width: 992px) {
    .dashboard-main {
        padding: 15px;
    }
     #groups-table th,
    #groups-table td {
        padding: 10px 8px; /* Reduce padding on smaller screens */
        font-size: 0.85em; /* Slightly smaller font */
    }
}

@media (max-width: 768px) {
    .stats-section {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .controls-grid {
        grid-template-columns: 1fr;
    }

    .search-container {
        order: -1;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

@media (max-width: 576px) {
    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .badge {
        margin-top: 5px;
        align-self: flex-start;
    }

    .form-actions {
        flex-direction: column;
    }

    .submit-btn,
    .cancel-btn {
        width: 100%;
    }

    .header-content h1 {
        font-size: 1.5rem;
    }
     #groups-table th,
    #groups-table td {
        font-size: 0.8em; /* Even smaller font on very small screens */
    }
}

/* Status indicator styles (copied, might not be needed for groups) */
.status-active { color: #2ecc71; font-weight: bold; }
.status-inactive { color: #e74c3c; font-weight: bold; }
.status-maintenance { color: var(--warning-color); font-weight: bold; }
.status-stopped { color: var(--danger-color); font-weight: bold; }

/* --- New Card Styles --- */

.groups-cards-container {
    display: grid;
    /* Create two columns for Departure/Return side-by-side */
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); /* Adjust minmax as needed */
    gap: 25px; /* Gap between cards */
    padding: 10px 0;
}

.group-card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden; /* Ensure content stays within rounded corners */
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
}

.group-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--hover-shadow);
}

.card-header-trip {
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    background-color: #f8f9fa; /* Light header background */
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header-trip h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-dark);
}

/* Style header based on trip type */
.departure-card .card-header-trip {
    background-color: rgba(52, 152, 219, 0.1); /* Light blue tint for departure */
}
.return-card .card-header-trip {
    background-color: rgba(46, 204, 113, 0.1); /* Light green tint for return */
}
.departure-card .card-header-trip h3 { color: var(--primary-dark); }
.return-card .card-header-trip h3 { color: #1E8449; } /* Darker green */


.card-actions {
    display: flex;
    gap: 5px;
}

.card-body-trip {
    padding: 15px;
    display: grid;
    grid-template-columns: 1fr 1fr; /* Two columns for info items */
    gap: 10px 15px; /* Row and column gap */
    font-size: 0.9rem;
}

.info-item {
    background-color: #fdfdfe;
    padding: 8px 10px;
    border-radius: 4px;
    border: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column; /* Stack label and value */
}

.info-item-full {
    grid-column: 1 / -1; /* Make item span both columns */
}

.info-item .label {
    font-weight: 500;
    color: var(--text-muted);
    margin-bottom: 4px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 5px;
}
.info-item .label i {
    width: 15px; /* Align icons */
    text-align: center;
    color: var(--primary-color); /* Default icon color */
}
.return-card .info-item .label i {
     color: #1E8449; /* Green icons for return card */
}


.info-item .value {
    font-weight: 600;
    color: var(--text-dark);
    word-wrap: break-word; /* Wrap long links or text */
}

.info-item .value a {
    color: var(--primary-color);
    text-decoration: none;
}
.info-item .value a:hover {
    text-decoration: underline;
}
.info-item .value a i {
    font-size: 0.8em;
    margin-right: 3px;
}

.cost-item {
    margin-top: 10px;
    border-top: 1px dashed var(--border-color);
    padding-top: 10px;
    background-color: transparent;
    border: none;
}

.cost-item .label i {
    color: var(--warning-color); /* Gold color for cost icon */
}

.cost-value {
    font-size: 1.1em;
    font-weight: 700;
    color: var(--primary-dark);
}

.notes-item {
    margin-top: 5px;
    background-color: #fffaf0; /* Light yellow background for notes */
    border-color: #fdeccf;
}
.notes-item .label i {
    color: #D9A600; /* Darker yellow for notes icon */
}


/* Loading Placeholder Styles (if not already defined well) */
.loading-placeholder {
    grid-column: 1 / -1; /* Span full width if using grid */
    text-align: center;
    padding: 40px 20px;
    font-size: 1.1rem;
    color: var(--text-muted);
}

.loading-placeholder i {
    margin-left: 10px;
    font-size: 1.4rem;
    color: var(--primary-color);
}
.loading-placeholder.error {
    color: var(--danger-color);
}
.loading-placeholder.error i {
    color: var(--danger-color);
}


/* Responsive adjustments for cards */
@media (max-width: 768px) {
    .groups-cards-container {
        grid-template-columns: 1fr; /* Stack cards on smaller screens */
    }
    .card-body-trip {
        grid-template-columns: 1fr; /* Stack info items within card */
    }
    .info-item-full {
        grid-column: 1; /* Ensure full width items stay full width */
    }
}
