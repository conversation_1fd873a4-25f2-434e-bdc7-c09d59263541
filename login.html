<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام الإدارة المالية</title>
    <!-- Include Shared Styles -->
    <link rel="stylesheet" href="shared_styles.css">
    <!-- Include Login Specific Styles -->
    <link rel="stylesheet" href="login.css">
    <!-- Include Supabase (needed for createClient, though not used directly here) -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Include Config -->
    <script src="config.js"></script>
    <!-- Defer Login Script -->
    <script defer src="login.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts (Example: Tajawal) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navbar (Placeholder - No links needed on login) -->
    <nav class="main-navbar">
        <div class="navbar-brand">
            <i class="fas fa-chart-line"></i>
            نظام الإدارة المالية
        </div>
        <!-- No links or user section on login page -->
    </nav>

    <!-- Main Content -->
    <div class="container">
        <main class="login-main">
            <div class="login-card card">
                <div class="card-header">
                    <h2><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</h2>
                </div>
                <div class="card-body">
                    <form id="login-form">
                        <p class="login-instructions">الرجاء إدخال رقم الهاتف المعتمد للدخول.</p>
                        <div class="form-group">
                            <label for="phone-number">رقم الهاتف</label>
                            <input type="tel" id="phone-number" name="phone-number" required placeholder="مثال: 050xxxxxxx" inputmode="tel">
                        </div>
                        <div id="login-message" class="message" style="display: none;"></div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary login-btn">
                                <i class="fas fa-lock"></i> دخول
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <!-- Footer (Placeholder - No home button needed on login) -->
    <footer class="main-footer">
        <p>&copy; 2024 نظام الإدارة المالية</p>
    </footer>
</body>
</html>
