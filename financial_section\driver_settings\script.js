// --- Auth Check ---
checkAuth('../../login.html');

// --- Supabase Initialization ---
let _supabase;
try {
    // Ensure config.js with SUPABASE_URL and SUPABASE_ANON_KEY is loaded before this script
    if (typeof supabase !== 'undefined' && supabase.createClient && typeof SUPABASE_URL !== 'undefined' && typeof SUPABASE_ANON_KEY !== 'undefined') {
        _supabase = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('Supabase client initialized for driver settings.');
    } else {
        console.error('Supabase client or config variables (SUPABASE_URL, SUPABASE_ANON_KEY) not found.');
        throw new Error('Supabase client or configuration not available.');
    }
} catch (error) {
    console.error('Error initializing Supabase client:', error);
    alert('خطأ فادح: لا يمكن الاتصال بقاعدة البيانات. تأكد من تحميل ملف config.js بشكل صحيح.');
    // Optionally disable functionality or redirect
}

// --- DOM Elements ---
const settingsTableBody = document.getElementById('settings-table-body');
const listMessage = document.getElementById('list-message');
const backToMainDashboardBtn = document.getElementById('back-to-main-dashboard-btn');

// Debug: Check if elements exist
console.log('DOM Elements check:');
console.log('settingsTableBody:', settingsTableBody);
console.log('listMessage:', listMessage);
console.log('backToMainDashboardBtn:', backToMainDashboardBtn);

// --- State Variables ---
let allDrivers = []; // Will store { id, name, work_shift }
let driverSettingsMap = {}; // Map driver_id -> { id (setting_id), default_base_salary, current_balance } // Added current_balance

// --- Helper Functions ---
const showMessage = (element, message, type = 'info', duration = 5000) => {
    if (!element) return;
    element.textContent = message;
    element.className = `message ${type} show`;
    element.style.display = 'block';
    // Auto-hide logic for success/info messages
    if (duration > 0 && (type === 'success' || type === 'info')) {
        setTimeout(() => {
            // Hide only if the message hasn't changed in the meantime
            if (element.textContent === message) {
                element.style.display = 'none';
                element.classList.remove('show');
            }
        }, duration);
    }
};

// Function to translate work shift (handles both Arabic and English values)
const translateWorkShift = (shift) => {
    if (!shift) return 'غير محدد';

    switch (shift.toLowerCase()) {
        case 'morning':
        case 'صباحي':
            return 'صباحي';
        case 'evening':
        case 'مسائي':
            return 'مسائي';
        case 'full_day':
        case 'دوام كامل':
        case 'كامل':
            return 'دوام كامل';
        default:
            return shift; // Return original value
    }
};

// Function to format currency (ensure it handles null/undefined)
const formatCurrency = (amount) => {
    const value = parseFloat(amount);
    return isNaN(value) ? '0.00' : value.toFixed(2);
};

// --- Data Fetching and Rendering ---

// Fetch all drivers with name and work_shift
const fetchDrivers = async () => {
    if (!_supabase) {
        showMessage(listMessage, 'خطأ: اتصال قاعدة البيانات غير متاح.', 'error', 0);
        return; // Stop if supabase is not initialized
    }
    try {
        console.log('Starting to fetch drivers...');
        const { data, error } = await _supabase
            .from('drivers')
            .select('id, name, work_shift') // Fetch id, name, and work_shift
            .order('name', { ascending: true });

        console.log('Supabase response:', { data, error });

        if (error) throw error;
        allDrivers = data || [];
        console.log(`Fetched ${allDrivers.length} drivers:`, allDrivers);
    } catch (error) {
        console.error('Error fetching drivers:', error);
        showMessage(listMessage, `خطأ في جلب قائمة السائقين: ${error.message}`, 'error', 0);
        throw error; // Re-throw to stop execution if drivers are essential
    }
};

// Fetch existing driver default settings including current_balance and opening_balance
const fetchDriverSettings = async () => {
    if (!_supabase) return; // Stop if supabase is not initialized
    try {
        const { data, error } = await _supabase
            .from('driver_default_settings')
            .select('id, driver_id, default_base_salary, current_balance, opening_balance');
        if (error) throw error;
        driverSettingsMap = (data || []).reduce((map, setting) => {
            map[setting.driver_id] = setting;
            return map;
        }, {});
        console.log('Fetched driver default settings (including balances).');
    } catch (error) {
        console.error('Error fetching driver settings:', error);
        showMessage(listMessage, `خطأ في جلب الإعدادات الافتراضية: ${error.message}`, 'error');
        // Don't re-throw, allow rendering even if settings fetch fails
    }
};

// Render the settings table with name, work_shift, salary input, opening balance input, current balance input, and save button
const renderSettingsTable = () => {
    console.log('renderSettingsTable called');
    console.log('settingsTableBody:', settingsTableBody);
    console.log('allDrivers:', allDrivers);

    if (!settingsTableBody) {
        console.error('settingsTableBody element not found!');
        return;
    }

    settingsTableBody.innerHTML = ''; // Clear existing rows

    if (allDrivers.length === 0) {
        console.log('No drivers to display');
        settingsTableBody.innerHTML = `<tr><td colspan="8" class="loading-message">لا يوجد سائقين لعرضهم.</td></tr>`;
        updateStatistics();
        return;
    }

    console.log(`Rendering ${allDrivers.length} drivers...`);

    allDrivers.forEach(driver => {
        const setting = driverSettingsMap[driver.id];
        const currentSalary = setting ? parseFloat(setting.default_base_salary) : 0;
        const openingBalance = setting ? parseFloat(setting.opening_balance || 0) : 0;
        const currentBalance = setting ? parseFloat(setting.current_balance || 0) : 0;

        const row = document.createElement('tr');
        row.dataset.driverId = driver.id; // Store driver ID on the row

        // Determine status
        const hasSettings = !!setting;
        const isConfigured = hasSettings && currentSalary > 0;
        const statusClass = isConfigured ? 'status-configured' : (hasSettings ? 'status-pending' : 'status-incomplete');
        const statusText = isConfigured ? 'مُعد' : (hasSettings ? 'بحاجة إعداد' : 'غير مُعد');

        row.innerHTML = `
            <td class="select-col">
                <input type="checkbox" class="driver-checkbox" value="${driver.id}">
            </td>
            <td>${driver.name || 'اسم غير معروف'}</td>
            <td>${translateWorkShift(driver.work_shift)}</td>
            <td>
                <input type="number"
                       class="salary-input"
                       value="${currentSalary.toFixed(2)}"
                       step="0.01"
                       min="0"
                       placeholder="0.00"
                       data-original-value="${currentSalary.toFixed(2)}">
            </td>
            <td>
                <input type="number"
                       class="opening-balance-input"
                       value="${openingBalance.toFixed(2)}"
                       step="0.01"
                       placeholder="0.00"
                       data-original-value="${openingBalance.toFixed(2)}">
            </td>
            <td>
                <span class="balance-display ${currentBalance > 0 ? 'balance-positive' : currentBalance < 0 ? 'balance-negative' : 'balance-zero'} clickable"
                      onclick="showBalanceInfo('${driver.id}', '${driver.name}')">
                    ${currentBalance.toFixed(2)} ريال
                </span>
            </td>
            <td>
                <span class="status-badge ${statusClass}">${statusText}</span>
            </td>
            <td>
                <button class="control-btn save-row-btn" title="حفظ هذا السائق" disabled>
                    <i class="fas fa-save"></i>
                </button>
            </td>
        `;

        const salaryInput = row.querySelector('.salary-input');
        const openingBalanceInput = row.querySelector('.opening-balance-input');
        const saveButton = row.querySelector('.save-row-btn');

        // Function to check if save button should be enabled
        const checkEnableSave = () => {
            const originalSalary = parseFloat(salaryInput.dataset.originalValue);
            const newSalaryStr = salaryInput.value.trim();
            const originalOpeningBalance = parseFloat(openingBalanceInput.dataset.originalValue);
            const newOpeningBalanceStr = openingBalanceInput.value.trim();

            let salaryChanged = false;
            let openingBalanceChanged = false;
            let salaryValid = false;
            let openingBalanceValid = false;

            // Validate and check salary change
            if (newSalaryStr !== '' && !isNaN(newSalaryStr)) {
                const newSalary = parseFloat(newSalaryStr);
                if (newSalary >= 0) {
                    salaryValid = true;
                    if (Math.abs(newSalary - originalSalary) > 0.001) {
                        salaryChanged = true;
                    }
                }
            } else if (newSalaryStr === '') {
                 salaryValid = false;
            }

            // Validate and check opening balance change
            if (newOpeningBalanceStr !== '' && !isNaN(newOpeningBalanceStr)) {
                const newOpeningBalance = parseFloat(newOpeningBalanceStr);
                openingBalanceValid = true;
                if (Math.abs(newOpeningBalance - originalOpeningBalance) > 0.001) {
                    openingBalanceChanged = true;
                }
            } else if (newOpeningBalanceStr === '') {
                 openingBalanceValid = false;
            }

            // Enable button if at least one value changed AND all inputs are valid
            const anyChanged = salaryChanged || openingBalanceChanged;
            const allValid = salaryValid && openingBalanceValid;
            saveButton.disabled = !(anyChanged && allValid);
        };

        // Add event listeners to inputs
        salaryInput.addEventListener('input', checkEnableSave);
        openingBalanceInput.addEventListener('input', checkEnableSave);

        // Add event listener for the row's save button
        saveButton.addEventListener('click', () => {
            // Pass input elements to the save handler
            handleSaveDriverSetting(driver.id, salaryInput, openingBalanceInput, saveButton);
        });

        settingsTableBody.appendChild(row);
    });

    // Update statistics after rendering
    updateStatistics();
    updateDisplayedCount();
};

// Update statistics cards
const updateStatistics = () => {
    const totalDrivers = allDrivers.length;
    const configuredDrivers = allDrivers.filter(driver => {
        const setting = driverSettingsMap[driver.id];
        return setting && parseFloat(setting.default_base_salary) > 0;
    }).length;
    const pendingDrivers = totalDrivers - configuredDrivers;
    const totalSalaries = Object.values(driverSettingsMap)
        .reduce((sum, setting) => sum + parseFloat(setting.default_base_salary || 0), 0);

    // Update DOM elements
    const totalElement = document.getElementById('total-drivers-count');
    const configuredElement = document.getElementById('configured-drivers-count');
    const pendingElement = document.getElementById('pending-drivers-count');
    const salariesElement = document.getElementById('total-salaries');

    if (totalElement) totalElement.textContent = totalDrivers;
    if (configuredElement) configuredElement.textContent = configuredDrivers;
    if (pendingElement) pendingElement.textContent = pendingDrivers;
    if (salariesElement) salariesElement.textContent = totalSalaries.toFixed(2);
};

// Update displayed count
const updateDisplayedCount = () => {
    const displayedElement = document.getElementById('displayed-count');
    if (displayedElement) {
        const visibleRows = settingsTableBody.querySelectorAll('tr:not([style*="display: none"])');
        displayedElement.textContent = `${visibleRows.length} سائق`;
    }
};

// Filter drivers function
const filterDrivers = () => {
    const searchTerm = document.getElementById('driver-search')?.value.toLowerCase() || '';
    const shiftFilter = document.getElementById('work-shift-filter')?.value || '';

    const rows = settingsTableBody.querySelectorAll('tr');
    rows.forEach(row => {
        const driverId = row.dataset.driverId;
        if (!driverId) return; // Skip non-driver rows

        const driver = allDrivers.find(d => d.id === driverId);
        if (!driver) return;

        const nameMatch = driver.name.toLowerCase().includes(searchTerm);
        const shiftMatch = !shiftFilter || translateWorkShift(driver.work_shift) === shiftFilter;

        row.style.display = (nameMatch && shiftMatch) ? '' : 'none';
    });

    updateDisplayedCount();
};

// Toggle select all
const toggleSelectAll = () => {
    const selectAllCheckbox = document.getElementById('select-all');
    const driverCheckboxes = document.querySelectorAll('.driver-checkbox');

    driverCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
};

// Save all changes
const saveAllChanges = async () => {
    const changedRows = Array.from(settingsTableBody.querySelectorAll('tr')).filter(row => {
        const saveBtn = row.querySelector('.save-row-btn');
        return saveBtn && !saveBtn.disabled;
    });

    if (changedRows.length === 0) {
        showMessage(listMessage, 'لا توجد تغييرات لحفظها.', 'info', 3000);
        return;
    }

    const saveAllBtn = document.querySelector('.save-all-btn');
    const originalText = saveAllBtn.innerHTML;
    saveAllBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    saveAllBtn.disabled = true;

    let successCount = 0;
    let errorCount = 0;

    for (const row of changedRows) {
        try {
            const saveBtn = row.querySelector('.save-row-btn');
            await saveBtn.click();
            successCount++;
        } catch (error) {
            errorCount++;
            console.error('Error saving row:', error);
        }
    }

    saveAllBtn.innerHTML = originalText;
    saveAllBtn.disabled = false;

    if (errorCount === 0) {
        showMessage(listMessage, `تم حفظ ${successCount} سائق بنجاح!`, 'success', 3000);
    } else {
        showMessage(listMessage, `تم حفظ ${successCount} سائق، فشل ${errorCount} سائق.`, 'warning', 5000);
    }
};

// Refresh data
const refreshData = async () => {
    const refreshBtn = document.querySelector('.refresh-btn');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
    refreshBtn.disabled = true;

    try {
        await fetchDrivers();
        await fetchDriverSettings();
        renderSettingsTable();
        showMessage(listMessage, 'تم تحديث البيانات بنجاح!', 'success', 2000);
    } catch (error) {
        showMessage(listMessage, `خطأ في تحديث البيانات: ${error.message}`, 'error', 5000);
    } finally {
        refreshBtn.innerHTML = originalText;
        refreshBtn.disabled = false;
    }
};

// Add event listeners for search and filter
document.addEventListener('DOMContentLoaded', () => {
    const searchInput = document.getElementById('driver-search');
    const shiftFilter = document.getElementById('work-shift-filter');

    if (searchInput) {
        searchInput.addEventListener('input', filterDrivers);
    }

    if (shiftFilter) {
        shiftFilter.addEventListener('change', filterDrivers);
    }
});

// --- Save Logic (Individual Row) ---

// Updated to accept salary and opening balance inputs only
const handleSaveDriverSetting = async (driverId, salaryInputElement, openingBalanceInputElement, saveButton) => {
    if (!driverId || !salaryInputElement || !openingBalanceInputElement || !saveButton || !_supabase) return;

    const driverName = salaryInputElement.closest('tr')?.cells[1]?.textContent || 'غير معروف';
    const newSalaryStr = salaryInputElement.value.trim();
    const newOpeningBalanceStr = openingBalanceInputElement.value.trim();
    let newSalary, newOpeningBalance;
    let salaryChanged = false, openingBalanceChanged = false;

    // Final validation before saving
    let validationError = false;
    salaryInputElement.style.borderColor = '';
    openingBalanceInputElement.style.borderColor = '';

    // Validate salary
    if (newSalaryStr === '' || isNaN(newSalaryStr) || parseFloat(newSalaryStr) < 0) {
        showMessage(listMessage, `الراتب للسائق "${driverName}" يجب أن يكون رقمًا موجبًا أو صفرًا.`, 'error', 0);
        salaryInputElement.style.borderColor = 'red';
        validationError = true;
    } else {
        newSalary = parseFloat(newSalaryStr);
        const originalSalary = parseFloat(salaryInputElement.dataset.originalValue);
        if (Math.abs(newSalary - originalSalary) > 0.001) {
            salaryChanged = true;
        }
    }

    // Validate opening balance
    if (newOpeningBalanceStr === '' || isNaN(newOpeningBalanceStr)) {
        showMessage(listMessage, `الرصيد الافتتاحي للسائق "${driverName}" يجب أن يكون رقمًا.`, 'error', 0);
        openingBalanceInputElement.style.borderColor = 'red';
        validationError = true;
    } else {
        newOpeningBalance = parseFloat(newOpeningBalanceStr);
        const originalOpeningBalance = parseFloat(openingBalanceInputElement.dataset.originalValue);
        if (Math.abs(newOpeningBalance - originalOpeningBalance) > 0.001) {
            openingBalanceChanged = true;
        }
    }

    if (validationError) return;

    // Check if any changes were made
    if (!salaryChanged && !openingBalanceChanged) {
        showMessage(listMessage, `لم يتم إجراء أي تغييرات للسائق "${driverName}".`, 'info', 3000);
        return;
    }

    // Disable button and show loading
    saveButton.disabled = true;
    const originalButtonContent = saveButton.innerHTML;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    try {
        // Prepare update data
        const updateData = {};
        if (salaryChanged) updateData.default_base_salary = newSalary;
        if (openingBalanceChanged) updateData.opening_balance = newOpeningBalance;

        // Check if driver settings exist
        const existingSetting = driverSettingsMap[driverId];

        let result;
        if (existingSetting) {
            // Update existing record
            result = await _supabase
                .from('driver_default_settings')
                .update(updateData)
                .eq('driver_id', driverId);
        } else {
            // Insert new record - trigger will automatically set current_balance = opening_balance
            const insertData = {
                driver_id: driverId,
                default_base_salary: newSalary,
                opening_balance: newOpeningBalance
            };
            result = await _supabase
                .from('driver_default_settings')
                .insert([insertData]);
        }

        if (result.error) throw result.error;

        // Fetch the updated record to get the current_balance calculated by the trigger
        const { data: updatedData, error: fetchError } = await _supabase
            .from('driver_default_settings')
            .select('default_base_salary, opening_balance, current_balance')
            .eq('driver_id', driverId)
            .single();

        if (fetchError) throw fetchError;

        // Update local data with the values from database (including trigger-calculated current_balance)
        driverSettingsMap[driverId] = {
            ...driverSettingsMap[driverId],
            driver_id: driverId,
            default_base_salary: updatedData.default_base_salary,
            opening_balance: updatedData.opening_balance,
            current_balance: updatedData.current_balance
        };

        // Update original values in inputs
        salaryInputElement.dataset.originalValue = parseFloat(updatedData.default_base_salary).toFixed(2);
        openingBalanceInputElement.dataset.originalValue = parseFloat(updatedData.opening_balance).toFixed(2);

        // Update the balance display in the row
        const currentBalance = parseFloat(updatedData.current_balance);
        const balanceDisplay = salaryInputElement.closest('tr').querySelector('.balance-display');
        if (balanceDisplay) {
            balanceDisplay.textContent = `${currentBalance.toFixed(2)} ريال`;
            balanceDisplay.className = `balance-display ${currentBalance > 0 ? 'balance-positive' : currentBalance < 0 ? 'balance-negative' : 'balance-zero'} clickable`;
        }

        showMessage(listMessage, `تم حفظ إعدادات السائق "${driverName}" بنجاح.`, 'success', 3000);

    } catch (error) {
        console.error('Error saving driver settings:', error);
        showMessage(listMessage, `خطأ في حفظ إعدادات السائق "${driverName}": ${error.message}`, 'error', 0);
    } finally {
        // Restore button
        saveButton.innerHTML = originalButtonContent;
        saveButton.disabled = false;
    }
};

// Function to show balance info modal with detailed monthly closings
const showBalanceInfo = async (driverId, driverName) => {
    const modal = document.getElementById('balance-info-modal');
    const driverNameSpan = document.getElementById('balance-info-driver-name');
    const tbody = document.getElementById('balance-breakdown-tbody');
    const messageArea = document.getElementById('balance-info-message');

    if (!modal || !tbody) return;

    // Set driver name
    if (driverNameSpan) driverNameSpan.textContent = driverName;

    // Clear previous data
    tbody.innerHTML = '<tr><td colspan="3" class="loading-message"><i class="fas fa-spinner fa-spin"></i> جاري تحميل التفاصيل...</td></tr>';

    // Show modal
    modal.style.display = 'block';

    try {
        // Get driver settings
        const setting = driverSettingsMap[driverId];

        if (!setting) {
            tbody.innerHTML = '<tr><td colspan="3" class="loading-message">لا توجد إعدادات لهذا السائق</td></tr>';
            return;
        }

        // Fetch monthly closings for this driver
        const { data: monthlyClosings, error: closingsError } = await _supabase
            .from('driver_monthly_closings')
            .select(`
                *,
                budget_months!inner(
                    id,
                    month_name,
                    month_number,
                    budget_years!inner(
                        year_number
                    )
                )
            `)
            .eq('driver_id', driverId)
            .order('closing_date', { ascending: false });

        if (closingsError) {
            console.error('Error fetching monthly closings:', closingsError);
        }

        // Clear loading message
        tbody.innerHTML = '';

        // Add basic driver info
        const defaultSalary = parseFloat(setting.default_base_salary) || 0;
        const openingBalance = parseFloat(setting.opening_balance) || 0;
        const currentBalance = parseFloat(setting.current_balance) || 0;

        // Header row for basic info
        const headerRow = document.createElement('tr');
        headerRow.innerHTML = `
            <td colspan="3" style="background: var(--primary-color); color: white; font-weight: bold; text-align: center;">
                <i class="fas fa-info-circle"></i> المعلومات الأساسية
            </td>
        `;
        tbody.appendChild(headerRow);

        // Default salary row
        const salaryRow = document.createElement('tr');
        salaryRow.innerHTML = `
            <td>الراتب الأساسي الشهري</td>
            <td>${defaultSalary.toFixed(2)} ريال</td>
            <td>الراتب المحدد في الإعدادات</td>
        `;
        tbody.appendChild(salaryRow);

        // Opening balance row
        if (openingBalance !== 0) {
            const openingRow = document.createElement('tr');
            openingRow.innerHTML = `
                <td>رصيد الافتتاح</td>
                <td class="${openingBalance > 0 ? 'balance-positive' : 'balance-negative'}">${openingBalance.toFixed(2)} ريال</td>
                <td>الرصيد الأولي عند بداية التسجيل</td>
            `;
            tbody.appendChild(openingRow);
        }

        // Current balance row
        const currentRow = document.createElement('tr');
        currentRow.innerHTML = `
            <td><strong>الرصيد الحالي</strong></td>
            <td class="${currentBalance > 0 ? 'balance-positive' : currentBalance < 0 ? 'balance-negative' : 'balance-zero'}"><strong>${currentBalance.toFixed(2)} ريال</strong></td>
            <td>الرصيد الإجمالي الحالي</td>
        `;
        tbody.appendChild(currentRow);

        // Monthly closings section
        if (monthlyClosings && monthlyClosings.length > 0) {
            // Header row for monthly closings
            const closingsHeaderRow = document.createElement('tr');
            closingsHeaderRow.innerHTML = `
                <td colspan="3" style="background: var(--info-color); color: white; font-weight: bold; text-align: center; padding-top: 20px;">
                    <i class="fas fa-calendar-check"></i> تفاصيل الإقفالات الشهرية (${monthlyClosings.length} شهر)
                </td>
            `;
            tbody.appendChild(closingsHeaderRow);

            // Add each monthly closing
            monthlyClosings.forEach((closing, index) => {
                const monthName = closing.budget_months?.month_name || 'غير محدد';
                const year = closing.budget_months?.budget_years?.year_number || '';
                const monthYear = `${monthName} ${year}`;

                // Month header
                const monthHeaderRow = document.createElement('tr');
                monthHeaderRow.innerHTML = `
                    <td colspan="3" style="background: var(--card-bg); font-weight: bold; border-top: 2px solid var(--border-color);">
                        <i class="fas fa-calendar"></i> ${monthYear}
                        <small style="float: left; color: var(--text-muted);">
                            ${new Date(closing.closing_date).toLocaleDateString('ar-SA')}
                        </small>
                    </td>
                `;
                tbody.appendChild(monthHeaderRow);

                // Previous balance
                const prevBalanceRow = document.createElement('tr');
                const prevBalance = parseFloat(closing.previous_balance_at_closing) || 0;
                prevBalanceRow.innerHTML = `
                    <td style="padding-right: 20px;">الرصيد السابق</td>
                    <td class="${prevBalance > 0 ? 'balance-positive' : prevBalance < 0 ? 'balance-negative' : 'balance-zero'}">${prevBalance.toFixed(2)} ريال</td>
                    <td>الرصيد من الشهر السابق</td>
                `;
                tbody.appendChild(prevBalanceRow);

                // Base salary
                const salaryAtClosingRow = document.createElement('tr');
                const salaryAtClosing = parseFloat(closing.base_salary_at_closing) || 0;
                salaryAtClosingRow.innerHTML = `
                    <td style="padding-right: 20px;">الراتب الأساسي</td>
                    <td>${salaryAtClosing.toFixed(2)} ريال</td>
                    <td>الراتب المستحق للشهر</td>
                `;
                tbody.appendChild(salaryAtClosingRow);

                // Commissions
                const commissionsRow = document.createElement('tr');
                const commissions = parseFloat(closing.total_commissions_at_closing) || 0;
                if (commissions > 0) {
                    commissionsRow.innerHTML = `
                        <td style="padding-right: 20px;">إجمالي العمولات</td>
                        <td class="balance-positive">+${commissions.toFixed(2)} ريال</td>
                        <td>العمولات المكتسبة</td>
                    `;
                    tbody.appendChild(commissionsRow);
                }

                // Deductions
                const deductionsRow = document.createElement('tr');
                const deductions = parseFloat(closing.total_deductions_at_closing) || 0;
                if (deductions > 0) {
                    deductionsRow.innerHTML = `
                        <td style="padding-right: 20px;">إجمالي الخصومات</td>
                        <td class="balance-negative">-${deductions.toFixed(2)} ريال</td>
                        <td>الخصومات المطبقة</td>
                    `;
                    tbody.appendChild(deductionsRow);
                }

                // Payments
                const paymentsRow = document.createElement('tr');
                const payments = parseFloat(closing.total_payments_at_closing) || 0;
                if (payments > 0) {
                    paymentsRow.innerHTML = `
                        <td style="padding-right: 20px;">إجمالي المدفوعات</td>
                        <td class="balance-negative">-${payments.toFixed(2)} ريال</td>
                        <td>المبالغ المدفوعة للسائق</td>
                    `;
                    tbody.appendChild(paymentsRow);
                }

                // Net due
                const netDueRow = document.createElement('tr');
                const netDue = parseFloat(closing.calculated_net_due_at_closing) || 0;
                netDueRow.innerHTML = `
                    <td style="padding-right: 20px;"><strong>صافي المستحق</strong></td>
                    <td class="${netDue > 0 ? 'balance-positive' : netDue < 0 ? 'balance-negative' : 'balance-zero'}"><strong>${netDue.toFixed(2)} ريال</strong></td>
                    <td>المبلغ المستحق بعد الحسابات</td>
                `;
                tbody.appendChild(netDueRow);

                // Final remaining balance
                const finalBalanceRow = document.createElement('tr');
                const finalBalance = parseFloat(closing.final_remaining_balance) || 0;
                finalBalanceRow.innerHTML = `
                    <td style="padding-right: 20px;"><strong>الرصيد النهائي</strong></td>
                    <td class="${finalBalance > 0 ? 'balance-positive' : finalBalance < 0 ? 'balance-negative' : 'balance-zero'}"><strong>${finalBalance.toFixed(2)} ريال</strong></td>
                    <td>الرصيد المتبقي بعد الإقفال</td>
                `;
                tbody.appendChild(finalBalanceRow);

                // Notes if available
                if (closing.notes && closing.notes.trim()) {
                    const notesRow = document.createElement('tr');
                    notesRow.innerHTML = `
                        <td style="padding-right: 20px;">ملاحظات</td>
                        <td colspan="2" style="font-style: italic; color: var(--text-muted);">${closing.notes}</td>
                    `;
                    tbody.appendChild(notesRow);
                }
            });
        } else {
            // No monthly closings found
            const noClosingsRow = document.createElement('tr');
            noClosingsRow.innerHTML = `
                <td colspan="3" style="text-align: center; color: var(--text-muted); font-style: italic; padding: 20px;">
                    <i class="fas fa-info-circle"></i> لا توجد إقفالات شهرية مسجلة لهذا السائق
                </td>
            `;
            tbody.appendChild(noClosingsRow);
        }

    } catch (error) {
        console.error('Error in showBalanceInfo:', error);
        tbody.innerHTML = '<tr><td colspan="3" class="loading-message">خطأ غير متوقع في تحميل التفاصيل</td></tr>';
    }
};

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log('Driver settings page loaded.');
    if (listMessage) listMessage.style.display = 'none';

    if (!_supabase) {
         if (settingsTableBody) settingsTableBody.innerHTML = `<tr><td colspan="8" class="loading-message error">خطأ في الاتصال بقاعدة البيانات.</td></tr>`;
         return;
    }

    try {
        settingsTableBody.innerHTML = `<tr><td colspan="8" class="loading-message"><i class="fas fa-spinner fa-spin"></i> جاري تحميل البيانات...</td></tr>`;
        await fetchDrivers();
        await fetchDriverSettings();
        renderSettingsTable();
    } catch (error) {
        if (settingsTableBody) settingsTableBody.innerHTML = `<tr><td colspan="8" class="loading-message error">فشل تحميل البيانات.</td></tr>`;
    }

    // Setup Event Listeners
    const refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshData);
    }

    // Balance info modal event listeners
    const balanceInfoModal = document.getElementById('balance-info-modal');
    const closeBalanceInfoModal = document.getElementById('close-balance-info-modal');

    if (closeBalanceInfoModal) {
        closeBalanceInfoModal.addEventListener('click', () => {
            if (balanceInfoModal) balanceInfoModal.style.display = 'none';
        });
    }

    if (balanceInfoModal) {
        balanceInfoModal.addEventListener('click', (event) => {
            if (event.target === balanceInfoModal) {
                balanceInfoModal.style.display = 'none';
            }
        });
    }
});
