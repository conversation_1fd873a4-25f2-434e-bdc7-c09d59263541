/* تنسيقات خاصة بلوحة معلومات مصاريف الحافلات */

:root {
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    --border-radius: 8px;
    --transition: all 0.3s ease;
    
    /* ألوان المربعات */
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --light-color: #ecf0f1;
    --dark-color: #34495e;
    
    /* ألوان أنواع المصاريف */
    --صيانة-color: #3498db;
    --تأمين-color: #9b59b6;
    --تغير-زيت-color: #f1c40f;
    --تغير-كفرات-color: #e67e22;
    --ايجار-color: #1abc9c;
    --وقود-color: #e74c3c;
    --تراخيص-color: #2ecc71;
    --غسيل-color: #3498db;
    --فحص-دوري-color: #f39c12;
    --قطع-غيار-color: #16a085;
    --مخالفات-color: #c0392b;
    --أخرى-color: #7f8c8d;
}

/* تنسيق الخط والعناصر الأساسية */
body {
    font-family: 'Tajawal', sans-serif;
}

.dashboard-page-header {
    text-align: center;
    margin-bottom: 2rem;
}

.dashboard-page-header h1 {
    color: var(--primary-color);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.dashboard-page-header p {
    color: var(--secondary-color);
    font-size: 1.1rem;
}

/* تنسيقات قسم لوحة المصاريف */
.expenses-dashboard {
    display: flex;
    gap: 20px;
    margin-bottom: 2rem;
}

.expense-boxes {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 20px;
    flex: 1;
}

/* تصميم مربع المصروف */
.expense-box {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.expense-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background-color: var(--primary-color); /* سيتم تخصيصه بالجافاسكربت */
}

.expense-box:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.expense-box[data-type="صيانة"]::before { background-color: var(--صيانة-color); }
.expense-box[data-type="تأمين"]::before { background-color: var(--تأمين-color); }
.expense-box[data-type="تغير زيت"]::before { background-color: var(--تغير-زيت-color); }
.expense-box[data-type="تغير كفرات"]::before { background-color: var(--تغير-كفرات-color); }
.expense-box[data-type="ايجار"]::before { background-color: var(--ايجار-color); }
.expense-box[data-type="وقود"]::before { background-color: var(--وقود-color); }
.expense-box[data-type="تراخيص"]::before { background-color: var(--تراخيص-color); }
.expense-box[data-type="غسيل"]::before { background-color: var(--غسيل-color); }
.expense-box[data-type="فحص دوري"]::before { background-color: var(--فحص-دوري-color); }
.expense-box[data-type="قطع غيار"]::before { background-color: var(--قطع-غيار-color); }
.expense-box[data-type="مخالفات"]::before { background-color: var(--مخالفات-color); }
.expense-box[data-type="أخرى"]::before { background-color: var(--أخرى-color); }

.expense-icon {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: var(--primary-color); /* يتم تحديثه بالجافاسكريبت */
}

.expense-box[data-type="صيانة"] .expense-icon { color: var(--صيانة-color); }
.expense-box[data-type="تأمين"] .expense-icon { color: var(--تأمين-color); }
.expense-box[data-type="تغير زيت"] .expense-icon { color: var(--تغير-زيت-color); }
.expense-box[data-type="تغير كفرات"] .expense-icon { color: var(--تغير-كفرات-color); }
.expense-box[data-type="ايجار"] .expense-icon { color: var(--ايجار-color); }
.expense-box[data-type="وقود"] .expense-icon { color: var(--وقود-color); }
.expense-box[data-type="تراخيص"] .expense-icon { color: var(--تراخيص-color); }
.expense-box[data-type="غسيل"] .expense-icon { color: var(--غسيل-color); }
.expense-box[data-type="فحص دوري"] .expense-icon { color: var(--فحص-دوري-color); }
.expense-box[data-type="قطع غيار"] .expense-icon { color: var(--قطع-غيار-color); }
.expense-box[data-type="مخالفات"] .expense-icon { color: var(--مخالفات-color); }
.expense-box[data-type="أخرى"] .expense-icon { color: var(--أخرى-color); }

.expense-box h3 {
    margin: 0.5rem 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.expense-amount {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0.5rem 0 0;
    color: var(--secondary-color);
}

/* تصميم مربع الإجمالي الكبير */
.total-expense {
    background: linear-gradient(135deg, #3a7bd5, #3a6073);
    color: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--box-shadow);
    min-width: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.total-expense .total-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.8);
}

.total-expense h2 {
    font-size: 1.8rem;
    margin: 0.5rem 0;
    color: white;
}

.total-expense p {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 1rem 0;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* أزرار اختيار الفترة */
.period-selector {
    display: flex;
    gap: 8px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.period-btn {
    background-color: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
}

.period-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

.period-btn.active {
    background-color: rgba(255, 255, 255, 0.4);
    font-weight: 600;
}

/* قسم الحافلات */
.buses-section {
    margin-top: 3rem;
}

.buses-section h2 {
    font-size: 1.6rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    text-align: center;
}

.buses-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    grid-gap: 20px;
    justify-content: center;
}

.bus-item {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
}

.bus-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

/* إزالة الخلفية البيضاء خلف أيقونة الباص وتعديل اللون */
.bus-icon {
    font-size: 2.5rem;
    color: #333; /* تغيير لون الأيقونة إلى أسود */
    margin-bottom: 0.8rem;
    transition: color 0.3s ease; /* إضافة تأثير انتقالي للون */
}

.bus-item:hover .bus-icon {
    color: #f1c40f; /* تغيير لون الأيقونة إلى أصفر عند المرور عليها */
}

.bus-number {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--secondary-color);
}

/* تنسيق نوع الباص */
.bus-type {
    font-size: 0.9rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* رسائل التحميل */
.loading-message {
    text-align: center;
    padding: 2rem;
    color: var(--secondary-color);
    font-style: italic;
    grid-column: 1 / -1;
}

.message {
    padding: 1rem 1.5rem;
    margin: 1rem 0;
    border-radius: var(--border-radius);
    text-align: center;
}

.message.info {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--info-color);
}

.message.success {
    background-color: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
}

.message.error {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
}

.message.warning {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

/* تجاوب مع الشاشات المختلفة */
@media (max-width: 1200px) {
    .expenses-dashboard {
        flex-direction: column;
    }

    .total-expense {
        order: -1; /* جعل المجموع في الأعلى */
    }
}

@media (max-width: 768px) {
    .expense-boxes {
        grid-template-columns: 1fr;
    }

    .total-expense {
        padding: 1.5rem;
    }

    .total-expense p {
        font-size: 2rem;
    }

    .buses-container {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
}

@media (max-width: 576px) {
    .dashboard-page-header h1 {
        font-size: 1.6rem;
    }

    .dashboard-page-header p {
        font-size: 1rem;
    }

    .period-selector {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .period-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
}

/* زر إنشاء التقرير في الترويسة */
.report-btn {
    background-color: var(--accent-color, #9b59b6); /* لون مميز */
    color: #fff;
    border: none;
    padding: 8px 15px;
}
.report-btn:hover {
    background-color: #8e44ad; /* لون أغمق عند التحويم */
}

/* تنسيقات نافذة التقرير المنبثقة */
.modal {
    /* ... (أنماط المودال الأساسية من shared_styles.css أو هنا) ... */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1050; /* تأكد من أنها فوق العناصر الأخرى */
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}
.modal.show {
    opacity: 1;
    visibility: visible;
}
.modal-content {
    background-color: #fff;
    padding: 25px 30px;
    border-radius: var(--border-radius, 8px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
    width: 90%;
    max-width: 650px; /* تقليل العرض قليلاً */
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    position: relative;
}
.close-modal-btn {
    position: absolute;
    top: 10px;
    left: 15px; /* RTL */
    background: none;
    border: none;
    font-size: 1.8rem;
    line-height: 1;
    cursor: pointer;
    color: var(--text-muted, #777);
    padding: 5px;
}
.close-modal-btn:hover {
    color: var(--danger-color, #e74c3c);
}
.modal h2 {
    margin-top: 0;
    margin-bottom: 20px;
    text-align: center;
    color: var(--primary-color);
    font-size: 1.5rem;
}
.modal h2 i {
    margin-left: 10px;
}

/* تنسيق خيارات التقرير */
.report-options {
    margin-bottom: 15px;
}
.report-options .form-group {
    margin-bottom: 15px;
}
.report-options label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
}
/* تنسيق Choices.js multi-select */
.choices {
    margin-bottom: 0; /* إزالة الهامش السفلي الافتراضي */
}
.choices__inner {
    background-color: #fff;
    border: 1px solid var(--border-color, #ccc);
    border-radius: var(--border-radius, 4px);
    padding: 5px 7px; /* تقليل الحشو قليلاً */
    font-size: 0.95rem;
    min-height: 40px;
}
.choices__list--multiple .choices__item {
    background-color: var(--primary-color, #3498db);
    border-color: var(--primary-dark, #2980b9);
    font-size: 0.85rem;
    border-radius: 3px;
}
.choices[data-type*="select-multiple"] .choices__button,
.choices[data-type*="text"] .choices__button {
    border-left: 1px solid var(--primary-dark, #2980b9); /* RTL */
    margin-left: 5px;
    margin-right: -2px; /* RTL */
}
