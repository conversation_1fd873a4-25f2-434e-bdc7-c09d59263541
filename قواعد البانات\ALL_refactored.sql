-- =====================================================================
-- الجزء الأول: إنشاء الجداول الأساسية (الأعمدة والمفاتيح الأساسية)
-- =====================================================================

-- تعريف الأنواع المخصصة أولاً
CREATE TYPE public.transaction_type AS ENUM (
    'deposit',
    'withdrawal',
    'transfer_in',
    'transfer_out',
    'payment',
    'refund',
    'expense',
    'salary',
    'commission',
    'other'
);

-- الجداول التي ليس لها تبعيات خارجية مباشرة أو تبعياتها قليلة
CREATE TABLE public.banks (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  account_number text NULL,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  notes text NULL,
  branch character varying(255) NULL,
  contact_phone character varying(20) NULL,
  bank_type text NULL,
  CONSTRAINT banks_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.budget_years (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  year_number integer NOT NULL,
  is_active boolean NULL DEFAULT true,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  description text NULL,
  CONSTRAINT budget_years_pkey PRIMARY KEY (id),
  CONSTRAINT budget_years_year_number_key UNIQUE (year_number)
) TABLESPACE pg_default;

CREATE TABLE public.buses (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  bus_number text NOT NULL,
  bus_type text NOT NULL,
  model text NULL,
  status text NULL DEFAULT 'نشط'::text,
  notes text NULL,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  plate_letters text NULL,
  plate_numbers text NULL,
  CONSTRAINT buses_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.enterprises (
  id serial NOT NULL,
  name text NOT NULL,
  commercial_reg_number text NULL,
  tax_number text NULL,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  CONSTRAINT enterprises_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.nathriyat_types (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  icon text NULL,
  description text NULL,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  CONSTRAINT nathriyat_types_pkey PRIMARY KEY (id),
  CONSTRAINT nathriyat_types_name_unique UNIQUE (name)
) TABLESPACE pg_default;

CREATE TABLE public.neighborhoods (
  id uuid NOT NULL DEFAULT extensions.uuid_generate_v4(),
  name text NOT NULL,
  created_at timestamp with time zone NULL DEFAULT now(),
  CONSTRAINT neighborhoods_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.schools (
  id uuid NOT NULL DEFAULT extensions.uuid_generate_v4(),
  name text NULL,
  stage text NULL,
  created_at timestamp with time zone NULL DEFAULT now(),
  CONSTRAINT schools_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.student_groups (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying(255) NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT student_groups_pkey PRIMARY KEY (id),
  CONSTRAINT student_groups_name_key UNIQUE (name)
) TABLESPACE pg_default;

-- الجداول التي تعتمد على الجداول السابقة
CREATE TABLE public.budget_months (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  budget_year_id uuid NOT NULL,
  month_number integer NOT NULL,
  month_name text NOT NULL,
  opening_balance numeric(12, 2) NULL DEFAULT 0,
  is_active boolean NULL DEFAULT false,
  is_closed boolean NULL DEFAULT false,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  current_balance numeric(12, 2) NULL DEFAULT 0,
  closing_balance numeric(12, 2) NULL DEFAULT 0,
  total_income numeric(12, 2) NULL DEFAULT 0,
  total_expenses numeric(12, 2) NULL DEFAULT 0,
  is_opening_balance_automatic boolean NULL DEFAULT false,
  CONSTRAINT budget_months_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.drivers (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  phone text NOT NULL,
  work_shift text NOT NULL,
  is_active boolean NULL DEFAULT true,
  notes text NULL,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  bus_id uuid NULL,
  CONSTRAINT drivers_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.students (
  id uuid NOT NULL DEFAULT extensions.uuid_generate_v4(),
  name text NOT NULL,
  parent_phone text NOT NULL,
  student_phone text NULL,
  stage text NOT NULL,
  school_id uuid NOT NULL,
  grade text NOT NULL,
  neighborhood_id uuid NOT NULL,
  location_url text NULL,
  house_door_image text NULL,
  is_active boolean NULL DEFAULT true,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  group_id uuid NULL,
  mobile_number character varying(20) NULL,
  preferred_bank_id uuid NULL,
  has_subscription boolean NULL DEFAULT false,
  CONSTRAINT students_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.enterprise_groups (
  id serial NOT NULL,
  enterprise_id integer NULL,
  name text NULL,
  departure_employees_count integer NULL DEFAULT 1,
  departure_meeting_point text NULL,
  departure_meeting_time time without time zone NULL,
  departure_time time without time zone NULL,
  return_employees_count integer NULL DEFAULT 1,
  return_meeting_point text NULL,
  return_meeting_time time without time zone NULL,
  return_time time without time zone NULL,
  notes text NULL,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  cost numeric(10, 2) NULL DEFAULT 0.00,
  has_vat boolean NOT NULL DEFAULT false,
  vat_percentage numeric(5, 2) NULL DEFAULT 15.00,
  vat_amount numeric(10, 2) NULL DEFAULT 0.00,
  total_cost_with_vat numeric(10, 2) NULL DEFAULT 0.00,
  CONSTRAINT enterprise_groups_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

-- جدول تسجيل المعاملات المالية المركزي (من code.sql)
CREATE TABLE public.financial_transactions_log (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    transaction_date date NOT NULL,
    amount numeric(15, 2) NOT NULL,
    transaction_type public.transaction_type NOT NULL, -- استخدام النوع الموجود
    description text NULL,
    source_table character varying(100) NOT NULL,
    source_record_id text NOT NULL,
    bank_id uuid NULL,
    budget_month_id uuid NULL,
    is_reversal boolean NOT NULL DEFAULT false,
    original_log_entry_id uuid NULL,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    CONSTRAINT financial_transactions_log_pkey PRIMARY KEY (id)
);

-- باقي الجداول
CREATE TABLE public.bank_accounts (
  id uuid NOT NULL DEFAULT extensions.uuid_generate_v4(),
  bank_id uuid NULL,
  opening_balance numeric(15, 2) NULL DEFAULT 0,
  closing_balance numeric(15, 2) NULL DEFAULT 0,
  month_id uuid NULL,
  created_at timestamp with time zone NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp with time zone NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT bank_accounts_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.enterprise_subscriptions (
  id bigserial NOT NULL,
  enterprise_id integer NOT NULL,
  budget_month_id uuid NOT NULL,
  service_type text NULL,
  total_amount numeric(10, 2) NOT NULL DEFAULT 0.00,
  paid_amount numeric(10, 2) NOT NULL DEFAULT 0.00,
  payment_status text NULL DEFAULT 'not_paid'::text,
  subscription_date date NULL,
  payment_date date NULL,
  notes text NULL,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  payment_bank_id uuid NULL,
  CONSTRAINT enterprise_subscriptions_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.bank_transactions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  bank_id uuid NOT NULL,
  amount numeric(10, 2) NOT NULL,
  transaction_type public.transaction_type NOT NULL,
  transaction_date date NOT NULL DEFAULT CURRENT_DATE,
  description text NULL,
  transaction_source text NULL,
  reference_id uuid NULL,
  reference_code text NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  transaction_source_type character varying(50) NULL,
  reference_table text NULL,
  budget_month_id uuid,
  enterprise_subscription_id bigint NULL,
  financial_transaction_log_id uuid NULL, --  عمود جديد للربط مع سجل المعاملات المالية
  CONSTRAINT bank_transactions_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.bus_expenses (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  budget_month_id uuid NOT NULL,
  bus_id uuid NOT NULL,
  driver_id uuid NULL,
  expense_type text NOT NULL,
  amount numeric(10, 2) NOT NULL,
  expense_date date NOT NULL,
  payment_method text NULL,
  bank_id uuid NULL,
  reference_number text NULL,
  details text NULL,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  include_tax boolean NULL DEFAULT false,
  tax_amount numeric(10, 2) NULL DEFAULT 0,
  total_with_tax numeric GENERATED ALWAYS AS ((amount + tax_amount)) STORED,
  CONSTRAINT bus_expenses_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.driver_default_settings (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  driver_id uuid NOT NULL,
  default_base_salary numeric(10, 2) NOT NULL DEFAULT 0.00,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  current_balance numeric NULL DEFAULT 0,
  last_closed_month_id uuid NULL,
  CONSTRAINT driver_default_settings_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.driver_expenses (
  id bigserial NOT NULL,
  driver_id uuid NOT NULL,
  budget_month_id uuid NOT NULL,
  expense_type text NOT NULL,
  total_amount numeric(10, 2) NOT NULL,
  expense_date date NOT NULL,
  is_deduction boolean NULL DEFAULT false,
  notes text NULL,
  bank_id uuid NULL,
  bank_paid_amount numeric(10, 2) NULL,
  vat_amount numeric(10, 2) NULL,
  fine_or_accident_type text NULL,
  commission_type text NULL,
  created_at timestamp with time zone NULL DEFAULT now(),
  CONSTRAINT driver_expenses_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.driver_monthly_closings (
  id bigserial NOT NULL,
  driver_id uuid NULL,
  budget_month_id uuid NULL,
  closing_date timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  previous_balance_at_closing numeric(10, 2) NULL DEFAULT 0.00,
  base_salary_at_closing numeric(10, 2) NULL DEFAULT 0.00,
  total_commissions_at_closing numeric(10, 2) NULL DEFAULT 0.00,
  total_deductions_at_closing numeric(10, 2) NULL DEFAULT 0.00,
  total_payments_at_closing numeric(10, 2) NULL DEFAULT 0.00,
  calculated_net_due_at_closing numeric(10, 2) NULL DEFAULT 0.00,
  final_remaining_balance numeric(10, 2) NULL DEFAULT 0.00,
  closed_by_user_id uuid NULL,
  notes text NULL,
  created_at timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT driver_monthly_closings_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.enterprise_group_payments (
  id bigserial NOT NULL,
  enterprise_subscription_id bigint NOT NULL,
  enterprise_group_id bigint NOT NULL,
  payment_amount numeric(10, 2) NOT NULL,
  payment_date date NOT NULL,
  payment_bank_id uuid NULL,
  notes text NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT enterprise_group_payments_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.enterprise_monthly_closings (
  id bigserial NOT NULL,
  enterprise_id integer NOT NULL,
  budget_month_id uuid NOT NULL,
  closing_balance numeric(10, 2) NOT NULL DEFAULT 0.00,
  closing_date timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  is_closed boolean NOT NULL DEFAULT false,
  closed_by_user_id uuid NULL,
  notes text NULL,
  created_at timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp with time zone NULL,
  CONSTRAINT enterprise_monthly_closings_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.nathriyat_transactions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  nathriyat_type_id uuid NOT NULL,
  budget_month_id uuid NOT NULL,
  bank_id uuid NULL,
  amount numeric(12, 2) NOT NULL,
  transaction_date date NOT NULL,
  notes text NULL,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  CONSTRAINT nathriyat_transactions_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.student_group_members (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  group_id uuid NOT NULL,
  student_id uuid NOT NULL,
  added_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT student_group_members_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.student_month_costs (
  student_id uuid NOT NULL,
  budget_month_id uuid NOT NULL,
  month_cost numeric(10, 2) NOT NULL,
  CONSTRAINT student_month_costs_pkey PRIMARY KEY (student_id, budget_month_id)
) TABLESPACE pg_default;

CREATE TABLE public.student_payments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  student_id uuid NOT NULL,
  budget_month_id uuid NOT NULL,
  bank_id uuid NOT NULL,
  amount numeric(10, 2) NOT NULL,
  payment_date date NOT NULL,
  payment_status text NOT NULL DEFAULT 'paid'::text,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT student_payments_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.student_subscription_defaults (
  id bigint GENERATED ALWAYS AS IDENTITY NOT NULL,
  student_id uuid NOT NULL,
  default_service_type text NULL,
  default_amount numeric NULL DEFAULT 0,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  current_balance numeric NULL DEFAULT 0,
  CONSTRAINT student_subscription_defaults_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

CREATE TABLE public.subscription_locks (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  student_id uuid NOT NULL,
  budget_month_id uuid NOT NULL,
  locked_remaining_amount numeric(10, 2) NOT NULL,
  locked_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT subscription_locks_pkey PRIMARY KEY (id)
) TABLESPACE pg_default;

-- إنشاء العرض (View)
CREATE VIEW public.bus_driver_info AS
SELECT
  b.id AS bus_id,
  b.bus_number,
  b.bus_type,
  b.model,
  b.status,
  d.id AS driver_id,
  d.name AS driver_name,
  d.phone AS driver_phone,
  d.work_shift AS driver_shift,
  0 AS student_count
FROM
  public.buses b
LEFT JOIN public.drivers d ON b.id = d.bus_id AND d.is_active = true;


-- =====================================================================
-- الجزء الثاني: إضافة القيود (Constraints) والفهارس (Indexes)
-- =====================================================================

-- Constraints for banks
ALTER TABLE public.banks
  ADD CONSTRAINT banks_bank_type_check CHECK (((bank_type = ANY (ARRAY['اجل'::text, 'مركزي'::text]))));

-- Constraints for budget_months
ALTER TABLE public.budget_months
  ADD CONSTRAINT budget_months_budget_year_id_month_number_key UNIQUE (budget_year_id, month_number),
  ADD CONSTRAINT budget_months_budget_year_id_fkey FOREIGN KEY (budget_year_id) REFERENCES public.budget_years (id) ON DELETE CASCADE;

-- Constraints for buses
ALTER TABLE public.buses
  ADD CONSTRAINT check_plate_letters_length CHECK (((plate_letters IS NULL) OR (length(plate_letters) = 5))),
  ADD CONSTRAINT check_plate_numbers_length CHECK (((plate_numbers IS NULL) OR (length(plate_numbers) = 4)));

-- Constraints for drivers
ALTER TABLE public.drivers
  ADD CONSTRAINT drivers_bus_id_fkey FOREIGN KEY (bus_id) REFERENCES public.buses (id) ON DELETE SET NULL;

-- Constraints for students
ALTER TABLE public.students
  ADD CONSTRAINT students_neighborhood_id_fkey FOREIGN KEY (neighborhood_id) REFERENCES public.neighborhoods (id) ON DELETE RESTRICT,
  ADD CONSTRAINT students_preferred_bank_id_fkey FOREIGN KEY (preferred_bank_id) REFERENCES public.banks (id) ON DELETE SET NULL,
  ADD CONSTRAINT students_school_id_fkey FOREIGN KEY (school_id) REFERENCES public.schools (id) ON DELETE RESTRICT;
  -- ADD CONSTRAINT students_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.student_groups(id) ON DELETE SET NULL; -- Assuming group_id in students refers to student_groups

-- Constraints for enterprise_groups
ALTER TABLE public.enterprise_groups
  ADD CONSTRAINT enterprise_groups_enterprise_id_fkey FOREIGN KEY (enterprise_id) REFERENCES public.enterprises (id) ON DELETE CASCADE;

-- Constraints for financial_transactions_log (from code.sql)
ALTER TABLE public.financial_transactions_log
  ADD CONSTRAINT financial_transactions_log_bank_id_fkey FOREIGN KEY (bank_id) REFERENCES public.banks (id) ON DELETE SET NULL,
  ADD CONSTRAINT financial_transactions_log_budget_month_id_fkey FOREIGN KEY (budget_month_id) REFERENCES public.budget_months (id) ON DELETE SET NULL,
  ADD CONSTRAINT financial_transactions_log_original_entry_fkey FOREIGN KEY (original_log_entry_id) REFERENCES public.financial_transactions_log (id) ON DELETE SET NULL,
  ADD CONSTRAINT financial_transactions_log_amount_check CHECK (amount >= 0);

-- Constraints for bank_accounts
ALTER TABLE public.bank_accounts
  ADD CONSTRAINT bank_accounts_bank_id_fkey FOREIGN KEY (bank_id) REFERENCES public.banks (id) ON DELETE CASCADE,
  ADD CONSTRAINT bank_accounts_month_id_fkey FOREIGN KEY (month_id) REFERENCES public.budget_months (id) ON DELETE CASCADE;

-- Constraints for enterprise_subscriptions
ALTER TABLE public.enterprise_subscriptions
  ADD CONSTRAINT enterprise_subscriptions_enterprise_id_budget_month_id_key UNIQUE (enterprise_id, budget_month_id),
  ADD CONSTRAINT enterprise_subscriptions_budget_month_id_fkey FOREIGN KEY (budget_month_id) REFERENCES public.budget_months (id) ON DELETE RESTRICT,
  ADD CONSTRAINT enterprise_subscriptions_enterprise_id_fkey FOREIGN KEY (enterprise_id) REFERENCES public.enterprises (id) ON DELETE CASCADE,
  ADD CONSTRAINT enterprise_subscriptions_payment_bank_id_fkey FOREIGN KEY (payment_bank_id) REFERENCES public.banks (id) ON DELETE SET NULL;

-- Constraints for bank_transactions
ALTER TABLE public.bank_transactions
  ADD CONSTRAINT bank_transactions_bank_id_fkey FOREIGN KEY (bank_id) REFERENCES public.banks (id) ON DELETE CASCADE,
  ADD CONSTRAINT bank_transactions_enterprise_subscription_id_fkey FOREIGN KEY (enterprise_subscription_id) REFERENCES public.enterprise_subscriptions (id) ON DELETE SET NULL,
  ADD CONSTRAINT fk_bank_transactions_budget_month FOREIGN KEY (budget_month_id) REFERENCES public.budget_months (id) ON UPDATE CASCADE ON DELETE SET NULL,
  ADD CONSTRAINT bank_transactions_amount_check CHECK ((amount >= (0)::numeric)),
  ADD CONSTRAINT fk_bank_transactions_financial_log FOREIGN KEY (financial_transaction_log_id) REFERENCES public.financial_transactions_log (id) ON DELETE SET NULL; -- من code (4).sql

-- Constraints for bus_expenses
ALTER TABLE public.bus_expenses
  ADD CONSTRAINT fk_bank FOREIGN KEY (bank_id) REFERENCES public.banks (id),
  ADD CONSTRAINT fk_bus_expenses_buses FOREIGN KEY (bus_id) REFERENCES public.buses (id) ON DELETE CASCADE,
  ADD CONSTRAINT fk_bus_expenses_drivers FOREIGN KEY (driver_id) REFERENCES public.drivers (id) ON DELETE SET NULL,
  ADD CONSTRAINT fk_bus_expenses_budget_month FOREIGN KEY (budget_month_id) REFERENCES public.budget_months(id) ON DELETE RESTRICT; -- Added missing FK

-- Constraints for driver_default_settings
ALTER TABLE public.driver_default_settings
  ADD CONSTRAINT driver_default_settings_driver_id_key UNIQUE (driver_id),
  ADD CONSTRAINT driver_default_settings_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers (id) ON DELETE CASCADE,
  ADD CONSTRAINT driver_default_settings_default_base_salary_check CHECK ((default_base_salary >= (0)::numeric)),
  ADD CONSTRAINT driver_default_settings_last_closed_month_fkey FOREIGN KEY (last_closed_month_id) REFERENCES public.budget_months(id) ON DELETE SET NULL; -- Added missing FK if applicable

-- Constraints for driver_expenses
ALTER TABLE public.driver_expenses
  ADD CONSTRAINT driver_expenses_bank_id_fkey FOREIGN KEY (bank_id) REFERENCES public.banks (id) ON DELETE SET NULL,
  ADD CONSTRAINT driver_expenses_budget_month_id_fkey FOREIGN KEY (budget_month_id) REFERENCES public.budget_months (id) ON DELETE RESTRICT,
  ADD CONSTRAINT driver_expenses_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers (id) ON DELETE CASCADE;

-- Constraints for driver_monthly_closings
ALTER TABLE public.driver_monthly_closings
  ADD CONSTRAINT unique_driver_month_closing UNIQUE (driver_id, budget_month_id),
  ADD CONSTRAINT driver_monthly_closings_budget_month_id_fkey FOREIGN KEY (budget_month_id) REFERENCES public.budget_months (id) ON DELETE RESTRICT,
  ADD CONSTRAINT driver_monthly_closings_closed_by_user_id_fkey FOREIGN KEY (closed_by_user_id) REFERENCES auth.users (id) ON DELETE SET NULL,
  ADD CONSTRAINT driver_monthly_closings_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers (id) ON DELETE SET NULL;

-- Constraints for enterprise_group_payments
ALTER TABLE public.enterprise_group_payments
  ADD CONSTRAINT uq_group_payment_per_subscription UNIQUE (enterprise_subscription_id, enterprise_group_id, payment_date, payment_amount),
  ADD CONSTRAINT enterprise_group_payments_enterprise_group_id_fkey FOREIGN KEY (enterprise_group_id) REFERENCES public.enterprise_groups (id) ON DELETE RESTRICT,
  ADD CONSTRAINT enterprise_group_payments_enterprise_subscription_id_fkey FOREIGN KEY (enterprise_subscription_id) REFERENCES public.enterprise_subscriptions (id) ON DELETE CASCADE,
  ADD CONSTRAINT enterprise_group_payments_payment_bank_id_fkey FOREIGN KEY (payment_bank_id) REFERENCES public.banks (id) ON DELETE SET NULL,
  ADD CONSTRAINT enterprise_group_payments_payment_amount_check CHECK ((payment_amount > (0)::numeric));

-- Constraints for enterprise_monthly_closings
ALTER TABLE public.enterprise_monthly_closings
  ADD CONSTRAINT unique_enterprise_month_closing_record UNIQUE (enterprise_id, budget_month_id),
  ADD CONSTRAINT fk_budget_month_closing FOREIGN KEY (budget_month_id) REFERENCES public.budget_months (id) ON DELETE RESTRICT,
  ADD CONSTRAINT fk_enterprise_closing FOREIGN KEY (enterprise_id) REFERENCES public.enterprises (id) ON DELETE CASCADE,
  ADD CONSTRAINT fk_user_closing FOREIGN KEY (closed_by_user_id) REFERENCES auth.users (id) ON DELETE SET NULL;

-- Constraints for nathriyat_transactions
ALTER TABLE public.nathriyat_transactions
  ADD CONSTRAINT nathriyat_transactions_bank_id_fkey FOREIGN KEY (bank_id) REFERENCES public.banks (id) ON DELETE SET NULL,
  ADD CONSTRAINT nathriyat_transactions_budget_month_id_fkey FOREIGN KEY (budget_month_id) REFERENCES public.budget_months (id) ON DELETE RESTRICT,
  ADD CONSTRAINT nathriyat_transactions_nathriyat_type_id_fkey FOREIGN KEY (nathriyat_type_id) REFERENCES public.nathriyat_types (id) ON DELETE CASCADE;

-- Constraints for student_group_members
ALTER TABLE public.student_group_members
  ADD CONSTRAINT student_group_members_group_id_student_id_key UNIQUE (group_id, student_id),
  ADD CONSTRAINT student_group_members_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.student_groups (id) ON DELETE CASCADE,
  ADD CONSTRAINT student_group_members_student_id_fkey FOREIGN KEY (student_id) REFERENCES public.students (id) ON DELETE CASCADE;

-- Constraints for student_month_costs
ALTER TABLE public.student_month_costs
  ADD CONSTRAINT student_month_costs_student_id_fkey FOREIGN KEY (student_id) REFERENCES public.students(id) ON DELETE CASCADE,
  ADD CONSTRAINT student_month_costs_budget_month_id_fkey FOREIGN KEY (budget_month_id) REFERENCES public.budget_months(id) ON DELETE CASCADE;


-- Constraints for student_payments
ALTER TABLE public.student_payments
  ADD CONSTRAINT student_payments_bank_id_fkey FOREIGN KEY (bank_id) REFERENCES public.banks (id) ON UPDATE CASCADE ON DELETE RESTRICT,
  ADD CONSTRAINT student_payments_budget_month_id_fkey FOREIGN KEY (budget_month_id) REFERENCES public.budget_months (id) ON UPDATE CASCADE ON DELETE CASCADE,
  ADD CONSTRAINT student_payments_student_id_fkey FOREIGN KEY (student_id) REFERENCES public.students (id) ON UPDATE CASCADE ON DELETE CASCADE,
  ADD CONSTRAINT positive_payment_amount CHECK (((payment_status = 'not_paid'::text) OR (amount > (0)::numeric)));

-- Constraints for student_subscription_defaults
ALTER TABLE public.student_subscription_defaults
  ADD CONSTRAINT student_subscription_defaults_student_id_key UNIQUE (student_id),
  ADD CONSTRAINT student_subscription_defaults_student_id_fkey FOREIGN KEY (student_id) REFERENCES public.students (id) ON DELETE CASCADE,
  ADD CONSTRAINT student_subscription_defaults_default_amount_check CHECK ((default_amount >= (0)::numeric)),
  ADD CONSTRAINT student_subscription_defaults_default_service_type_check CHECK ((default_service_type = ANY (ARRAY['ذهاب وعودة'::text, 'ذهاب فقط'::text, 'عودة فقط'::text, 'خدمة خاصة'::text, 'other'::text])));

-- Constraints for subscription_locks
ALTER TABLE public.subscription_locks
  ADD CONSTRAINT unique_student_month_lock UNIQUE (student_id, budget_month_id),
  ADD CONSTRAINT subscription_locks_budget_month_id_fkey FOREIGN KEY (budget_month_id) REFERENCES public.budget_months (id) ON UPDATE CASCADE ON DELETE CASCADE,
  ADD CONSTRAINT subscription_locks_student_id_fkey FOREIGN KEY (student_id) REFERENCES public.students (id) ON UPDATE CASCADE ON DELETE CASCADE;

-- Indexes
CREATE INDEX IF NOT EXISTS idx_bank_transactions_bank_id ON public.bank_transactions USING btree (bank_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_bank_transactions_transaction_date ON public.bank_transactions USING btree (transaction_date) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_bank_transactions_transaction_source ON public.bank_transactions USING btree (transaction_source) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_bank_transactions_reference_id ON public.bank_transactions USING btree (reference_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_bank_transactions_enterprise_subscription_id ON public.bank_transactions USING btree (enterprise_subscription_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_bank_transactions_budget_month_id ON public.bank_transactions USING btree (budget_month_id) TABLESPACE pg_default;
CREATE UNIQUE INDEX IF NOT EXISTS idx_bank_transactions_financial_log_id_unique ON public.bank_transactions (financial_transaction_log_id) WHERE financial_transaction_log_id IS NOT NULL; -- من code (4).sql

CREATE INDEX IF NOT EXISTS idx_budget_months_budget_year_id ON public.budget_months USING btree (budget_year_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_budget_months_year_month ON public.budget_months USING btree (budget_year_id, month_number) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_budget_years_year_number ON public.budget_years USING btree (year_number) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_bus_expenses_budget_month ON public.bus_expenses USING btree (budget_month_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_bus_expenses_driver ON public.bus_expenses USING btree (driver_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_bus_expenses_date ON public.bus_expenses USING btree (expense_date) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_bus_expenses_type ON public.bus_expenses USING btree (expense_type) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_bus_expenses_buses ON public.bus_expenses USING btree (bus_id) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_buses_bus_number ON public.buses USING btree (bus_number) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_buses_bus_type ON public.buses USING btree (bus_type) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_buses_status ON public.buses USING btree (status) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_buses_plate_letters ON public.buses USING btree (plate_letters) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_buses_plate_numbers ON public.buses USING btree (plate_numbers) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_driver_expenses_driver_month ON public.driver_expenses USING btree (driver_id, budget_month_id) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_drivers_name ON public.drivers USING btree (name) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_drivers_phone ON public.drivers USING btree (phone) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_drivers_work_shift ON public.drivers USING btree (work_shift) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_drivers_is_active ON public.drivers USING btree (is_active) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_drivers_bus_id ON public.drivers USING btree (bus_id) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_enterprise_group_payments_subscription_id ON public.enterprise_group_payments USING btree (enterprise_subscription_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_enterprise_group_payments_group_id ON public.enterprise_group_payments USING btree (enterprise_group_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_enterprise_group_payments_bank_id ON public.enterprise_group_payments USING btree (payment_bank_id) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_enterprise_monthly_closings_lookup ON public.enterprise_monthly_closings USING btree (enterprise_id, is_closed, budget_month_id DESC) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_enterprise_subscriptions_payment_bank_id ON public.enterprise_subscriptions USING btree (payment_bank_id) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_nathriyat_transactions_type_id ON public.nathriyat_transactions USING btree (nathriyat_type_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_nathriyat_transactions_budget_month_id ON public.nathriyat_transactions USING btree (budget_month_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_nathriyat_transactions_bank_id ON public.nathriyat_transactions USING btree (bank_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_nathriyat_transactions_date ON public.nathriyat_transactions USING btree (transaction_date) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_student_group_members_group_id ON public.student_group_members USING btree (group_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_student_group_members_student_id ON public.student_group_members USING btree (student_id) TABLESPACE pg_default;

CREATE INDEX IF NOT EXISTS idx_students_name ON public.students USING btree (name) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_students_school ON public.students USING btree (school_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_students_neighborhood ON public.students USING btree (neighborhood_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_students_is_active ON public.students USING btree (is_active) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_students_mobile_number ON public.students USING btree (mobile_number) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_students_bank_id ON public.students USING btree (preferred_bank_id) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_students_group_id ON public.students USING btree (group_id) TABLESPACE pg_default;

-- Indexes for financial_transactions_log (from code.sql)
CREATE INDEX IF NOT EXISTS idx_financial_log_source ON public.financial_transactions_log USING btree (source_table, source_record_id);
CREATE INDEX IF NOT EXISTS idx_financial_log_bank_id ON public.financial_transactions_log USING btree (bank_id);
CREATE INDEX IF NOT EXISTS idx_financial_log_budget_month_id ON public.financial_transactions_log USING btree (budget_month_id);
CREATE INDEX IF NOT EXISTS idx_financial_log_transaction_date ON public.financial_transactions_log USING btree (transaction_date);


-- =====================================================================
-- الجزء الثالث: إنشاء الدوال (Functions)
-- =====================================================================

-- دالة عامة لتحديث حقل updated_at (إذا لم تكن موجودة)
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- افترض أن الدوال الأخرى مثل update_timestamp, trigger_set_timestamp, update_modified_column
-- هي إما مشابهة لـ update_updated_at_column أو معرفة مسبقًا في قاعدة البيانات.
-- إذا كانت هناك تعريفات محددة لهذه الدوال، يجب إضافتها هنا.
-- مثال لدالة trigger_set_timestamp إذا كانت مختلفة:
CREATE OR REPLACE FUNCTION public.trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- الدوال الخاصة بالجداول (إذا كانت موجودة ومستخدمة)
-- CREATE OR REPLACE FUNCTION public.update_budget_months_updated_at() ...
-- CREATE OR REPLACE FUNCTION public.update_next_month_opening_balance() ...
-- CREATE OR REPLACE FUNCTION public.update_budget_years_updated_at() ...
-- CREATE OR REPLACE FUNCTION public.update_salaries_on_default_change() ...
-- CREATE OR REPLACE FUNCTION public.update_enterprise_groups_timestamp() ...
-- CREATE OR REPLACE FUNCTION public.update_monthly_closings_updated_at() ...


-- دالة تسجيل معاملة مالية (من code (1).sql)
CREATE OR REPLACE FUNCTION public.log_financial_transaction(
    p_transaction_date date,
    p_amount numeric,
    p_transaction_type public.transaction_type,
    p_description text,
    p_source_table character varying,
    p_source_record_id text,
    p_bank_id uuid,
    p_budget_month_id uuid,
    p_is_reversal boolean DEFAULT false,
    p_original_log_entry_id uuid DEFAULT NULL
)
RETURNS uuid -- سنرجع معرّف السجل الجديد في اللوغ
LANGUAGE plpgsql
AS $$
DECLARE
    v_log_id uuid;
BEGIN
    INSERT INTO public.financial_transactions_log (
        transaction_date,
        amount,
        transaction_type,
        description,
        source_table,
        source_record_id,
        bank_id,
        budget_month_id,
        is_reversal,
        original_log_entry_id
    )
    VALUES (
        p_transaction_date,
        ABS(p_amount), -- المبلغ دائماً موجب
        p_transaction_type,
        p_description,
        p_source_table,
        p_source_record_id,
        p_bank_id,
        p_budget_month_id,
        p_is_reversal,
        p_original_log_entry_id
    ) RETURNING id INTO v_log_id;

    RETURN v_log_id;
END;
$$;

-- دالة معالج المشغل العام للدفعات (من code (2).sql - تأكد من اكتمال التعريف)
CREATE OR REPLACE FUNCTION public.generic_payment_trigger_handler()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_description text;
    v_source_table_name text := TG_TABLE_NAME;
    v_original_record_id text; --  معرف السجل الأصلي (سواء كان NEW أو OLD)
    v_bank_id uuid;
    v_budget_month_id uuid;
    v_transaction_date date;
    v_amount numeric;
    v_paid_amount numeric; --  لجدول enterprise_subscriptions

    v_income_transaction_type public.transaction_type := 'إيداع';
    v_expense_transaction_type public.transaction_type := 'سحب';
    v_current_transaction_type public.transaction_type;
    v_reversed_transaction_type public.transaction_type;

    v_log_entry_id uuid;
    v_old_log_entry_id uuid;
    v_is_new_transaction boolean := false;
    v_is_old_transaction_reversal boolean := false;

BEGIN
    -- تحديد الحقول المشتركة بناءً على الجدول
    IF v_source_table_name = 'student_payments' THEN
        v_current_transaction_type := v_income_transaction_type;
        IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := NEW.id::text;
            v_bank_id := NEW.bank_id;
            v_budget_month_id := NEW.budget_month_id;
            v_transaction_date := NEW.payment_date;
            v_amount := NEW.amount;
            v_description := 'Student payment: ' || NEW.student_id::text;
            v_is_new_transaction := true;
        END IF;
        IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
            --  إذا كان UPDATE، سنعكس القديم ثم نضيف الجديد
            --  إذا كان DELETE، سنعكس القديم فقط
            v_original_record_id := OLD.id::text; --  نستخدمه لتحديد السجل الأصلي للعكس
            v_bank_id := OLD.bank_id;
            v_budget_month_id := OLD.budget_month_id;
            v_transaction_date := OLD.payment_date;
            v_amount := OLD.amount;
            v_description := 'Reversal for student payment: ' || OLD.student_id::text;
            v_is_old_transaction_reversal := true;
        END IF;

    ELSIF v_source_table_name = 'enterprise_group_payments' THEN
        v_current_transaction_type := v_income_transaction_type;
        IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := NEW.id::text;
            v_bank_id := NEW.payment_bank_id;
            v_budget_month_id := (SELECT es.budget_month_id FROM public.enterprise_subscriptions es WHERE es.id = NEW.enterprise_subscription_id);
            v_transaction_date := NEW.payment_date;
            v_amount := NEW.payment_amount;
            v_description := 'Enterprise group payment: ' || NEW.enterprise_group_id::text;
            v_is_new_transaction := true;
        END IF;
        IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := OLD.id::text;
            v_bank_id := OLD.payment_bank_id;
            v_budget_month_id := (SELECT es.budget_month_id FROM public.enterprise_subscriptions es WHERE es.id = OLD.enterprise_subscription_id);
            v_transaction_date := OLD.payment_date;
            v_amount := OLD.payment_amount;
            v_description := 'Reversal for enterprise group payment: ' || OLD.enterprise_group_id::text;
            v_is_old_transaction_reversal := true;
        END IF;

    ELSIF v_source_table_name = 'enterprise_subscriptions' THEN
        --  هذا خاص، نتعامل فقط مع تغيير paid_amount أو إدخال بدفعة أولية
        --  نفترض أن الدفعات هنا هي إيرادات
        v_current_transaction_type := v_income_transaction_type;
        IF TG_OP = 'INSERT' THEN
            IF NEW.paid_amount > 0 AND NEW.payment_bank_id IS NOT NULL AND NEW.payment_date IS NOT NULL THEN
                v_original_record_id := NEW.id::text;
                v_bank_id := NEW.payment_bank_id;
                v_budget_month_id := NEW.budget_month_id;
                v_transaction_date := NEW.payment_date;
                v_amount := NEW.paid_amount;
                v_description := 'Initial payment for enterprise subscription: ' || NEW.enterprise_id::text;
                v_is_new_transaction := true;
            END IF;
        ELSIF TG_OP = 'UPDATE' THEN
            --  نعكس الدفعة القديمة إذا تغيرت وتأثرت بها البنود المالية
            IF OLD.paid_amount > 0 AND OLD.payment_bank_id IS NOT NULL AND OLD.payment_date IS NOT NULL AND
               (OLD.paid_amount IS DISTINCT FROM NEW.paid_amount OR
                OLD.payment_bank_id IS DISTINCT FROM NEW.payment_bank_id OR
                OLD.payment_date IS DISTINCT FROM NEW.payment_date)
            THEN
                v_original_record_id := OLD.id::text; --  للعكس
                v_bank_id := OLD.payment_bank_id;
                v_budget_month_id := OLD.budget_month_id;
                v_transaction_date := OLD.payment_date;
                v_amount := OLD.paid_amount;
                v_description := 'Reversal for enterprise subscription payment update: ' || OLD.enterprise_id::text;
                v_is_old_transaction_reversal := true;
            END IF;
            --  نضيف الدفعة الجديدة إذا كانت موجودة ومختلفة
            IF NEW.paid_amount > 0 AND NEW.payment_bank_id IS NOT NULL AND NEW.payment_date IS NOT NULL THEN
                v_original_record_id := NEW.id::text; --  للجديد
                v_bank_id := NEW.payment_bank_id;
                v_budget_month_id := NEW.budget_month_id;
                v_transaction_date := NEW.payment_date;
                v_amount := NEW.paid_amount;
                v_description := 'Updated payment for enterprise subscription: ' || NEW.enterprise_id::text;
                v_is_new_transaction := true; --  سيتم التعامل معها لاحقًا
            END IF;
        END IF;
        --  لا يوجد DELETE مباشر هنا يؤثر على paid_amount عادة، الحذف يكون للسجل كله

    ELSIF v_source_table_name = 'nathriyat_transactions' THEN
        v_current_transaction_type := v_expense_transaction_type;
        IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := NEW.id::text;
            v_bank_id := NEW.bank_id;
            v_budget_month_id := NEW.budget_month_id;
            v_transaction_date := NEW.transaction_date;
            v_amount := NEW.amount;
            v_description := 'Nathriyat transaction: ' || NEW.nathriyat_type_id::text;
            v_is_new_transaction := true;
        END IF;
        IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := OLD.id::text;
            v_bank_id := OLD.bank_id;
            v_budget_month_id := OLD.budget_month_id;
            v_transaction_date := OLD.transaction_date;
            v_amount := OLD.amount;
            v_description := 'Reversal for nathriyat transaction: ' || OLD.nathriyat_type_id::text;
            v_is_old_transaction_reversal := true;
        END IF;

    ELSIF v_source_table_name = 'bus_expenses' THEN
        v_current_transaction_type := v_expense_transaction_type;
        IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := NEW.id::text;
            v_bank_id := NEW.bank_id; --  فقط إذا كان المصروف من البنك
            v_budget_month_id := NEW.budget_month_id;
            v_transaction_date := NEW.expense_date;
            v_amount := NEW.amount; --  أو total_with_tax إذا كان الدفع يشمل الضريبة من البنك
            v_description := 'Bus expense: ' || NEW.expense_type;
            v_is_new_transaction := true;
        END IF;
        IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := OLD.id::text;
            v_bank_id := OLD.bank_id;
            v_budget_month_id := OLD.budget_month_id;
            v_transaction_date := OLD.expense_date;
            v_amount := OLD.amount;
            v_description := 'Reversal for bus expense: ' || OLD.expense_type;
            v_is_old_transaction_reversal := true;
        END IF;

    ELSIF v_source_table_name = 'driver_expenses' THEN
        --  فقط إذا كان هناك bank_paid_amount و bank_id
        v_current_transaction_type := v_expense_transaction_type;
        IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
            IF NEW.bank_id IS NOT NULL AND NEW.bank_paid_amount > 0 THEN
                v_original_record_id := NEW.id::text; --  هنا ID من driver_expenses هو bigserial
                v_bank_id := NEW.bank_id;
                v_budget_month_id := NEW.budget_month_id;
                v_transaction_date := NEW.expense_date;
                v_amount := NEW.bank_paid_amount;
                v_description := 'Driver expense (bank paid): ' || NEW.expense_type;
                v_is_new_transaction := true;
            END IF;
        END IF;
        IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
            IF OLD.bank_id IS NOT NULL AND OLD.bank_paid_amount > 0 THEN
                v_original_record_id := OLD.id::text;
                v_bank_id := OLD.bank_id;
                v_budget_month_id := OLD.budget_month_id;
                v_transaction_date := OLD.expense_date;
                v_amount := OLD.bank_paid_amount;
                v_description := 'Reversal for driver expense (bank paid): ' || OLD.expense_type;
                v_is_old_transaction_reversal := true;
            END IF;
        END IF;
    ELSE
        -- جدول غير مدعوم بواسطة هذا التريجر العام
        RAISE NOTICE 'generic_payment_trigger_handler: Table % not supported.', v_source_table_name;
        RETURN NULL;
    END IF;

    -- تحديد نوع المعاملة العكسية
    IF v_current_transaction_type = v_income_transaction_type THEN
        v_reversed_transaction_type := v_expense_transaction_type;
    ELSE
        v_reversed_transaction_type := v_income_transaction_type;
    END IF;

    -- التعامل مع العمليات
    -- 1. اعكس المعاملة القديمة إذا كانت UPDATE أو DELETE
    IF v_is_old_transaction_reversal AND v_amount > 0 AND v_bank_id IS NOT NULL THEN --  فقط إذا كانت هناك معاملة بنكية لعكسها
        --  ابحث عن السجل الأصلي في اللوغ لعكسه
        SELECT id INTO v_old_log_entry_id
        FROM public.financial_transactions_log ftl
        WHERE ftl.source_table = v_source_table_name
          AND ftl.source_record_id = v_original_record_id --  هذا هو ID السجل في الجدول المصدر
          AND ftl.is_reversal = false --  ابحث عن المعاملة الأصلية غير المعكوسة
        ORDER BY ftl.created_at DESC LIMIT 1; --  خذ الأحدث إذا كان هناك عدة (نادر)

        IF v_old_log_entry_id IS NOT NULL THEN
            PERFORM public.log_financial_transaction(
                p_transaction_date := v_transaction_date, --  أو تاريخ اليوم للعكس؟
                p_amount := v_amount,
                p_transaction_type := v_reversed_transaction_type, --  النوع المعكوس
                p_description := v_description,
                p_source_table := v_source_table_name,
                p_source_record_id := v_original_record_id,
                p_bank_id := v_bank_id,
                p_budget_month_id := v_budget_month_id,
                p_is_reversal := true,
                p_original_log_entry_id := v_old_log_entry_id
            );
        ELSE
             RAISE NOTICE 'generic_payment_trigger_handler: Could not find original log entry to reverse for table %, record id %', v_source_table_name, v_original_record_id;
        END IF;
    END IF;

    -- 2. أضف المعاملة الجديدة إذا كانت INSERT أو UPDATE
    IF v_is_new_transaction AND v_amount > 0 AND v_bank_id IS NOT NULL THEN
         --  في حالة UPDATE لـ enterprise_subscriptions، قد يكون v_amount هو نفسه القديم إذا لم يتغير paid_amount
         --  لذا، نحتاج إلى التأكد من أننا لا نسجل نفس الشيء مرتين إذا لم يكن هناك تغيير حقيقي.
         --  هذا يتم التعامل معه جزئيًا بمنطق v_is_old_transaction_reversal أعلاه.
        IF v_source_table_name = 'enterprise_subscriptions' AND TG_OP = 'UPDATE' THEN
            IF OLD.paid_amount IS DISTINCT FROM NEW.paid_amount OR
               OLD.payment_bank_id IS DISTINCT FROM NEW.payment_bank_id OR
               OLD.payment_date IS DISTINCT FROM NEW.payment_date THEN
                --  فقط إذا كان هناك تغيير فعلي
                PERFORM public.log_financial_transaction(
                    p_transaction_date := v_transaction_date,
                    p_amount := v_amount,
                    p_transaction_type := v_current_transaction_type,
                    p_description := v_description,
                    p_source_table := v_source_table_name,
                    p_source_record_id := v_original_record_id,
                    p_bank_id := v_bank_id,
                    p_budget_month_id := v_budget_month_id
                );
            END IF;
        ELSE
            PERFORM public.log_financial_transaction(
                p_transaction_date := v_transaction_date,
                p_amount := v_amount,
                p_transaction_type := v_current_transaction_type,
                p_description := v_description,
                p_source_table := v_source_table_name,
                p_source_record_id := v_original_record_id,
                p_bank_id := v_bank_id,
                p_budget_month_id := v_budget_month_id
            );
        END IF;
    END IF;

    RETURN NULL; -- نتيجة التريجر AFTER لا تهم عادةً
END;
$$;


-- دالة مزامنة سجل المعاملات المالية إلى جدول المعاملات البنكية (من code (5).sql - تأكد من اكتمال التعريف)
CREATE OR REPLACE FUNCTION public.sync_log_to_bank_transactions()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_bank_transaction_id uuid;
BEGIN
    IF TG_OP = 'INSERT' THEN
        --  فقط إذا كانت المعاملة مرتبطة ببنك وليست معاملة عكسية مباشرة
        IF NEW.bank_id IS NOT NULL AND NEW.amount > 0 AND NEW.is_reversal = false THEN
            INSERT INTO public.bank_transactions (
                bank_id,
                amount,
                transaction_type, --  هذا هو transaction_type من financial_transactions_log
                transaction_date,
                description,
                transaction_source, --  يمكن أن يكون اسم الجدول المصدر الأصلي أو معرّف سجل اللوغ
                reference_id,       --  معرّف السجل في الجدول المصدر الأصلي
                reference_table,    --  اسم الجدول المصدر الأصلي
                budget_month_id,
                financial_transaction_log_id --  الربط باللوغ
            )
            VALUES (
                NEW.bank_id,
                NEW.amount,
                NEW.transaction_type, --  النوع من اللوغ (سحب/إيداع)
                NEW.transaction_date,
                NEW.description,
                NEW.source_table || '-' || NEW.source_record_id, --  كمصدر للمعاملة البنكية
                CASE WHEN NEW.source_record_id ~ '^[0-9a-f]{8}-([0-9a-f]{4}-){3}[0-9a-f]{12}$' THEN NEW.source_record_id::uuid ELSE NULL END, --  إذا كان UUID
                NEW.source_table,
                NEW.budget_month_id,
                NEW.id --  معرّف سجل اللوغ
            ) RETURNING id INTO v_bank_transaction_id;
        END IF;

    ELSIF TG_OP = 'UPDATE' THEN
        --  إذا تم تغيير شيء مؤثر على المعاملة البنكية
        IF OLD.bank_id IS DISTINCT FROM NEW.bank_id OR
           OLD.amount IS DISTINCT FROM NEW.amount OR
           OLD.transaction_date IS DISTINCT FROM NEW.transaction_date OR
           OLD.transaction_type IS DISTINCT FROM NEW.transaction_type OR
           OLD.is_reversal IS DISTINCT FROM NEW.is_reversal OR
           OLD.description IS DISTINCT FROM NEW.description OR
           OLD.budget_month_id IS DISTINCT FROM NEW.budget_month_id
        THEN
            --  احذف المعاملة البنكية القديمة المرتبطة بهذا السجل (إذا كانت موجودة)
            DELETE FROM public.bank_transactions WHERE financial_transaction_log_id = OLD.id;

            --  أضف المعاملة البنكية الجديدة إذا كان bank_id موجودًا في السجل المحدث
            --  ولا تسجل المعاملات العكسية في البنك بنفس الطريقة
            IF NEW.bank_id IS NOT NULL AND NEW.amount > 0 AND NEW.is_reversal = false THEN
                INSERT INTO public.bank_transactions (
                    bank_id, amount, transaction_type, transaction_date, description,
                    transaction_source, reference_id, reference_table, budget_month_id, financial_transaction_log_id
                )
                VALUES (
                    NEW.bank_id, NEW.amount, NEW.transaction_type, NEW.transaction_date, NEW.description,
                    NEW.source_table || '-' || NEW.source_record_id,
                    CASE WHEN NEW.source_record_id ~ '^[0-9a-f]{8}-([0-9a-f]{4}-){3}[0-9a-f]{12}$' THEN NEW.source_record_id::uuid ELSE NULL END,
                    NEW.source_table,
                    NEW.budget_month_id, NEW.id
                );
            END IF;
        END IF;

    ELSIF TG_OP = 'DELETE' THEN
        --  احذف المعاملة البنكية المرتبطة بهذا السجل
        DELETE FROM public.bank_transactions WHERE financial_transaction_log_id = OLD.id;
    END IF;

    RETURN NULL;
END;
$$;


-- =====================================================================
-- الجزء الرابع: إنشاء المشغلات (Triggers)
-- =====================================================================

-- مشغلات تحديث updated_at للجداول المختلفة
CREATE TRIGGER update_bank_accounts_updated_at BEFORE UPDATE ON public.bank_accounts FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER handle_updated_at_bank_transactions BEFORE UPDATE ON public.bank_transactions FOR EACH ROW EXECUTE FUNCTION public.trigger_set_timestamp(); -- أو update_updated_at_column
CREATE TRIGGER update_banks_updated_at BEFORE UPDATE ON public.banks FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
-- CREATE TRIGGER update_banks_timestamp BEFORE UPDATE ON public.banks FOR EACH ROW EXECUTE FUNCTION public.update_timestamp(); -- إذا كانت دالة update_timestamp مختلفة

CREATE TRIGGER update_budget_months_updated_at BEFORE UPDATE ON public.budget_months FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
-- CREATE TRIGGER trigger_update_budget_months_updated_at BEFORE UPDATE ON public.budget_months FOR EACH ROW EXECUTE FUNCTION public.update_budget_months_updated_at(); -- إذا كانت دالة خاصة
-- CREATE TRIGGER update_budget_months_timestamp BEFORE UPDATE ON public.budget_months FOR EACH ROW EXECUTE FUNCTION public.update_timestamp(); -- إذا كانت دالة خاصة
-- CREATE TRIGGER update_next_month_balance_trigger AFTER UPDATE ON public.budget_months FOR EACH ROW EXECUTE FUNCTION public.update_next_month_opening_balance();
-- CREATE TRIGGER update_next_month_opening_balance_after_update AFTER UPDATE OF closing_balance ON public.budget_months FOR EACH ROW WHEN (OLD.closing_balance IS DISTINCT FROM NEW.closing_balance) EXECUTE FUNCTION public.update_next_month_opening_balance();

CREATE TRIGGER update_budget_years_updated_at BEFORE UPDATE ON public.budget_years FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
-- CREATE TRIGGER trigger_update_budget_years_updated_at BEFORE UPDATE ON public.budget_years FOR EACH ROW EXECUTE FUNCTION public.update_budget_years_updated_at();
-- CREATE TRIGGER update_budget_years_timestamp BEFORE UPDATE ON public.budget_years FOR EACH ROW EXECUTE FUNCTION public.update_timestamp();

CREATE TRIGGER update_buses_updated_at BEFORE UPDATE ON public.buses FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column(); --  كانت update_modified_column

CREATE TRIGGER update_driver_default_settings_updated_at BEFORE UPDATE ON public.driver_default_settings FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
-- CREATE TRIGGER set_driver_default_settings_timestamp BEFORE UPDATE ON public.driver_default_settings FOR EACH ROW EXECUTE FUNCTION public.trigger_set_timestamp();
-- CREATE TRIGGER trigger_update_salaries_on_default_change AFTER UPDATE ON public.driver_default_settings FOR EACH ROW EXECUTE FUNCTION public.update_salaries_on_default_change();

CREATE TRIGGER update_drivers_updated_at BEFORE UPDATE ON public.drivers FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
-- CREATE TRIGGER update_drivers_timestamp BEFORE UPDATE ON public.drivers FOR EACH ROW EXECUTE FUNCTION public.update_timestamp();

CREATE TRIGGER handle_updated_at_enterprise_group_payments BEFORE UPDATE ON public.enterprise_group_payments FOR EACH ROW EXECUTE FUNCTION public.trigger_set_timestamp();
-- CREATE TRIGGER update_enterprise_groups_timestamp BEFORE UPDATE ON public.enterprise_groups FOR EACH ROW EXECUTE FUNCTION public.update_enterprise_groups_timestamp(); --  لجدول enterprise_groups
CREATE TRIGGER update_enterprise_groups_updated_at BEFORE UPDATE ON public.enterprise_groups FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


-- CREATE TRIGGER trigger_update_enterprise_monthly_closings_updated_at BEFORE UPDATE ON public.enterprise_monthly_closings FOR EACH ROW EXECUTE FUNCTION public.update_monthly_closings_updated_at();
CREATE TRIGGER update_enterprise_monthly_closings_updated_at BEFORE UPDATE ON public.enterprise_monthly_closings FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


CREATE TRIGGER update_enterprises_timestamp BEFORE UPDATE ON public.enterprises FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column(); --  كانت update_timestamp

CREATE TRIGGER update_nathriyat_transactions_updated_at BEFORE UPDATE ON public.nathriyat_transactions FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
-- تم إزالة: CREATE TRIGGER trigger_create_bank_transaction_after_nathriyat_insert ... سيتم التعامل معه بواسطة المشغل العام

CREATE TRIGGER update_nathriyat_types_updated_at BEFORE UPDATE ON public.nathriyat_types FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_neighborhoods_updated_at BEFORE UPDATE ON public.neighborhoods FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_schools_updated_at BEFORE UPDATE ON public.schools FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_students_updated_at BEFORE UPDATE ON public.students FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
-- CREATE TRIGGER update_students_timestamp BEFORE UPDATE ON public.students FOR EACH ROW EXECUTE FUNCTION public.update_timestamp();


-- مشغل تحديث updated_at لجدول financial_transactions_log (من code.sql)
CREATE TRIGGER update_financial_transactions_log_updated_at
BEFORE UPDATE ON public.financial_transactions_log
FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- مشغلات نظام تسجيل المعاملات المالية (من code (3).sql)
CREATE TRIGGER student_payments_financial_log_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.student_payments
FOR EACH ROW EXECUTE FUNCTION public.generic_payment_trigger_handler();

CREATE TRIGGER enterprise_group_payments_financial_log_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.enterprise_group_payments
FOR EACH ROW EXECUTE FUNCTION public.generic_payment_trigger_handler();

CREATE TRIGGER enterprise_subscriptions_financial_log_trigger
AFTER INSERT OR UPDATE OF paid_amount, payment_bank_id, payment_date ON public.enterprise_subscriptions
FOR EACH ROW EXECUTE FUNCTION public.generic_payment_trigger_handler();

CREATE TRIGGER nathriyat_transactions_financial_log_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.nathriyat_transactions
FOR EACH ROW EXECUTE FUNCTION public.generic_payment_trigger_handler();

CREATE TRIGGER bus_expenses_financial_log_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.bus_expenses
FOR EACH ROW EXECUTE FUNCTION public.generic_payment_trigger_handler();

CREATE TRIGGER driver_expenses_financial_log_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.driver_expenses
FOR EACH ROW EXECUTE FUNCTION public.generic_payment_trigger_handler();

-- مشغل مزامنة financial_transactions_log إلى bank_transactions (من code (5).sql)
CREATE TRIGGER financial_log_to_bank_transactions_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.financial_transactions_log
FOR EACH ROW EXECUTE FUNCTION public.sync_log_to_bank_transactions();

