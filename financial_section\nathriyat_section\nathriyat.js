console.log("Nathriyat script loaded.");

// --- Auth Check ---
checkAuth('../../auth.js'); // Adjust path as needed

// --- Supabase Initialization ---
let _supabase;
try {
    if (typeof supabase !== 'undefined' && supabase.createClient && typeof SUPABASE_URL !== 'undefined' && typeof SUPABASE_ANON_KEY !== 'undefined') {
        _supabase = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('Supabase Initialized for Nathriyat');
    } else {
        throw new Error('Supabase client or config variables not available.');
    }
} catch (error) {
    console.error('Error initializing Supabase client:', error);
    alert('خطأ فادح: لا يمكن الاتصال بقاعدة البيانات.');
}

// --- DOM Elements ---
const messageArea = document.getElementById('message-area');
const listMessage = document.getElementById('list-message');
const backToDashboardBtn = document.getElementById('back-to-dashboard-btn');
const nathriyatForm = document.getElementById('nathriyat-form');
const nathriyaIdField = document.getElementById('nathriya_id');
const transactionDateField = document.getElementById('transaction_date'); // Changed from expenseDateField
const amountField = document.getElementById('amount');
const notesField = document.getElementById('notes'); // Changed from descriptionField
// Removed categoryField and recipientField
// const categoryField = document.getElementById('category');
// const recipientField = document.getElementById('recipient');
const saveNathriyaBtn = document.getElementById('save-nathriya-btn');
const cancelEditBtn = document.getElementById('cancel-edit-btn');
const nathriyatTableBody = document.getElementById('nathriyat-tbody');
const nathriyatCountBadge = document.getElementById('nathriyat-count');
const paginationControls = document.getElementById('pagination-controls');
// إعادة إضافة: عناصر عرض الشهر النشط
const activeMonthDisplay = document.getElementById('active-month-display');
const activeMonthNameSpan = document.getElementById('active-month-name');
const monthSwitcherSelect = document.getElementById('month-switcher-select'); // إضافة: عنصر قائمة تبديل الشهر
// إضافة عناصر DOM الجديدة لأنواع النثريات
const addNathriyatTypeBtn = document.getElementById('add-nathriyat-type-btn');
const nathriyatTypeModal = document.getElementById('nathriyat-type-modal');
const closeTypeModalBtn = document.getElementById('close-type-modal-btn');
const cancelTypeBtn = document.getElementById('cancel-type-btn');
const nathriyatTypeForm = document.getElementById('nathriyat-type-form');
const typeModalTitle = document.getElementById('type-modal-title');
const typeIdField = document.getElementById('type_id');
const typeNameField = document.getElementById('type_name');
const typeIconField = document.getElementById('type_icon');
const typeDescriptionField = document.getElementById('type_description');
const saveTypeBtn = document.getElementById('save-type-btn');
const typeFormMessage = document.getElementById('type-form-message');
const nathriyatTypesTableBody = document.getElementById('nathriyat-types-tbody');
const typesCountBadge = document.getElementById('types-count');
const typesMessage = document.getElementById('types-message');
const bankIdSelect = document.getElementById('bank_id'); // حقل اختيار البنك في النموذج

// عناصر نافذة تفاصيل النوع
const typeDetailsModal = document.getElementById('type-details-modal');
const closeTypeDetailsModalBtn = document.getElementById('close-type-details-modal-btn');
const typeDetailsTitleSpan = document.querySelector('#type-details-title span');
const typeDetailsTableBody = document.getElementById('type-details-tbody');
const typeDetailsTotal = document.getElementById('type-details-total');
const typeDetailsMessage = document.getElementById('type-details-message');

const addNewNathriyaBtn = document.getElementById('add-new-nathriya-btn'); // زر إضافة معاملة جديدة
const addNathriyaFormSection = document.getElementById('add-nathriya-form-section'); // قسم نموذج الإضافة

// إضافة أزرار وعناصر النافذة المنبثقة
const addNathriyaModal = document.getElementById('add-nathriya-modal');
const closeAddNathriyaModalBtn = document.getElementById('close-add-nathriya-modal-btn');
const cancelNathriyaBtn = document.getElementById('cancel-nathriya-btn');
const addNathriyaForm = document.getElementById('add-nathriya-form');
const nathriyatTypeSelect = document.getElementById('nathriyat_type_id');
const bankSelect = document.querySelector('#add-nathriya-modal #bank_id'); // التأكد من أن bankSelect يستهدف القائمة داخل النافذة المنبثقة الصحيحة
// const transactionDateInput = document.getElementById('transaction_date'); // Already defined above
const amountInput = document.querySelector('#add-nathriya-modal #amount');
// const notesInput = document.getElementById('notes'); // Already defined above
const addNathriyaMessage = document.getElementById('add-nathriya-message');

// --- State ---
let currentNathriyat = [];
let editingNathriyaId = null;
let currentPage = 1;
const itemsPerPage = 15;
let totalItems = 0;
// إعادة إضافة: متغيرات حالة الشهر النشط
let activeMonthId = null;
let activeMonthInfo = null; // لتخزين اسم الشهر والسنة
let allBudgetMonths = []; // إضافة: لتخزين كل الأشهر المتاحة للتبديل
// إضافة متغيرات حالة لأنواع النثريات
let currentNathriyatTypes = [];
let editingTypeId = null;
let centralBanks = []; // لتخزين البنوك المركزية

// --- Helper Functions ---
const showMessage = (element, message, type = 'info', duration = 4000) => {
    if (!element) return;
    element.textContent = message;
    element.className = `message ${type} show`;
    element.style.display = 'block';

    if (element.timeoutId) clearTimeout(element.timeoutId);

    if (duration > 0) {
        element.timeoutId = setTimeout(() => {
            if (element.textContent === message) {
                element.classList.remove('show');
                setTimeout(() => { element.style.display = 'none'; }, 300);
            }
            element.timeoutId = null;
        }, duration);
    } else {
        element.timeoutId = null;
    }
};

const formatCurrency = (amount) => {
    const value = parseFloat(amount);
    return isNaN(value) ? '0.00' : value.toFixed(2);
};

const formatDate = (dateString) => {
    if (!dateString) return '';
    try {
        // Assuming dateString is in 'YYYY-MM-DD' format from the database
        const date = new Date(dateString + 'T00:00:00Z'); // Use UTC to avoid timezone issues with date-only strings
        return date.toLocaleDateString('ar-SA', { year: 'numeric', month: '2-digit', day: '2-digit' });
    } catch (e) {
        console.error("Error formatting date:", dateString, e);
        return dateString; // Return original string if formatting fails
    }
};

// إعادة إضافة: دالة getMonthName (قد لا تكون ضرورية إذا كنا نجلب الاسم مباشرة)
const getMonthName = (monthNumber) => {
    const names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"];
    const index = parseInt(monthNumber, 10) - 1;
    return names[index] || `شهر ${monthNumber}`;
};

// --- Pagination Functions ---
const renderPaginationControls = () => {
    // ... (Keep existing pagination logic or add if not present) ...
    // Ensure it uses `totalItems` and `currentPage`
};

const goToPage = (page) => {
    // ... (Keep existing pagination logic or add if not present) ...
    // Ensure it calls `fetchNathriyat()` after updating `currentPage`
};

// --- Data Fetching ---

// تعديل: دالة جلب الشهر النشط لتعطي الأولوية للشهر المحدد من قبل المستخدم
const fetchActiveMonth = async () => {
    if (!_supabase) {
        showMessage(messageArea, 'خطأ: لا يمكن الاتصال بقاعدة البيانات.', 'error', 0);
        activeMonthId = null;
        sessionStorage.removeItem('activeBudgetMonthId'); // Clear both potential keys on error
        sessionStorage.removeItem('selectedBudgetMonthId');
        if(activeMonthDisplay) activeMonthDisplay.classList.remove('loaded');
        if (activeMonthNameSpan) activeMonthNameSpan.textContent = 'خطأ';
        if(monthSwitcherSelect) monthSwitcherSelect.style.display = 'none'; // Hide switcher on error
        return false;
    }

    // --- PRIORITY 1: Check for user selection from select_budget_month.html ---
    const userSelectedMonthId = sessionStorage.getItem('selectedBudgetMonthId');
    if (userSelectedMonthId) {
        console.log(`Found user-selected month ID in sessionStorage: ${userSelectedMonthId}. Validating...`);
        try {
            const { data: selectedMonthData, error: selectedMonthError } = await _supabase
                .from('budget_months')
                .select('id, month_name, budget_year_id') // Select necessary fields
                .eq('id', userSelectedMonthId)
                .single();

            if (selectedMonthError) {
                console.warn(`Error validating user-selected month ID ${userSelectedMonthId}:`, selectedMonthError.message);
                sessionStorage.removeItem('selectedBudgetMonthId'); // Remove invalid ID
                // Continue to fallback logic
            } else if (selectedMonthData) {
                activeMonthId = selectedMonthData.id;
                // IMPORTANT: Update activeBudgetMonthId as well for consistency within this page's logic (switcher)
                sessionStorage.setItem('activeBudgetMonthId', activeMonthId);
                console.log(`Validated user-selected month: ID: ${activeMonthId}, Name: ${selectedMonthData.month_name}`);
                await fetchAndDisplayActiveMonthDetails(activeMonthId, selectedMonthData);
                if(activeMonthDisplay) activeMonthDisplay.classList.add('loaded');
                await fetchAllMonthsAndPopulateSwitcher(); // Populate switcher
                return true; // Success using user selection
            } else {
                 console.warn(`User-selected month ID ${userSelectedMonthId} not found in DB. Clearing sessionStorage.`);
                 sessionStorage.removeItem('selectedBudgetMonthId');
                 // Continue to fallback logic
            }
        } catch (validationError) {
            console.error('Unexpected error during user-selected month validation:', validationError);
            sessionStorage.removeItem('selectedBudgetMonthId'); // Remove potentially problematic ID
            // Continue to fallback logic
        }
    } else {
        console.log("No user-selected month ID found in sessionStorage (selectedBudgetMonthId). Checking for activeBudgetMonthId...");
    }
    // --- END OF PRIORITY 1 ---


    // --- PRIORITY 2 (Fallback): Check for active month ID (e.g., from dashboard or previous switch) ---
    const storedMonthId = sessionStorage.getItem('activeBudgetMonthId');
    if (storedMonthId) {
        console.log(`Found active month ID in sessionStorage: ${storedMonthId}. Validating...`);
        try {
            // Reuse validation logic from previous step, but use storedMonthId
            const { data: storedMonthData, error: storedMonthError } = await _supabase
                .from('budget_months')
                .select('id, month_name, budget_year_id, is_active') // Keep is_active check here if needed
                .eq('id', storedMonthId)
                .single();

            if (storedMonthError) {
                console.warn(`Error validating stored active month ID ${storedMonthId}:`, storedMonthError.message);
                sessionStorage.removeItem('activeBudgetMonthId'); // Remove invalid ID
            } else if (storedMonthData) {
                activeMonthId = storedMonthData.id;
                console.log(`Validated active month from sessionStorage: ID: ${activeMonthId}, Name: ${storedMonthData.month_name}`);
                await fetchAndDisplayActiveMonthDetails(activeMonthId, storedMonthData);
                if(activeMonthDisplay) activeMonthDisplay.classList.add('loaded');
                await fetchAllMonthsAndPopulateSwitcher(); // Populate switcher
                return true; // Success using stored active month
            } else {
                 console.warn(`Stored active month ID ${storedMonthId} not found in DB. Clearing sessionStorage.`);
                 sessionStorage.removeItem('activeBudgetMonthId');
            }
        } catch (validationError) {
            console.error('Unexpected error during active month validation:', validationError);
            sessionStorage.removeItem('activeBudgetMonthId');
        }
    } else {
        console.log("No active month ID found in sessionStorage (activeBudgetMonthId). Falling back to DB query for is_active=true.");
    }
    // --- END OF PRIORITY 2 ---


    // --- PRIORITY 3 (Final Fallback): Query DB for is_active = true ---
    console.log("Executing final fallback: Fetching active month directly from DB where is_active=true...");
    try {
        const { data: activeMonths, error } = await _supabase
            .from('budget_months')
            .select('id, month_name, budget_year_id')
            .eq('is_active', true);

        if (error) throw error;

        let activeData = null;
        if (activeMonths && activeMonths.length >= 1) {
            activeData = activeMonths[0]; // Use the first one found
            activeMonthId = activeData.id;
            sessionStorage.setItem('activeBudgetMonthId', activeMonthId); // Update session storage

            if (activeMonths.length > 1) {
                console.warn(`Fallback Warning: Found ${activeMonths.length} active months in DB. Using the first one.`);
                showMessage(messageArea, `تحذير: تم العثور على ${activeMonths.length} أشهر نشطة في قاعدة البيانات ولم يتم تحديد شهر بواسطة المستخدم. تم استخدام الشهر الأول.`, 'warning', 5000);
            } else {
                 console.log(`Fallback successful: Found single active month in DB: ID: ${activeMonthId}, Name: ${activeData.month_name}`);
            }

            await fetchAndDisplayActiveMonthDetails(activeMonthId, activeData);
            if(activeMonthDisplay) activeMonthDisplay.classList.add('loaded');
            await fetchAllMonthsAndPopulateSwitcher(); // Populate switcher
            return true; // Success
        } else {
            // No active month found in DB
            console.warn('Fallback failed: No active month found in the database.');
            activeMonthId = null;
            sessionStorage.removeItem('activeBudgetMonthId'); // Ensure cleared
            sessionStorage.removeItem('selectedBudgetMonthId'); // Ensure cleared
            showMessage(messageArea, 'لم يتم العثور على شهر مالي نشط أو محدد. يرجى تفعيل أو تحديد شهر أولاً.', 'warning', 0);
            if (activeMonthNameSpan) activeMonthNameSpan.textContent = 'غير محدد';
            if (nathriyatTableBody) nathriyatTableBody.innerHTML = `<tr><td colspan="6" class="loading-message">الرجاء تحديد شهر نشط أولاً.</td></tr>`;
            if (nathriyatCountBadge) nathriyatCountBadge.textContent = '0';
            if(activeMonthDisplay) activeMonthDisplay.classList.remove('loaded');
            if(monthSwitcherSelect) monthSwitcherSelect.style.display = 'none'; // Hide switcher
            return false; // Failure
        }
    } catch (error) {
        console.error('Error during final fallback fetch of active month from DB:', error);
        showMessage(messageArea, `خطأ في جلب الشهر النشط: ${error.message}`, 'error', 0);
        activeMonthId = null;
        sessionStorage.removeItem('activeBudgetMonthId'); // Ensure cleared
        sessionStorage.removeItem('selectedBudgetMonthId'); // Ensure cleared
        if (activeMonthNameSpan) activeMonthNameSpan.textContent = 'خطأ';
        if(activeMonthDisplay) activeMonthDisplay.classList.remove('loaded');
        if(monthSwitcherSelect) monthSwitcherSelect.style.display = 'none'; // Hide switcher on error
        return false; // Failure
    }
};

// إعادة إضافة: دالة جلب وعرض تفاصيل الشهر النشط
const fetchAndDisplayActiveMonthDetails = async (monthId, monthData = null) => {
    if (!activeMonthNameSpan) return;

    // Function to display the month and year
    const displayMonthYear = (name, yearNum) => { // Changed parameter name for clarity
        activeMonthNameSpan.textContent = `${name} ${yearNum}`;
        activeMonthInfo = { name, year: yearNum }; // Store info using 'year' key for consistency if needed elsewhere, but value is yearNum
        if(activeMonthDisplay) activeMonthDisplay.classList.add('loaded');
    };

    // If month data (including year_id) is already available
    if (monthData && monthData.month_name && monthData.budget_year_id) {
        try {
            const { data: yearData, error: yearError } = await _supabase
                .from('budget_years')
                .select('year_number') // <-- Corrected column name
                .eq('id', monthData.budget_year_id)
                .single();

            if (yearError) throw yearError;

            if (yearData) {
                displayMonthYear(monthData.month_name, yearData.year_number); // <-- Use correct property
            } else {
                activeMonthNameSpan.textContent = `${monthData.month_name} (سنة غير معروفة)`;
                if(activeMonthDisplay) activeMonthDisplay.classList.remove('loaded');
            }
        } catch (error) {
            console.error('Error fetching budget year for active month:', error);
            activeMonthNameSpan.textContent = `${monthData.month_name} (خطأ في جلب السنة)`;
            if(activeMonthDisplay) activeMonthDisplay.classList.remove('loaded');
        }
        return;
    }

    // If only monthId is available (e.g., from sessionStorage)
    activeMonthNameSpan.textContent = 'جاري تحميل...';
    if(activeMonthDisplay) activeMonthDisplay.classList.remove('loaded');
    try {
        const { data, error } = await _supabase
            .from('budget_months')
            .select(`
                month_name,
                budget_years ( year_number ) // <-- Corrected column name
            `)
            .eq('id', monthId)
            .single();

        if (error) throw error;

        // Use the correct nested property name
        if (data && data.month_name && data.budget_years && data.budget_years.year_number) {
            displayMonthYear(data.month_name, data.budget_years.year_number); // <-- Use correct property
        } else if (data && data.month_name) {
            activeMonthNameSpan.textContent = `${data.month_name} (سنة غير معروفة)`;
        } else {
            activeMonthNameSpan.textContent = 'غير محدد';
        }
    } catch (error) {
        console.error('Error fetching active month details:', error);
        activeMonthNameSpan.textContent = 'خطأ في التحميل';
    }
};

// تعديل: دالة جلب النثريات لإعادة الاعتماد على الشهر النشط
const fetchNathriyat = async () => {
    // التأكد من وجود شهر نشط قبل المتابعة
    if (!activeMonthId) {
        console.warn("fetchNathriyat called without an activeMonthId.");
        if (nathriyatTableBody) nathriyatTableBody.innerHTML = `<tr><td colspan="6" class="loading-message">الرجاء تحديد شهر نشط أولاً لعرض المعاملات.</td></tr>`; // Adjusted colspan
        if (listMessage) showMessage(listMessage, 'لم يتم تحديد شهر نشط لعرض المعاملات.', 'warning', 0);
        if (nathriyatCountBadge) nathriyatCountBadge.textContent = '0';
        currentNathriyat = [];
        totalItems = 0;
        renderPaginationControls();
        return;
    }

    if (!nathriyatTableBody) return; // Ensure table body exists
    // تعديل رسالة التحميل لتشمل الشهر
    const monthYearText = activeMonthInfo ? `(${activeMonthInfo.name} ${activeMonthInfo.year})` : `(${activeMonthId})`;
    nathriyatTableBody.innerHTML = `<tr><td colspan="6" class="loading-message">جاري تحميل المعاملات النثرية للشهر ${monthYearText}...</td></tr>`; // Adjusted colspan
    if (paginationControls) paginationControls.innerHTML = '';
    if (listMessage) listMessage.style.display = 'none';

    try {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const tableName = 'nathriyat_transactions';
        console.log(`Fetching data from table: ${tableName} for month ID: ${activeMonthId}`);

        // بناء الاستعلام مع الفلترة الصحيحة للشهر
        let query = _supabase
            .from(tableName)
            .select(`
                *,
                nathriyat_types ( name ),
                banks ( name )
            `, { count: 'exact' })
            .eq('budget_month_id', activeMonthId); // <-- إعادة فلترة الشهر

        // إضافة الترتيب والتقسيم للصفحات
        query = query.order('transaction_date', { ascending: false })
                     .range(startIndex, startIndex + itemsPerPage - 1);

        // تنفيذ الاستعلام
        const { data, error, count } = await query;

        if (error) throw error;

        currentNathriyat = data || [];
        totalItems = count || 0;

        renderNathriyatTable();
        renderPaginationControls();
        if (nathriyatCountBadge) nathriyatCountBadge.textContent = totalItems;

        if (totalItems === 0 && listMessage) {
            // تعديل الرسالة لتشمل الشهر
            showMessage(listMessage, `لا توجد معاملات نثرية مسجلة للشهر ${monthYearText}.`, 'info', 0);
        } else if (listMessage) {
            listMessage.style.display = 'none'; // إخفاء الرسالة إذا كان هناك بيانات
        }

    } catch (error) {
        console.error('Error fetching nathriyat transactions:', error);
        showMessage(listMessage, `خطأ في جلب المعاملات النثرية: ${error.message}`, 'error', 0);
        currentNathriyat = [];
        totalItems = 0;
        renderNathriyatTable(); // Render empty state on error
        renderPaginationControls();
        if (nathriyatCountBadge) nathriyatCountBadge.textContent = '0';
    }
};

// إضافة: دالة جلب أنواع النثريات
const fetchNathriyatTypes = async () => {
    if (!_supabase || !nathriyatTypesTableBody) return;
    
    nathriyatTypesTableBody.innerHTML = `<tr><td colspan="4" class="loading-message">جاري تحميل أنواع النثريات...</td></tr>`;
    if (typesMessage) typesMessage.style.display = 'none';

    try {
        const { data, error, count } = await _supabase
            .from('nathriyat_types')
            .select('*', { count: 'exact' })
            .order('name', { ascending: true });

        if (error) throw error;

        currentNathriyatTypes = data || [];
        
        // تحديث شارة العدد
        if (typesCountBadge) typesCountBadge.textContent = currentNathriyatTypes.length;
        
        // عرض قائمة الأنواع
        renderNathriyatTypesTable();
        
        // تحقق إذا كانت القائمة فارغة
        if (currentNathriyatTypes.length === 0) {
            showMessage(typesMessage, 'لا توجد أنواع نثريات مسجلة. يمكنك إضافة نوع جديد من خلال الزر أعلاه.', 'info', 0);
        }

        // إذا كان هذا الاستدعاء الأول، قم بتحديث قائمة الأنواع في نموذج إضافة المعاملة النثرية
        await populateNathriyatTypesDropdown();

    } catch (error) {
        console.error('Error fetching nathriyat types:', error);
        showMessage(typesMessage, `خطأ في جلب أنواع النثريات: ${error.message}`, 'error', 0);
        nathriyatTypesTableBody.innerHTML = '<tr><td colspan="4" class="loading-message error">خطأ في تحميل البيانات.</td></tr>';
    }
};

// إضافة: دالة لجلب البنوك (المركزية فقط)
const fetchBanks = async () => {
    if (!_supabase || !bankIdSelect) return;

    try {
        // جلب البنوك من النوع 'مركزي' فقط
        const { data, error } = await _supabase
            .from('banks')
            .select('id, name')
            .eq('bank_type', 'مركزي') // فلترة حسب النوع المركزي
            .order('name', { ascending: true });

        if (error) throw error;

        centralBanks = data || [];
        console.log(`Fetched ${centralBanks.length} central banks.`);

        // ملء قائمة البنوك في نموذج إضافة المعاملة
        populateBankDropdown();

    } catch (error) {
        console.error('Error fetching central banks:', error);
        showMessage(messageArea, `خطأ في جلب قائمة البنوك المركزية: ${error.message}`, 'error', 0);
        centralBanks = [];
        populateBankDropdown(); // مسح القائمة أو عرض رسالة خطأ
    }
};

// إضافة: دالة جلب كل الأشهر لملء القائمة المنسدلة
const fetchAllMonthsAndPopulateSwitcher = async () => {
    if (!_supabase || !monthSwitcherSelect) return;

    monthSwitcherSelect.innerHTML = '<option value="">جاري تحميل الأشهر...</option>';
    monthSwitcherSelect.disabled = true;

    try {
        const { data, error } = await _supabase
            .from('budget_months')
            .select(`
                id,
                month_name,
                budget_years ( year_number )
            `)
            .order('budget_years(year_number)', { ascending: false })
            .order('month_number', { ascending: false }); // Or however you want to order months

        if (error) throw error;

        allBudgetMonths = data || [];
        populateMonthSwitcher(); // Call the population function

    } catch (error) {
        console.error('Error fetching all budget months:', error);
        monthSwitcherSelect.innerHTML = '<option value="">خطأ في تحميل الأشهر</option>';
        monthSwitcherSelect.style.display = 'none'; // Hide on error
    } finally {
        monthSwitcherSelect.disabled = false;
    }
};

// --- UI Population ---

// إضافة: دالة لملء قائمة تبديل الشهر
const populateMonthSwitcher = () => {
    if (!monthSwitcherSelect || allBudgetMonths.length === 0) {
        if(monthSwitcherSelect) monthSwitcherSelect.style.display = 'none';
        return;
    }

    monthSwitcherSelect.innerHTML = ''; // Clear existing options

    allBudgetMonths.forEach(month => {
        if (month.budget_years) { // Ensure year data is present
            const option = document.createElement('option');
            option.value = month.id;
            option.textContent = `${month.month_name} ${month.budget_years.year_number}`;
            if (month.id === activeMonthId) {
                option.selected = true;
            }
            monthSwitcherSelect.appendChild(option);
        }
    });

    monthSwitcherSelect.style.display = 'inline-block'; // Show the dropdown
};

// إضافة: دالة لملء قائمة البنوك في نموذج إضافة المعاملة
const populateBankDropdown = () => {
    if (!bankIdSelect) return;

    bankIdSelect.innerHTML = '<option value="" disabled selected>اختر البنك...</option>';

    if (centralBanks.length === 0) {
        bankIdSelect.innerHTML += '<option value="" disabled>لا توجد بنوك مركزية متاحة</option>';
        return;
    }

    centralBanks.forEach(bank => {
        const option = document.createElement('option');
        option.value = bank.id;
        option.textContent = bank.name;
        bankIdSelect.appendChild(option);
    });
};

// إضافة: دالة لملء قائمة منسدلة بأنواع النثريات في نموذج المعاملات
const populateNathriyatTypesDropdown = async () => {
    // إنشاء قائمة منسدلة في نموذج إضافة المعاملة النثرية إذا لم تكن موجودة بالفعل
    const transactionForm = document.getElementById('nathriyat-form');
    
    if (!transactionForm) return;
    
    // تحقق مما إذا كانت القائمة المنسدلة موجودة بالفعل
    let typeSelect = document.getElementById('nathriyat-type-select');
    
    // إذا لم تكن موجودة، أنشئها قبل حقل المبلغ
    if (!typeSelect) {
        const amountField = document.getElementById('amount');
        if (!amountField) return;
        
        const amountGroup = amountField.closest('.form-group');
        if (!amountGroup) return;
        
        // إنشاء مجموعة النموذج للقائمة المنسدلة
        const typeGroup = document.createElement('div');
        typeGroup.className = 'form-group';
        
        // إنشاء التسمية
        const typeLabel = document.createElement('label');
        typeLabel.htmlFor = 'nathriyat-type-select';
        typeLabel.innerHTML = 'نوع النثرية <span class="required">*</span>';
        
        // إنشاء القائمة المنسدلة
        typeSelect = document.createElement('select');
        typeSelect.id = 'nathriyat-type-select';
        typeSelect.required = true;
        
        // إضافة العناصر إلى المجموعة
        typeGroup.appendChild(typeLabel);
        typeGroup.appendChild(typeSelect);
        
        // إدراج المجموعة قبل مجموعة المبلغ
        amountGroup.parentNode.insertBefore(typeGroup, amountGroup);
    }
    
    // ملء القائمة المنسدلة بأنواع النثريات
    typeSelect.innerHTML = '<option value="" disabled selected>اختر نوع النثرية</option>';
    
    // إذا كانت الأنواع محملة، استخدمها، وإلا قم بجلبها
    let types = currentNathriyatTypes;
    if (!types.length) {
        try {
            const { data, error } = await _supabase
                .from('nathriyat_types')
                .select('id, name')
                .order('name', { ascending: true });
                
            if (error) throw error;
            types = data || [];
        } catch (error) {
            console.error('Error fetching nathriyat types for dropdown:', error);
            typeSelect.innerHTML += '<option value="" disabled>فشل تحميل الأنواع</option>';
            return;
        }
    }
    
    // إضافة كل نوع كخيار
    types.forEach(type => {
        const option = document.createElement('option');
        option.value = type.id;
        option.textContent = type.name;
        typeSelect.appendChild(option);
    });
    
    // إذا كان عدد الأنواع صفرًا، أضف رسالة
    if (types.length === 0) {
        typeSelect.innerHTML += '<option value="" disabled>لا توجد أنواع مسجلة</option>';
    }
};

// --- Rendering ---
const renderNathriyatTable = () => {
    if (!nathriyatTableBody) return;
    nathriyatTableBody.innerHTML = ''; // Clear previous content

    if (currentNathriyat.length === 0) {
        // تعديل الرسالة لتشمل الشهر النشط
        const monthYearText = activeMonthInfo ? `(${activeMonthInfo.name} ${activeMonthInfo.year})` : '';
        const message = totalItems === 0 && activeMonthId ? `لا توجد معاملات نثرية مسجلة لهذا الشهر ${monthYearText}.` : 'جاري التحميل أو لا يوجد شهر نشط.';
        nathriyatTableBody.innerHTML = `<tr><td colspan="6" class="loading-message">${message}</td></tr>`; // Adjusted colspan
        return;
    }

    currentNathriyat.forEach(item => {
        const row = document.createElement('tr');
        row.dataset.id = item.id;

        // الحصول على أسماء النوع والبنك من البيانات المدمجة
        const typeName = item.nathriyat_types?.name || 'غير محدد';
        const bankName = item.banks?.name || 'غير محدد';

        row.innerHTML = `
            <td>${formatDate(item.transaction_date)}</td>
            <td>${typeName}</td>
            <td>${item.notes || '-'}</td>
            <td>${formatCurrency(item.amount)} ريال</td>
            <td>${bankName}</td>
            <td>
                <button class="action-btn edit-btn" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" title="حذف">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </td>
        `;

        // Add event listeners for edit/delete
        // تعديل: استدعاء دالة فتح النافذة المنبثقة للتعديل
        row.querySelector('.edit-btn').addEventListener('click', () => openAddNathriyaModal(item));
        row.querySelector('.delete-btn').addEventListener('click', () => handleDeleteNathriya(item.id, item.notes)); // Pass notes for confirmation message

        nathriyatTableBody.appendChild(row);
    });
};

// إضافة: دالة لعرض جدول أنواع النثريات
const renderNathriyatTypesTable = () => {
    if (!nathriyatTypesTableBody) return;
    nathriyatTypesTableBody.innerHTML = ''; // مسح المحتوى السابق

    if (currentNathriyatTypes.length === 0) {
        nathriyatTypesTableBody.innerHTML = `<tr><td colspan="4" class="loading-message">لا توجد أنواع نثريات مسجلة.</td></tr>`;
        return;
    }

    currentNathriyatTypes.forEach(type => {
        const row = document.createElement('tr');
        row.dataset.id = type.id;

        // تحضير عرض الأيقونة
        const iconDisplay = type.icon ? 
            `<div class="icon-preview"><i class="fas ${type.icon}"></i></div>` : 
            '<span class="text-muted">-</span>';

        row.innerHTML = `
            <td>${type.name || '-'}</td>
            <td class="type-icon">${iconDisplay}</td>
            <td>${type.description || '-'}</td>
            <td>
                <button class="action-btn details-btn" title="عرض التفاصيل">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn edit-btn" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" title="حذف">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </td>
        `;

        // إضافة مستمعي الأحداث للأزرار
        row.querySelector('.details-btn').addEventListener('click', () => openTypeDetailsModal(type.id, type.name)); // استدعاء دالة فتح التفاصيل
        row.querySelector('.edit-btn').addEventListener('click', () => handleEditType(type));
        row.querySelector('.delete-btn').addEventListener('click', () => handleDeleteType(type.id, type.name));

        nathriyatTypesTableBody.appendChild(row);
    });
};

// --- Form Handling ---
// تعديل: إزالة النموذج الرئيسي أو إعادة توجيهه للنافذة المنبثقة
// النموذج الرئيسي لم يعد يستخدم لإضافة/تعديل المعاملات مباشرة
const resetForm = () => {
    // This function might be obsolete or needs repurposing if the main form is removed/hidden
    if (nathriyatForm) nathriyatForm.reset();
    nathriyaIdField.value = '';
    editingNathriyaId = null;
    // saveNathriyaBtn.textContent = 'حفظ المعاملة';
    // saveNathriyaBtn.innerHTML = '<i class="fas fa-save"></i> حفظ المعاملة';
    // cancelEditBtn.style.display = 'none';
    // transactionDateField.valueAsDate = new Date();
    // if (bankIdSelect) bankIdSelect.selectedIndex = 0;
    // const typeSelect = document.getElementById('nathriyat-type-select');
    // if (typeSelect) typeSelect.selectedIndex = 0;
    console.log("Main form reset (potentially obsolete).");
};

// إزالة أو تعديل: معالج النموذج الرئيسي لم يعد يستخدم لإضافة/تعديل
const handleFormSubmit = async (event) => {
    event.preventDefault();
    showMessage(messageArea, 'للإضافة أو التعديل، يرجى استخدام زر "إضافة معاملة نثرية جديدة".', 'info');
    // Prevent default form submission logic as it's handled by the modal now.
    /*
    // إزالة التحقق من الشهر النشط
    // if (!activeMonthId) { ... }

    // ... (get form data) ...

    // إزالة budget_month_id من formData
    const formData = {
        transaction_date: transactionDateField.value,
        amount: parseFloat(amountField.value),
        notes: notesField.value.trim() || null,
        // budget_month_id: activeMonthId, // <-- Removed
        nathriyat_type_id: nathriyatTypeSelect.value,
        bank_id: bankIdSelect.value
    };

    // ... (validation) ...

    saveNathriyaBtn.disabled = true;
    saveNathriyaBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

    try {
        // ... (update or insert logic, without budget_month_id) ...
        // ... (show success message) ...
        resetForm();
        fetchNathriyat(); // Refresh the table
    } catch (error) {
        // ... (error handling) ...
    } finally {
        saveNathriyaBtn.disabled = false;
        // Text content is reset in resetForm
    }
    */
};

// --- Edit and Delete Handling ---
// تعديل: دالة التعديل الآن تفتح النافذة المنبثقة بدلاً من ملء النموذج الرئيسي
const handleEditNathriya = (nathriya) => {
    openAddNathriyaModal(nathriya); // Open the modal for editing
    /* --- إزالة الكود القديم لملء النموذج الرئيسي ---
    editingNathriyaId = nathriya.id;
    nathriyaIdField.value = nathriya.id;
    transactionDateField.value = nathriya.transaction_date;
    amountField.value = nathriya.amount;
    notesField.value = nathriya.notes || '';

    const typeSelect = document.getElementById('nathriyat-type-select');
    if (typeSelect) typeSelect.value = nathriya.nathriyat_type_id || '';
    if (bankIdSelect) bankIdSelect.value = nathriya.bank_id || '';

    saveNathriyaBtn.textContent = 'تحديث المعاملة';
    saveNathriyaBtn.innerHTML = '<i class="fas fa-save"></i> تحديث المعاملة';
    cancelEditBtn.style.display = 'inline-block';
    window.scrollTo({ top: 0, behavior: 'smooth' });
    */
};

const handleDeleteNathriya = async (id, notes) => {
    // ... (delete logic remains the same) ...
    if (!confirm(`هل أنت متأكد من حذف المعاملة النثرية: "${notes || 'بدون ملاحظات'}"؟ لا يمكن التراجع عن هذا الإجراء.`)) { // Changed confirmation message
        return;
    }

    showMessage(messageArea, 'جاري حذف المعاملة النثرية...', 'info'); // Changed text

    try {
        const tableName = 'nathriyat_transactions'; // *** اسم الجدول الصحيح ***
        const { error } = await _supabase
            .from(tableName)
            .delete()
            .eq('id', id);

        if (error) throw error;

        showMessage(messageArea, 'تم حذف المعاملة النثرية بنجاح.', 'success'); // Changed text
        fetchNathriyat(); // Refresh the table

    } catch (error) {
        console.error('Error deleting nathriya transaction:', error); // Changed log message
        showMessage(messageArea, `خطأ في حذف المعاملة النثرية: ${error.message}`, 'error'); // Changed text
    }
};

// إضافة: دالة لفتح النافذة المنبثقة لإضافة/تعديل نوع النثرية
const openTypeModal = (typeToEdit = null) => {
    resetTypeForm();
    
    if (typeToEdit) {
        // تعديل نوع موجود
        editingTypeId = typeToEdit.id;
        typeIdField.value = typeToEdit.id;
        typeNameField.value = typeToEdit.name || '';
        typeIconField.value = typeToEdit.icon || '';
        typeDescriptionField.value = typeToEdit.description || '';
        
        typeModalTitle.textContent = 'تعديل نوع النثرية';
        saveTypeBtn.innerHTML = '<i class="fas fa-save"></i> تحديث النوع';
    } else {
        // إضافة نوع جديد
        editingTypeId = null;
        typeModalTitle.textContent = 'إضافة نوع نثرية جديد';
        saveTypeBtn.innerHTML = '<i class="fas fa-save"></i> حفظ النوع';
    }
    
    // إعداد منتقي الأيقونات
    setupIconSelector();
    
    // تعديل: استخدام الفئة show بدلاً من style.display
    if (nathriyatTypeModal) {
        nathriyatTypeModal.classList.add('show');
        document.body.style.overflow = 'hidden'; // منع التمرير
    }
    
    // التركيز على حقل الاسم
    setTimeout(() => {
        typeNameField.focus();
    }, 100);
};

// إضافة: دالة لإعادة تعيين نموذج إضافة/تعديل الأنواع
const resetTypeForm = () => {
    nathriyatTypeForm.reset();
    typeIdField.value = '';
    editingTypeId = null;
    
    if (typeFormMessage) typeFormMessage.style.display = 'none';
};

// إضافة: دالة لإغلاق النافذة المنبثقة
const closeTypeModal = () => {
    // تعديل: استخدام الفئة show بدلاً من style.display
    if (nathriyatTypeModal) {
        nathriyatTypeModal.classList.remove('show');
        document.body.style.overflow = ''; // استعادة التمرير
    }
    resetTypeForm();
};

// إضافة: دالة لمعالجة حفظ نوع النثرية
const handleTypeFormSubmit = async (event) => {
    event.preventDefault();
    
    const typeName = typeNameField.value.trim();
    const typeIcon = typeIconField.value.trim();
    const typeDescription = typeDescriptionField.value.trim();
    
    // التحقق من إدخال الاسم
    if (!typeName) {
        showMessage(typeFormMessage, 'يرجى إدخال اسم النوع.', 'warning');
        typeNameField.focus();
        return;
    }
    
    // تعطيل زر الحفظ وإظهار رسالة التحميل
    saveTypeBtn.disabled = true;
    saveTypeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    showMessage(typeFormMessage, 'جاري حفظ البيانات...', 'info');
    
    try {
        let result;
        
        if (editingTypeId) {
            // تحديث نوع موجود
            const { data, error } = await _supabase
                .from('nathriyat_types')
                .update({
                    name: typeName,
                    icon: typeIcon || null,
                    description: typeDescription || null
                })
                .eq('id', editingTypeId)
                .select()
                .single();
                
            if (error) throw error;
            result = data;
            showMessage(typeFormMessage, 'تم تحديث النوع بنجاح!', 'success');
        } else {
            // إضافة نوع جديد
            const { data, error } = await _supabase
                .from('nathriyat_types')
                .insert({
                    name: typeName,
                    icon: typeIcon || null,
                    description: typeDescription || null
                })
                .select()
                .single();
                
            if (error) throw error;
            result = data;
            showMessage(typeFormMessage, 'تمت إضافة النوع بنجاح!', 'success');
        }
        
        // تحديث قائمة الأنواع
        fetchNathriyatTypes();
        
        // إغلاق النافذة بعد فترة وجيزة
        setTimeout(() => {
            closeTypeModal();
        }, 1500);
        
    } catch (error) {
        console.error('Error saving nathriyat type:', error);
        
        // عرض رسالة خطأ مخصصة بناءً على نوع الخطأ
        let errorMessage = 'حدث خطأ أثناء حفظ النوع.';
        
        if (error.message.includes('nathriyat_types_name_unique')) {
            errorMessage = 'هذا الاسم موجود بالفعل. يرجى اختيار اسم آخر.';
        } else if (error.message) {
            errorMessage += ` ${error.message}`;
        }
        
        showMessage(typeFormMessage, errorMessage, 'error');
    } finally {
        // إعادة تفعيل زر الحفظ
        saveTypeBtn.disabled = false;
        saveTypeBtn.innerHTML = editingTypeId ? '<i class="fas fa-save"></i> تحديث النوع' : '<i class="fas fa-save"></i> حفظ النوع';
    }
};

// إضافة: دالة لمعالجة تعديل نوع
const handleEditType = (type) => {
    openTypeModal(type);
};

// إضافة: دالة لمعالجة حذف نوع
const handleDeleteType = async (typeId, typeName) => {
    if (!confirm(`هل أنت متأكد من حذف نوع النثرية "${typeName}"؟\nسيؤدي هذا إلى حذف جميع السجلات المرتبطة به.`)) {
        return;
    }
    
    showMessage(typesMessage, 'جاري حذف النوع...', 'info');
    
    try {
        const { error } = await _supabase
            .from('nathriyat_types')
            .delete()
            .eq('id', typeId);
            
        if (error) throw error;
        
        showMessage(typesMessage, `تم حذف نوع النثرية "${typeName}" بنجاح.`, 'success');
        
        // تحديث قائمة الأنواع
        fetchNathriyatTypes();
        
    } catch (error) {
        console.error('Error deleting nathriyat type:', error);
        
        // عرض رسالة خطأ مفصلة
        let errorMessage = `فشل حذف نوع النثرية.`;
        
        if (error.message.includes('violates foreign key constraint')) {
            errorMessage = `لا يمكن حذف هذا النوع لأنه مستخدم في معاملات نثرية موجودة.`;
        } else if (error.message) {
            errorMessage += ` ${error.message}`;
        }
        
        showMessage(typesMessage, errorMessage, 'error', 0);
    }
};

// --- إضافة دالة لمعالجة اختيار الأيقونات ---
const setupIconSelector = () => {
    const iconOptions = document.querySelectorAll('.icon-option');
    const iconInput = document.getElementById('type_icon');
    const iconPreview = document.querySelector('.selected-icon-display .icon-preview i');
    
    if (!iconOptions.length || !iconInput || !iconPreview) return;
    
    // تعيين أيقونة افتراضية
    let selectedIcon = iconInput.value || 'fa-coins';
    updateSelectedIconDisplay(selectedIcon);
    
    // إضافة مستمع أحداث لكل خيار أيقونة
    iconOptions.forEach(option => {
        const iconName = option.dataset.icon;
        
        // تحديد الأيقونة المختارة مسبقاً
        if (iconName === selectedIcon) {
            option.classList.add('selected');
        }
        
        option.addEventListener('click', () => {
            // إزالة الفئة "selected" من جميع الخيارات
            iconOptions.forEach(opt => opt.classList.remove('selected'));
            
            // إضافة الفئة "selected" للخيار المحدد
            option.classList.add('selected');
            
            // تحديث قيمة الحقل المخفي
            selectedIcon = iconName;
            iconInput.value = selectedIcon;
            
            // تحديث عرض الأيقونة المختارة
            updateSelectedIconDisplay(selectedIcon);
        });
    });
};

// دالة لتحديث عرض الأيقونة المختارة
const updateSelectedIconDisplay = (iconName) => {
    const iconPreview = document.querySelector('.selected-icon-display .icon-preview i');
    if (!iconPreview) return;
    
    // إزالة جميع الفئات السابقة وإضافة الفئة الجديدة
    iconPreview.className = '';
    iconPreview.classList.add('fas', iconName);
};

// --- إضافة: وظائف نافذة تفاصيل النوع ---

// دالة لفتح نافذة تفاصيل النوع
const openTypeDetailsModal = async (typeId, typeName) => {
    if (!typeDetailsModal || !typeDetailsTitleSpan || !typeDetailsTableBody || !typeDetailsTotal || !typeDetailsMessage) return;

    // تعيين عنوان النافذة
    typeDetailsTitleSpan.textContent = typeName;
    // إظهار رسالة التحميل ومسح الجدول السابق
    typeDetailsTableBody.innerHTML = `<tr><td colspan="4" class="loading-message">جاري تحميل التفاصيل...</td></tr>`;
    typeDetailsTotal.textContent = '0.00 ريال';
    typeDetailsMessage.style.display = 'none';

    // إظهار النافذة
    typeDetailsModal.classList.add('show');
    document.body.style.overflow = 'hidden';

    // جلب وعرض التفاصيل
    await fetchAndRenderTypeDetails(typeId);
};

// دالة لإغلاق نافذة تفاصيل النوع
const closeTypeDetailsModal = () => {
    if (!typeDetailsModal) return;
    typeDetailsModal.classList.remove('show');
    document.body.style.overflow = '';
};

// تعديل: دالة لجلب وعرض تفاصيل نوع معين لإعادة فلتر الشهر
const fetchAndRenderTypeDetails = async (typeId) => {
    // إعادة التحقق من الشهر النشط
    if (!_supabase || !activeMonthId) {
        showMessage(typeDetailsMessage, 'خطأ: لا يمكن تحميل التفاصيل بدون شهر نشط.', 'error', 0);
        typeDetailsTableBody.innerHTML = `<tr><td colspan="4" class="loading-message error">الرجاء تحديد شهر نشط أولاً.</td></tr>`;
        return;
    }

    try {
        const { data, error } = await _supabase
            .from('nathriyat_transactions')
            .select(`
                transaction_date,
                notes,
                amount,
                banks ( name )
            `)
            .eq('budget_month_id', activeMonthId) // <-- إعادة فلترة الشهر
            .eq('nathriyat_type_id', typeId)
            .order('transaction_date', { ascending: false });

        if (error) throw error;

        const transactions = data || [];
        let totalAmount = 0;

        // مسح الجدول
        typeDetailsTableBody.innerHTML = '';

        if (transactions.length === 0) {
            // تعديل الرسالة لتشمل الشهر
            typeDetailsTableBody.innerHTML = `<tr><td colspan="4" class="loading-message">لا توجد معاملات لهذا النوع في الشهر النشط.</td></tr>`;
        } else {
            transactions.forEach(tx => {
                // ... (rendering logic remains the same) ...
                const row = document.createElement('tr');
                const bankName = tx.banks?.name || 'غير محدد';
                row.innerHTML = `
                    <td>${formatDate(tx.transaction_date)}</td>
                    <td>${tx.notes || '-'}</td>
                    <td>${formatCurrency(tx.amount)} ريال</td>
                    <td>${bankName}</td>
                `;
                typeDetailsTableBody.appendChild(row);
                totalAmount += parseFloat(tx.amount) || 0;
            });
        }

        // تحديث المجموع (يمثل مجموع الشهر النشط)
        typeDetailsTotal.textContent = `${formatCurrency(totalAmount)} ريال`;
        // تعديل تذييل الجدول ليعكس مجموع الشهر
        const footerCell = document.querySelector('#type-details-modal tfoot td[colspan="2"]');
        if (footerCell) footerCell.textContent = 'المجموع لهذا الشهر:';


    } catch (error) {
        console.error('Error fetching type details:', error);
        showMessage(typeDetailsMessage, `خطأ في جلب تفاصيل النوع: ${error.message}`, 'error', 0);
        typeDetailsTableBody.innerHTML = `<tr><td colspan="4" class="loading-message error">فشل تحميل التفاصيل.</td></tr>`;
    }
};

// --- Event Listeners ---
const setupEventListeners = () => {
    // تعديل: إزالة أو تعطيل مستمع النموذج الرئيسي
    if (nathriyatForm) {
        nathriyatForm.addEventListener('submit', handleFormSubmit);
        // تعطيل النموذج الرئيسي بصريًا
        // nathriyatForm.style.opacity = '0.5';
        // nathriyatForm.style.pointerEvents = 'none';
        // إخفاء النموذج الرئيسي تمامًا
        const formSection = document.getElementById('add-nathriya-form-section');
        if (formSection) formSection.style.display = 'none';
    }

    // إزالة مستمع زر إلغاء التعديل للنموذج الرئيسي
    // if (cancelEditBtn) { ... }

    if (backToDashboardBtn) {
        backToDashboardBtn.addEventListener('click', () => {
            window.location.href = '../financial_dashboard.html'; // Adjust path if needed
        });
    }

    // إضافة مستمعي أحداث للنافذة المنبثقة وأزرار الأنواع (لا تغيير هنا)
    if (addNathriyatTypeBtn) {
        addNathriyatTypeBtn.addEventListener('click', () => openTypeModal());
    }
    // ... (other type modal listeners remain) ...
    if (closeTypeModalBtn) {
        closeTypeModalBtn.addEventListener('click', closeTypeModal);
    }

    if (cancelTypeBtn) {
        cancelTypeBtn.addEventListener('click', closeTypeModal);
    }

    if (nathriyatTypeForm) {
        nathriyatTypeForm.addEventListener('submit', handleTypeFormSubmit);
    }

    if (nathriyatTypeModal) {
        nathriyatTypeModal.addEventListener('click', (event) => {
            if (event.target === nathriyatTypeModal) {
                closeTypeModal();
            }
        });
    }

    // إضافة مستمعات أحداث لنافذة تفاصيل النوع (لا تغيير هنا)
    if (closeTypeDetailsModalBtn) {
        closeTypeDetailsModalBtn.addEventListener('click', closeTypeDetailsModal);
    }

    if (typeDetailsModal) {
        typeDetailsModal.addEventListener('click', (event) => {
            if (event.target === typeDetailsModal) {
                closeTypeDetailsModal();
            }
        });
    }

    // تعديل: إغلاق جميع النوافذ بزر ESC (لا تغيير هنا)
    document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape') {
            if (nathriyatTypeModal && nathriyatTypeModal.classList.contains('show')) {
                closeTypeModal();
            } else if (typeDetailsModal && typeDetailsModal.classList.contains('show')) {
                closeTypeDetailsModal();
            }
        }
    });

    // تعديل: زر إضافة معاملة جديدة الآن يفتح النافذة المنبثقة
    if (addNewNathriyaBtn) {
        addNewNathriyaBtn.addEventListener('click', () => {
            openAddNathriyaModal(); // Open the modal for adding new transaction
        });
    }

    // إضافة: مستمع حدث لتغيير الشهر
    if (monthSwitcherSelect) {
        monthSwitcherSelect.addEventListener('change', handleMonthSwitch);
    }
};

// --- دالة لفتح نافذة إضافة نثرية جديدة ---
const openAddNathriyaModal = (nathriyaToEdit = null) => {
    // ... (logic remains the same, it doesn't depend on active month) ...
    // إعادة تعيين النموذج
    resetAddNathriyaForm();

    // جلب أنواع النثريات والبنوك إذا لم يتم جلبها بعد
    if (!nathriyatTypeSelect.options.length || nathriyatTypeSelect.options.length <= 1) {
        loadNathriyatTypes();
    }

    // التأكد من أن قائمة البنوك يتم تحميلها عند فتح النافذة
    if (!bankSelect || !bankSelect.options.length || bankSelect.options.length <= 1) {
        loadBanks(); // استدعاء دالة تحميل البنوك
    }

    // ضبط القيم الافتراضية
    const modalTransactionDateInput = document.querySelector('#add-nathriya-modal #transaction_date');
    if (modalTransactionDateInput) modalTransactionDateInput.valueAsDate = new Date(); // تعيين التاريخ الحالي

    const modalNathriyaIdField = document.querySelector('#add-nathriya-modal #nathriya_id');
    const modalTitle = document.getElementById('add-nathriya-title');
    const modalSaveBtn = document.querySelector('#add-nathriya-modal #save-nathriya-btn'); // Target modal save button
    const modalAmountInput = document.querySelector('#add-nathriya-modal #amount');
    const modalNotesInput = document.querySelector('#add-nathriya-modal #notes');
    const modalTypeSelect = document.querySelector('#add-nathriya-modal #nathriyat_type_id');
    const modalBankSelect = document.querySelector('#add-nathriya-modal #bank_id');


    // إذا كان تعديل لمعاملة موجودة
    if (nathriyaToEdit) {
        if (modalNathriyaIdField) modalNathriyaIdField.value = nathriyaToEdit.id;
        if (modalTitle) modalTitle.innerHTML = '<i class="fas fa-edit"></i> تعديل معاملة نثرية';

        // تحديد القيم من المعاملة الموجودة
        if (modalTypeSelect) modalTypeSelect.value = nathriyaToEdit.nathriyat_type_id;
        if (modalTransactionDateInput) modalTransactionDateInput.value = formatDateForInput(nathriyaToEdit.transaction_date);
        if (modalAmountInput) modalAmountInput.value = nathriyaToEdit.amount;
        if (modalBankSelect) modalBankSelect.value = nathriyaToEdit.bank_id || '';
        if (modalNotesInput) modalNotesInput.value = nathriyaToEdit.notes || '';

        // تغيير نص زر الحفظ
        if (modalSaveBtn) modalSaveBtn.innerHTML = '<i class="fas fa-save"></i> تحديث المعاملة';
    } else {
        if (modalNathriyaIdField) modalNathriyaIdField.value = '';
        if (modalTitle) modalTitle.innerHTML = '<i class="fas fa-plus-circle"></i> إضافة معاملة نثرية جديدة';
        if (modalSaveBtn) modalSaveBtn.innerHTML = '<i class="fas fa-save"></i> حفظ المعاملة';
    }

    // إظهار النافذة
    addNathriyaModal.classList.add('show');
    document.body.style.overflow = 'hidden'; // منع التمرير
};

// --- دالة لإغلاق النافذة المنبثقة ---
const closeAddNathriyaModal = () => {
    addNathriyaModal.classList.remove('show');
    document.body.style.overflow = ''; // استعادة التمرير
    resetAddNathriyaForm();
};

// --- دالة لإعادة تعيين نموذج إضافة النثرية ---
const resetAddNathriyaForm = () => {
    if (addNathriyaForm) {
        addNathriyaForm.reset();
        document.getElementById('nathriya_id').value = '';
    }
    
    // إخفاء رسائل الخطأ
    if (addNathriyaMessage) {
        addNathriyaMessage.style.display = 'none';
    }
};

// --- دالة لتهيئة تاريخ يناسب حقل الإدخال ---
const formatDateForInput = (dateString) => {
    if (!dateString) return '';
    
    try {
        const date = new Date(dateString);
        return date.toISOString().split('T')[0];
    } catch (e) {
        console.error("Error formatting date for input:", e);
        return '';
    }
};

// --- دالة جلب أنواع النثريات ---
const loadNathriyatTypes = async () => {
    if (!_supabase || !nathriyatTypeSelect) return;
    
    // إظهار حالة التحميل
    nathriyatTypeSelect.classList.add('select-loading');
    nathriyatTypeSelect.disabled = true;
    
    // الاحتفاظ بالخيار الأول (placeholder)
    const firstOption = nathriyatTypeSelect.options[0];
    nathriyatTypeSelect.innerHTML = '';
    if (firstOption) nathriyatTypeSelect.appendChild(firstOption);
    
    try {
        const { data, error } = await _supabase
            .from('nathriyat_types')
            .select('id, name, icon')
            .order('name', { ascending: true });
            
        if (error) throw error;
        
        // ملء القائمة بالأنواع
        if (data && data.length > 0) {
            data.forEach(type => {
                const option = document.createElement('option');
                option.value = type.id;
                option.textContent = type.name;
                // يمكن استخدام الأيقونة في حال أردنا عرضًا متقدمًا
                option.dataset.icon = type.icon || '';
                nathriyatTypeSelect.appendChild(option);
            });
        } else {
            // إذا لم تكن هناك أنواع مسجلة
            const option = document.createElement('option');
            option.disabled = true;
            option.textContent = 'لا توجد أنواع نثريات مسجلة';
            nathriyatTypeSelect.appendChild(option);
        }
    } catch (error) {
        console.error('Error loading nathriyat types:', error);
        showAddNathriyaMessage(`خطأ في تحميل أنواع النثريات: ${error.message}`, 'error');
        
        // إضافة خيار للإشارة إلى الخطأ
        const option = document.createElement('option');
        option.disabled = true;
        option.textContent = 'خطأ في تحميل الأنواع';
        nathriyatTypeSelect.appendChild(option);
    } finally {
        // إنهاء حالة التحميل
        nathriyatTypeSelect.classList.remove('select-loading');
        nathriyatTypeSelect.disabled = false;
    }
};

// --- دالة جلب البنوك ---
const loadBanks = async () => {
    // التأكد من استهداف العنصر الصحيح
    const modalBankSelect = document.querySelector('#add-nathriya-modal #bank_id');
    if (!_supabase || !modalBankSelect) {
        console.error("Supabase client or bank select element not found in modal.");
        return;
    }

    // إظهار حالة التحميل
    modalBankSelect.classList.add('select-loading');
    modalBankSelect.disabled = true;

    // الاحتفاظ بالخيار الأول (placeholder أو -- بدون بنك --)
    const firstOption = modalBankSelect.options[0];
    modalBankSelect.innerHTML = '';
    if (firstOption) modalBankSelect.appendChild(firstOption);

    try {
        // جلب جميع البنوك (يمكنك إضافة فلتر حسب bank_type إذا أردت)
        const { data, error } = await _supabase
            .from('banks')
            .select('id, name')
            .order('name', { ascending: true });

        if (error) throw error;

        // ملء القائمة بالبنوك
        if (data && data.length > 0) {
            data.forEach(bank => {
                const option = document.createElement('option');
                option.value = bank.id;
                option.textContent = bank.name;
                modalBankSelect.appendChild(option);
            });
        } else {
            // إذا لم تكن هناك بنوك مسجلة
            const option = document.createElement('option');
            option.disabled = true;
            option.textContent = 'لا توجد بنوك مسجلة';
            modalBankSelect.appendChild(option);
        }
    } catch (error) {
        console.error('Error loading banks:', error);
        // استخدام منطقة الرسائل الخاصة بالنافذة المنبثقة
        showAddNathriyaMessage(`خطأ في تحميل البنوك: ${error.message}`, 'error');

        // إضافة خيار للإشارة إلى الخطأ
        const option = document.createElement('option');
        option.disabled = true;
        option.textContent = 'خطأ في تحميل البنوك';
        modalBankSelect.appendChild(option);
    } finally {
        // إنهاء حالة التحميل
        modalBankSelect.classList.remove('select-loading');
        modalBankSelect.disabled = false;
    }
};

// --- دالة عرض الرسائل في نافذة الإضافة ---
const showAddNathriyaMessage = (message, type = 'info', duration = 5000) => {
    if (!addNathriyaMessage) return;
    
    addNathriyaMessage.textContent = message;
    addNathriyaMessage.className = `message ${type} show`;
    addNathriyaMessage.style.display = 'block';
    
    if (duration > 0) {
        setTimeout(() => {
            if (addNathriyaMessage.textContent === message) {
                addNathriyaMessage.style.display = 'none';
            }
        }, duration);
    }
};

// --- تعديل: دالة حفظ معاملة نثرية لإعادة الاعتماد على الشهر النشط ---
const handleAddNathriyaFormSubmit = async (event) => {
    event.preventDefault();

    // إعادة التحقق من الشهر النشط
    if (!_supabase || !activeMonthId) {
        showAddNathriyaMessage('لا يمكن الحفظ. تأكد من وجود شهر نشط وتسجيل الدخول.', 'error');
        return;
    }

    // استهداف العناصر داخل النافذة المنبثقة بشكل صحيح
    const modalNathriyaIdField = document.querySelector('#add-nathriya-modal #nathriya_id');
    const modalTypeSelect = document.querySelector('#add-nathriya-modal #nathriyat_type_id');
    const modalTransactionDateInput = document.querySelector('#add-nathriya-modal #transaction_date');
    const modalAmountInput = document.querySelector('#add-nathriya-modal #amount');
    const modalBankSelect = document.querySelector('#add-nathriya-modal #bank_id');
    const modalNotesInput = document.querySelector('#add-nathriya-modal #notes');
    const modalSaveBtn = document.querySelector('#add-nathriya-modal #save-nathriya-btn'); // Target modal save button

    // التحقق من الحقول المطلوبة
    const typeId = modalTypeSelect.value;
    const date = modalTransactionDateInput.value;
    const amountValue = modalAmountInput.value;
    const amount = parseFloat(amountValue);
    const bankId = modalBankSelect.value || null;
    const notes = modalNotesInput.value.trim() || null;
    const nathriyaId = modalNathriyaIdField.value || null;

    if (!typeId) {
        showAddNathriyaMessage('الرجاء اختيار نوع النثرية.', 'warning');
        modalTypeSelect.focus();
        return;
    }

    if (!date) {
        showAddNathriyaMessage('الرجاء تحديد تاريخ المعاملة.', 'warning');
        modalTransactionDateInput.focus();
        return;
    }

    if (!amountValue || isNaN(amount) || amount <= 0) {
        showAddNathriyaMessage('الرجاء إدخال مبلغ صحيح أكبر من صفر.', 'warning');
        modalAmountInput.focus();
        return;
    }

    // تعطيل زر الحفظ وإظهار حالة التحميل
    modalSaveBtn.disabled = true;
    modalSaveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    showAddNathriyaMessage('جاري حفظ المعاملة...', 'info');

    // تجهيز بيانات المعاملة مع الشهر النشط
    const transactionData = {
        nathriyat_type_id: typeId,
        budget_month_id: activeMonthId, // <-- Re-added active month ID
        transaction_date: date,
        amount: amount,
        bank_id: bankId,
        notes: notes
    };

    try {
        let result;

        if (nathriyaId) {
            // تحديث معاملة موجودة
            const { data, error } = await _supabase
                .from('nathriyat_transactions')
                .update(transactionData)
                .eq('id', nathriyaId)
                .select()
                .single();

            if (error) throw error;
            result = data;
            showAddNathriyaMessage('تم تحديث المعاملة بنجاح!', 'success');
        } else {
            // إضافة معاملة جديدة
            const { data, error } = await _supabase
                .from('nathriyat_transactions')
                .insert(transactionData)
                .select()
                .single();

            if (error) throw error;
            result = data;
            showAddNathriyaMessage('تمت إضافة المعاملة بنجاح!', 'success');
        }

        // تحديث الجدول بعد الإضافة/التعديل
        fetchNathriyat();

        // إغلاق النافذة بعد فترة وجيزة
        setTimeout(() => {
            closeAddNathriyaModal();
        }, 1500);

    } catch (error) {
        console.error('Error saving nathriya transaction:', error);

        // عرض رسالة خطأ مناسبة (إعادة التحقق من budget_month_id)
        let errorMessage = 'خطأ في حفظ المعاملة.';
        if (error.message?.includes('foreign key constraint')) {
            if (error.message.includes('nathriyat_type_id')) {
                errorMessage = 'نوع النثرية غير صالح أو تم حذفه.';
            } else if (error.message.includes('bank_id')) {
                errorMessage = 'البنك المحدد غير صالح أو تم حذفه.';
            } else if (error.message.includes('budget_month_id')) {
                 errorMessage = 'الشهر المالي المحدد غير صالح أو غير موجود.';
            }
        } else if (error.message) {
            errorMessage += ` ${error.message}`;
        }

        showAddNathriyaMessage(errorMessage, 'error', 0);
    } finally {
        // إعادة تفعيل زر الحفظ
        modalSaveBtn.disabled = false;
        modalSaveBtn.innerHTML = nathriyaId ?
            '<i class="fas fa-save"></i> تحديث المعاملة' :
            '<i class="fas fa-save"></i> حفظ المعاملة';
    }
};

// --- إضافة مستمعي الأحداث ---
const setupAddNathriyaModalListeners = () => {
    // ... (listeners for modal buttons and form submission remain the same) ...
    // زر فتح النافذة المنبثقة (تم نقله إلى setupEventListeners الرئيسي)
    // if (addNewNathriyaBtn) { ... }

    // زر إغلاق النافذة المنبثقة
    if (closeAddNathriyaModalBtn) {
        closeAddNathriyaModalBtn.addEventListener('click', closeAddNathriyaModal);
    }

    // زر إلغاء العملية
    if (cancelNathriyaBtn) {
        cancelNathriyaBtn.addEventListener('click', closeAddNathriyaModal);
    }

    // تقديم النموذج
    if (addNathriyaForm) {
        addNathriyaForm.addEventListener('submit', handleAddNathriyaFormSubmit);
    }

    // إغلاق النافذة عند النقر خارجها
    if (addNathriyaModal) {
        addNathriyaModal.addEventListener('click', (event) => {
            if (event.target === addNathriyaModal) {
                closeAddNathriyaModal();
            }
        });
    }
};

// إضافة: دالة لمعالجة تبديل الشهر
const handleMonthSwitch = async (event) => {
    const newMonthId = event.target.value;
    if (!newMonthId || newMonthId === activeMonthId) {
        return; // No change or invalid selection
    }

    console.log(`Switching active month via dropdown to: ${newMonthId}`);

    // Show loading state
    showMessage(messageArea, 'جاري تبديل الشهر وتحميل البيانات...', 'info', 0);
    if (nathriyatTableBody) nathriyatTableBody.innerHTML = `<tr><td colspan="6" class="loading-message">جاري تحميل المعاملات للشهر الجديد...</td></tr>`;
    if (nathriyatCountBadge) nathriyatCountBadge.textContent = '-';
    monthSwitcherSelect.disabled = true;
    if(activeMonthDisplay) activeMonthDisplay.classList.remove('loaded');
    if (activeMonthNameSpan) activeMonthNameSpan.textContent = 'جاري التبديل...';


    // Update state and sessionStorage
    activeMonthId = newMonthId;
    sessionStorage.setItem('activeBudgetMonthId', activeMonthId);
    // Also update selectedBudgetMonthId if the user explicitly switches using the dropdown
    // This makes the dropdown selection override any previous selection from select_budget_month
    sessionStorage.setItem('selectedBudgetMonthId', activeMonthId);

    try {
        // Fetch details for the new month and update display
        await fetchAndDisplayActiveMonthDetails(activeMonthId); // Fetch details for the new ID

        // Fetch transactions for the new month
        await fetchNathriyat();

        // Optional: Re-fetch type details if the details modal might be open or needs refresh based on month
        // Example: If type details modal is open, refresh its content
        if (typeDetailsModal && typeDetailsModal.classList.contains('show')) {
             // Ensure typeDetailsModal has dataset.typeId attribute set when opened
             const currentTypeId = typeDetailsModal.dataset.typeId;
             if (currentTypeId) {
                 await fetchAndRenderTypeDetails(currentTypeId);
             }
        }

        showMessage(messageArea, `تم التبديل إلى الشهر ${activeMonthInfo ? activeMonthInfo.name + ' ' + activeMonthInfo.year : ''} بنجاح.`, 'success', 4000);

    } catch (error) {
        console.error("Error switching month:", error);
        showMessage(messageArea, `حدث خطأ أثناء تبديل الشهر: ${error.message}`, 'error', 0);
        // Optionally revert selection or handle error state
        const previousValidMonthId = sessionStorage.getItem('activeBudgetMonthId'); // Get the last known good ID
        monthSwitcherSelect.value = previousValidMonthId || ''; // Revert dropdown if possible
        activeMonthId = previousValidMonthId; // Revert state variable
        sessionStorage.setItem('selectedBudgetMonthId', activeMonthId); // Revert selected ID as well
        // Re-fetch old data? Or show error message prominently.
        await fetchActiveMonth(); // Try to re-establish a valid state

    } finally {
        monthSwitcherSelect.disabled = false; // Re-enable dropdown
    }
};


// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    if (!_supabase) {
        showMessage(messageArea, 'خطأ فادح: لم يتم تهيئة الاتصال بقاعدة البيانات.', 'error', 0);
        if(monthSwitcherSelect) monthSwitcherSelect.style.display = 'none'; // Hide switcher on init error
        return;
    }

    // 1. Attempt to set the active month (includes populating switcher on success)
    // This now prioritizes selectedBudgetMonthId
    const hasActiveMonth = await fetchActiveMonth();

    // 2. Fetch data needed for UI elements (Types and Banks) regardless of active month
    try {
        // Fetch types and banks only once unless they change frequently
        await Promise.all([
            fetchNathriyatTypes(),
            fetchBanks()
        ]);
    } catch (error) {
        console.error("Error fetching initial types/banks:", error);
        showMessage(messageArea, 'حدث خطأ أثناء تحميل بيانات الأنواع أو البنوك.', 'error');
    }

    // 3. Fetch Nathriyat Transactions *only* if an active month was successfully identified
    if (hasActiveMonth) {
        await fetchNathriyat();
    } else {
        // Message already shown by fetchActiveMonth
        if (nathriyatTableBody) nathriyatTableBody.innerHTML = `<tr><td colspan="6" class="loading-message">الرجاء تحديد شهر نشط أولاً لعرض المعاملات.</td></tr>`;
        if (nathriyatCountBadge) nathriyatCountBadge.textContent = '0';
        if(monthSwitcherSelect) monthSwitcherSelect.style.display = 'none'; // Ensure switcher is hidden if no active month
    }

    // 4. Setup event listeners (includes month switcher listener)
    setupEventListeners();

    // 5. Setup modal-specific listeners
    setupAddNathriyaModalListeners();
});