<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف المالي للسائق</title>
    <link rel="icon" href="../../favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Link to shared styles -->
    <link rel="stylesheet" href="../../shared_styles.css">
    <!-- Sidebar Component Styles -->
    <link rel="stylesheet" href="../../shared_components/sidebar.css">
    <!-- Link to the local style.css file -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Top Navigation Bar -->
        <nav class="top-navbar">
            <div class="navbar-left">
                <button id="sidebar-toggle" class="sidebar-toggle-btn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="navbar-brand">
                    <i class="fas fa-user-tie"></i>
                    <span>الملف المالي للسائق</span>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Header -->
            <header class="page-header">
                <h1>الملف المالي للسائق: <span id="driver-name-header">جاري التحميل...</span></h1>
                <p>نظرة شاملة على ماليات السائق للشهر الحالي والسجل المالي.</p>
                <div id="page-message" class="message" style="display: none;"></div>
            </header>

            <!-- Summary Section (Placeholder) -->
            <section class="summary-section">
                <div class="section-header-with-button"> <!-- New container for header and button -->
                    <h2>ملخص الشهر الحالي (<span id="current-month-year">...</span>)</h2>
                    <button id="save-current-balance-btn" class="control-btn accent-btn" title="حفظ الرصيد المتبقي الحالي لهذا الشهر في إعدادات السائق ليتم ترحيله للشهر القادم">
                        <i class="fas fa-save"></i> حفظ الرصيد الحالي
                    </button>
                    <button id="edit-previous-balance-btn" class="control-btn warning-btn" title="تعديل الرصيد السابق للسائق" style="margin-right: 10px;">
                        <i class="fas fa-edit"></i> تعديل الرصيد السابق
                    </button>
                </div>
                <div class="summary-grid">
                    <div class="summary-card">
                        <h3><i class="fas fa-history"></i> الرصيد السابق</h3>
                        <p id="summary-previous-balance">... ريال</p>
                    </div>
                    <div class="summary-card">
                        <h3><i class="fas fa-money-bill-wave"></i> الراتب الأساسي</h3>
                        <p id="summary-base-salary">... ريال</p>
                    </div>
                    <div class="summary-card">
                        <h3><i class="fas fa-star"></i> مجموع العمولات</h3>
                        <p id="summary-commissions">... ريال</p>
                    </div>
                    <div class="summary-card">
                        <h3><i class="fas fa-arrow-down"></i> الخصومات</h3>
                        <p id="summary-deductions">... ريال</p>
                    </div>
                    <div class="summary-card">
                        <h3><i class="fas fa-file-invoice-dollar"></i> إجمالي المستحق</h3>
                        <p id="summary-total-due">... ريال</p>
                    </div>
                    <div class="summary-card">
                        <h3><i class="fas fa-hand-holding-usd"></i> المبلغ المدفوع</h3>
                        <p id="summary-paid">... ريال</p>
                    </div>
                    <div class="summary-card">
                        <h3><i class="fas fa-wallet"></i> الرصيد المتبقي</h3>
                        <p id="summary-remaining">... ريال</p>
                    </div>
                </div>
            </section>

            <!-- Details/Transactions Section -->
            <section class="details-section">
                <!-- Removed the old section-header -->
                <div class="details-grid"> <!-- New Grid Container -->
                    <!-- Right Column: Commissions -->
                    <div class="details-column commissions-column">
                        <div class="column-header">
                            <h2><i class="fas fa-star"></i> العمولات</h2>
                            <button id="add-commission-btn" class="control-btn success-btn">
                                <i class="fas fa-plus-circle"></i> إضافة عمولة
                            </button>
                        </div>
                        <div id="commission-list" class="transaction-list">
                            <!-- Commission records will be loaded here -->
                            <p>سيتم عرض سجل العمولات هنا...</p>
                        </div>
                    </div>
                    <!-- Left Column: Deductions & Advances -->
                    <div class="details-column deductions-column">
                         <div class="column-header">
                            <h2><i class="fas fa-minus-circle"></i> الخصومات والسلف</h2>
                            <button id="add-deduction-btn" class="control-btn accent-btn">
                                <i class="fas fa-plus-circle"></i> إضافة خصم/سلفة
                            </button>
                        </div>
                        <div id="deduction-list" class="transaction-list">
                             <!-- Deduction/Advance records will be loaded here -->
                            <p>سيتم عرض سجل الخصومات والسلف هنا...</p>
                        </div>
                    </div>
                </div>
                <!-- Removed the old table-container -->
            </section>

            <!-- Details Section (New) -->
            <section class="details-section card">
                <div class="card-header">
                    <h2><i class="fas fa-list-alt"></i> تفاصيل الحركات المالية للشهر</h2>
                    <button id="add-payment-btn" class="control-btn payment-btn">
                        <i class="fas fa-money-check-alt"></i> إضافة دفعة
                    </button>
                </div>
                <div class="card-body">
                    <div id="transactions-message" class="message" style="display: none;"></div>
                    <div class="table-responsive">
                        <table id="transactions-table">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>النوع</th>
                                    <th>الوصف/الملاحظات</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>ضريبة القيمة المضافة</th>
                                    <th>مدفوع بنكياً</th>
                                    <th>مخصوم/مضاف للراتب</th>
                                    <th>الإجراءات</th> <!-- Added Actions Header -->
                                </tr>
                            </thead>
                            <tbody id="transactions-tbody">
                                <!-- Transactions will be loaded here by JS -->
                                <tr>
                                    <td colspan="8" class="loading-placeholder"> <!-- Updated colspan to 8 -->
                                        <i class="fas fa-spinner fa-spin"></i> جاري تحميل الحركات...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

        </main>

        <!-- Operation Modal (Existing) -->
        <div id="operation-modal" class="modal" style="display: none;">
            <div class="modal-content form-card">
                <div class="card-header">
                    <h2 id="operation-modal-title"><i class="fas fa-plus-circle"></i> إضافة عملية جديدة</h2>
                    <button class="close-btn" id="close-operation-modal">&times;</button>
                </div>
                <div class="card-body">
                    <!-- 1. Type Selection View -->
                    <div id="type-selection-view">
                        <p>الرجاء اختيار نوع العملية:</p>
                        <div class="type-selection-buttons">
                            <button class="btn btn-primary type-select-btn" data-form="advance"><i class="fas fa-hand-holding-usd"></i> سلفة</button>
                            <button class="btn btn-warning type-select-btn" data-form="fine"><i class="fas fa-receipt"></i> مخالفة</button>
                            <button class="btn btn-danger type-select-btn" data-form="accident"><i class="fas fa-car-crash"></i> حادث</button>
                        </div>
                    </div>

                    <!-- 2. Advance Form View -->
                    <div id="advance-form-view" style="display: none;">
                        <form id="advance-form">
                            <input type="hidden" id="advance-driver-id">
                            <div class="form-group">
                                <label for="advance-amount">مبلغ السلفة</label>
                                <input type="number" id="advance-amount" required step="0.01" min="0.01" placeholder="0.00">
                            </div>
                            <div class="form-group">
                                <label for="advance-date">تاريخ السلفة</label>
                                <input type="date" id="advance-date" required>
                            </div>
                            <div class="form-group">
                                <label for="advance-description">السبب/الوصف</label>
                                <textarea id="advance-description" rows="3" required placeholder="سبب طلب السلفة..."></textarea>
                            </div>
                            <div id="advance-message" class="message" style="display: none;"></div>
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ السلفة
                                </button>
                                <button type="button" class="btn btn-secondary cancel-operation">
                                    إلغاء
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- 3. Fine Form View -->
                    <div id="fine-form-view" style="display: none;">
                        <form id="fine-form">
                            <input type="hidden" id="fine-driver-id">
                            <div class="form-group">
                                <label for="fine-type">نوع المخالفة</label>
                                <select id="fine-type" required>
                                    <option value="" disabled selected>-- اختر النوع --</option>
                                    <option value="speeding">سرعة زائدة</option>
                                    <option value="parking">موقف خاطئ</option>
                                    <option value="red_light">قطع إشارة</option>
                                    <option value="mobile">استخدام جوال</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                             <div class="form-group">
                                <label for="fine-date">تاريخ المخالفة</label>
                                <input type="date" id="fine-date" required>
                            </div>
                            <div class="form-group">
                                <label for="fine-amount">مبلغ المخالفة الأساسي</label>
                                <input type="number" id="fine-amount" required step="0.01" min="0" placeholder="0.00">
                            </div>
                            <!-- VAT Calculation -->
                            <div class="form-group vat-section">
                                <label class="vat-label">
                                    <input type="checkbox" id="fine-add-vat"> إضافة ضريبة القيمة المضافة (15%)
                                </label>
                                <div id="fine-vat-details" style="display: none; margin-top: 10px;">
                                    <p>مبلغ الضريبة: <span id="fine-vat-amount">0.00</span> ريال</p>
                                    <p>الإجمالي شامل الضريبة: <span id="fine-total-amount">0.00</span> ريال</p>
                                </div>
                            </div>
                            <!-- Payment Method -->
                            <div class="form-group">
                                <label>طريقة الدفع/الخصم</label>
                                <div class="payment-options">
                                    <label><input type="radio" name="fine_payment_method" value="bank" required> دفع بنكي</label>
                                    <label><input type="radio" name="fine_payment_method" value="salary"> خصم من الراتب</label>
                                    <label><input type="radio" name="fine_payment_method" value="mixed"> مختلط (بنكي + راتب)</label>
                                </div>
                            </div>
                            <!-- Bank Details (Conditional) -->
                            <div id="fine-bank-details" style="display: none;">
                                <div class="form-group">
                                    <label for="fine-bank-id">اختر البنك</label>
                                    <select id="fine-bank-id">
                                        <option value="">-- اختر البنك --</option>
                                        <!-- Options loaded by JS -->
                                    </select>
                                </div>
                                <div class="form-group" id="fine-bank-amount-group" style="display: none;"> <!-- Only for Mixed -->
                                    <label for="fine-bank-amount">المبلغ المدفوع بنكياً</label>
                                    <input type="number" id="fine-bank-amount" step="0.01" min="0.01" placeholder="0.00">
                                </div>
                            </div>
                             <div class="form-group">
                                <label for="fine-description">ملاحظات إضافية</label>
                                <textarea id="fine-description" rows="2" placeholder="أي تفاصيل أخرى..."></textarea>
                            </div>
                            <div id="fine-message" class="message" style="display: none;"></div>
                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary back-to-type-select">
                                    <i class="fas fa-arrow-right"></i> رجوع
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ المخالفة
                                </button>
                                <button type="button" class="btn btn-secondary cancel-operation">
                                    إلغاء
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- 4. Accident Form View -->
                    <div id="accident-form-view" style="display: none;">
                        <form id="accident-form">
                             <input type="hidden" id="accident-driver-id">
                            <div class="form-group">
                                <label for="accident-type">نوع الحادث</label>
                                <select id="accident-type" required>
                                    <option value="" disabled selected>-- اختر النوع --</option>
                                    <option value="minor_damage">ضرر بسيط</option>
                                    <option value="major_damage">ضرر كبير</option>
                                    <option value="third_party_fault">خطأ طرف ثالث</option>
                                    <option value="driver_fault">خطأ السائق</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                             <div class="form-group">
                                <label for="accident-date">تاريخ الحادث</label>
                                <input type="date" id="accident-date" required>
                            </div>
                            <div class="form-group">
                                <label for="accident-amount">مبلغ تكلفة الإصلاح الأساسي</label>
                                <input type="number" id="accident-amount" required step="0.01" min="0" placeholder="0.00">
                            </div>
                            <!-- VAT Calculation -->
                            <div class="form-group vat-section">
                                <label class="vat-label">
                                    <input type="checkbox" id="accident-add-vat"> إضافة ضريبة القيمة المضافة (15%)
                                </label>
                                <div id="accident-vat-details" style="display: none; margin-top: 10px;">
                                    <p>مبلغ الضريبة: <span id="accident-vat-amount">0.00</span> ريال</p>
                                    <p>الإجمالي شامل الضريبة: <span id="accident-total-amount">0.00</span> ريال</p>
                                </div>
                            </div>
                            <!-- Payment Method -->
                            <div class="form-group">
                                <label>طريقة الدفع/الخصم</label>
                                <div class="payment-options">
                                    <label><input type="radio" name="accident_payment_method" value="bank" required> دفع بنكي</label>
                                    <label><input type="radio" name="accident_payment_method" value="salary"> خصم من الراتب</label>
                                    <label><input type="radio" name="accident_payment_method" value="mixed"> مختلط (بنكي + راتب)</label>
                                </div>
                            </div>
                            <!-- Bank Details (Conditional) -->
                            <div id="accident-bank-details" style="display: none;">
                                <div class="form-group">
                                    <label for="accident-bank-id">اختر البنك</label>
                                    <select id="accident-bank-id">
                                        <option value="">-- اختر البنك --</option>
                                        <!-- Options loaded by JS -->
                                    </select>
                                </div>
                                <div class="form-group" id="accident-bank-amount-group" style="display: none;"> <!-- Only for Mixed -->
                                    <label for="accident-bank-amount">المبلغ المدفوع بنكياً</label>
                                    <input type="number" id="accident-bank-amount" step="0.01" min="0.01" placeholder="0.00">
                                </div>
                            </div>
                             <div class="form-group">
                                <label for="accident-description">ملاحظات إضافية</label>
                                <textarea id="accident-description" rows="2" placeholder="تفاصيل الحادث، رقم التقرير..."></textarea>
                            </div>
                            <div id="accident-message" class="message" style="display: none;"></div>
                            <div class="form-actions">
                                 <button type="button" class="btn btn-secondary back-to-type-select">
                                    <i class="fas fa-arrow-right"></i> رجوع
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ الحادث
                                </button>
                                <button type="button" class="btn btn-secondary cancel-operation">
                                    إلغاء
                                </button>
                            </div>
                        </form>
                    </div>

                </div> <!-- End card-body -->
            </div> <!-- End modal-content -->
        </div> <!-- End Operation Modal -->

        <!-- Commission Modal (New) -->
        <div id="commission-modal" class="modal" style="display: none;">
            <div class="modal-content form-card">
                <div class="card-header">
                    <h2><i class="fas fa-plus-circle"></i> إضافة عمولة جديدة</h2>
                    <button class="close-btn" id="close-commission-modal">&times;</button>
                </div>
                <div class="card-body">
                    <form id="commission-form">
                        <input type="hidden" id="commission-driver-id"> <!-- To store driver ID -->
                        <div class="form-group">
                            <label for="commission-date">تاريخ العمولة</label>
                            <input type="date" id="commission-date" required>
                        </div>
                        <div class="form-group">
                            <label for="commission-type">نوع العمولة</label>
                            <select id="commission-type" required>
                                <option value="" disabled selected>-- اختر النوع --</option>
                                <option value="extra_trip">رحلة إضافية</option>
                                <option value="performance_bonus">مكافأة أداء</option>
                                <option value="referral_bonus">مكافأة توصية</option>
                                <option value="long_distance">مسافة طويلة</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="commission-amount">المبلغ</label>
                            <input type="number" id="commission-amount" required step="0.01" min="0.01" placeholder="0.00">
                        </div>
                        <div class="form-group">
                            <label for="commission-notes">ملاحظات</label>
                            <textarea id="commission-notes" rows="3" placeholder="تفاصيل إضافية عن العمولة..."></textarea>
                        </div>
                        <div id="commission-message" class="message" style="display: none;"></div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ العمولة
                            </button>
                            <button type="button" class="btn btn-secondary" id="cancel-commission">
                                إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- End Commission Modal -->

        <!-- Payment Modal (New) -->
        <div id="payment-modal" class="modal" style="display: none;">
            <div class="modal-content form-card">
                <div class="card-header">
                    <h2><i class="fas fa-money-check-alt"></i> إضافة دفعة للسائق</h2>
                    <!-- Ensure close button has a class or ID -->
                    <button type="button" class="close-btn" aria-label="Close">&times;</button>
                </div>
                <div class="card-body">
                    <form id="payment-form">
                        <!-- Hidden input for driver ID might not be needed if using currentDriverId from state -->
                        <div class="form-group">
                            <label for="payment-amount">المبلغ المدفوع</label>
                            <input type="number" id="payment-amount" required step="0.01" min="0.01" placeholder="0.00">
                        </div>
                        <div class="form-group">
                            <label for="payment-date">تاريخ الدفعة</label>
                            <input type="date" id="payment-date" required>
                        </div>
                        <!-- Bank Details (Always visible now) -->
                        <div class="form-group">
                            <label for="payment-bank-id">اختر البنك</label>
                            <select id="payment-bank-id" required>
                                <option value="">-- اختر البنك --</option>
                                <!-- Options loaded by JS -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="payment-notes">ملاحظات</label>
                            <textarea id="payment-notes" rows="3" placeholder="تفاصيل إضافية عن الدفعة (اختياري)..."></textarea>
                        </div>
                        <div id="payment-message" class="message modal-message" style="display: none;"></div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الدفعة
                            </button>
                            <!-- Ensure cancel button has a class or ID to close modal -->
                            <button type="button" class="btn btn-secondary close-btn">
                                إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- End Payment Modal -->

        <!-- Deduction/Advance Modal (New Structure) -->
        <div id="deduction-modal" class="modal" style="display: none;">
            <div class="modal-content form-card">
                <div class="card-header">
                    <h2 id="deduction-modal-title"><i class="fas fa-minus-circle"></i> إضافة خصم / سلفة</h2>
                    <button type="button" class="close-btn" aria-label="Close">&times;</button>
                </div>
                <div class="card-body">
                    <div id="deduction-message" class="message modal-message" style="display: none;"></div>

                    <!-- 1. Type Selection View -->
                    <div id="type-selection-view">
                        <p>الرجاء تحديد نوع الحركة:</p>
                        <div class="type-selection-buttons">
                            <button type="button" class="btn btn-primary type-select-btn" data-form="advance">
                                <i class="fas fa-hand-holding-usd"></i> سلفة
                            </button>
                            <button type="button" class="btn btn-warning type-select-btn" data-form="fine">
                                <i class="fas fa-receipt"></i> مخالفة
                            </button>
                            <button type="button" class="btn btn-danger type-select-btn" data-form="accident">
                                <i class="fas fa-car-crash"></i> حادث
                            </button>
                            <!-- Add other types if needed -->
                        </div>
                    </div>

                    <!-- 2. Deduction Form (Initially Hidden) -->
                    <form id="deduction-form" style="display: none;">
                        <input type="hidden" id="deduction-expense-type">

                        <div class="form-group">
                            <label for="deduction-date">تاريخ الحركة:</label>
                            <input type="date" id="deduction-date" required>
                        </div>

                        <!-- Specific Type (for Fine/Accident) -->
                        <div class="form-group" id="specific-type-group" style="display: none;">
                            <label for="deduction-specific-type" id="specific-type-label">نوع المخالفة/الحادث:</label>
                            <select id="deduction-specific-type">
                                <option value="">-- اختر --</option>
                                <!-- Add options like 'مخالفة سرعة', 'قطع إشارة', 'حادث بسيط', etc. -->
                                <option value="مخالفة سرعة">مخالفة سرعة</option>
                                <option value="قطع إشارة">قطع إشارة</option>
                                <option value="حادث بسيط">حادث بسيط</option>
                                <option value="تلفيات مركبة">تلفيات مركبة</option>
                                <option value="اخرى">أخرى</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="deduction-amount">المبلغ الأساسي:</label>
                            <input type="number" id="deduction-amount" step="0.01" min="0" placeholder="0.00" required>
                        </div>

                        <!-- VAT Section (for Fine/Accident) -->
                        <div class="vat-section" id="vat-section" style="display: none;">
                            <label class="vat-label">
                                <input type="checkbox" id="include-vat-checkbox"> إضافة ضريبة القيمة المضافة (15%)
                            </label>
                            <div id="vat-details" style="display: none; margin-top: 10px; background-color: #f0f0f0; padding: 8px; border-radius: 4px;">
                                <p style="margin: 5px 0;">مبلغ الضريبة: <span id="vat-amount-display">0.00 ريال</span></p>
                                <p style="margin: 5px 0;">الإجمالي شامل الضريبة: <span id="total-with-vat-display">0.00 ريال</span></p>
                            </div>
                        </div>

                        <!-- Payment Method (for Fine/Accident) -->
                        <div class="form-group" id="payment-method-section" style="display: none;">
                            <label>طريقة الدفع/الخصم:</label>
                            <div class="payment-options">
                                <label>
                                    <input type="radio" name="payment_method" value="deduct" checked> خصم من الراتب
                                </label>
                                <label>
                                    <input type="radio" name="payment_method" value="bank"> دفع بنكي كامل
                                </label>
                                <label>
                                    <input type="radio" name="payment_method" value="mixed"> دفع مختلط (بنك + خصم)
                                </label>
                            </div>
                        </div>

                        <!-- Bank Details (for Bank/Mixed) -->
                        <div id="bank-details-section" style="display: none;">
                            <div class="form-group">
                                <label for="deduction-bank-select">اختر البنك:</label>
                                <select id="deduction-bank-select">
                                    <option value="">-- اختر البنك --</option>
                                    <!-- Options populated by JS -->
                                </select>
                            </div>
                            <!-- Bank Paid Amount (for Mixed) -->
                            <div class="form-group" id="bank-paid-amount-group" style="display: none;">
                                 <label for="deduction-bank-paid-amount">المبلغ المدفوع من البنك:</label>
                                 <input type="number" id="deduction-bank-paid-amount" step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="deduction-notes">ملاحظات:</label>
                            <textarea id="deduction-notes" rows="3"></textarea>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary back-to-type-select">
                                <i class="fas fa-arrow-right"></i> رجوع لاختيار النوع
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الحركة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- End Deduction/Advance Modal -->

        <!-- Modal for editing previous balance -->
        <div id="edit-previous-balance-modal" class="modal" style="display: none;">
            <div class="modal-content form-card" style="max-width: 400px;">
                <div class="card-header">
                    <h2><i class="fas fa-edit"></i> تعديل الرصيد السابق</h2>
                    <button class="close-btn" id="close-edit-previous-balance-modal">&times;</button>
                </div>
                <div class="card-body">
                    <form id="edit-previous-balance-form">
                        <div class="form-group">
                            <label for="previous-balance-current">الرصيد السابق الحالي</label>
                            <input type="text" id="previous-balance-current" readonly style="background:#f5f5f5;">
                        </div>
                        <div class="form-group">
                            <label for="previous-balance-new">الرصيد الجديد</label>
                            <input type="number" id="previous-balance-new" required step="0.01" placeholder="أدخل الرصيد الجديد">
                        </div>
                        <div id="edit-previous-balance-message" class="message" style="display: none;"></div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التعديل
                            </button>
                            <button type="button" class="btn btn-secondary" id="cancel-edit-previous-balance">إلغاء</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="main-footer">
             <button id="back-to-drivers-list-btn" class="control-btn">
                <i class="fas fa-arrow-right"></i> العودة لقائمة السائقين
            </button>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="../../config.js"></script>
    <script src="../../auth.js"></script>
    <!-- Load Sidebar Component -->
    <script src="../../shared_components/sidebar.js"></script>
    <script src="script.js"></script>
    <script>
        // Initialize sidebar after page load
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof loadSidebar === 'function') {
                loadSidebar();
            }
        });
    </script>
</body>
</html>
