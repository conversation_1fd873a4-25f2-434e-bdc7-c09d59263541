// --- Auth Check ---
// Removed: checkAuth('../login.html'); // This page does not require login

// --- Supabase Initialization ---
let _supabase;
try {
    // Assuming config.js is loaded and defines SUPABASE_URL and SUPABASE_ANON_KEY
    if (typeof supabase !== 'undefined' && supabase.createClient && typeof SUPABASE_URL !== 'undefined' && typeof SUPABASE_ANON_KEY !== 'undefined') {
        _supabase = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('Supabase client initialized for student defaults.');
    } else {
        throw new Error('Supabase client or config variables not available.');
    }
} catch (error) {
    console.error('Error initializing Supabase client:', error);
    alert('خطأ فادح: لا يمكن الاتصال بقاعدة البيانات. تأكد من تحميل config.js بشكل صحيح.');
    // Optionally disable functionality or show a persistent error message
}

// --- DOM Elements ---
const searchInput = document.getElementById('search-input');
const searchBtn = document.getElementById('search-btn');
const defaultsTableBody = document.getElementById('defaults-tbody');
const listMessage = document.getElementById('list-message');
const studentsCountBadge = document.getElementById('students-count');
const paginationControls = document.getElementById('pagination-controls');
const backToDashboardBtn = document.getElementById('back-to-dashboard-btn'); // Keep for general navigation

// إضافة عناصر واجهة القائمة الجانبية والنافذة المنبثقة
const studentGroupsList = document.getElementById('student-groups-list');
const addGroupBtn = document.getElementById('add-group-btn');
const addGroupModal = document.getElementById('add-group-modal');
const closeGroupModalBtn = document.getElementById('close-group-modal-btn');
const addGroupForm = document.getElementById('add-group-form');
const groupNameInput = document.getElementById('group-name-input');
const saveGroupBtn = document.getElementById('save-group-btn');
const cancelGroupBtn = document.getElementById('cancel-group-btn');
const modalMessageArea = document.getElementById('modal-message-area');



// إضافة عناصر نافذة تعيين المجموعة
const assignGroupModal = document.getElementById('assign-group-modal');
const closeAssignModalBtn = document.getElementById('close-assign-modal-btn');
const assignGroupForm = document.getElementById('assign-group-form');
const assignStudentIdInput = document.getElementById('assign-student-id');
const assignStudentNameSpan = document.getElementById('assign-student-name');
const assignGroupSelect = document.getElementById('assign-group-select');
const saveAssignmentBtn = document.getElementById('save-assignment-btn');
const cancelAssignmentBtn = document.getElementById('cancel-assignment-btn');
const assignModalMessageArea = document.getElementById('assign-modal-message-area');


// --- State ---
let allStudents = []; // Holds all student data {id, name}
let studentDefaults = {}; // Holds default data keyed by student_id {student_id: {id, student_id, default_service_type, default_amount, current_balance}} // Added current_balance
let displayedStudents = []; // Students currently shown in the table (after search/pagination)
let currentPage = 1;
const itemsPerPage = 15; // Number of items per page
let totalItems = 0; // Total number of students matching the current filter
let allGroups = []; // مصفوفة لتخزين جميع مجموعات الطلاب
let studentGroupAssignments = {}; // تخزين تعيينات الطلاب للمجموعات
let studentsWithDefaults = []; // الطلاب الذين لديهم إعدادات افتراضية
let hiddenStudents = []; // الطلاب المخفيين (لديهم رصيد غير صفر)
let selectedStudentsForEdit = new Set(); // الطلاب المحددين للتعديل
let selectedHiddenStudents = new Set(); // الطلاب المخفيين المحددين
let currentAssignStudentId = null; // لتخزين معرف الطالب المراد تعيينه
let newStudentsWithoutDefaults = []; // الطلاب الذين ليس لديهم إعدادات افتراضية
let selectedNewStudents = new Set(); // الطلاب الجدد المحددين للإضافة

// --- Constants ---
// Ensure these match the CHECK constraint in your DB ('service_type_enum')
const validServiceTypes = ['ذهاب وعودة', 'ذهاب فقط', 'عودة فقط', 'خدمة خاصة', 'other'];
const defaultServiceType = 'other'; // Fallback if needed (though UI should prevent saving invalid state)
const defaultAmount = 0; // Fallback if needed

// --- Helper Functions ---
const showMessage = (element, message, type = 'info', duration = 4000) => {
    if (!element) return;
    element.textContent = message;
    element.className = `message ${type} show`; // Ensure 'show' class is added
    element.style.display = 'block'; // Make sure it's visible

    // Clear previous timeouts if any
    if (element.timeoutId) {
        clearTimeout(element.timeoutId);
    }

    if (duration > 0) {
        element.timeoutId = setTimeout(() => {
            // Only hide if the message hasn't changed
            if (element.textContent === message) {
                 element.classList.remove('show');
                 // Use a small delay before setting display to none for fade-out effect if CSS transition is added
                 setTimeout(() => { element.style.display = 'none'; }, 300); // Adjust timing based on CSS transition
            }
            element.timeoutId = null; // Clear the stored timeout ID
        }, duration);
    } else {
         element.timeoutId = null; // No timeout needed for permanent messages (like errors)
    }
};

const formatCurrency = (amount) => {
    // Handles null, undefined, or non-numeric values gracefully
    const num = parseFloat(amount);
    if (isNaN(num)) return ''; // Return empty string for invalid input in the table
    return num.toFixed(2); // Format to 2 decimal places
};

// إضافة دالة لعرض رسائل في النافذة المنبثقة
const showModalMessage = (message, type = 'info', duration = 4000) => {
    if (!modalMessageArea) return;
    modalMessageArea.textContent = message;
    modalMessageArea.className = `message ${type} show`;
    modalMessageArea.style.display = 'block';

    // مسح المؤقت السابق إن وجد
    if (modalMessageArea.timeoutId) {
        clearTimeout(modalMessageArea.timeoutId);
    }

    if (duration > 0) {
        modalMessageArea.timeoutId = setTimeout(() => {
            if (modalMessageArea.textContent === message) {
                modalMessageArea.classList.remove('show');
                setTimeout(() => { modalMessageArea.style.display = 'none'; }, 300);
            }
            modalMessageArea.timeoutId = null;
        }, duration);
    } else {
        modalMessageArea.timeoutId = null; // لا حاجة لمؤقت للرسائل الدائمة
    }
};

// إضافة دالة لعرض رسائل في نافذة التعيين
const showAssignModalMessage = (message, type = 'info', duration = 4000) => {
    if (!assignModalMessageArea) return;
    assignModalMessageArea.textContent = message;
    assignModalMessageArea.className = `message ${type} show`;
    assignModalMessageArea.style.display = 'block';

    // مسح المؤقت السابق إن وجد
    if (assignModalMessageArea.timeoutId) {
        clearTimeout(assignModalMessageArea.timeoutId);
    }

    if (duration > 0) {
        assignModalMessageArea.timeoutId = setTimeout(() => {
            if (assignModalMessageArea.textContent === message) {
                assignModalMessageArea.classList.remove('show');
                setTimeout(() => { assignModalMessageArea.style.display = 'none'; }, 300);
            }
            assignModalMessageArea.timeoutId = null;
        }, duration);
    } else {
        assignModalMessageArea.timeoutId = null; // لا حاجة لمؤقت للرسائل الدائمة
    }
};


// --- Pagination Functions ---
const renderPaginationControls = () => {
    if (!paginationControls) return;
    paginationControls.innerHTML = ''; // Clear existing controls
    const totalPages = Math.ceil(totalItems / itemsPerPage);

    if (totalPages <= 1) {
        return; // No pagination needed for 0 or 1 page
    }

    // Previous button (using right arrow for RTL)
    const prevButton = document.createElement('button');
    prevButton.innerHTML = '<i class="fas fa-angle-right"></i>';
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', () => goToPage(currentPage - 1));
    paginationControls.appendChild(prevButton);

    // Page buttons logic
    const maxPageButtons = 5; // Max number of page number buttons to show
    let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
    let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);

    // Adjust startPage if endPage is at the limit
    if (endPage === totalPages && endPage - startPage + 1 < maxPageButtons) {
        startPage = Math.max(1, endPage - maxPageButtons + 1);
    }

    // Ellipsis and first page button
    if (startPage > 1) {
        const firstPageBtn = document.createElement('button');
        firstPageBtn.textContent = '1';
        firstPageBtn.addEventListener('click', () => goToPage(1));
        paginationControls.appendChild(firstPageBtn);
        if (startPage > 2) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            paginationControls.appendChild(ellipsis);
        }
    }

    // Page number buttons
    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.textContent = i.toString();
        pageBtn.className = i === currentPage ? 'active' : '';
        pageBtn.addEventListener('click', () => goToPage(i));
        paginationControls.appendChild(pageBtn);
    }

     // Ellipsis and last page button
     if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            paginationControls.appendChild(ellipsis);
        }
        const lastPageBtn = document.createElement('button');
        lastPageBtn.textContent = totalPages.toString();
        lastPageBtn.addEventListener('click', () => goToPage(totalPages));
        paginationControls.appendChild(lastPageBtn);
    }

    // Next button (using left arrow for RTL)
    const nextButton = document.createElement('button');
    nextButton.innerHTML = '<i class="fas fa-angle-left"></i>';
    nextButton.disabled = currentPage === totalPages;
    nextButton.addEventListener('click', () => goToPage(currentPage + 1));
    paginationControls.appendChild(nextButton);
};

const goToPage = (page) => {
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    if (page < 1 || page > totalPages) return; // Stay within bounds
    currentPage = page;
    applyFiltersAndRender(); // Re-filter and render the new page
};

// --- Data Fetching ---
const fetchAllStudents = async () => {
    if (!_supabase) return; // Don't proceed if Supabase isn't initialized
    try {
        const { data, error, count } = await _supabase
            .from('students')
            .select('id, name', { count: 'exact' }) // Fetch count along with data
            .order('name', { ascending: true });

        if (error) throw error;

        allStudents = data || [];
        console.log(`Fetched ${allStudents.length} students.`);

    } catch (error) {
        console.error('Error fetching students:', error);
        showMessage(listMessage, `خطأ في جلب قائمة الطلاب: ${error.message}`, 'error', 0); // Show permanent error
        allStudents = []; // Ensure it's an empty array on error
    }
};

const fetchStudentDefaults = async () => {
     if (!_supabase) return;
    try {
        const { data, error } = await _supabase
            .from('student_subscription_defaults')
            .select('*') // Fetch all columns, including updated_at for sorting
            .order('updated_at', { ascending: false }); // ترتيب حسب آخر تحديث

        if (error) throw error;

        studentDefaults = {}; // Reset before populating
        (data || []).forEach(def => {
            // Ensure numeric fields are parsed correctly
            def.default_amount = parseFloat(def.default_amount) || 0;
            def.current_balance = parseFloat(def.current_balance) || 0;
            def.opening_balance = parseFloat(def.opening_balance) || 0;
            // تأكد من وجود تاريخ الإنشاء
            if (!def.created_at) {
                def.created_at = new Date().toISOString(); // قيمة افتراضية إذا لم يكن موجود
            }
            studentDefaults[def.student_id] = def; // Store the whole default object, keyed by student_id
        });
        console.log(`Fetched ${Object.keys(studentDefaults).length} student defaults.`);

    } catch (error) {
        console.error('Error fetching student defaults:', error);
        showMessage(listMessage, `خطأ في جلب الإعدادات الافتراضية: ${error.message}`, 'error', 0);
        studentDefaults = {}; // Ensure it's an empty object on error
    }
};

// إضافة دالة جلب مجموعات الطلاب
const fetchStudentGroups = async () => {
    if (!_supabase) return;
    try {
        // تحميل المجموعات من قاعدة البيانات
        const { data, error } = await _supabase
            .from('student_groups')
            .select('id, name')
            .order('name', { ascending: true });

        if (error) throw error;

        allGroups = data || [];
        console.log(`تم جلب ${allGroups.length} مجموعة طلاب.`);

    } catch (error) {
        console.error('خطأ في جلب مجموعات الطلاب:', error);
        allGroups = [];
    }
};

// دالة جلب تعيينات الطلاب للمجموعات
const fetchStudentGroupAssignments = async () => {
    if (!_supabase) return;
    try {
        // جلب تعيينات الطلاب من جدول student_group_members مع أسماء المجموعات
        const { data: membersData, error: membersError } = await _supabase
            .from('student_group_members')
            .select(`
                student_id,
                group_id,
                student_groups!inner(id, name)
            `);

        if (membersError) throw membersError;

        studentGroupAssignments = {};
        (membersData || []).forEach(member => {
            if (member.student_groups) {
                studentGroupAssignments[member.student_id] = {
                    group_id: member.group_id,
                    group_name: member.student_groups.name
                };
            }
        });

        console.log(`تم جلب ${Object.keys(studentGroupAssignments).length} تعيين مجموعة.`);

    } catch (error) {
        console.error('خطأ في جلب تعيينات المجموعات:', error);
        studentGroupAssignments = {};
    }
};

// دالة جلب الطلاب الذين لديهم إعدادات افتراضية
const fetchStudentsWithDefaults = async () => {
    if (!_supabase) return;
    try {
        // جلب الطلاب الذين لديهم إعدادات افتراضية مع بياناتهم
        const { data: studentsData, error: studentsError } = await _supabase
            .from('students')
            .select(`
                id,
                name,
                student_subscription_defaults!inner(
                    default_service_type,
                    default_amount,
                    current_balance,
                    opening_balance,
                    created_at,
                    updated_at
                )
            `);

        if (studentsError) throw studentsError;

        // تنسيق البيانات مع إضافة تاريخ التحديث للترتيب
        studentsWithDefaults = studentsData.map(student => ({
            id: student.id,
            name: student.name,
            defaultServiceType: student.student_subscription_defaults.default_service_type,
            defaultAmount: student.student_subscription_defaults.default_amount,
            currentBalance: student.student_subscription_defaults.current_balance,
            openingBalance: student.student_subscription_defaults.opening_balance,
            createdAt: student.student_subscription_defaults.created_at,
            updatedAt: student.student_subscription_defaults.updated_at
        }));

        // ترتيب الطلاب حسب تاريخ آخر تحديث (الأحدث أولاً)
        studentsWithDefaults.sort((a, b) => {
            const dateA = new Date(a.updatedAt || a.createdAt);
            const dateB = new Date(b.updatedAt || b.createdAt);
            return dateB - dateA; // ترتيب تنازلي
        });

        console.log(`تم جلب ${studentsWithDefaults.length} طالب لديهم إعدادات افتراضية.`);

        // تحديث العداد
        updateStudentsCounter();

    } catch (error) {
        console.error('خطأ في جلب الطلاب:', error);
        studentsWithDefaults = [];
        updateStudentsCounter();
    }
};

// دالة جلب الطلاب المخفيين
const fetchHiddenStudents = async () => {
    if (!_supabase) return;
    try {
        // جلب الطلاب المخفيين من جدول archived_students
        const { data: hiddenData, error: hiddenError } = await _supabase
            .from('archived_students')
            .select(`
                student_id,
                student_name,
                remaining_balance,
                archived_at,
                archived_reason
            `)
            .order('archived_at', { ascending: false });

        if (hiddenError) throw hiddenError;

        hiddenStudents = hiddenData || [];
        console.log(`تم جلب ${hiddenStudents.length} طالب مخفي.`);

        // تحديث العداد
        updateHiddenStudentsCounter();

    } catch (error) {
        console.error('خطأ في جلب الطلاب المخفيين:', error);
        hiddenStudents = [];
        updateHiddenStudentsCounter();
    }
};

// دالة جلب الطلاب الذين ليس لديهم إعدادات افتراضية
const fetchNewStudentsWithoutDefaults = async () => {
    if (!_supabase) return;
    try {
        // جلب جميع الطلاب النشطين
        const { data: allActiveStudents, error: studentsError } = await _supabase
            .from('students')
            .select('id, name, parent_phone, stage, grade')
            .eq('is_active', true)
            .order('name', { ascending: true });

        if (studentsError) throw studentsError;

        // جلب الطلاب الذين لديهم إعدادات افتراضية
        const { data: studentsWithDefaultsData, error: defaultsError } = await _supabase
            .from('student_subscription_defaults')
            .select('student_id');

        if (defaultsError) throw defaultsError;

        // إنشاء مجموعة من معرفات الطلاب الذين لديهم إعدادات افتراضية
        const studentsWithDefaultsIds = new Set(
            studentsWithDefaultsData.map(item => item.student_id)
        );

        // تصفية الطلاب الذين ليس لديهم إعدادات افتراضية
        newStudentsWithoutDefaults = allActiveStudents.filter(
            student => !studentsWithDefaultsIds.has(student.id)
        );

        console.log(`تم جلب ${newStudentsWithoutDefaults.length} طالب بدون إعدادات افتراضية.`);

        // تحديث العداد
        updateNewStudentsCounter();

    } catch (error) {
        console.error('خطأ في جلب الطلاب بدون إعدادات افتراضية:', error);
        newStudentsWithoutDefaults = [];
        updateNewStudentsCounter();
    }
};

// تحديث عداد الطلاب (تم إزالة الزر من الواجهة)
const updateStudentsCounter = () => {
    // تم حذف زر الطلاب المسجلين من شريط التنقل
    console.log('updateStudentsCounter: Button removed from interface');
};

// تحديث عداد الطلاب المخفيين
const updateHiddenStudentsCounter = () => {
    const hiddenBtn = document.getElementById('hidden-students-btn');
    const hiddenCount = document.getElementById('hidden-students-count');
    const hiddenText = document.getElementById('hidden-students-text');

    if (!hiddenBtn || !hiddenCount || !hiddenText) {
        console.warn('Hidden counter elements not found');
        return;
    }

    const count = hiddenStudents.length;

    if (count > 0) {
        hiddenBtn.style.display = 'flex';
        hiddenBtn.style.visibility = 'visible';
        hiddenCount.textContent = count;
        hiddenText.textContent = count === 1 ? 'طالب مخفي' : 'طلاب مخفيين';
        console.log('Hidden counter updated - showing:', count);
    } else {
        hiddenBtn.style.display = 'none';
        console.log('Hidden counter hidden - no hidden students');
    }
};

// تحديث عداد الطلاب الجدد (تم إزالة الزر من الواجهة)
const updateNewStudentsCounter = () => {
    // تم حذف زر الطلاب الجدد من شريط التنقل
    console.log('updateNewStudentsCounter: Button removed from interface');
};

// --- Rendering ---
const renderDefaultsTable = () => {
    if (!defaultsTableBody) return;
    defaultsTableBody.innerHTML = ''; // Clear previous content
    listMessage.style.display = 'none'; // Hide message area initially

    if (displayedStudents.length === 0) {
        const searchTerm = searchInput ? searchInput.value.trim() : '';
        let message = 'لا يوجد طلاب لعرضهم.';

        if (searchTerm) {
            message = `لا توجد نتائج تطابق البحث "${searchTerm}".`;
        } else if (allStudents.length === 0) {
            message = 'لا يوجد طلاب مسجلين في النظام.';
        }

        defaultsTableBody.innerHTML = `<tr><td colspan="7" class="loading-message">${message}</td></tr>`;
        return;
    }

    displayedStudents.forEach(student => {
        const studentId = student.id;
        const currentDefault = studentDefaults[studentId]; // Get the default object for this student, if exists

        // Determine current values, using empty strings or defaults if no default exists
        const currentService = currentDefault?.default_service_type || '';
        const currentAmount = currentDefault?.default_amount !== undefined && currentDefault?.default_amount !== null
                                ? formatCurrency(currentDefault.default_amount)
                                : '';
        const currentBalance = currentDefault?.current_balance !== undefined && currentDefault?.current_balance !== null
                                ? formatCurrency(currentDefault.current_balance)
                                : '0.00'; // Default balance to 0.00 if not set

        const row = document.createElement('tr');
        row.dataset.studentId = studentId; // Store student ID for reference

        // Create Service Type Select dropdown
        const serviceOptions = validServiceTypes.map(type =>
            `<option value="${type}" ${currentService === type ? 'selected' : ''}>${type}</option>`
        ).join('');
        const serviceSelectHTML = `
            <select class="default-service-select" data-original-value="${currentService}">
                <option value="">-- اختر الخدمة --</option>
                ${serviceOptions}
            </select>`;

        // Create Amount Input
        const amountInputHTML = `
            <input type="number" class="default-amount-input" min="0" step="0.01"
                   value="${currentAmount}" data-original-value="${currentAmount}" placeholder="أدخل المبلغ">`;

        // Create Opening Balance Input
        const openingBalance = currentDefault?.opening_balance !== undefined && currentDefault?.opening_balance !== null
                                ? formatCurrency(currentDefault.opening_balance)
                                : '';
        const openingBalanceInputHTML = `
            <input type="number" class="opening-balance-input" step="0.01"
                   value="${openingBalance}" data-original-value="${openingBalance}" placeholder="رصيد الافتتاح">`;

        // Create Current Balance Display (Clickable for details)
        const currentBalanceDisplayHTML = `
            <div class="balance-display clickable ${currentDefault?.current_balance > 0 ? 'balance-positive' : currentDefault?.current_balance < 0 ? 'balance-negative' : 'balance-zero'}"
                 onclick="openBalanceInfoModal('${studentId}', '${student.name}', ${currentDefault?.current_balance || 0}, ${currentDefault?.opening_balance || 0})"
                 title="اضغط لعرض تفاصيل الرصيد"
                 style="cursor: pointer;">
                ${formatCurrency(currentDefault?.current_balance || 0)} ريال
            </div>`;

        // Get student group assignment
        const groupAssignment = studentGroupAssignments[studentId];
        const groupDisplayHTML = groupAssignment
            ? `<span class="group-badge assigned">${groupAssignment.group_name}</span>`
            : `<span class="group-badge unassigned">غير محدد</span>`;

        // Create Action Buttons based on view mode
        let actionButtonsHTML;
        if (isShowingRegisteredStudents) {
            // للطلاب المسجلين - أزرار التعديل والحفظ
            actionButtonsHTML = `
                <div class="action-buttons">
                    <button class="btn-icon btn-assign" title="تعيين للمجموعة">
                        <i class="fas fa-users"></i>
                    </button>
                    <button class="btn-icon btn-delete" title="حذف الإعدادات">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="btn-icon btn-save" title="حفظ التغييرات" disabled>
                        <i class="fas fa-save"></i>
                    </button>
                </div>`;
        } else {
            // للطلاب الجدد - زر إضافة إعدادات افتراضية
            actionButtonsHTML = `
                <div class="action-buttons">
                    <button class="btn-icon btn-add-defaults" title="إضافة إعدادات افتراضية">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="btn-icon btn-save-new" title="حفظ الإعدادات" style="display: none;">
                        <i class="fas fa-save"></i>
                    </button>
                    <button class="btn-icon btn-cancel-new" title="إلغاء" style="display: none;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>`;
        }

        // Populate row cells based on view mode
        if (isShowingRegisteredStudents) {
            // للطلاب المسجلين - عرض كامل مع إمكانية التعديل
            row.innerHTML = `
                <td>${student.name}</td>
                <td>${serviceSelectHTML}</td>
                <td>${amountInputHTML}</td>
                <td>${openingBalanceInputHTML}</td>
                <td>${currentBalanceDisplayHTML}</td>
                <td>${groupDisplayHTML}</td>
                <td>${actionButtonsHTML}</td>
            `;
        } else {
            // للطلاب الجدد - عرض مبسط مع زر الإضافة أو نموذج الإدخال
            row.innerHTML = `
                <td>${student.name}</td>
                <td class="service-cell">
                    <span class="placeholder-text">-</span>
                    <select class="default-service-select-new" style="display: none;">
                        <option value="">اختر نوع الخدمة</option>
                        <option value="ذهاب فقط">ذهاب فقط</option>
                        <option value="عودة فقط">عودة فقط</option>
                        <option value="ذهاب وعودة">ذهاب وعودة</option>
                    </select>
                </td>
                <td class="amount-cell">
                    <span class="placeholder-text">-</span>
                    <input type="text" class="default-amount-input-new" placeholder="المبلغ الشهري" style="display: none;">
                </td>
                <td class="opening-balance-cell">
                    <span class="placeholder-text">-</span>
                    <input type="text" class="opening-balance-input-new" placeholder="الرصيد الافتتاحي" style="display: none;">
                </td>
                <td>-</td>
                <td>${groupDisplayHTML}</td>
                <td>${actionButtonsHTML}</td>
            `;
        }

        // --- Add event listeners for this row ---
        const selectElement = row.querySelector('.default-service-select');
        const amountInputElement = row.querySelector('.default-amount-input');
        const openingBalanceInputElement = row.querySelector('.opening-balance-input');
        const saveButton = row.querySelector('.btn-save');
        const deleteButton = row.querySelector('.btn-delete');
        const addDefaultsButton = row.querySelector('.btn-add-defaults');
        const assignButton = row.querySelector('.btn-assign');

        // عناصر الطلاب الجدد
        const selectElementNew = row.querySelector('.default-service-select-new');
        const amountInputElementNew = row.querySelector('.default-amount-input-new');
        const openingBalanceInputElementNew = row.querySelector('.opening-balance-input-new');
        const saveButtonNew = row.querySelector('.btn-save-new');
        const cancelButtonNew = row.querySelector('.btn-cancel-new');

        // دالة فحص التغييرات - فقط للطلاب المسجلين
        const checkChanges = () => {
            if (!isShowingRegisteredStudents || !selectElement || !amountInputElement || !openingBalanceInputElement || !saveButton) {
                return; // تجاهل للطلاب الجدد أو إذا كانت العناصر غير موجودة
            }

            const serviceChanged = selectElement.value !== selectElement.dataset.originalValue;

            const amountRawValue = amountInputElement.value.trim();
            const originalAmountValue = amountInputElement.dataset.originalValue;
            let amountChanged = amountRawValue !== originalAmountValue;
             if (amountRawValue === '' && (originalAmountValue === '0.00' || originalAmountValue === '')) {
                 amountChanged = false;
             }
             if (amountRawValue !== '' && (originalAmountValue === '0.00' || originalAmountValue === '')) {
                 amountChanged = true;
             }
             if (parseFloat(amountRawValue) === 0 && originalAmountValue !== '' && parseFloat(originalAmountValue) !== 0) {
                 amountChanged = true;
             }

            const openingBalanceRawValue = openingBalanceInputElement.value.trim();
            const originalOpeningBalanceValue = openingBalanceInputElement.dataset.originalValue;
            let openingBalanceChanged = openingBalanceRawValue !== originalOpeningBalanceValue;
            if (openingBalanceRawValue === '' && (originalOpeningBalanceValue === '0.00' || originalOpeningBalanceValue === '')) {
                openingBalanceChanged = false;
            }
            if (openingBalanceRawValue !== '' && (originalOpeningBalanceValue === '0.00' || originalOpeningBalanceValue === '')) {
                openingBalanceChanged = true;
            }
            if (parseFloat(openingBalanceRawValue) === 0 && originalOpeningBalanceValue !== '' && parseFloat(originalOpeningBalanceValue) !== 0) {
                openingBalanceChanged = true;
            }

            // Validation
            const isValidAmount = amountRawValue === '' || (!isNaN(parseFloat(amountRawValue)) && parseFloat(amountRawValue) >= 0);
            const isValidOpeningBalance = openingBalanceRawValue === '' || !isNaN(parseFloat(openingBalanceRawValue));
            const hasService = selectElement.value !== '';
            const hasAmount = amountRawValue !== '';

            // Enable save button logic:
            const serviceAmountConsistent = (hasService && hasAmount) || (!hasService && !hasAmount);
            const anyChange = serviceChanged || amountChanged || openingBalanceChanged;

            const canSave = anyChange && isValidAmount && isValidOpeningBalance && serviceAmountConsistent;

            saveButton.disabled = !canSave;
            row.classList.toggle('data-changed', canSave);
        };

        // إضافة مستمعات الأحداث فقط إذا كانت العناصر موجودة
        if (selectElement) {
            selectElement.addEventListener('change', checkChanges);
        }
        if (amountInputElement) {
            amountInputElement.addEventListener('input', checkChanges);
            amountInputElement.addEventListener('blur', () => {
                 if (amountInputElement.value.trim() !== '') {
                     const formatted = formatCurrency(amountInputElement.value);
                     if (amountInputElement.value !== formatted) {
                        amountInputElement.value = formatted;
                        checkChanges();
                     }
                 }
            });
        }
        if (openingBalanceInputElement) {
            openingBalanceInputElement.addEventListener('input', checkChanges);
            openingBalanceInputElement.addEventListener('blur', () => {
                 const balanceValue = openingBalanceInputElement.value.trim();
                 if (balanceValue !== '') {
                     const formatted = formatCurrency(balanceValue);
                     if (openingBalanceInputElement.value !== formatted) {
                        openingBalanceInputElement.value = formatted;
                        checkChanges();
                     }
                 }
            });
        }

        // Event listeners for action buttons
        if (deleteButton) {
            deleteButton.addEventListener('click', () => handleDeleteDefaults(studentId, student.name, row));
        }

        if (addDefaultsButton) {
            addDefaultsButton.addEventListener('click', () => showNewStudentForm(row, studentId, student.name));
        }

        if (assignButton) {
            assignButton.addEventListener('click', () => openAssignGroupModal(studentId, student.name));
        }

        if (saveButton) {
            saveButton.addEventListener('click', () => handleSaveDefaults(studentId, row));
        }

        // مستمعات أحداث الطلاب الجدد
        if (saveButtonNew) {
            saveButtonNew.addEventListener('click', () => handleSaveNewDefaults(studentId, student.name, row));
        }

        if (cancelButtonNew) {
            cancelButtonNew.addEventListener('click', () => hideNewStudentForm(row));
        }

        // إضافة مستمعات للحقول الجديدة لتنسيق العملة
        if (amountInputElementNew) {
            amountInputElementNew.addEventListener('blur', () => {
                const value = amountInputElementNew.value.trim();
                if (value !== '') {
                    const formatted = formatCurrency(value);
                    if (amountInputElementNew.value !== formatted) {
                        amountInputElementNew.value = formatted;
                    }
                }
            });
        }

        if (openingBalanceInputElementNew) {
            openingBalanceInputElementNew.addEventListener('blur', () => {
                const value = openingBalanceInputElementNew.value.trim();
                if (value !== '') {
                    const formatted = formatCurrency(value);
                    if (openingBalanceInputElementNew.value !== formatted) {
                        openingBalanceInputElementNew.value = formatted;
                    }
                }
            });
        }

        defaultsTableBody.appendChild(row);
    });
};

// إضافة دالة لعرض مجموعات الطلاب في القائمة
const renderStudentGroups = () => {
    if (!studentGroupsList) return;

    studentGroupsList.innerHTML = ''; // مسح المحتوى السابق

    if (allGroups.length === 0) {
        studentGroupsList.innerHTML = `<li class="no-groups">لا توجد مجموعات مسجلة.</li>`;
        return;
    }

    allGroups.forEach(group => {
        const li = document.createElement('li');
        li.textContent = group.name;
        li.dataset.groupId = group.id;

        // إضافة أزرار التعديل والحذف
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'group-actions';

        // زر التعديل
        const editBtn = document.createElement('button');
        editBtn.className = 'group-action-btn edit-btn';
        editBtn.innerHTML = '<i class="fas fa-edit"></i>';
        editBtn.title = 'تعديل المجموعة';
        editBtn.addEventListener('click', (e) => {
            e.stopPropagation(); // منع انتشار الحدث للعنصر الأب
            handleEditGroup(group.id, group.name);
        });

        // زر الحذف
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'group-action-btn delete-btn';
        deleteBtn.innerHTML = '<i class="fas fa-trash-alt"></i>';
        deleteBtn.title = 'حذف المجموعة';
        deleteBtn.addEventListener('click', (e) => {
            e.stopPropagation(); // منع انتشار الحدث للعنصر الأب
            handleDeleteGroup(group.id, group.name);
        });

        actionsDiv.appendChild(editBtn);
        actionsDiv.appendChild(deleteBtn);
        li.appendChild(actionsDiv);

        // إضافة حدث النقر لفتح صفحة تفاصيل المجموعة
        li.addEventListener('click', () => navigateToGroupDetails(group.id));

        studentGroupsList.appendChild(li);
    });
};

// دالة للانتقال إلى صفحة تفاصيل المجموعة
const navigateToGroupDetails = (groupId) => {
    window.location.href = `group_details.html?id=${groupId}`;
};

// دالة معالجة تعديل المجموعة
const handleEditGroup = (groupId, currentName) => {
    if (!groupNameInput || !addGroupModal || !modalMessageArea) return;

    // تعيين قيمة الاسم الحالي وعنوان النافذة المنبثقة
    groupNameInput.value = currentName;
    const modalTitle = addGroupModal.querySelector('h2');
    if (modalTitle) modalTitle.textContent = 'تعديل اسم المجموعة';

    // إظهار النافذة المنبثقة
    modalMessageArea.style.display = 'none';
    addGroupModal.style.display = 'block';

    // تغيير معالج النموذج ليقوم بتحديث المجموعة بدلاً من إضافة مجموعة جديدة
    const form = addGroupForm;

    // إزالة المعالجات السابقة وإضافة معالج جديد
    const newSubmitHandler = async (e) => {
        e.preventDefault();
        await updateGroupName(groupId, currentName);
    };

    // حفظ المعالج الحالي لاستعادته لاحقاً
    form._originalSubmitHandler = form._originalSubmitHandler || form.onsubmit;
    form.onsubmit = newSubmitHandler;

    // التركيز على حقل الإدخال
    setTimeout(() => {
        groupNameInput.focus();
        groupNameInput.select();
    }, 100);
};

// دالة لتحديث اسم المجموعة
const updateGroupName = async (groupId, oldName) => {
    if (!_supabase || !groupNameInput || !saveGroupBtn) return;

    const newName = groupNameInput.value.trim();

    if (!newName) {
        showModalMessage('الرجاء إدخال اسم للمجموعة', 'warning');
        return;
    }

    if (newName === oldName) {
        closeAddGroupModal();
        return; // لم يتغير الاسم
    }

    // تعطيل زر الحفظ وإظهار رمز التحميل
    saveGroupBtn.disabled = true;
    saveGroupBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

    try {
        // التحقق مما إذا كان الاسم الجديد موجوداً بالفعل
        const { data: existing, error: checkError } = await _supabase
            .from('student_groups')
            .select('id')
            .eq('name', newName)
            .neq('id', groupId) // استثناء المجموعة الحالية
            .maybeSingle();

        if (checkError) throw checkError;

        if (existing) {
            showModalMessage(`اسم المجموعة "${newName}" موجود بالفعل`, 'error');
            return;
        }

        // تحديث اسم المجموعة
        const { data, error } = await _supabase
            .from('student_groups')
            .update({ name: newName })
            .eq('id', groupId)
            .select()
            .single();

        if (error) throw error;

        // تحديث المجموعة في الحالة المحلية
        const groupIndex = allGroups.findIndex(g => g.id === groupId);
        if (groupIndex !== -1) {
            allGroups[groupIndex] = data;
            // ترتيب المجموعات أبجدياً
            allGroups.sort((a, b) => a.name.localeCompare(b.name));
        }

        // إعادة عرض المجموعات
        renderStudentGroups();

        showModalMessage(`تم تحديث اسم المجموعة إلى "${newName}" بنجاح`, 'success');

        // إغلاق النافذة بعد فترة قصيرة
        setTimeout(() => {
            closeAddGroupModal();
            // استعادة معالج النموذج الأصلي
            if (addGroupForm && addGroupForm._originalSubmitHandler) {
                addGroupForm.onsubmit = addGroupForm._originalSubmitHandler;
            }
            // تغيير عنوان النافذة مرة أخرى
            const modalTitle = addGroupModal.querySelector('h2');
            if (modalTitle) modalTitle.textContent = 'إضافة مجموعة طلاب جديدة';
        }, 1500);

    } catch (error) {
        console.error('خطأ في تحديث اسم المجموعة:', error);

        // عرض رسالة خطأ مناسبة
        let errorMessage = 'حدث خطأ أثناء تحديث اسم المجموعة';
        if (error.message) {
            errorMessage += `: ${error.message}`;
        }

        showModalMessage(errorMessage, 'error');
    } finally {
        // إعادة تفعيل زر الحفظ
        if (saveGroupBtn) {
            saveGroupBtn.disabled = false;
            saveGroupBtn.innerHTML = '<i class="fas fa-save"></i> حفظ المجموعة';
        }
    }
};

// دالة معالجة حذف المجموعة
const handleDeleteGroup = (groupId, groupName) => {
    if (!confirm(`هل أنت متأكد من حذف المجموعة "${groupName}"؟ \nسيتم حذف جميع الطلاب من هذه المجموعة.`)) {
        return;
    }

    deleteGroup(groupId, groupName);
};

// دالة لحذف المجموعة
const deleteGroup = async (groupId, groupName) => {
    if (!_supabase) return;

    try {
        showMessage(listMessage, `جاري حذف المجموعة "${groupName}"...`, 'info');

        const { error } = await _supabase
            .from('student_groups')
            .delete()
            .eq('id', groupId);

        if (error) throw error;

        // حذف المجموعة من الحالة المحلية
        allGroups = allGroups.filter(group => group.id !== groupId);

        // إعادة عرض المجموعات
        renderStudentGroups();

        showMessage(listMessage, `تم حذف المجموعة "${groupName}" بنجاح`, 'success');

    } catch (error) {
        console.error('خطأ في حذف المجموعة:', error);
        showMessage(listMessage, `حدث خطأ أثناء حذف المجموعة: ${error.message}`, 'error');
    }
};

// --- Statistics Update ---
const updateStatistics = (students) => {
    const totalStudentsElement = document.getElementById('total-students');
    const configuredStudentsElement = document.getElementById('configured-students');
    const totalBalanceElement = document.getElementById('total-balance');

    if (!totalStudentsElement || !configuredStudentsElement || !totalBalanceElement) return;

    const totalStudents = students.length;
    const configuredStudents = students.filter(student => studentDefaults[student.id]).length;
    const totalBalance = students.reduce((sum, student) => {
        const defaults = studentDefaults[student.id];
        return sum + (defaults?.current_balance || 0);
    }, 0);

    totalStudentsElement.textContent = totalStudents;
    configuredStudentsElement.textContent = configuredStudents;
    totalBalanceElement.textContent = `${formatCurrency(totalBalance)} ريال`;
};

// متغير لتتبع حالة العرض الحالية
let isShowingRegisteredStudents = false;

// --- Data Handling ---
const applyFiltersAndRender = () => {
    const searchTerm = searchInput ? searchInput.value.toLowerCase().trim() : '';
    const serviceFilter = document.getElementById('service-filter');
    const balanceFilter = document.getElementById('balance-filter');

    const selectedService = serviceFilter ? serviceFilter.value : '';
    const selectedBalance = balanceFilter ? balanceFilter.value : '';

    let studentsToShow;

    if (isShowingRegisteredStudents) {
        // عرض الطلاب المسجلين (الذين لديهم إعدادات افتراضية)
        studentsToShow = allStudents.filter(student =>
            studentDefaults[student.id] // فقط الطلاب الذين لديهم إعدادات افتراضية
        );

        // ترتيب الطلاب المسجلين حسب تاريخ آخر تحديث (الأحدث أولاً)
        // يشمل الإضافة والتعديل
        studentsToShow.sort((a, b) => {
            const aUpdatedAt = studentDefaults[a.id]?.updated_at || studentDefaults[a.id]?.created_at;
            const bUpdatedAt = studentDefaults[b.id]?.updated_at || studentDefaults[b.id]?.created_at;

            if (!aUpdatedAt && !bUpdatedAt) return 0;
            if (!aUpdatedAt) return 1;
            if (!bUpdatedAt) return -1;

            return new Date(bUpdatedAt) - new Date(aUpdatedAt); // ترتيب تنازلي (الأحدث أولاً)
        });
    } else {
        // عرض الطلاب الجدد (الذين ليس لديهم إعدادات افتراضية)
        studentsToShow = allStudents.filter(student =>
            !studentDefaults[student.id] // فقط الطلاب الذين ليس لديهم إعدادات افتراضية
        );
    }

    // Filter students based on search term and filters
    const filteredStudents = studentsToShow.filter(student => {
        // Search filter
        const matchesSearch = !searchTerm || student.name.toLowerCase().includes(searchTerm);

        if (isShowingRegisteredStudents) {
            // للطلاب المسجلين - تطبيق فلاتر الخدمة والرصيد
            const studentDefault = studentDefaults[student.id];
            const studentService = studentDefault?.default_service_type || '';
            const matchesService = !selectedService || studentService === selectedService;

            const studentBalance = studentDefault?.current_balance || 0;
            let matchesBalance = true;
            if (selectedBalance === 'positive') {
                matchesBalance = studentBalance > 0;
            } else if (selectedBalance === 'negative') {
                matchesBalance = studentBalance < 0;
            } else if (selectedBalance === 'zero') {
                matchesBalance = studentBalance === 0;
            }

            return matchesSearch && matchesService && matchesBalance;
        } else {
            // للطلاب الجدد - فقط البحث بالاسم
            return matchesSearch;
        }
    });

    totalItems = filteredStudents.length;

    // Update statistics
    updateStatistics(filteredStudents);

    if (studentsCountBadge) studentsCountBadge.textContent = `${totalItems} طالب`;

    // Apply pagination to the filtered list
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    displayedStudents = filteredStudents.slice(startIndex, endIndex); // Get the slice for the current page

    renderDefaultsTable(); // Render the table with the paginated subset
    renderPaginationControls(); // Update pagination controls based on total filtered items

    // Show message if no results match search
    if (totalItems === 0 && searchTerm) {
        showMessage(listMessage, `لا توجد نتائج للبحث عن "${searchTerm}".`, 'info');
    } else if (totalItems === 0) {
        if (isShowingRegisteredStudents) {
            showMessage(listMessage, 'لا يوجد طلاب مسجلين حالياً.', 'info');
        } else {
            showMessage(listMessage, 'لا يوجد طلاب جدد بحاجة لإعدادات افتراضية.', 'info');
        }
    } else {
        // listMessage is hidden by default in renderDefaultsTable
    }
};

const handleSaveDefaults = async (studentId, rowElement) => {
    if (!_supabase) return;

    const selectElement = rowElement.querySelector('.default-service-select');
    const amountInputElement = rowElement.querySelector('.default-amount-input');
    const openingBalanceInputElement = rowElement.querySelector('.opening-balance-input');
    const saveButton = rowElement.querySelector('.btn-save');
    const studentName = rowElement.cells[0].textContent;

    const newService = selectElement.value;
    const newAmountRaw = amountInputElement.value.trim();
    const newOpeningBalanceRaw = openingBalanceInputElement.value.trim();

    let newAmount = null;
    let newOpeningBalance = null;

    // Validate and parse amount
    if (newAmountRaw !== '') {
        newAmount = parseFloat(newAmountRaw);
        if (isNaN(newAmount) || newAmount < 0) {
            showMessage(listMessage, `خطأ: المبلغ الافتراضي للطالب ${studentName} غير صالح.`, 'error', 0);
            return;
        }
        newAmount = newAmount.toFixed(2);
    }

    // Validate and parse opening balance
    if (newOpeningBalanceRaw !== '') {
        const parsedOpeningBalance = parseFloat(newOpeningBalanceRaw);
        if (isNaN(parsedOpeningBalance)) {
             showMessage(listMessage, `خطأ: رصيد الافتتاح للطالب ${studentName} غير صالح.`, 'error', 0);
             return;
        }
        newOpeningBalance = parsedOpeningBalance.toFixed(2);
    }


    // Determine operation: update, insert, or delete (based on service/amount)
    const existingDefault = studentDefaults[studentId];
    let operation = null;
    let dataToUpsert = null;

    // Determine what to save based on what changed
    const hasServiceAndAmount = newService && newAmount !== null;
    const hasOpeningBalance = newOpeningBalance !== null;

    if (hasServiceAndAmount || hasOpeningBalance) {
        operation = existingDefault ? 'update' : 'insert';
        dataToUpsert = {
            student_id: studentId
        };

        // Add service and amount if provided
        if (hasServiceAndAmount) {
            dataToUpsert.default_service_type = newService;
            dataToUpsert.default_amount = newAmount;
        }

        // Add opening balance if provided
        if (hasOpeningBalance) {
            dataToUpsert.opening_balance = newOpeningBalance;
        }

    } else if (!hasServiceAndAmount && !hasOpeningBalance && existingDefault) {
        // Clear all values
        operation = 'update';
        dataToUpsert = {
            default_service_type: null,
            default_amount: null,
            opening_balance: null
        };
    } else {
        // Nothing to save
        showMessage(listMessage, `لا توجد تغييرات للحفظ للطالب ${studentName}.`, 'warning', 3000);
        return;
    }

    // --- Perform DB Operation ---
    saveButton.disabled = true;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    try {
        let resultError = null;
        let resultData = null;

        if (operation === 'insert') {
            // Ensure required fields are present if DB enforces NOT NULL without defaults
            if (!dataToUpsert.default_service_type && /* check DB constraint */ true) {
                 // Example: Set a default if required and null was intended
                 // dataToUpsert.default_service_type = 'other';
                 console.warn(`Setting default_service_type to 'other' for insert as it was null/empty.`);
                 // Adjust based on actual DB schema (e.g., if it allows NULL or has a DB default)
                 // For now, assuming NULL is okay or handled by DB. If not, uncomment and adjust above line.
            }
            console.log(`Inserting default for student ${studentId}:`, dataToUpsert);
            const { data, error } = await _supabase
                .from('student_subscription_defaults')
                .insert(dataToUpsert)
                .select()
                .single();
            resultError = error;
            resultData = data;
        } else if (operation === 'update') {
             console.log(`Updating default for student ${studentId} (ID: ${existingDefault.id}):`, dataToUpsert);
             const { data, error } = await _supabase
                .from('student_subscription_defaults')
                .update(dataToUpsert) // Pass the object with fields to update
                .eq('id', existingDefault.id)
                .select()
                .single();
            resultError = error;
            resultData = data;
        } else if (operation === 'delete') { // This case might be removed based on Option 2 above
            console.log(`Deleting default for student ${studentId} (ID: ${existingDefault.id})`);
            const { error } = await _supabase
                .from('student_subscription_defaults')
                .delete()
                .eq('id', existingDefault.id);
            resultError = error;
        }

        if (resultError) {
             console.error(`Supabase ${operation} error:`, resultError);
            // Provide more specific feedback if possible (e.g., constraint violation)
            let userMessage = `فشل ${operation === 'insert' ? 'إضافة' : operation === 'update' ? 'تحديث' : 'حذف'} الإعداد الافتراضي.`;
            if (resultError.message.includes("violates check constraint")) {
                userMessage += " تأكد من أن القيم المدخلة صالحة.";
            } else if (resultError.message.includes("violates foreign key constraint")) {
                 userMessage += " خطأ في معرّف الطالب.";
            } else if (resultError.message.includes("violates not-null constraint")) {
                 userMessage += " حقل مطلوب مفقود (مثل نوع الخدمة).";
            } else {
                 userMessage += ` ${resultError.message}`;
            }
            throw new Error(userMessage);
        }

        // --- Update Local State and UI on Success ---
        if (operation === 'insert' || operation === 'update') {
            // Ensure numeric fields are numbers in the local state
            if (resultData.default_amount !== undefined) {
                resultData.default_amount = parseFloat(resultData.default_amount) || 0;
            }
            if (resultData.opening_balance !== undefined) {
                resultData.opening_balance = parseFloat(resultData.opening_balance) || 0;
            }
            if (resultData.current_balance !== undefined) {
                resultData.current_balance = parseFloat(resultData.current_balance) || 0;
            }

            studentDefaults[studentId] = resultData;

            // Update original values
            selectElement.dataset.originalValue = resultData.default_service_type || '';
            amountInputElement.dataset.originalValue = resultData.default_amount ? formatCurrency(resultData.default_amount) : '';
            openingBalanceInputElement.dataset.originalValue = resultData.opening_balance ? formatCurrency(resultData.opening_balance) : '';

            // Update displayed values
            selectElement.value = resultData.default_service_type || '';
            amountInputElement.value = resultData.default_amount ? formatCurrency(resultData.default_amount) : '';
            openingBalanceInputElement.value = resultData.opening_balance ? formatCurrency(resultData.opening_balance) : '';

            // Update current balance display
            const balanceDisplay = rowElement.querySelector('.balance-display');
            if (balanceDisplay && resultData.current_balance !== undefined) {
                const balance = resultData.current_balance;
                balanceDisplay.textContent = `${formatCurrency(balance)} ريال`;
                balanceDisplay.className = `balance-display ${balance > 0 ? 'balance-positive' : balance < 0 ? 'balance-negative' : 'balance-zero'}`;
            }

        } else if (operation === 'delete') {
            delete studentDefaults[studentId];
            selectElement.dataset.originalValue = '';
            amountInputElement.dataset.originalValue = '';
            openingBalanceInputElement.dataset.originalValue = '';
            selectElement.value = '';
            amountInputElement.value = '';
            openingBalanceInputElement.value = '';
        }

        rowElement.classList.remove('data-changed');
        showMessage(listMessage, `تم ${operation === 'insert' ? 'إضافة' : operation === 'update' ? 'تحديث' : 'حذف'} الإعدادات للطالب ${studentName} بنجاح.`, 'success');

    } catch (error) {
        console.error(`Error performing ${operation} for student ${studentId}:`, error);
        showMessage(listMessage, `خطأ في حفظ الإعدادات: ${error.message}`, 'error', 0);
    } finally {
         saveButton.innerHTML = '<i class="fas fa-save"></i>';
         saveButton.disabled = true; // Keep disabled after attempt
    }
};

// --- Modal Handling ---
// دالة لفتح النافذة المنبثقة لإضافة مجموعة
const openAddGroupModal = () => {
    if (!addGroupModal) return;

    groupNameInput.value = ''; // مسح المدخلات السابقة
    if (modalMessageArea) modalMessageArea.style.display = 'none'; // إخفاء منطقة الرسائل

    addGroupModal.style.display = 'block'; // إظهار النافذة المنبثقة

    // التركيز على حقل الإدخال
    if (groupNameInput) {
        setTimeout(() => {
            groupNameInput.focus();
        }, 100);
    }
};

// دالة لإغلاق النافذة المنبثقة لإضافة مجموعة
const closeAddGroupModal = () => {
    if (!addGroupModal) return;
    addGroupModal.style.display = 'none';
    // استعادة معالج النموذج الأصلي عند الإغلاق
    if (addGroupForm && addGroupForm._originalSubmitHandler) {
        addGroupForm.onsubmit = addGroupForm._originalSubmitHandler;
        // تغيير عنوان النافذة مرة أخرى
        const modalTitle = addGroupModal.querySelector('h2');
        if (modalTitle) modalTitle.textContent = 'إضافة مجموعة طلاب جديدة';
    }
};


// دالة لمعالجة إضافة مجموعة جديدة
const handleAddGroup = async (event) => {
    event.preventDefault(); // منع السلوك الافتراضي للنموذج

    if (!_supabase || !groupNameInput) return;

    const groupName = groupNameInput.value.trim();

    // التحقق من إدخال اسم للمجموعة
    if (!groupName) {
        showModalMessage('الرجاء إدخال اسم للمجموعة', 'warning');
        groupNameInput.focus();
        return;
    }

    // تعطيل زر الحفظ وإظهار رمز التحميل
    if (saveGroupBtn) {
        saveGroupBtn.disabled = true;
        saveGroupBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    }

    try {
        // التحقق مما إذا كان اسم المجموعة موجوداً بالفعل
        const { data: existing, error: checkError } = await _supabase
            .from('student_groups')
            .select('id')
            .eq('name', groupName)
            .maybeSingle();

        if (checkError) throw checkError;

        if (existing) {
            showModalMessage(`اسم المجموعة "${groupName}" موجود بالفعل`, 'error');
            return;
        }

        // إضافة مجموعة جديدة
        const { data, error } = await _supabase
            .from('student_groups')
            .insert({ name: groupName })
            .select()
            .single();

        if (error) throw error;

        // إضافة المجموعة الجديدة للقائمة وإعادة عرضها
        allGroups.push(data);
        // ترتيب المجموعات أبجدياً
        allGroups.sort((a, b) => a.name.localeCompare(b.name));
        renderStudentGroups();

        showModalMessage(`تمت إضافة المجموعة "${data.name}" بنجاح`, 'success');

        // إغلاق النافذة بعد فترة قصيرة
        setTimeout(() => {
            closeAddGroupModal();
        }, 1500);

    } catch (error) {
        console.error('خطأ في إضافة المجموعة:', error);

        // عرض رسالة خطأ مناسبة
        let errorMessage = 'حدث خطأ أثناء إضافة المجموعة';
        if (error.message && error.message.includes('student_groups_name_key')) {
            errorMessage = `اسم المجموعة "${groupName}" موجود بالفعل`;
        } else if (error.message) {
            errorMessage += `: ${error.message}`;
        }

        showModalMessage(errorMessage, 'error');
    } finally {
        // إعادة تفعيل زر الحفظ
        if (saveGroupBtn) {
            saveGroupBtn.disabled = false;
            saveGroupBtn.innerHTML = '<i class="fas fa-save"></i> حفظ المجموعة';
        }
    }
};

// --- وظائف نافذة تعيين المجموعة ---

// دالة حذف إعدادات الطالب مع التحقق من الرصيد
const handleDeleteDefaults = async (studentId, studentName, rowElement) => {
    if (!_supabase) return;

    try {
        // جلب الرصيد الحالي للطالب
        const { data: studentDefault, error: fetchError } = await _supabase
            .from('student_subscription_defaults')
            .select('current_balance')
            .eq('student_id', studentId)
            .single();

        if (fetchError) throw fetchError;

        const currentBalance = studentDefault?.current_balance || 0;

        let confirmMessage;
        let deleteAction;

        if (currentBalance === 0) {
            // الرصيد صفر - حذف عادي
            confirmMessage = `هل أنت متأكد من حذف جميع الإعدادات الافتراضية للطالب "${studentName}"؟\n\nسيتم حذف:\n- نوع الخدمة الافتراضي\n- المبلغ الافتراضي\n- رصيد الافتتاح\n\nهذا الإجراء لا يمكن التراجع عنه.`;
            deleteAction = 'delete';
        } else {
            // يوجد رصيد - إخفاء تلقائي
            const balanceText = currentBalance > 0 ? `له رصيد متبقي قدره ${formatCurrency(currentBalance)} ريال` : `عليه مديونية قدرها ${formatCurrency(Math.abs(currentBalance))} ريال`;

            confirmMessage = `الطالب "${studentName}" ${balanceText}\n\nسيتم إخفاؤه من القائمة الرئيسية ونقله إلى قسم الطلاب المخفيين.\nيمكنك الوصول إليه من خلال زر "الطلاب المخفيين" في الأعلى.\n\nهل تريد المتابعة؟`;
            deleteAction = 'hide';
        }

        if (deleteAction === 'delete') {
            const confirmDelete = confirm(confirmMessage);
            if (!confirmDelete) return;

            await performDeleteStudent(studentId, studentName, rowElement);
        } else if (deleteAction === 'hide') {
            const confirmHide = confirm(confirmMessage);
            if (!confirmHide) return;

            await performHideStudent(studentId, studentName, currentBalance, rowElement);
        }

    } catch (error) {
        console.error('Error in handleDeleteDefaults:', error);
        showMessage(listMessage, `خطأ في معالجة حذف الطالب ${studentName}: ${error.message}`, 'error', 0);
    }
};

// دالة إظهار مودال خيارات الحذف
const showDeleteOptionsModal = (studentName, currentBalance) => {
    return new Promise((resolve) => {
        const balanceText = currentBalance > 0 ? `له رصيد متبقي قدره ${formatCurrency(currentBalance)} ريال` : `عليه مديونية قدرها ${formatCurrency(Math.abs(currentBalance))} ريال`;

        const modalHTML = `
            <div id="delete-options-modal" class="modal" style="display: flex;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-exclamation-triangle"></i> تحذير: يوجد رصيد للطالب</h3>
                    </div>
                    <div class="modal-body">
                        <div class="warning-info">
                            <p><strong>الطالب:</strong> ${studentName}</p>
                            <p><strong>الرصيد:</strong> ${balanceText}</p>
                        </div>

                        <div class="delete-options">
                            <h4>اختر العملية المطلوبة:</h4>

                            <div class="option-card danger">
                                <div class="option-header">
                                    <i class="fas fa-trash-alt"></i>
                                    <h5>حذف نهائي</h5>
                                </div>
                                <p>سيتم حذف جميع البيانات والحسابات نهائياً</p>
                                <button class="btn btn-danger" onclick="resolveDeleteChoice('delete')">
                                    <i class="fas fa-trash"></i> حذف نهائي
                                </button>
                            </div>

                            <div class="option-card warning">
                                <div class="option-header">
                                    <i class="fas fa-archive"></i>
                                    <h5>نقل للأرشيف</h5>
                                </div>
                                <p>سيتم نقل الطالب لقسم الطلاب المحذوفين مع الاحتفاظ بالرصيد</p>
                                <button class="btn btn-warning" onclick="resolveDeleteChoice('archive')">
                                    <i class="fas fa-archive"></i> نقل للأرشيف
                                </button>
                            </div>
                        </div>

                        <div class="modal-actions">
                            <button class="btn btn-secondary" onclick="resolveDeleteChoice('cancel')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // إضافة دالة الحل للنافذة
        window.resolveDeleteChoice = (choice) => {
            const modal = document.getElementById('delete-options-modal');
            if (modal) modal.remove();
            delete window.resolveDeleteChoice;
            resolve(choice);
        };
    });
};

// دالة الحذف النهائي
const performDeleteStudent = async (studentId, studentName, rowElement) => {
    try {
        showMessage(listMessage, `جاري حذف إعدادات الطالب ${studentName}...`, 'info', 0);

        const { error } = await _supabase
            .from('student_subscription_defaults')
            .delete()
            .eq('student_id', studentId);

        if (error) throw error;

        // تحديث البيانات المحلية
        delete studentDefaults[studentId];

        // إعادة تعيين القيم في الصف
        resetRowValues(rowElement);

        // تحديث الإحصائيات
        updateStatistics(allStudents);

        // تحديث قائمة الطلاب
        await fetchStudentsWithDefaults();

        showMessage(listMessage, `تم حذف إعدادات الطالب ${studentName} بنجاح.`, 'success', 5000);

    } catch (error) {
        console.error('Error deleting student defaults:', error);
        showMessage(listMessage, `خطأ في حذف إعدادات الطالب ${studentName}: ${error.message}`, 'error', 0);
    }
};

// دالة إخفاء الطالب
const performHideStudent = async (studentId, studentName, currentBalance, rowElement) => {
    try {
        showMessage(listMessage, `جاري إخفاء الطالب ${studentName}...`, 'info', 0);

        // جلب البيانات الكاملة للطالب قبل الحذف
        const studentData = studentDefaults[studentId];
        if (!studentData) {
            throw new Error('لا يمكن العثور على بيانات الطالب');
        }

        // إنشاء سجل في جدول الطلاب المخفيين مع حفظ جميع البيانات
        const { error: archiveError } = await _supabase
            .from('archived_students')
            .upsert({
                student_id: studentId,
                student_name: studentName,
                remaining_balance: currentBalance,
                archived_at: new Date().toISOString(),
                archived_reason: 'إخفاء من إعدادات الاشتراكات الافتراضية - يوجد رصيد',
                // حفظ البيانات الكاملة للاستعادة
                original_service_type: studentData.default_service_type,
                original_amount: studentData.default_amount,
                original_opening_balance: studentData.opening_balance
            });

        if (archiveError) throw archiveError;

        // حذف من الإعدادات الافتراضية
        const { error: deleteError } = await _supabase
            .from('student_subscription_defaults')
            .delete()
            .eq('student_id', studentId);

        if (deleteError) throw deleteError;

        // تحديث البيانات المحلية
        delete studentDefaults[studentId];

        // إزالة الطالب من قائمة الطلاب المعروضين
        studentsWithDefaults = studentsWithDefaults.filter(student => student.id !== studentId);

        // إعادة تعيين القيم في الصف
        resetRowValues(rowElement);

        // تحديث الإحصائيات
        updateStatistics(allStudents);

        // تحديث قائمة الطلاب والطلاب المخفيين
        await Promise.all([
            fetchStudentsWithDefaults(),
            fetchHiddenStudents()
        ]);

        showMessage(listMessage, `تم إخفاء الطالب ${studentName} بنجاح. يمكنك الوصول إليه من قسم الطلاب المخفيين.`, 'success', 5000);

    } catch (error) {
        console.error('Error hiding student:', error);
        showMessage(listMessage, `خطأ في إخفاء الطالب ${studentName}: ${error.message}`, 'error', 0);
    }
};

// دالة مساعدة لإعادة تعيين قيم الصف
const resetRowValues = (rowElement) => {
    const selectElement = rowElement.querySelector('.default-service-select');
    const amountInputElement = rowElement.querySelector('.default-amount-input');
    const openingBalanceInputElement = rowElement.querySelector('.opening-balance-input');
    const balanceDisplay = rowElement.querySelector('.balance-display');
    const saveButton = rowElement.querySelector('.btn-save');

    if (selectElement) {
        selectElement.value = '';
        selectElement.dataset.originalValue = '';
    }
    if (amountInputElement) {
        amountInputElement.value = '';
        amountInputElement.dataset.originalValue = '';
    }
    if (openingBalanceInputElement) {
        openingBalanceInputElement.value = '';
        openingBalanceInputElement.dataset.originalValue = '';
    }
    if (balanceDisplay) {
        balanceDisplay.textContent = '0.00 ريال';
        balanceDisplay.className = 'balance-display balance-zero';
    }
    if (saveButton) {
        saveButton.disabled = true;
    }

    // إزالة تمييز الصف
    rowElement.classList.remove('data-changed');
};

// دالة لفتح نافذة تعيين المجموعة
const openAssignGroupModal = (studentId, studentName) => {
    if (!assignGroupModal || !assignStudentIdInput || !assignStudentNameSpan || !assignGroupSelect) return;

    // تعيين بيانات الطالب
    currentAssignStudentId = studentId;
    assignStudentIdInput.value = studentId;
    assignStudentNameSpan.textContent = studentName;

    // ملء قائمة المجموعات
    populateAssignGroupSelect();

    // تحديد المجموعة الحالية للطالب (إن وجدت)
    const currentAssignment = studentGroupAssignments[studentId];
    if (currentAssignment) {
        assignGroupSelect.value = currentAssignment.group_id;
        showAssignModalMessage(`الطالب حالياً في مجموعة: ${currentAssignment.group_name}`, 'info', 0);
    } else {
        showAssignModalMessage('الطالب غير مُعيَّن لأي مجموعة حالياً.', 'info', 0);
    }

    // إظهار النافذة
    assignGroupModal.style.display = 'flex'; // Use flex for centering
};

// دالة لإغلاق نافذة تعيين المجموعة
const closeAssignGroupModal = () => {
    if (!assignGroupModal) return;
    assignGroupModal.style.display = 'none';
    currentAssignStudentId = null; // مسح معرف الطالب الحالي
};

// دالة لملء قائمة المجموعات في نافذة التعيين
const populateAssignGroupSelect = () => {
    if (!assignGroupSelect) return;

    assignGroupSelect.innerHTML = ''; // مسح الخيارات القديمة

    if (allGroups.length === 0) {
        assignGroupSelect.innerHTML = '<option value="" disabled selected>-- لا توجد مجموعات --</option>';
        assignGroupSelect.disabled = true;
        return;
    }

    assignGroupSelect.disabled = false;
    assignGroupSelect.innerHTML = '<option value="" disabled selected>-- اختر مجموعة --</option>';

    // إضافة خيار إزالة من المجموعة
    const removeOption = document.createElement('option');
    removeOption.value = 'REMOVE';
    removeOption.textContent = '❌ إزالة من المجموعة';
    removeOption.style.color = '#e53e3e';
    assignGroupSelect.appendChild(removeOption);

    allGroups.forEach(group => {
        const option = document.createElement('option');
        option.value = group.id;
        option.textContent = group.name;
        assignGroupSelect.appendChild(option);
    });
};

// دالة لمعالجة تعيين الطالب لمجموعة
const handleAssignGroupSubmit = async (event) => {
    event.preventDefault();
    if (!_supabase || !currentAssignStudentId || !assignGroupSelect || !saveAssignmentBtn) return;

    const selectedGroupId = assignGroupSelect.value;

    if (!selectedGroupId) {
        showAssignModalMessage('الرجاء اختيار خيار من القائمة.', 'warning');
        return;
    }

    // تعطيل الزر وإظهار التحميل
    saveAssignmentBtn.disabled = true;

    if (selectedGroupId === 'REMOVE') {
        saveAssignmentBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإزالة...';
        showAssignModalMessage('جاري إزالة الطالب من المجموعة...', 'info');
    } else {
        saveAssignmentBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التعيين...';
        showAssignModalMessage('جاري تعيين الطالب للمجموعة...', 'info');
    }

    try {
        // حذف أي تعيين سابق للطالب (إن وجد)
        const { error: deleteError } = await _supabase
            .from('student_group_members')
            .delete()
            .eq('student_id', currentAssignStudentId);

        if (deleteError) {
            console.warn('Warning deleting existing assignment:', deleteError);
            // لا نتوقف هنا لأنه قد لا يكون هناك تعيين سابق
        }

        if (selectedGroupId === 'REMOVE') {
            // إزالة من البيانات المحلية
            delete studentGroupAssignments[currentAssignStudentId];

            showAssignModalMessage('تم إزالة الطالب من المجموعة بنجاح!', 'success');
        } else {
            // إضافة التعيين الجديد
            const { error: insertError } = await _supabase
                .from('student_group_members')
                .insert({
                    group_id: selectedGroupId,
                    student_id: currentAssignStudentId
                });

            if (insertError) throw insertError;

            // تحديث البيانات المحلية
            const selectedGroup = allGroups.find(group => group.id === selectedGroupId);
            if (selectedGroup) {
                studentGroupAssignments[currentAssignStudentId] = {
                    group_id: selectedGroupId,
                    group_name: selectedGroup.name
                };
            }

            showAssignModalMessage('تم تعيين الطالب للمجموعة بنجاح!', 'success');
        }

        // تحديث عرض الجدول
        applyFiltersAndRender();

        // إغلاق النافذة بعد فترة قصيرة
        setTimeout(() => {
            closeAssignGroupModal();
        }, 1500);

    } catch (error) {
        console.error('خطأ في تعيين الطالب للمجموعة:', error);
        showAssignModalMessage(`حدث خطأ: ${error.message}`, 'error', 0);
    } finally {
        // إعادة تفعيل الزر
        saveAssignmentBtn.disabled = false;
        saveAssignmentBtn.innerHTML = '<i class="fas fa-check"></i> تعيين الطالب';
    }
};


// --- وظائف مودال تفاصيل الرصيد ---

// فتح مودال تفاصيل الرصيد
const openBalanceInfoModal = async (studentId, studentName, currentBalance, openingBalance) => {
    if (!_supabase) return;

    const modal = document.getElementById('balance-info-modal');
    const studentNameSpan = document.getElementById('balance-info-student-name');
    const tbody = document.getElementById('balance-breakdown-tbody');
    const messageEl = document.getElementById('balance-info-message');

    if (!modal || !studentNameSpan || !tbody) return;

    // تعيين اسم الطالب
    studentNameSpan.textContent = studentName;

    // إظهار رسالة التحميل
    tbody.innerHTML = '<tr><td colspan="3"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</td></tr>';
    if (messageEl) messageEl.style.display = 'none';

    // إظهار المودال
    modal.classList.add('show');

    try {
        // جلب تفاصيل المدفوعات للطالب
        const { data: payments, error: paymentsError } = await _supabase
            .from('student_payments')
            .select(`
                id,
                amount,
                payment_date,
                created_at
            `)
            .eq('student_id', studentId)
            .order('created_at', { ascending: false });

        if (paymentsError) throw paymentsError;

        // حساب إجمالي المدفوعات
        const totalPaid = payments.reduce((sum, payment) => sum + (payment.amount || 0), 0);

        // جلب تفاصيل الإقفالات السابقة من جدول subscription_locks
        const { data: locksData, error: locksError } = await _supabase
            .from('subscription_locks')
            .select(`
                locked_remaining_amount,
                locked_at,
                budget_months!inner(
                    month_number,
                    month_name,
                    budget_years!inner(year_number)
                )
            `)
            .eq('student_id', studentId)
            .order('locked_at', { ascending: false });

        if (locksError) {
            console.warn('خطأ في جلب الإقفالات السابقة:', locksError);
        }

        // بناء محتوى الجدول
        let tableContent = '';

        // إضافة رصيد الافتتاح
        if (openingBalance !== 0) {
            tableContent += `
                <tr>
                    <td>رصيد الافتتاح</td>
                    <td class="amount ${openingBalance > 0 ? 'positive' : 'negative'}">${formatCurrency(openingBalance)} ريال</td>
                    <td>الرصيد الأولي للطالب</td>
                </tr>
            `;
        }

        // إضافة الإقفالات السابقة
        if (locksData && locksData.length > 0) {
            locksData.forEach(lock => {
                tableContent += `
                    <tr>
                        <td>إقفال ${lock.budget_months.month_name} ${lock.budget_months.budget_years.year_number}</td>
                        <td class="amount ${lock.locked_remaining_amount > 0 ? 'positive' : lock.locked_remaining_amount < 0 ? 'negative' : 'zero'}">${formatCurrency(lock.locked_remaining_amount)} ريال</td>
                        <td>تم في ${new Date(lock.locked_at).toLocaleDateString('ar-SA')}</td>
                    </tr>
                `;
            });
        }

        // إضافة المدفوعات
        if (payments && payments.length > 0) {
            payments.forEach(payment => {
                tableContent += `
                    <tr>
                        <td>دفعة</td>
                        <td class="amount negative">- ${formatCurrency(payment.amount)} ريال</td>
                        <td>${new Date(payment.payment_date).toLocaleDateString('ar-SA')}</td>
                    </tr>
                `;
            });
        }

        // إضافة الرصيد الحالي
        tableContent += `
            <tr class="total-row">
                <td><strong>الرصيد الحالي</strong></td>
                <td class="amount ${currentBalance > 0 ? 'positive' : currentBalance < 0 ? 'negative' : 'zero'}"><strong>${formatCurrency(currentBalance)} ريال</strong></td>
                <td>${currentBalance > 0 ? 'رصيد متبقي' : currentBalance < 0 ? 'مديونية' : 'متوازن'}</td>
            </tr>
        `;

        // إذا لم توجد بيانات
        if (!tableContent.trim()) {
            tableContent = '<tr><td colspan="3">لا توجد بيانات متاحة</td></tr>';
        }

        tbody.innerHTML = tableContent;

    } catch (error) {
        console.error('خطأ في جلب تفاصيل الرصيد:', error);
        if (messageEl) {
            messageEl.style.display = 'block';
            messageEl.textContent = `خطأ في تحميل تفاصيل الرصيد: ${error.message}`;
            messageEl.className = 'message error';
        }
        tbody.innerHTML = '<tr><td colspan="3">فشل في تحميل البيانات</td></tr>';
    }
};

// إغلاق مودال تفاصيل الرصيد
const closeBalanceInfoModal = () => {
    const modal = document.getElementById('balance-info-modal');
    if (modal) {
        modal.classList.remove('show');
    }
};

// --- وظائف مودال الطلاب المسجلين ---

// فتح مودال الطلاب المسجلين
const openStudentsModal = () => {
    const modal = document.getElementById('new-students-modal');
    if (!modal) return;

    // تحديث عنوان المودال
    const modalTitle = modal.querySelector('h3');
    if (modalTitle) {
        modalTitle.innerHTML = '<i class="fas fa-users"></i> الطلاب المسجلين';
    }

    // عرض المودال
    modal.style.display = 'flex';

    // تحميل قائمة الطلاب
    renderStudentsInModal();
};

// إغلاق مودال الطلاب المسجلين
const closeStudentsModal = () => {
    const modal = document.getElementById('new-students-modal');
    if (modal) {
        modal.style.display = 'none';
    }

    // مسح التحديدات
    selectedStudentsForEdit.clear();
    updateBulkEditButton();
};

// عرض الطلاب في المودال
const renderStudentsInModal = () => {
    const container = document.getElementById('students-container');
    if (!container) return;

    container.innerHTML = '';

    if (studentsWithDefaults.length === 0) {
        container.innerHTML = `
            <div class="no-students">
                <i class="fas fa-info-circle"></i>
                <p>لا يوجد طلاب مسجلين حالياً</p>
            </div>
        `;
        return;
    }

    studentsWithDefaults.forEach(student => {
        const studentCard = document.createElement('div');
        studentCard.className = 'student-card';
        studentCard.innerHTML = `
            <div class="student-info">
                <label class="checkbox-container">
                    <input type="checkbox" class="student-checkbox" data-student-id="${student.id}">
                    <span class="checkmark"></span>
                </label>
                <div class="student-details">
                    <h4>${student.name}</h4>
                    <div class="student-meta">
                        <span class="service-type">${student.defaultServiceType || 'غير محدد'}</span>
                        <span class="amount">${formatCurrency(student.defaultAmount || 0)} ريال</span>
                        <span class="balance ${student.currentBalance > 0 ? 'positive' : student.currentBalance < 0 ? 'negative' : 'zero'}">
                            ${formatCurrency(student.currentBalance || 0)} ريال
                        </span>
                    </div>
                </div>
            </div>
            <div class="student-actions">
                <button class="btn btn-sm btn-primary" onclick="editStudentDefaults('${student.id}', '${student.name}')">
                    <i class="fas fa-edit"></i> تعديل
                </button>
            </div>
        `;

        // إضافة معالج تغيير التحديد
        const checkbox = studentCard.querySelector('.student-checkbox');
        checkbox.addEventListener('change', (e) => {
            if (e.target.checked) {
                selectedStudentsForEdit.add(student.id);
            } else {
                selectedStudentsForEdit.delete(student.id);
            }
            updateBulkEditButton();
        });

        container.appendChild(studentCard);
    });
};

// تطبيق الفلاتر في مودال الطلاب المسجلين
const applyModalFilters = () => {
    const searchInput = document.getElementById('modal-search-input');
    const serviceFilter = document.getElementById('modal-service-filter');

    const searchTerm = searchInput ? searchInput.value.toLowerCase().trim() : '';
    const selectedService = serviceFilter ? serviceFilter.value : '';

    // تصفية الطلاب
    const filteredStudents = studentsWithDefaults.filter(student => {
        // فلتر البحث
        const matchesSearch = !searchTerm || student.name.toLowerCase().includes(searchTerm);

        // فلتر نوع الخدمة
        const matchesService = !selectedService || student.defaultServiceType === selectedService;

        return matchesSearch && matchesService;
    });

    // عرض النتائج المفلترة
    renderFilteredStudentsInModal(filteredStudents, searchTerm);
};

// عرض الطلاب المفلترين في المودال
const renderFilteredStudentsInModal = (filteredStudents, searchTerm = '') => {
    const container = document.getElementById('students-container');
    if (!container) return;

    container.innerHTML = '';

    if (filteredStudents.length === 0) {
        let message = 'لا يوجد طلاب مسجلين حالياً';
        if (searchTerm) {
            message = `لا توجد نتائج تطابق البحث "${searchTerm}"`;
        }

        container.innerHTML = `
            <div class="no-students">
                <i class="fas fa-info-circle"></i>
                <p>${message}</p>
            </div>
        `;
        return;
    }

    filteredStudents.forEach(student => {
        const studentCard = document.createElement('div');
        studentCard.className = 'student-card';
        studentCard.innerHTML = `
            <div class="student-info">
                <label class="checkbox-container">
                    <input type="checkbox" class="student-checkbox" data-student-id="${student.id}">
                    <span class="checkmark"></span>
                </label>
                <div class="student-details">
                    <h4>${student.name}</h4>
                    <div class="student-meta">
                        <span class="service-type">${student.defaultServiceType || 'غير محدد'}</span>
                        <span class="amount">${formatCurrency(student.defaultAmount || 0)} ريال</span>
                        <span class="balance ${student.currentBalance > 0 ? 'positive' : student.currentBalance < 0 ? 'negative' : 'zero'}">
                            ${formatCurrency(student.currentBalance || 0)} ريال
                        </span>
                    </div>
                </div>
            </div>
            <div class="student-actions">
                <button class="btn btn-sm btn-primary" onclick="editStudentDefaults('${student.id}', '${student.name}')">
                    <i class="fas fa-edit"></i> تعديل
                </button>
            </div>
        `;

        // إضافة معالج تغيير التحديد
        const checkbox = studentCard.querySelector('.student-checkbox');
        checkbox.addEventListener('change', (e) => {
            if (e.target.checked) {
                selectedStudentsForEdit.add(student.id);
            } else {
                selectedStudentsForEdit.delete(student.id);
            }
            updateBulkEditButton();
        });

        container.appendChild(studentCard);
    });
};

// تحديث زر التعديل المجمع
const updateBulkEditButton = () => {
    const bulkButton = document.getElementById('bulk-edit-students-btn');
    const countDisplay = document.getElementById('bulk-edit-count');
    const selectedCountDisplay = document.getElementById('selected-students-count-display');

    if (!bulkButton || !countDisplay || !selectedCountDisplay) return;

    const count = selectedStudentsForEdit.size;

    if (count > 0) {
        bulkButton.disabled = false;
        countDisplay.textContent = count;
        selectedCountDisplay.textContent = `تم تحديد ${count} ${count === 1 ? 'طالب' : 'طلاب'}`;
    } else {
        bulkButton.disabled = true;
        countDisplay.textContent = '0';
        selectedCountDisplay.textContent = 'لم يتم تحديد أي طالب';
    }
};

// --- وظائف مودال الطلاب المخفيين ---

// فتح مودال الطلاب المخفيين
const openHiddenStudentsModal = () => {
    const modal = document.getElementById('hidden-students-modal');
    if (!modal) return;

    // عرض المودال
    modal.style.display = 'flex';

    // تحميل قائمة الطلاب المخفيين
    renderHiddenStudentsInModal();
};

// إغلاق مودال الطلاب المخفيين
const closeHiddenStudentsModal = () => {
    const modal = document.getElementById('hidden-students-modal');
    if (modal) {
        modal.style.display = 'none';
    }

    // مسح التحديدات
    selectedHiddenStudents.clear();
};

// عرض الطلاب المخفيين في المودال
const renderHiddenStudentsInModal = () => {
    const container = document.getElementById('hidden-students-container');
    if (!container) return;

    container.innerHTML = '';

    if (hiddenStudents.length === 0) {
        container.innerHTML = `
            <div class="no-students">
                <i class="fas fa-info-circle"></i>
                <p>لا يوجد طلاب مخفيين حالياً</p>
            </div>
        `;
        return;
    }

    hiddenStudents.forEach(student => {
        const studentCard = document.createElement('div');
        studentCard.className = 'hidden-student-card';
        studentCard.innerHTML = `
            <div class="student-info">
                <div class="student-details">
                    <h4>${student.student_name}</h4>
                    <div class="student-meta">
                        <span class="balance ${student.remaining_balance > 0 ? 'positive' : student.remaining_balance < 0 ? 'negative' : 'zero'}">
                            الرصيد: ${formatCurrency(student.remaining_balance || 0)} ريال
                        </span>
                        <span class="archived-date">
                            تم الإخفاء: ${new Date(student.archived_at).toLocaleDateString('ar-SA')}
                        </span>
                    </div>
                    <p class="archived-reason">${student.archived_reason}</p>
                </div>
            </div>
            <div class="student-actions">
                <button class="btn btn-sm btn-success" onclick="restoreHiddenStudent('${student.student_id}', '${student.student_name}')">
                    <i class="fas fa-undo"></i> استعادة
                </button>
                <button class="btn btn-sm btn-danger" onclick="permanentDeleteHiddenStudent('${student.student_id}', '${student.student_name}')">
                    <i class="fas fa-trash"></i> حذف نهائي
                </button>
            </div>
        `;

        container.appendChild(studentCard);
    });
};

// استعادة طالب مخفي
const restoreHiddenStudent = async (studentId, studentName) => {
    if (!_supabase) return;

    const confirmRestore = confirm(`هل تريد استعادة الطالب "${studentName}" إلى القائمة الرئيسية؟`);
    if (!confirmRestore) return;

    try {
        // جلب البيانات المحفوظة للطالب
        const { data: archivedData, error: fetchError } = await _supabase
            .from('archived_students')
            .select('*')
            .eq('student_id', studentId)
            .single();

        if (fetchError) throw fetchError;

        // إعادة إنشاء السجل في student_subscription_defaults بالبيانات الأصلية
        const { error: insertError } = await _supabase
            .from('student_subscription_defaults')
            .insert({
                student_id: studentId,
                default_service_type: archivedData.original_service_type || 'ذهاب وعودة',
                default_amount: archivedData.original_amount || 250.00,
                opening_balance: archivedData.original_opening_balance || 0.00
                // current_balance سيتم تعيينه تلقائياً بواسطة التريجر
            });

        if (insertError) throw insertError;

        // حذف من جدول الطلاب المخفيين
        const { error: deleteError } = await _supabase
            .from('archived_students')
            .delete()
            .eq('student_id', studentId);

        if (deleteError) throw deleteError;

        // تحديث البيانات المحلية
        hiddenStudents = hiddenStudents.filter(student => student.student_id !== studentId);

        // تحديث العدادات والواجهة
        updateHiddenStudentsCounter();
        await Promise.all([
            fetchStudentDefaults(),
            fetchStudentsWithDefaults()
        ]);
        renderHiddenStudentsInModal();
        applyFiltersAndRender(); // تحديث الجدول الرئيسي

        alert(`تم استعادة الطالب "${studentName}" بنجاح مع جميع بياناته الأصلية`);

    } catch (error) {
        console.error('خطأ في استعادة الطالب:', error);
        alert(`حدث خطأ في استعادة الطالب: ${error.message}`);
    }
};

// حذف نهائي لطالب مخفي
const permanentDeleteHiddenStudent = async (studentId, studentName) => {
    if (!_supabase) return;

    const confirmDelete = confirm(`تحذير: هل تريد حذف الطالب "${studentName}" نهائياً؟\n\nهذا الإجراء لا يمكن التراجع عنه وسيتم فقدان جميع البيانات المرتبطة بالطالب.`);
    if (!confirmDelete) return;

    try {
        // حذف من جدول الطلاب المخفيين
        const { error: deleteError } = await _supabase
            .from('archived_students')
            .delete()
            .eq('student_id', studentId);

        if (deleteError) throw deleteError;

        // تحديث البيانات المحلية
        hiddenStudents = hiddenStudents.filter(student => student.student_id !== studentId);

        // تحديث العدادات والواجهة
        updateHiddenStudentsCounter();
        renderHiddenStudentsInModal();

        alert(`تم حذف الطالب "${studentName}" نهائياً`);

    } catch (error) {
        console.error('خطأ في الحذف النهائي:', error);
        alert(`حدث خطأ في الحذف النهائي: ${error.message}`);
    }
};

// --- وظائف نافذة الطلاب ---

// فتح نافذة الطلاب الجدد
const openNewStudentsModal = () => {
    const newStudentsModal = document.getElementById('new-students-modal');
    if (!newStudentsModal) return;

    // تحديث قائمة الطلاب
    renderNewStudentsList();

    // إظهار النافذة
    newStudentsModal.style.display = 'flex';
};

// دالة لفتح مودال جميع الطلاب (للوصول من زر "جلب إعدادات الطلاب")
const openAllStudentsModal = () => {
    openNewStudentsModal(); // استخدام نفس المودال
};

// دالة تبديل العرض بين الطلاب الجدد والمسجلين
const toggleStudentsView = () => {
    isShowingRegisteredStudents = !isShowingRegisteredStudents;

    const tableTitle = document.getElementById('table-title');
    const toggleBtnText = document.getElementById('toggle-btn-text');

    if (isShowingRegisteredStudents) {
        // التبديل إلى عرض الطلاب المسجلين
        if (tableTitle) tableTitle.textContent = 'الطلاب المسجلين';
        if (toggleBtnText) toggleBtnText.textContent = 'عرض الطلاب الجدد';
    } else {
        // التبديل إلى عرض الطلاب الجدد
        if (tableTitle) tableTitle.textContent = 'الطلاب الجدد';
        if (toggleBtnText) toggleBtnText.textContent = 'عرض الطلاب المسجلين';
    }

    // إعادة تعيين الصفحة الحالية
    currentPage = 1;

    // إعادة تطبيق الفلاتر والعرض
    applyFiltersAndRender();
};

// دالة إضافة إعدادات افتراضية لطالب جديد
const handleAddDefaults = async (studentId, studentName) => {
    if (!_supabase) return;

    try {
        const { error } = await _supabase
            .from('student_subscription_defaults')
            .insert({
                student_id: studentId,
                default_service_type: 'ذهاب وعودة',
                default_amount: 250.00,
                opening_balance: 0.00
            });

        if (error) throw error;

        // تحديث البيانات المحلية
        await fetchStudentDefaults();

        // إعادة تطبيق الفلاتر والعرض
        applyFiltersAndRender();

        showMessage(listMessage, `تم إضافة إعدادات افتراضية للطالب ${studentName} بنجاح.`, 'success', 3000);

    } catch (error) {
        console.error('خطأ في إضافة الإعدادات الافتراضية:', error);
        showMessage(listMessage, `خطأ في إضافة الإعدادات الافتراضية: ${error.message}`, 'error', 5000);
    }
};

// دالة لإظهار نموذج إدخال الطالب الجديد
const showNewStudentForm = (rowElement, studentId, studentName) => {
    // إخفاء النصوص الافتراضية
    const placeholderTexts = rowElement.querySelectorAll('.placeholder-text');
    placeholderTexts.forEach(text => text.style.display = 'none');

    // إظهار حقول الإدخال
    const selectElement = rowElement.querySelector('.default-service-select-new');
    const amountInput = rowElement.querySelector('.default-amount-input-new');
    const openingBalanceInput = rowElement.querySelector('.opening-balance-input-new');

    if (selectElement) {
        selectElement.style.display = 'block';
        selectElement.value = 'ذهاب وعودة'; // قيمة افتراضية
    }
    if (amountInput) {
        amountInput.style.display = 'block';
        amountInput.value = '250.00'; // قيمة افتراضية
    }
    if (openingBalanceInput) {
        openingBalanceInput.style.display = 'block';
        openingBalanceInput.value = '0.00'; // قيمة افتراضية
    }

    // إخفاء زر الإضافة وإظهار أزرار الحفظ والإلغاء
    const addButton = rowElement.querySelector('.btn-add-defaults');
    const saveButton = rowElement.querySelector('.btn-save-new');
    const cancelButton = rowElement.querySelector('.btn-cancel-new');

    if (addButton) addButton.style.display = 'none';
    if (saveButton) saveButton.style.display = 'inline-block';
    if (cancelButton) cancelButton.style.display = 'inline-block';
};

// دالة لإخفاء نموذج إدخال الطالب الجديد
const hideNewStudentForm = (rowElement) => {
    // إظهار النصوص الافتراضية
    const placeholderTexts = rowElement.querySelectorAll('.placeholder-text');
    placeholderTexts.forEach(text => text.style.display = 'block');

    // إخفاء حقول الإدخال
    const selectElement = rowElement.querySelector('.default-service-select-new');
    const amountInput = rowElement.querySelector('.default-amount-input-new');
    const openingBalanceInput = rowElement.querySelector('.opening-balance-input-new');

    if (selectElement) {
        selectElement.style.display = 'none';
        selectElement.value = '';
    }
    if (amountInput) {
        amountInput.style.display = 'none';
        amountInput.value = '';
    }
    if (openingBalanceInput) {
        openingBalanceInput.style.display = 'none';
        openingBalanceInput.value = '';
    }

    // إظهار زر الإضافة وإخفاء أزرار الحفظ والإلغاء
    const addButton = rowElement.querySelector('.btn-add-defaults');
    const saveButton = rowElement.querySelector('.btn-save-new');
    const cancelButton = rowElement.querySelector('.btn-cancel-new');

    if (addButton) addButton.style.display = 'inline-block';
    if (saveButton) saveButton.style.display = 'none';
    if (cancelButton) cancelButton.style.display = 'none';
};

// دالة لحفظ إعدادات الطالب الجديد
const handleSaveNewDefaults = async (studentId, studentName, rowElement) => {
    if (!_supabase) return;

    const selectElement = rowElement.querySelector('.default-service-select-new');
    const amountInput = rowElement.querySelector('.default-amount-input-new');
    const openingBalanceInput = rowElement.querySelector('.opening-balance-input-new');
    const saveButton = rowElement.querySelector('.btn-save-new');

    if (!selectElement || !amountInput || !openingBalanceInput) return;

    const serviceType = selectElement.value;
    const amountRaw = amountInput.value.trim();
    const openingBalanceRaw = openingBalanceInput.value.trim();

    // التحقق من صحة البيانات
    if (!serviceType) {
        showMessage(listMessage, 'الرجاء اختيار نوع الخدمة.', 'error', 3000);
        return;
    }

    let amount = 0;
    let openingBalance = 0;

    if (amountRaw !== '') {
        amount = parseFloat(amountRaw);
        if (isNaN(amount)) {
            showMessage(listMessage, 'المبلغ الشهري غير صالح.', 'error', 3000);
            return;
        }
    }

    // التحقق من أن المبلغ ليس صفر
    if (amount === 0) {
        showMessage(listMessage, 'المبلغ الشهري يجب أن يكون أكبر أو أصغر من الصفر.', 'error', 3000);
        return;
    }

    if (openingBalanceRaw !== '') {
        openingBalance = parseFloat(openingBalanceRaw);
        if (isNaN(openingBalance)) {
            showMessage(listMessage, 'الرصيد الافتتاحي غير صالح.', 'error', 3000);
            return;
        }
    }

    // تعطيل زر الحفظ أثناء العملية
    if (saveButton) {
        saveButton.disabled = true;
        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    }

    try {
        const { error } = await _supabase
            .from('student_subscription_defaults')
            .insert({
                student_id: studentId,
                default_service_type: serviceType,
                default_amount: amount,
                opening_balance: openingBalance
            });

        if (error) throw error;

        // تحديث البيانات المحلية
        await fetchStudentDefaults();

        // إعادة تطبيق الفلاتر والعرض
        applyFiltersAndRender();

        showMessage(listMessage, `تم إضافة إعدادات افتراضية للطالب ${studentName} بنجاح.`, 'success', 3000);

    } catch (error) {
        console.error('خطأ في إضافة الإعدادات الافتراضية:', error);
        showMessage(listMessage, `خطأ في إضافة الإعدادات الافتراضية: ${error.message}`, 'error', 5000);

        // إعادة تفعيل زر الحفظ في حالة الخطأ
        if (saveButton) {
            saveButton.disabled = false;
            saveButton.innerHTML = '<i class="fas fa-save"></i>';
        }
    }
};

// إغلاق نافذة الطلاب الجدد
const closeNewStudentsModal = () => {
    const newStudentsModal = document.getElementById('new-students-modal');
    if (!newStudentsModal) return;

    newStudentsModal.style.display = 'none';
    selectedNewStudents.clear();
    updateBulkActionButton();
};

// عرض قائمة الطلاب الجدد
const renderNewStudentsList = () => {
    const container = document.getElementById('new-students-container');

    if (!container) return;

    container.innerHTML = '';

    if (newStudentsWithoutDefaults.length === 0) {
        container.innerHTML = `
            <div class="no-new-students" style="text-align: center; padding: 2rem; color: var(--text-muted);">
                <i class="fas fa-check-circle" style="font-size: 3rem; color: var(--success-color); margin-bottom: 1rem;"></i>
                <h4>ممتاز! جميع الطلاب لديهم إعدادات افتراضية</h4>
                <p>لا يوجد طلاب جدد بحاجة لإضافة إعدادات افتراضية.</p>
            </div>
        `;
        return;
    }

    newStudentsWithoutDefaults.forEach(student => {
        const studentItem = document.createElement('div');
        studentItem.className = 'new-student-item';
        studentItem.dataset.studentId = student.id;

        const initials = student.name.split(' ').map(n => n[0]).join('').substring(0, 2);

        studentItem.innerHTML = `
            <div class="student-info-new">
                <div class="student-avatar">${initials}</div>
                <div class="student-details-new">
                    <div class="student-name-new">${student.name}</div>
                    <div class="student-status-new">بحاجة لإعدادات افتراضية</div>
                </div>
            </div>
            <div class="student-actions-new">
                <input type="checkbox" class="student-checkbox" data-student-id="${student.id}">
                <button class="btn-add-individual" data-student-id="${student.id}">
                    <i class="fas fa-plus"></i> إضافة
                </button>
            </div>
        `;

        container.appendChild(studentItem);
    });

    // إضافة مستمعات الأحداث
    setupNewStudentsEventListeners();
};

// إعداد مستمعات الأحداث للطلاب الجدد
const setupNewStudentsEventListeners = () => {
    // تحديد الكل
    const selectAllCheckbox = document.getElementById('select-all-new-students');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', (e) => {
            const checkboxes = document.querySelectorAll('.student-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
                const studentId = checkbox.dataset.studentId;
                if (e.target.checked) {
                    selectedNewStudents.add(studentId);
                } else {
                    selectedNewStudents.delete(studentId);
                }
            });
            updateBulkActionButton();
            updateSelectedCount();
        });
    }

    // تحديد فردي
    const checkboxes = document.querySelectorAll('.student-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
            const studentId = e.target.dataset.studentId;
            if (e.target.checked) {
                selectedNewStudents.add(studentId);
            } else {
                selectedNewStudents.delete(studentId);
            }
            updateBulkActionButton();
            updateSelectedCount();
        });
    });

    // إضافة فردية
    const addButtons = document.querySelectorAll('.btn-add-individual');
    addButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            const studentId = e.target.closest('.btn-add-individual').dataset.studentId;
            const student = newStudentsWithoutDefaults.find(s => s.id === studentId);
            if (student) {
                addDefaultsForStudent(student);
            }
        });
    });
};

// تحديث زر الإضافة المجمعة
const updateBulkActionButton = () => {
    const bulkButton = document.getElementById('bulk-add-defaults-btn');
    const bulkCount = document.getElementById('bulk-count');

    if (!bulkButton || !bulkCount) return;

    const count = selectedNewStudents.size;
    bulkButton.disabled = count === 0;
    bulkCount.textContent = count;
};

// تحديث عداد المحددين
const updateSelectedCount = () => {
    const selectedCountDisplay = document.getElementById('selected-count-display');
    if (!selectedCountDisplay) return;

    const count = selectedNewStudents.size;
    if (count === 0) {
        selectedCountDisplay.textContent = 'لم يتم تحديد أي طالب';
    } else if (count === 1) {
        selectedCountDisplay.textContent = 'تم تحديد طالب واحد';
    } else {
        selectedCountDisplay.textContent = `تم تحديد ${count} طلاب`;
    }
};

// إضافة إعدادات افتراضية لطالب واحد
const addDefaultsForStudent = async (student) => {
    if (!_supabase) return;

    try {
        const { error } = await _supabase
            .from('student_subscription_defaults')
            .insert({
                student_id: student.id,
                default_service_type: 'ذهاب وعودة',
                default_amount: 250.00,
                opening_balance: 0
            });

        if (error) throw error;

        // إزالة الطالب من قائمة الجدد
        newStudentsWithoutDefaults = newStudentsWithoutDefaults.filter(s => s.id !== student.id);

        // تحديث العداد والواجهة
        updateNewStudentsCounter();
        renderNewStudentsList();

        // تحديث الجدول الرئيسي
        await fetchStudentDefaults();
        applyFiltersAndRender();

        showNewStudentsMessage(`تم إضافة إعدادات افتراضية للطالب ${student.name} بنجاح.`, 'success');

    } catch (error) {
        console.error('خطأ في إضافة الإعدادات:', error);
        showNewStudentsMessage(`خطأ في إضافة إعدادات للطالب ${student.name}: ${error.message}`, 'error');
    }
};

// إضافة إعدادات افتراضية للطلاب المحددين
const addDefaultsForSelectedStudents = async () => {
    if (!_supabase || selectedNewStudents.size === 0) return;

    const bulkButton = document.getElementById('bulk-add-defaults-btn');
    if (bulkButton) {
        bulkButton.disabled = true;
        bulkButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإضافة...';
    }

    try {
        const studentsToAdd = Array.from(selectedNewStudents).map(studentId => ({
            student_id: studentId,
            default_service_type: 'ذهاب وعودة',
            default_amount: 250.00,
            opening_balance: 0
        }));

        const { error } = await _supabase
            .from('student_subscription_defaults')
            .insert(studentsToAdd);

        if (error) throw error;

        // إزالة الطلاب المضافين من قائمة الجدد
        newStudentsWithoutDefaults = newStudentsWithoutDefaults.filter(
            student => !selectedNewStudents.has(student.id)
        );

        selectedNewStudents.clear();

        // تحديث العداد والواجهة
        updateNewStudentsCounter();
        renderNewStudentsList();
        updateBulkActionButton();

        // تحديث الجدول الرئيسي
        await fetchStudentDefaults();
        applyFiltersAndRender();

        showNewStudentsMessage(`تم إضافة إعدادات افتراضية لـ ${studentsToAdd.length} طالب بنجاح.`, 'success');

    } catch (error) {
        console.error('خطأ في الإضافة المجمعة:', error);
        showNewStudentsMessage(`خطأ في الإضافة المجمعة: ${error.message}`, 'error');
    } finally {
        if (bulkButton) {
            bulkButton.disabled = false;
            bulkButton.innerHTML = '<i class="fas fa-plus-circle"></i> إضافة المحددين (<span id="bulk-count">0</span>)';
        }
    }
};

// عرض رسائل في نافذة الطلاب الجدد
const showNewStudentsMessage = (message, type = 'info', duration = 4000) => {
    const messageArea = document.getElementById('new-students-message-area');
    if (!messageArea) return;

    messageArea.textContent = message;
    messageArea.className = `message-area ${type} show`;
    messageArea.style.display = 'block';

    if (duration > 0) {
        setTimeout(() => {
            messageArea.classList.remove('show');
            setTimeout(() => { messageArea.style.display = 'none'; }, 300);
        }, duration);
    }
};



// --- Event Listeners ---
const setupEventListeners = () => {
    // Search
    if (searchInput) {
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                currentPage = 1;
                applyFiltersAndRender();
            }
        });
        searchInput.addEventListener('input', () => {
            currentPage = 1;
            applyFiltersAndRender();
        });
    }

    // Filters
    const serviceFilter = document.getElementById('service-filter');
    const balanceFilter = document.getElementById('balance-filter');

    if (serviceFilter) {
        serviceFilter.addEventListener('change', () => {
            currentPage = 1;
            applyFiltersAndRender();
        });
    }

    if (balanceFilter) {
        balanceFilter.addEventListener('change', () => {
            currentPage = 1;
            applyFiltersAndRender();
        });
    }

    // Refresh button
    const refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', async () => {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';

            try {
                await Promise.all([
                    fetchAllStudents(),
                    fetchStudentDefaults(),
                    fetchStudentGroups(),
                    fetchStudentGroupAssignments(),
                    fetchStudentsWithDefaults(),
                    fetchHiddenStudents(),
                    fetchNewStudentsWithoutDefaults()
                ]);
                applyFiltersAndRender();
                showMessage(listMessage, 'تم تحديث البيانات بنجاح.', 'success', 3000);
            } catch (error) {
                console.error('Error refreshing data:', error);
                showMessage(listMessage, 'حدث خطأ أثناء تحديث البيانات.', 'error', 5000);
            } finally {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> تحديث البيانات';
            }
        });
    }

    // Back to Dashboard
    if (backToDashboardBtn) {
        backToDashboardBtn.addEventListener('click', () => {
            // Adjust the path based on your actual dashboard location
            window.location.href = '../financial_dashboard.html';
        });
    }

    // Add Group Modal
    if (addGroupBtn) {
        addGroupBtn.addEventListener('click', openAddGroupModal);
    }
    if (closeGroupModalBtn) {
        closeGroupModalBtn.addEventListener('click', closeAddGroupModal);
    }
    if (cancelGroupBtn) {
        cancelGroupBtn.addEventListener('click', closeAddGroupModal);
    }
    if (addGroupForm) {
        // حفظ المعالج الأصلي لاستعادته
        addGroupForm._originalSubmitHandler = handleAddGroup;
        addGroupForm.addEventListener('submit', (e) => {
            // التأكد من استدعاء المعالج الصحيح (إضافة أو تعديل)
            if (addGroupForm.onsubmit === addGroupForm._originalSubmitHandler) {
                handleAddGroup(e);
            }
            // إذا كان onsubmit قد تم تغييره للتعديل، سيتم استدعاء معالج التعديل بدلاً من ذلك
        });
    }
    if (addGroupModal) {
        addGroupModal.addEventListener('click', (event) => {
            if (event.target === addGroupModal) {
                closeAddGroupModal();
            }
        });
    }

    // Assign Group Modal
    if (closeAssignModalBtn) {
        closeAssignModalBtn.addEventListener('click', closeAssignGroupModal);
    }
    if (cancelAssignmentBtn) {
        cancelAssignmentBtn.addEventListener('click', closeAssignGroupModal);
    }
    if (assignGroupForm) {
        assignGroupForm.addEventListener('submit', handleAssignGroupSubmit);
    }
    if (assignGroupModal) {
        assignGroupModal.addEventListener('click', (event) => {
            if (event.target === assignGroupModal) {
                closeAssignGroupModal();
            }
        });
    }

    // Students Modal (removed new-students-btn as it's deleted)
    const closeNewStudentsModalBtn = document.getElementById('close-new-students-modal-btn');
    const newStudentsModal = document.getElementById('new-students-modal');

    if (closeNewStudentsModalBtn) {
        closeNewStudentsModalBtn.addEventListener('click', closeStudentsModal);
    }
    if (newStudentsModal) {
        newStudentsModal.addEventListener('click', (event) => {
            if (event.target === newStudentsModal) {
                closeStudentsModal();
            }
        });
    }

    // Modal Search and Filter
    const modalSearchInput = document.getElementById('modal-search-input');
    const modalServiceFilter = document.getElementById('modal-service-filter');

    if (modalSearchInput) {
        modalSearchInput.addEventListener('input', applyModalFilters);
        modalSearchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                applyModalFilters();
            }
        });
    }

    if (modalServiceFilter) {
        modalServiceFilter.addEventListener('change', applyModalFilters);
    }

    // Hidden Students Modal
    const hiddenStudentsBtn = document.getElementById('hidden-students-btn');
    const closeHiddenStudentsModalBtn = document.getElementById('close-hidden-students-modal-btn');
    const hiddenStudentsModal = document.getElementById('hidden-students-modal');

    if (hiddenStudentsBtn) {
        hiddenStudentsBtn.addEventListener('click', openHiddenStudentsModal);
    }
    if (closeHiddenStudentsModalBtn) {
        closeHiddenStudentsModalBtn.addEventListener('click', closeHiddenStudentsModal);
    }
    if (hiddenStudentsModal) {
        hiddenStudentsModal.addEventListener('click', (event) => {
            if (event.target === hiddenStudentsModal) {
                closeHiddenStudentsModal();
            }
        });
    }

    // Toggle View Button
    const toggleViewBtn = document.getElementById('toggle-view-btn');
    if (toggleViewBtn) {
        toggleViewBtn.addEventListener('click', toggleStudentsView);
    }

    // Balance Info Modal
    const balanceInfoModal = document.getElementById('balance-info-modal');
    const closeBalanceInfoBtn = document.getElementById('close-balance-info-modal');

    if (closeBalanceInfoBtn) {
        closeBalanceInfoBtn.addEventListener('click', closeBalanceInfoModal);
    }

    if (balanceInfoModal) {
        balanceInfoModal.addEventListener('click', (event) => {
            if (event.target === balanceInfoModal) {
                closeBalanceInfoModal();
            }
        });
    }

    // Escape key listener for balance info modal
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            if (balanceInfoModal?.classList.contains('show')) {
                closeBalanceInfoModal();
            }
        }
    });

    // Sidebar functionality is handled by shared_components/sidebar.js
};


// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    if (!_supabase) {
        showMessage(listMessage, 'خطأ فادح: لم يتم تهيئة الاتصال بقاعدة البيانات.', 'error', 0);
        return;
    }

    // Sidebar functionality is handled by shared_components/sidebar.js

    setupEventListeners(); // Setup listeners early

    // Fetch necessary data
    showMessage(listMessage, 'جاري تحميل البيانات الأولية...', 'info', 0);
    try {
        await Promise.all([
            fetchAllStudents(),
            fetchStudentDefaults(),
            fetchStudentGroups(),
            fetchStudentGroupAssignments(),
            fetchStudentsWithDefaults(),
            fetchHiddenStudents(),
            fetchNewStudentsWithoutDefaults()
        ]);
        applyFiltersAndRender(); // Initial render after data is fetched
        showMessage(listMessage, 'تم تحميل البيانات بنجاح.', 'success', 3000);
    } catch (error) {
        console.error("Error during initial data fetch:", error);
        // Messages are shown within fetch functions
    }
});
