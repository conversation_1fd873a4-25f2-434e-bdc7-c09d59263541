:root {
    --primary-color: #3c4b64;
    --secondary-color: #4d5d7c;
    --accent-color: #2eb85c;
    --danger-color: #e55353;
    --warning-color: #f9b115;
    --info-color: #39f;
    --light-color: #ebedef;
    --dark-color: #636f83;
    --body-bg: #f5f5f5;
    --card-bg: #fff;
    --header-bg: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

body {
    background-color: var(--body-bg);
    color: #333;
    line-height: 1.6;
}

.dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.dashboard-header {
    background: var(--header-bg);
    color: white;
    padding: 1.5rem 2rem;
    position: relative;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow);
}

.header-content h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.header-content p {
    opacity: 0.8;
    font-size: 1rem;
}

.navbar-user {
    display: flex;
    align-items: center;
}

/* Main Content */
.dashboard-main {
    flex: 1;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Date Display */
.date-display {
    text-align: center;
    margin-bottom: 1.5rem;
    background-color: rgba(60, 75, 100, 0.1);
    padding: 0.8rem;
    border-radius: var(--border-radius);
}

.date-display h2 {
    color: var(--primary-color);
    font-size: 1.6rem;
    font-weight: 600;
}

/* Summary Section */
.summary-section {
    margin-bottom: 2rem;
}

.summary-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: auto auto auto;
    gap: 1.5rem;
    margin-top: 1rem;
}

/* Position the cards as requested: 3 right, 3 left, 1 center */
.summary-grid .summary-card:nth-child(1),
.summary-grid .summary-card:nth-child(2),
.summary-grid .summary-card:nth-child(3) {
    grid-column: 1; /* Right side (in RTL) */
}

.summary-grid .summary-card:nth-child(4),
.summary-grid .summary-card:nth-child(5),
.summary-grid .summary-card:nth-child(6) {
    grid-column: 3; /* Left side (in RTL) */
}

.summary-grid .summary-card:nth-child(7) {
    grid-column: 2; /* Center */
    grid-row: 1 / span 3; /* Make it span all rows */
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: linear-gradient(135deg, #4d5d7c, #3c4b64);
    color: white;
    transform: scale(1.05);
}

.summary-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    transition: var(--transition);
    border-right: 4px solid var(--primary-color);
    display: flex;
    flex-direction: column;
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.summary-card h3 {
    font-size: 1.1rem;
    color: var(--dark-color);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.summary-card p {
    font-size: 1.6rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* Different colors for different cards */
.summary-card:nth-child(1) { border-right-color: var(--primary-color); }
.summary-card:nth-child(2) { border-right-color: var(--accent-color); }
.summary-card:nth-child(3) { border-right-color: var(--info-color); }
.summary-card:nth-child(4) { border-right-color: var(--warning-color); }
.summary-card:nth-child(5) { border-right-color: var(--danger-color); }
.summary-card:nth-child(6) { border-right-color: #9c27b0; }
.summary-card:nth-child(7) { border-right-color: #ff5722; }

.summary-card:nth-child(7) h3,
.summary-card:nth-child(7) p {
    color: white;
    text-align: center;
}

.summary-card:nth-child(7) p {
    font-size: 2.2rem;
    margin-top: auto;
}

.summary-card:nth-child(7) h3 {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
}

/* Driver List Section */
.driver-list-section {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.driver-list-section h2 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--light-color);
}

/* Header for Driver List Section */
.driver-list-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--light-color);
}

.driver-list-section .section-header h2 {
    margin-bottom: 0; /* Remove bottom margin from h2 */
    border-bottom: none; /* Remove border from h2 */
}

.report-btn {
    background-color: var(--info-color); /* Blue color for report button */
}

.report-btn:hover {
    background-color: #007bff; /* Darker blue on hover */
}

.driver-files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 1.5rem;
    padding: 1rem 0;
}

.driver-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 1.5rem;
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    border: 1px solid var(--light-color);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.driver-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);
}

.driver-card.closed {
    background-color: #e9ecef; /* Light grey background */
    opacity: 0.7; /* Make it look faded/disabled */
    cursor: not-allowed; /* Indicate it's not clickable */
    border-color: var(--text-muted); /* Grey border */
}

.driver-card.closed::before {
    background: var(--text-muted); /* Grey top border */
}

.driver-card.closed:hover {
    transform: none; /* Disable hover effect */
    box-shadow: var(--shadow); /* Keep original shadow */
}

.driver-card.closed .driver-icon {
    background-color: #ced4da; /* Darker grey icon background */
    color: #6c757d; /* Muted icon color */
}

.driver-card.closed .driver-name {
    color: var(--text-muted); /* Muted text color */
}

.lock-icon {
    position: absolute;
    top: 10px;
    left: 10px; /* Position top-left (adjust as needed for RTL) */
    font-size: 1.2rem;
    color: var(--danger-color); /* Red lock icon */
    background-color: rgba(255, 255, 255, 0.8); /* Semi-transparent white background */
    padding: 3px 6px;
    border-radius: 4px;
    z-index: 1; /* Ensure it's above other content */
}

.driver-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.driver-icon {
    width: 64px;
    height: 64px;
    background-color: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    color: var(--primary-color);
    font-size: 1.8rem;
    transition: var(--transition);
}

.driver-card:hover .driver-icon {
    background-color: var(--primary-color);
    color: white;
}

.driver-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
    text-align: center;
}

.driver-salary {
    font-size: 1rem;
    color: var(--dark-color);
    opacity: 0.8;
}

/* Styles for the Reopen Driver Month Button */
.reopen-driver-btn {
    background-color: var(--warning-color); /* Use warning color for attention */
    color: #fff; /* White text for contrast */
    border: none;
    padding: 0.5rem 0.8rem; /* Smaller padding than control-btn */
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem; /* Slightly smaller font */
    margin-top: 0.75rem; /* Space above the button */
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    width: 100%; /* Make button take full width of card content area */
    justify-content: center; /* Center button text/icon */
}

.reopen-driver-btn:hover {
    background-color: #e0a800; /* Darker shade of warning-color on hover */
    transform: translateY(-1px);
}

.reopen-driver-btn i {
    margin-right: 5px; /* Space between icon and text */
}
/* End Styles for Reopen Driver Month Button */

.loading-message {
    text-align: center;
    padding: 2rem;
    color: var(--dark-color);
    font-style: italic;
}

/* Message styles */
.message {
    padding: 1rem;
    margin: 1rem 0;
    border-radius: var(--border-radius);
    text-align: center;
}

.message.success {
    background-color: rgba(46, 184, 92, 0.1);
    color: var(--accent-color);
    border: 1px solid var(--accent-color);
}

.message.error {
    background-color: rgba(229, 83, 83, 0.1);
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

/* Footer */
.dashboard-footer {
    background-color: var(--light-color);
    padding: 1.5rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.dashboard-footer p {
    color: var(--dark-color);
    font-size: 0.9rem;
}

/* Buttons */
.control-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.7rem 1.2rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.control-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Modal Styles (Generic - can be moved to shared_styles.css later) */
.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1000; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgba(0,0,0,0.6); /* Black w/ opacity */
}

.modal-content {
    background-color: #fefefe;
    margin: 10% auto; /* 10% from the top and centered */
    padding: 25px 30px;
    border: 1px solid #888;
    width: 80%; /* Could be more or less, depending on screen size */
    max-width: 600px; /* Maximum width */
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    position: relative;
    animation: fadeInModal 0.4s;
}

@keyframes fadeInModal {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.close-btn {
    color: #aaa;
    position: absolute;
    left: 15px; /* Position top-left for RTL */
    top: 10px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-btn:hover,
.close-btn:focus {
    color: black;
    text-decoration: none;
}

.modal h2 {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--primary-color);
    text-align: center;
    font-size: 1.4rem;
}
.modal h2 i {
    margin-left: 8px;
}

.modal p {
    margin-bottom: 20px;
    color: var(--text-muted);
    text-align: center;
    font-size: 0.95rem;
}

.modal-controls {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--light-color);
    display: flex; /* Use flexbox for alignment */
    gap: 20px; /* Add space between labels */
    align-items: center;
}

.modal-controls label {
    font-weight: 500;
    cursor: pointer;
    display: inline-flex; /* Align checkbox and text */
    align-items: center;
    gap: 5px; /* Space between checkbox and text */
}

.modal-list {
    max-height: 300px; /* Limit height and make scrollable */
    overflow-y: auto;
    border: 1px solid var(--light-color);
    border-radius: var(--border-radius);
    padding: 10px;
    margin-bottom: 20px;
    background-color: #f8f9fa;
}

.modal-list .list-item {
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    gap: 10px;
}
.modal-list .list-item:last-child {
    border-bottom: none;
}

.modal-list .list-item input[type="checkbox"] {
    margin-left: 5px;
    cursor: pointer;
}

.modal-list .list-item label {
    flex-grow: 1;
    cursor: pointer;
    font-size: 1rem;
}

.modal-actions {
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid var(--light-color);
    display: flex;
    justify-content: center;
    gap: 15px;
}

.modal-actions .control-btn {
    padding: 10px 20px;
    font-size: 1rem;
}

.success-btn {
    background-color: var(--accent-color);
}
.success-btn:hover {
    background-color: #218838;
}

.cancel-btn {
    background-color: var(--text-muted);
}
.cancel-btn:hover {
    background-color: #5a6268;
}

/* Styles for Report Charts and Analysis (Add within report HTML style block or here if preferred) */
.report-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.chart-container {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.report-analysis {
    background-color: #e9ecef;
    padding: 15px;
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    margin-top: 20px;
}

.report-analysis h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: var(--primary-dark);
}

.report-analysis ul {
    padding-right: 20px; /* Indent list for RTL */
    margin: 0;
}

.report-analysis li {
    margin-bottom: 8px;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .summary-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .summary-grid .summary-card:nth-child(1),
    .summary-grid .summary-card:nth-child(3),
    .summary-grid .summary-card:nth-child(5),
    .summary-grid .summary-card:nth-child(7) {
        grid-column: 1;
    }
    
    .summary-grid .summary-card:nth-child(2),
    .summary-grid .summary-card:nth-child(4),
    .summary-grid .summary-card:nth-child(6) {
        grid-column: 2;
    }
    
    .summary-grid .summary-card:nth-child(7) {
        grid-row: auto;
        transform: none;
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 1rem;
    }
    
    .dashboard-main {
        padding: 1rem;
    }
    
    .summary-grid {
        grid-template-columns: 1fr;
    }
    
    .summary-grid .summary-card {
        grid-column: 1 !important;
    }
    
    .driver-files-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
    
    .header-content h1 {
        font-size: 1.5rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.dashboard-main {
    animation: fadeIn 0.5s ease-in;
}

.summary-card {
    animation: fadeIn 0.5s ease-in;
}
