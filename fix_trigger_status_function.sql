-- إصلا<PERSON> خطأ column reference "table_name" is ambiguous
-- المشكلة: تضارب في أسماء الأعمدة بين متغير PL/pgSQL وعمود الجدول

-- حذف الدالة القديمة بجميع أشكالها المحتملة
DROP FUNCTION IF EXISTS public.check_payment_triggers_status();
DROP FUNCTION IF EXISTS public.check_payment_triggers_status(OUT table_name text, OUT trigger_exists boolean, OUT trigger_name text, OUT last_test_result text);
DROP FUNCTION IF EXISTS public.check_payment_triggers_status(OUT tbl_name text, OUT trigger_exists boolean, OUT trigger_name text, OUT table_status text);

-- إنشاء الدالة المُصلحة
CREATE OR REPLACE FUNCTION public.check_payment_triggers_status()
RETURNS TABLE(
    tbl_name text,
    trigger_exists boolean,
    trigger_name text,
    table_status text
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        t.tbl_name::text,
        EXISTS(
            SELECT 1 FROM information_schema.triggers tr
            WHERE tr.event_object_table = t.tbl_name
              AND tr.trigger_name LIKE '%financial_log_trigger'
        ) as trigger_exists,
        COALESCE(
            (SELECT tr.trigger_name FROM information_schema.triggers tr
             WHERE tr.event_object_table = t.tbl_name
               AND tr.trigger_name LIKE '%financial_log_trigger'
             LIMIT 1),
            'لا يوجد'
        )::text as trigger_name,
        CASE
            WHEN EXISTS(
                SELECT 1 FROM information_schema.tables ist
                WHERE ist.table_name = t.tbl_name
                  AND ist.table_schema = 'public'
            ) THEN 'الجدول موجود'
            ELSE 'الجدول غير موجود'
        END::text as table_status
    FROM (VALUES
        ('student_payments'),
        ('enterprise_group_payments'),
        ('enterprise_subscriptions'),
        ('nathriyat_transactions'),
        ('bus_expenses'),
        ('driver_expenses')
    ) AS t(tbl_name);
END;
$$;

-- اختبار الدالة
SELECT 'تم إصلاح دالة التحقق من التريجرات بنجاح!' as status;

-- عرض حالة التريجرات
SELECT * FROM public.check_payment_triggers_status();

-- دالة بديلة مبسطة للتحقق السريع
CREATE OR REPLACE FUNCTION public.quick_trigger_check()
RETURNS TABLE(
    table_name text,
    has_trigger boolean
)
LANGUAGE sql
AS $$
    SELECT
        tables.table_name::text,
        EXISTS(
            SELECT 1 FROM information_schema.triggers
            WHERE event_object_table = tables.table_name
              AND trigger_name LIKE '%financial_log_trigger'
        ) as has_trigger
    FROM (
        SELECT unnest(ARRAY[
            'student_payments',
            'enterprise_group_payments',
            'enterprise_subscriptions',
            'nathriyat_transactions',
            'bus_expenses',
            'driver_expenses'
        ]) as table_name
    ) tables;
$$;

-- اختبار الدالة البديلة
SELECT 'دالة التحقق السريع:' as info;
SELECT * FROM public.quick_trigger_check();

-- عرض جميع التريجرات المالية الموجودة
SELECT 'جميع التريجرات المالية الموجودة:' as info;
SELECT
    event_object_table as table_name,
    trigger_name,
    action_timing,
    event_manipulation
FROM information_schema.triggers
WHERE trigger_name LIKE '%financial_log_trigger'
   OR trigger_name LIKE '%payment%trigger'
ORDER BY event_object_table;
