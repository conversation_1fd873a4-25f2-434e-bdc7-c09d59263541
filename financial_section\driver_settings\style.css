/* Inherit base styles from shared_styles.css */

/* Dashboard Layout */
.dashboard-layout {
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    overflow-x: hidden;
}

/* Top Navigation Bar */
.top-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-toggle-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.sidebar-toggle-btn:hover {
    background-color: rgba(255,255,255,0.1);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 600;
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.navbar-user {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logout-btn {
    background: rgba(255,255,255,0.1);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: rgba(255,255,255,0.2);
}

/* Main Content */
.main-content {
    margin-top: 70px;
    padding: 0;
    min-height: calc(100vh - 70px);
    transition: margin-right 0.3s ease;
}

.content-wrapper {
    padding: 30px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Dashboard Header */
.dashboard-header {
    background: white;
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--card-shadow);
    border-top: 4px solid var(--primary-color);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 20px;
}

.header-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.dashboard-header h1 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.dashboard-header h1 i {
    color: var(--primary-color);
}

.header-description {
    color: var(--text-muted);
    font-size: 1rem;
    margin: 0;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Statistics Section */
.stats-section {
    margin-bottom: 40px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    border-top: 4px solid transparent;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.stat-card:nth-child(1) {
    border-top-color: var(--primary-color);
}

.stat-card:nth-child(2) {
    border-top-color: var(--success-color);
}

.stat-card:nth-child(3) {
    border-top-color: var(--info-color);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: var(--primary-color);
}

.stat-card:nth-child(2) .stat-icon {
    background: var(--success-color);
}

.stat-card:nth-child(3) .stat-icon {
    background: var(--info-color);
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin: 0 0 5px 0;
}

.stat-info p {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin: 0;
}

/* Cards */
.card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    margin-bottom: 30px;
}

.card-header {
    background: var(--card-bg);
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h2 {
    margin: 0;
    color: var(--secondary-color);
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header h2 i {
    color: var(--primary-color);
}

.card-body {
    padding: 25px;
}

/* Filters Section */
.filters-section {
    margin-bottom: 30px;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    color: var(--text-color);
    font-weight: 500;
    font-size: 0.9rem;
}

.search-input-container {
    position: relative;
}

.search-input-container input {
    padding-right: 45px;
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
}

.search-input-container input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.search-input-container i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

select {
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: white;
    color: var(--text-color);
    font-family: inherit;
    font-size: 0.95rem;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

/* Table Responsive */
.table-responsive {
    overflow-x: auto;
    border-radius: var(--border-radius);
}

/* Modern Table */
.modern-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.modern-table thead {
    background: var(--card-bg);
}

.modern-table th {
    padding: 15px;
    text-align: right;
    font-weight: 600;
    color: var(--secondary-color);
    border-bottom: 2px solid var(--border-color);
    font-size: 0.9rem;
}

.modern-table td {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.modern-table tbody tr {
    transition: background-color 0.2s ease;
}

.modern-table tbody tr:hover {
    background-color: var(--card-bg);
}

.loading-message {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 40px 20px;
}

.loading-message i {
    margin-left: 10px;
}

/* Buttons */
.btn {
    padding: 10px 16px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
}

.btn-secondary {
    background: var(--text-muted);
    color: white;
}

.btn-secondary:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #218838;
    transform: translateY(-2px);
}

.btn-success:disabled {
    background: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Badge */
.badge {
    background: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.badge-success {
    background: var(--success-color);
    color: white;
}

.badge-warning {
    background: var(--warning-color);
    color: white;
}

.badge-danger {
    background: var(--danger-color);
    color: white;
}

/* Statistics Cards */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    padding: 20px;
    background-color: #f8f9fa;
}

.stat-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: transform var(--transition-speed);
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background-color: var(--primary-color);
}

.stat-card:nth-child(2) .stat-icon {
    background-color: var(--success-color);
}

.stat-card:nth-child(3) .stat-icon {
    background-color: #ffc107;
}

.stat-card:nth-child(4) .stat-icon {
    background-color: var(--info-color);
}

.stat-info h3 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-dark);
}

.stat-info p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.control-btn {
    padding: 8px 15px; border: none; border-radius: var(--border-radius); cursor: pointer; font-weight: 500; display: inline-flex; align-items: center; justify-content: center; gap: 6px; color: white; transition: background-color var(--transition-speed), box-shadow var(--transition-speed); box-shadow: var(--button-shadow); font-size: 0.9rem;
}
.control-btn.add-btn { background-color: var(--success-color); }
.control-btn.add-btn:hover { background-color: #218838; }
.control-btn:disabled { background-color: var(--text-muted); cursor: not-allowed; opacity: 0.7; }

/* Table Container */
.table-container {
    padding: 0;
}

.table-header {
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.table-title h3 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--text-dark);
    display: flex;
    align-items: center;
    gap: 8px;
}

.table-title h3 i {
    color: var(--primary-color);
}

.drivers-count {
    background-color: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.table-wrapper {
    overflow-x: auto;
}

.drivers-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
}

.drivers-table th,
.drivers-table td {
    padding: 12px 15px;
    text-align: right;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
}

.drivers-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: var(--text-dark);
    position: sticky;
    top: 0;
    z-index: 10;
}

.drivers-table tbody tr {
    transition: background-color var(--transition-speed);
}

.drivers-table tbody tr:hover {
    background-color: rgba(105, 130, 100, 0.04);
}

.drivers-table .loading-message {
    text-align: center;
    color: var(--text-muted);
    padding: 40px;
    font-size: 1rem;
}

.drivers-table .loading-message i {
    margin-left: 8px;
}

/* Select Column */
.select-col {
    width: 40px;
    text-align: center;
}

.select-col input[type="checkbox"] {
    transform: scale(1.2);
    cursor: pointer;
}

/* Input Fields */
.drivers-table input[type="number"] {
    width: 120px;
    padding: 8px 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-family: inherit;
    text-align: left;
    direction: ltr;
    transition: border-color var(--transition-speed);
}

.drivers-table input[type="number"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(105, 130, 100, 0.1);
}

.drivers-table input[type="number"].changed {
    border-color: #ffc107;
    background-color: #fff3cd;
}

/* Status Badges */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

.status-configured {
    background-color: #d4edda;
    color: #155724;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-incomplete {
    background-color: #f8d7da;
    color: #721c24;
}

/* Style for the current balance cell */
.current-balance-cell {
    font-weight: 500;
    color: var(--text-muted); /* Use muted color as it's informational */
    text-align: center; /* Center the balance */
    direction: ltr; /* Ensure LTR for currency */
    white-space: nowrap; /* Prevent wrapping */
}

#settings-table th:last-child,
#settings-table td:last-child {
    text-align: center; /* Center the save button */
    width: 80px; /* Adjust width for the button column */
}

.save-row-btn {
    padding: 5px 10px; /* Smaller padding for row button */
    font-size: 0.85rem;
    background-color: var(--success-color); /* Green save button */
    min-width: 40px; /* Ensure button has some width */
}

.save-row-btn:hover:not(:disabled) {
    background-color: #218838; /* Darker green on hover */
}

.save-row-btn:disabled {
    background-color: var(--text-muted);
    opacity: 0.6;
    cursor: not-allowed;
}

.save-row-btn i {
    margin: 0; /* Remove default gap for icon-only feel */
}

.message { padding: 10px 15px; margin-bottom: 15px; border-radius: var(--border-radius); font-size: 0.9rem; display: none; }
.message.show { display: block; }
.message.info { background-color: rgba(23, 162, 184, 0.1); color: var(--info-color); }
.message.success { background-color: rgba(40, 167, 69, 0.1); color: var(--success-color); }
.message.error { background-color: rgba(220, 53, 69, 0.1); color: var(--danger-color); }

.dashboard-footer { background-color: var(--card-bg); text-align: center; padding: 15px; color: var(--text-muted); border-top: 1px solid var(--border-color); margin-top: auto; }
.dashboard-footer button { background-color: var(--text-muted); color: white; margin-bottom: 10px; }
.dashboard-footer button:hover { background-color: #5a6268; }

/* Balance Info Modal */
.balance-info-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.balance-info-modal .modal-content {
    background-color: white;
    margin: 2% auto;
    padding: 0;
    border-radius: var(--border-radius);
    width: 95%;
    max-width: 1000px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.balance-info-modal .modal-header {
    background: var(--card-bg);
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.balance-info-modal .modal-header h2 {
    margin: 0;
    color: var(--secondary-color);
    font-size: 1.3rem;
    font-weight: 600;
}

.close-modal-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-modal-btn:hover {
    background: var(--danger-color);
    color: white;
}

.balance-info-modal .modal-body {
    padding: 25px;
}

.balance-breakdown-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    font-size: 0.9rem;
}

.balance-breakdown-table th,
.balance-breakdown-table td {
    padding: 10px 12px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
    vertical-align: top;
}

.balance-breakdown-table th {
    background: var(--card-bg);
    font-weight: 600;
    color: var(--secondary-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

.balance-breakdown-table tbody tr:hover {
    background: var(--card-bg);
}

/* Special styling for header rows */
.balance-breakdown-table tr[style*="background: var(--primary-color)"],
.balance-breakdown-table tr[style*="background: var(--info-color)"] {
    font-size: 1rem;
}

.balance-breakdown-table tr[style*="background: var(--card-bg)"] {
    font-weight: 600;
}

/* Balance amount styling */
.balance-breakdown-table .balance-positive {
    background-color: #fee;
    color: #d32f2f;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 600;
}

.balance-breakdown-table .balance-negative {
    background-color: #e8f5e8;
    color: #2e7d32;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 600;
}

.balance-breakdown-table .balance-zero {
    background-color: var(--card-bg);
    color: var(--text-muted);
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 600;
}

/* Responsive adjustments for modal */
@media (max-width: 768px) {
    .balance-info-modal .modal-content {
        width: 98%;
        margin: 1% auto;
        max-height: 95vh;
    }

    .balance-breakdown-table {
        font-size: 0.8rem;
    }

    .balance-breakdown-table th,
    .balance-breakdown-table td {
        padding: 8px 6px;
    }
}

/* Balance Display */
.balance-display {
    padding: 8px 12px;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-align: center;
}

.balance-positive {
    background-color: #fee;
    color: #d32f2f;
    border: 1px solid #ffcdd2;
}

.balance-negative {
    background-color: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

.balance-zero {
    background-color: var(--card-bg);
    color: var(--text-muted);
    border: 1px solid var(--border-color);
}

.balance-display.clickable {
    cursor: pointer;
    transition: all 0.2s ease;
}

.balance-display.clickable:hover {
    opacity: 0.8;
    transform: scale(1.02);
}

/* Responsive Design */
@media (max-width: 768px) {
    .content-wrapper {
        padding: 20px 15px;
    }

    .navbar-brand span {
        display: none;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .filters-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .dashboard-header {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .header-actions {
        flex-direction: column;
        gap: 10px;
    }

    .card-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .modern-table {
        font-size: 0.8rem;
    }

    .modern-table th,
    .modern-table td {
        padding: 10px 8px;
    }

    .balance-info-modal .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .balance-info-modal .modal-body {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .dashboard-header {
        padding: 20px;
    }

    .dashboard-header h1 {
        font-size: 1.5rem;
    }

    .stat-card {
        padding: 20px;
    }

    .stat-info h3 {
        font-size: 1.5rem;
    }

    .btn {
        padding: 8px 12px;
        font-size: 0.8rem;
    }
}
