/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 70px;
    right: -300px;
    width: 300px;
    height: calc(100vh - 70px);
    background: white;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    transition: right 0.3s ease;
    z-index: 999;
    overflow-y: auto;
}

.sidebar.active {
    right: 0;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.sidebar-header h3 {
    margin: 0;
    color: #495057;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    color: #495057;
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.nav-link:hover {
    background-color: #f8f9fa;
    color: #667eea;
    border-right-color: #667eea;
}

.nav-link i:first-child {
    font-size: 1.1rem;
    width: 20px;
}

.nav-link span {
    flex: 1;
    margin-right: 15px;
    font-weight: 500;
}

.nav-link .arrow {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.nav-link:hover .arrow {
    transform: translateX(-3px);
}

/* Sidebar Overlay */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Section Modals */
.section-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1001;
    display: none;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(4px);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.section-modal.active {
    display: flex !important;
    opacity: 1;
}

.section-modal .modal-content {
    background: white;
    border-radius: 12px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-modal {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.close-modal:hover {
    background-color: rgba(255,255,255,0.1);
}

.modal-body {
    padding: 25px;
}

.modal-links {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.modal-link {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    color: #495057;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.modal-link:hover {
    background-color: #f8f9fa;
    color: #667eea;
    border-color: #667eea;
    transform: translateX(5px);
}

.modal-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.modal-link span {
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 280px;
        right: -280px;
    }

    .section-modal .modal-content {
        width: 95%;
        margin: 10px;
    }

    .modal-body {
        padding: 20px;
    }
}

@media (max-width: 576px) {
    .modal-body {
        padding: 15px;
    }

    .modal-link {
        padding: 12px 15px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}
