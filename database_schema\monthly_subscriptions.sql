-- =============================================
-- جدول الاشتراكات الشهرية للطلاب
-- =============================================
-- هذا الجدول يمثل وجود اشتراك لطالب معين في شهر مالي محدد.
-- وجود سجل هنا هو ما يبحث عنه كود group_details.js.

CREATE TABLE public.monthly_student_subscriptions (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY, -- معرف فريد لكل سجل اشتراك شهري
    student_id uuid NOT NULL,                               -- معرف الطالب (مفتاح أجنبي لجدول students)
    budget_month_id uuid NOT NULL,                          -- معرف الشهر المالي (لم يعد مفتاح أجنبي)
    subscription_amount numeric(10, 2) DEFAULT 0.00 NOT NULL, -- مبلغ الاشتراك المحدد لهذا الشهر (يمكن أن يأخذ القيمة الافتراضية عند الإنشاء)
    payment_status text DEFAULT 'not_paid'::text NOT NULL, -- حالة الدفع لهذا الشهر ('not_paid', 'partial', 'paid')
    notes text,                                             -- ملاحظات إضافية (اختياري)
    created_at timestamp with time zone DEFAULT now() NOT NULL, -- تاريخ إنشاء السجل

    -- الربط مع جدول الطلاب
    CONSTRAINT monthly_student_subscriptions_student_id_fkey FOREIGN KEY (student_id)
        REFERENCES public.students (id) ON UPDATE CASCADE ON DELETE CASCADE, -- عند حذف طالب، يحذف اشتراكه

    -- تم حذف الربط مع جدول الشهور المالية
    -- CONSTRAINT monthly_student_subscriptions_budget_month_id_fkey FOREIGN KEY (budget_month_id)
    --    REFERENCES public.budget_months (id) ON UPDATE CASCADE ON DELETE CASCADE, -- عند حذف شهر، تحذف اشتراكاته

    -- ضمان عدم تكرار اشتراك نفس الطالب في نفس الشهر
    CONSTRAINT unique_student_month_subscription UNIQUE (student_id, budget_month_id)
);

-- إضافة تعليقات للأعمدة للتوضيح (اختياري)
COMMENT ON TABLE public.monthly_student_subscriptions IS 'سجلات الاشتراكات الشهرية لكل طالب، تربط الطالب بشهر مالي محدد.';
COMMENT ON COLUMN public.monthly_student_subscriptions.student_id IS 'معرف الطالب المشترك.';
COMMENT ON COLUMN public.monthly_student_subscriptions.budget_month_id IS 'معرف الشهر المالي المرتبط به الاشتراك (لم يعد مفتاح أجنبي).';
COMMENT ON COLUMN public.monthly_student_subscriptions.subscription_amount IS 'المبلغ المستحق للاشتراك في هذا الشهر المحدد.';
COMMENT ON COLUMN public.monthly_student_subscriptions.payment_status IS 'حالة دفع الاشتراك لهذا الشهر (not_paid, partial, paid).';


-- =============================================
-- مثال على كيفية إضافة سجل اشتراك شهري
-- =============================================
-- هذا الأمر يجب أن يتم تنفيذه (من خلال كود آخر أو عملية تلقائية)
-- لكل طالب في كل شهر مالي لكي يتمكن group_details.js من العثور عليه.

-- استبدل القيم بين الأقواس بالمعرفات والمبالغ الفعلية
/*
INSERT INTO public.monthly_student_subscriptions
    (student_id, budget_month_id, subscription_amount, payment_status)
VALUES
    ('[STUDENT_UUID]', '[BUDGET_MONTH_UUID]', [AMOUNT], 'not_paid');
*/

-- مثال عملي (بافتراض وجود طالب ومعرف شهر):
/*
INSERT INTO public.monthly_student_subscriptions
    (student_id, budget_month_id, subscription_amount, payment_status)
VALUES
    ('a126dc80-c879-4e68-9132-aac50eb787e3', '1f3b2a32-96c7-4cb9-b8f9-6c806f1f42f4', 900.00, 'not_paid');
*/

