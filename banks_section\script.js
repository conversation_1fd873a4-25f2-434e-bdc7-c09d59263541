console.log("Banks script loaded.");

// --- Auth Check ---
checkAuth('../auth.js'); // Adjusted path

// Supabase Initialization - using variables already defined in config.js
let supabaseUrl, supabaseAnonKey;

// Safely access the global variables
if (typeof SUPABASE_URL !== 'undefined') {
    supabaseUrl = SUPABASE_URL;
} else {
    supabaseUrl = 'YOUR_FALLBACK_SUPABASE_URL'; // Add fallback or handle error
    console.error('SUPABASE_URL not found in global scope.');
}

if (typeof SUPABASE_ANON_KEY !== 'undefined') {
    supabaseAnonKey = SUPABASE_ANON_KEY;
} else {
    supabaseAnonKey = 'YOUR_FALLBACK_SUPABASE_ANON_KEY'; // Add fallback or handle error
    console.error('SUPABASE_ANON_KEY not found in global scope.');
}

const { createClient } = supabase;
const _supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('Supabase Initialized for Banks:', _supabase);

// --- DOM Elements ---
const bankForm = document.getElementById('bank-form');
const formMessage = document.getElementById('form-message');
const addBankBtn = document.getElementById('add-bank-btn');
const addBankSection = document.getElementById('add-bank-section');
const cancelEditBtn = document.getElementById('cancel-edit-btn');
const closeFormBtn = document.getElementById('close-form-btn');
const banksTableBody = document.getElementById('banks-tbody');
const listMessage = document.getElementById('list-message');
const searchInput = document.getElementById('search-input');
const searchBtn = document.getElementById('search-btn');
const printReportBtn = document.getElementById('print-report-btn');
const bankIdField = document.getElementById('bank_id'); // Hidden input for bank ID (PK)
const formTitle = document.getElementById('form-title');
const totalBanksEl = document.getElementById('total-banks');
// Add elements for other stats if implemented
// const totalTransactionsEl = document.getElementById('total-transactions');
// const totalBalanceEl = document.getElementById('total-balance');
const banksCountBadge = document.getElementById('banks-count');
const paginationControls = document.getElementById('pagination-controls');
const backToFinanceBtnSidebar = document.getElementById('back-to-finance-btn-sidebar'); // Added
const dashboardMessage = document.getElementById('dashboard-message'); // Added for general messages

// --- State ---
let currentBanks = [];
let editMode = false;
let stats = {
    totalBanks: 0,
    // Add other stats if needed
    // totalTransactions: 0,
    // totalBalance: 0
};

// Pagination variables
let currentPage = 1;
const banksPerPage = 12; // Number of banks per page
let totalPages = 1;

// --- Functions ---

// Function to display messages (form or list)
const showMessage = (element, message, type = 'info') => {
    if (!element) return; // Guard clause
    element.textContent = message;
    element.className = `message ${type} show`; // Ensure 'show' class is added

    // Auto hide success messages after 5 seconds
    if (type === 'success') {
        setTimeout(() => {
            element.style.display = 'none'; // Hide element directly
            element.classList.remove('show');
        }, 5000);
    } else {
         element.style.display = 'block'; // Ensure non-success messages are visible
    }
};

// Function to render pagination controls
const renderPaginationControls = () => {
    if (!paginationControls) return;
    paginationControls.innerHTML = '';

    if (!currentBanks || currentBanks.length <= banksPerPage) {
        return;
    }

    totalPages = Math.ceil(currentBanks.length / banksPerPage);

    // Previous button
    const prevButton = document.createElement('button');
    prevButton.innerHTML = '<i class="fas fa-angle-right"></i>';
    prevButton.className = currentPage === 1 ? 'disabled' : '';
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', () => {
        if (currentPage > 1) {
            goToPage(currentPage - 1);
        }
    });
    paginationControls.appendChild(prevButton);

    // Page buttons logic (simplified for brevity)
    const maxPageButtons = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
    let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);

    if (endPage - startPage + 1 < maxPageButtons) {
        startPage = Math.max(1, endPage - maxPageButtons + 1);
    }

    if (startPage > 1) {
        const firstPageBtn = document.createElement('button');
        firstPageBtn.textContent = '1';
        firstPageBtn.addEventListener('click', () => goToPage(1));
        paginationControls.appendChild(firstPageBtn);
        if (startPage > 2) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'pagination-ellipsis';
            paginationControls.appendChild(ellipsis);
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.textContent = i.toString();
        pageBtn.className = i === currentPage ? 'active' : '';
        pageBtn.addEventListener('click', () => goToPage(i));
        paginationControls.appendChild(pageBtn);
    }

     if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
             const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'pagination-ellipsis';
            paginationControls.appendChild(ellipsis);
        }
        const lastPageBtn = document.createElement('button');
        lastPageBtn.textContent = totalPages.toString();
        lastPageBtn.addEventListener('click', () => goToPage(totalPages));
        paginationControls.appendChild(lastPageBtn);
    }

    // Next button
    const nextButton = document.createElement('button');
    nextButton.innerHTML = '<i class="fas fa-angle-left"></i>';
    nextButton.className = currentPage === totalPages ? 'disabled' : '';
    nextButton.disabled = currentPage === totalPages;
    nextButton.addEventListener('click', () => {
        if (currentPage < totalPages) {
            goToPage(currentPage + 1);
        }
    });
    paginationControls.appendChild(nextButton);
};

// Function to navigate to a specific page
const goToPage = (page) => {
    currentPage = page;
    renderBanksTable(currentBanks); // Re-render table for the new page
};

// Function to get current page banks
const getCurrentPageBanks = () => {
    const startIndex = (currentPage - 1) * banksPerPage;
    const endIndex = startIndex + banksPerPage;
    return currentBanks.slice(startIndex, endIndex);
};

// Function to render the banks table
const renderBanksTable = (banks) => {
    if (!banksTableBody) return;
    banksTableBody.innerHTML = '';
    if (listMessage) listMessage.style.display = 'none';

    if (!banks || banks.length === 0) {
        // Adjusted colspan to 7
        banksTableBody.innerHTML = '<tr><td colspan="7" class="loading-message">لا يوجد بنوك لعرضها.</td></tr>';
        if (banksCountBadge) banksCountBadge.textContent = '0';
        if (paginationControls) paginationControls.innerHTML = ''; // No pagination needed
        return;
    }

    // Update total count badge
    if (banksCountBadge) banksCountBadge.textContent = banks.length;

    // Get only banks for the current page
    const pageBanks = getCurrentPageBanks();

    pageBanks.forEach(bank => {
        const row = document.createElement('tr');
        // Updated row content to include IBAN, bank_type, contact_phone, notes and remove balances
        row.innerHTML = `
            <td>${bank.name || 'غير محدد'}</td>
            <td>${bank.account_number || '---'}</td>
            <td>${bank.iban || '---'}</td>
            <td>${bank.bank_type || '---'}</td> <!-- Added bank_type -->
            <td>${bank.contact_phone || '---'}</td>
            <td>${bank.notes || '---'}</td>
            <td>
                <button class="action-btn edit-btn" data-id="${bank.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                <button class="action-btn delete-btn" data-id="${bank.id}" title="حذف"><i class="fas fa-trash-alt"></i></button>
            </td>
        `;

        // Add event listeners for edit/delete buttons
        const editBtn = row.querySelector('.edit-btn');
        const deleteBtn = row.querySelector('.delete-btn');
        if (editBtn) {
            editBtn.addEventListener('click', () => handleEditBank(bank));
        }
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => handleDeleteBank(bank.id, bank.name));
        }

        banksTableBody.appendChild(row);
    });

    // Render pagination controls
    renderPaginationControls();
};

// Function to update dashboard stats
const updateDashboardStats = async () => {
    try {
        // Get total banks count (FILTERED FOR 'اجل')
        const { count: totalCount, error: totalError } = await _supabase
            .from('banks')
            .select('*', { count: 'exact', head: true })
            .eq('bank_type', 'آجل'); // Filter stats for 'آجل' banks

        // Add logic for other stats if needed

        if (!totalError) {
            stats.totalBanks = totalCount || 0;
            // Update other stats

            // Update dashboard elements
            if (totalBanksEl) totalBanksEl.textContent = stats.totalBanks;
            // Update other stat elements
        } else {
            console.error('Error fetching stats data:', totalError);
        }
    } catch (error) {
        console.error('Error updating stats:', error);
    }
};

// Function to fetch banks from Supabase
const fetchBanks = async (searchTerm = '') => {
    if (!banksTableBody) return;
    // Adjusted colspan to 7
    banksTableBody.innerHTML = '<tr><td colspan="7" class="loading-message">جاري التحميل...</td></tr>';
    if (paginationControls) paginationControls.innerHTML = ''; // Clear pagination while loading

    try {
        let query = _supabase
            .from('banks') // Assuming table name is 'banks'
            .select('*')
            .eq('bank_type', 'آجل'); // Filter for 'آجل' banks

        // Add search condition if searchTerm is provided
        if (searchTerm) {
            // Search by name, account number, or IBAN within 'آجل' banks
            query = query.or(`name.ilike.%${searchTerm}%,account_number.ilike.%${searchTerm}%,iban.ilike.%${searchTerm}%`);
        }

        // Order by name
        query = query.order('name', { ascending: true });

        const { data, error } = await query;

        if (error) {
            console.error('Supabase Fetch Error:', error);
            showMessage(listMessage, `خطأ في جلب البيانات: ${error.message}`, 'error');
            // Adjusted colspan to 7
            banksTableBody.innerHTML = '<tr><td colspan="7" class="loading-message">خطأ في تحميل البيانات.</td></tr>';
        } else {
            console.log('Fetched banks (آجل):', data);
            currentBanks = data || [];
            currentPage = 1; // Reset to first page
            renderBanksTable(currentBanks); // Render the table with fetched data

            if (data.length === 0 && searchTerm) {
                showMessage(listMessage, `لا توجد نتائج للبحث عن "${searchTerm}" ضمن البنوك الآجلة.`, 'info');
            } else if (data.length === 0) {
                 showMessage(listMessage, 'لا يوجد بنوك آجلة مسجلة حالياً.', 'info');
            }

            // Update dashboard stats after fetching (consider if stats should be filtered too)
            updateDashboardStats(); // This might need adjustment if stats should only reflect 'آجل' banks
        }
    } catch (error) {
        console.error('JavaScript Fetch Error:', error);
        showMessage(listMessage, `حدث خطأ غير متوقع: ${error.message}`, 'error');
        // Adjusted colspan to 7
        banksTableBody.innerHTML = '<tr><td colspan="7" class="loading-message">خطأ غير متوقع.</td></tr>';
    }
};

// Function to handle form submission (Add or Update)
const handleFormSubmit = async (event) => {
    event.preventDefault();
    if (!bankForm || !formMessage) return;

    showMessage(formMessage, 'جاري حفظ البيانات...', 'info');
    const submitBtn = bankForm.querySelector('.submit-btn');
    if (submitBtn) submitBtn.disabled = true;

    const formData = new FormData(bankForm);
    const bankData = {};
    formData.forEach((value, key) => {
        // Trim whitespace, handle empty optional fields
        const trimmedValue = value.trim();
        // Handle optional fields: iban, contact_phone, notes, bank_type
        if (key === 'iban' || key === 'contact_phone' || key === 'notes' || key === 'bank_type') {
            bankData[key] = trimmedValue === '' ? null : trimmedValue;
        } else {
            bankData[key] = trimmedValue;
        }
    });

    const bankId = bankData.bank_id; // Get the hidden ID (PK)

    // Basic validation (Name and Account Number required based on UI)
    if (!bankData.bank_name || !bankData.account_number) {
        showMessage(formMessage, 'الرجاء ملء اسم البنك ورقم الحساب (*).', 'error');
        if (submitBtn) submitBtn.disabled = false;
        return;
    }
    // Optional: Validate bank_type if it becomes required
    // if (!bankData.bank_type) {
    //     showMessage(formMessage, 'الرجاء اختيار نوع البنك.', 'error');
    //     if (submitBtn) submitBtn.disabled = false;
    //     return;
    // }


    // Prepare data for Supabase (match column names)
    const dataToUpsert = {
        name: bankData.bank_name,
        account_number: bankData.account_number,
        iban: bankData.iban, // Added IBAN
        bank_type: bankData.bank_type, // Added bank_type
        contact_phone: bankData.contact_phone, // Added contact_phone
        notes: bankData.notes, // Added notes
    };

    try {
        let result;
        if (editMode && bankId) {
            // Update existing bank
            result = await _supabase
                .from('banks')
                .update(dataToUpsert)
                .eq('id', bankId) // Use 'id' (uuid) as the primary key
                .select();
        } else {
            // Insert new bank
            result = await _supabase
                .from('banks')
                .insert([dataToUpsert])
                .select();
        }

        const { data, error } = result;

        if (error) {
            console.error('Supabase Save Error:', error);
            let userMessage = `خطأ في الحفظ: ${error.message}`;
            if (error.code === '23505') { // Unique constraint violation
                 // Assuming unique constraint might be on name or account_number
                 userMessage = 'خطأ: اسم البنك أو رقم الحساب موجود مسبقاً.';
            } else if (error.message.includes('invalid input value for enum bank_type_enum')) {
                 userMessage = 'خطأ: قيمة نوع البنك غير صالحة.';
            }
            showMessage(formMessage, userMessage, 'error');
        } else {
            console.log('Supabase Save Success:', data);
            showMessage(formMessage, `تم ${editMode ? 'تحديث' : 'إضافة'} البنك بنجاح!`, 'success');
            // Keep modal open for inline form
            // setTimeout(() => {
            //     toggleModal(false); // Don't close inline form automatically
            // }, 1500);
            resetForm(); // Reset the form after successful save
            fetchBanks(); // Refresh list
        }
    } catch (error) {
        console.error('JavaScript Save Error:', error);
        showMessage(formMessage, `حدث خطأ غير متوقع: ${error.message}`, 'error');
    } finally {
        if (submitBtn) submitBtn.disabled = false;
    }
};

// Function to reset the form and UI elements
const resetForm = () => {
    editMode = false;
    if (bankForm) bankForm.reset();
    if (bankIdField) bankIdField.value = ''; // Clear hidden ID field
    if (formMessage) formMessage.style.display = 'none';
    if (formTitle) formTitle.textContent = 'إضافة بنك جديد';
    const submitBtn = bankForm?.querySelector('.submit-btn');
    if (submitBtn) submitBtn.textContent = 'حفظ البنك'; // Reset button text
    const cancelBtn = bankForm?.querySelector('#cancel-edit-btn');
    if (cancelBtn) cancelBtn.style.display = 'none'; // Hide cancel button
};

// Function to populate form for editing
const handleEditBank = (bank) => {
    if (!bank || !bankForm) return;
    // No need to toggle modal for inline form, just populate
    editMode = true;

    if (formTitle) formTitle.textContent = 'تعديل بيانات البنك';
    const submitBtn = bankForm.querySelector('.submit-btn');
    if (submitBtn) submitBtn.textContent = 'تحديث البنك';
    const cancelBtn = bankForm.querySelector('#cancel-edit-btn');
    if (cancelBtn) cancelBtn.style.display = 'inline-block'; // Show cancel button

    // Populate form fields
    if (bankIdField) bankIdField.value = bank.id || ''; // Set the hidden ID field
    document.getElementById('bank_name').value = bank.name || '';
    document.getElementById('account_number').value = bank.account_number || '';
    document.getElementById('iban').value = bank.iban || ''; // Populate IBAN
    document.getElementById('bank_type').value = bank.bank_type || ''; // Populate bank_type
    document.getElementById('contact_phone').value = bank.contact_phone || ''; // Populate contact phone
    document.getElementById('notes').value = bank.notes || ''; // Populate notes
    // Removed initial_balance population

    // Scroll to form and focus
    bankForm.scrollIntoView({ behavior: 'smooth', block: 'start' });
    setTimeout(() => {
        document.getElementById('bank_name')?.focus();
    }, 300); // Delay focus slightly
};

// Function to handle bank deletion
const handleDeleteBank = async (bankId, bankName) => {
    const confirmDelete = confirm(`هل أنت متأكد من حذف بنك "${bankName || 'هذا البنك'}"؟ لا يمكن التراجع عن هذا الإجراء.`);

    if (confirmDelete) {
        try {
            const { error } = await _supabase
                .from('banks')
                .delete()
                .eq('id', bankId); // Use 'id' (uuid) as the PK

            if (error) {
                console.error('Supabase Delete Error:', error);
                 // Check for foreign key constraint violation (e.g., if transactions are linked)
                let userMessage = `خطأ في الحذف: ${error.message}`;
                if (error.code === '23503') {
                    userMessage = 'خطأ: لا يمكن حذف البنك لوجود معاملات أو بيانات أخرى مرتبطة به.';
                }
                showMessage(listMessage, userMessage, 'error');
            } else {
                console.log('Bank deleted:', bankId);
                showMessage(listMessage, `تم حذف البنك بنجاح.`, 'success');
                fetchBanks(); // Refresh the list
            }
        } catch (error) {
            console.error('JavaScript Delete Error:', error);
            showMessage(listMessage, `حدث خطأ غير متوقع أثناء الحذف: ${error.message}`, 'error');
        }
    }
};

// Function to handle search
const handleSearch = () => {
    if (!searchInput) return;
    const searchTerm = searchInput.value.trim();
    fetchBanks(searchTerm);
};

// Function to handle printing report
const handlePrintReport = () => {
    const banksToPrint = [...currentBanks];
    let reportTitle = 'تقرير البنوك';

    // Sort by bank name
    banksToPrint.sort((a, b) => (a.name || '').localeCompare(b.name || ''));

    const printWindow = window.open('', '_blank', 'height=600,width=800');
    let reportContent = `
        <html dir="rtl">
        <head>
            <title>${reportTitle}</title>
            <style>
                body { font-family: 'Tajawal', sans-serif; padding: 20px; direction: rtl; }
                h1 { text-align: center; margin-bottom: 10px; color: #333; }
                .print-meta { display: flex; justify-content: space-between; margin-bottom: 20px; font-size: 0.9em; color: #555; }
                table { width: 100%; border-collapse: collapse; margin-top: 15px; }
                th, td { padding: 8px; text-align: right; border: 1px solid #ddd; font-size: 0.9em; word-wrap: break-word; } /* Added word-wrap */
                th { background-color: #f2f2f2; font-weight: bold; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                @media print {
                    .no-print { display: none; }
                    body { padding: 5px; }
                    h1 { font-size: 1.5em; }
                }
            </style>
        </head>
        <body>
            <div class="print-meta">
                <span>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</span>
                <span>عدد البنوك في التقرير: ${banksToPrint.length}</span>
            </div>
            <h1>${reportTitle}</h1>
            ${banksToPrint.length === 0 ? '<p style="text-align: center; color: #888;">لا يوجد بنوك لعرضها.</p>' : `
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم البنك</th>
                        <th>رقم الحساب</th>
                        <th>الآيبان</th> <!-- Added IBAN -->
                        <th>نوع البنك</th> <!-- Added Bank Type -->
                        <th>هاتف الاتصال</th> <!-- Added Contact Phone -->
                        <th>ملاحظات</th> <!-- Added Notes -->
                        <!-- Removed Balance Headers -->
                    </tr>
                </thead>
                <tbody>
            `}
    `;

    banksToPrint.forEach((bank, index) => {
        // Updated row content for print
        reportContent += `
            <tr>
                <td>${index + 1}</td>
                <td>${bank.name || ''}</td>
                <td>${bank.account_number || ''}</td>
                <td>${bank.iban || ''}</td>
                <td>${bank.bank_type || ''}</td> <!-- Added bank_type -->
                <td>${bank.contact_phone || ''}</td>
                <td>${bank.notes || ''}</td>
            </tr>
        `;
    });

    if (banksToPrint.length > 0) {
        reportContent += `
                </tbody>
            </table>
        `;
    }

    reportContent += `
            <div class="no-print" style="margin-top: 20px; text-align: center;">
                <button onclick="window.print()">طباعة</button>
                <button onclick="window.close()">إغلاق</button>
            </div>
        </body>
        </html>
    `;

    printWindow.document.write(reportContent);
    printWindow.document.close();
};


// Function to handle modal toggling (Now less relevant for inline form, but kept for potential future use)
function toggleModal(show = true) {
    // This function might not be needed if the form is always inline
    // Kept for potential future refactoring or if a modal is reintroduced
    console.warn('toggleModal called, but form is likely inline.');
    // If you revert to a modal, uncomment the logic below
    /*
    if (!addBankSection) return;
    console.log('Bank Modal toggled:', show ? 'show' : 'hide');

    if (show) {
        resetForm(); // Reset form before showing
        addBankSection.classList.add('show');
        setTimeout(() => {
            document.getElementById('bank_name')?.focus();
        }, 300);
        document.body.style.overflow = 'hidden';
    } else {
        addBankSection.classList.remove('show');
        document.body.style.overflow = '';
        resetForm(); // Also reset when closing modal
    }
    */
}

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOM loaded, initializing banks application...');

    // Setup event listeners
    setupEventListeners();

    // Fetch initial banks data
    fetchBanks();
});


// --- Event Listeners ---
const setupEventListeners = () => {
    if (bankForm) {
        bankForm.addEventListener('submit', handleFormSubmit);
    }

    // Remove listener for addBankBtn if form is inline
    // if (addBankBtn) {
    //     addBankBtn.addEventListener('click', () => {
    //         editMode = false;
    //         // toggleModal(true); // Don't toggle modal for inline
    //         resetForm(); // Just reset the inline form
    //         document.getElementById('bank_name')?.focus();
    //     });
    // }

    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', () => {
            // toggleModal(false); // Don't toggle modal for inline
            resetForm(); // Reset the form when cancelling edit
        });
    }

    // Remove listener for closeFormBtn if form is inline
    // if (closeFormBtn) {
    //     closeFormBtn.addEventListener('click', () => {
    //         toggleModal(false);
    //     });
    // }

    // Remove modal background click listener if form is inline
    // if (addBankSection) {
    //     addBankSection.addEventListener('click', (event) => {
    //         if (event.target === addBankSection) {
    //             toggleModal(false);
    //         }
    //     });
    // }

    if (searchBtn) {
        searchBtn.addEventListener('click', handleSearch);
    }

    if (searchInput) {
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
        // Optional: Add input event listener for live search
        // searchInput.addEventListener('input', handleSearch);
    }

    if (printReportBtn) {
        printReportBtn.addEventListener('click', handlePrintReport);
    }

    // Remove Escape key listener if form is inline (or adjust if needed)
    // document.addEventListener('keydown', function(event) {
    //     if (event.key === 'Escape' && addBankSection && addBankSection.classList.contains('show')) {
    //         toggleModal(false);
    //     }
    // });

    // Back button listener (sidebar)
    if (backToFinanceBtnSidebar) {
        backToFinanceBtnSidebar.addEventListener('click', () => {
            // Navigate to the main financial dashboard
            window.location.href = '../financial_section/financial_dashboard.html'; // Adjust path if needed
        });
    } else {
         console.error("Back to Finance Sidebar Button not found!");
    }
};
