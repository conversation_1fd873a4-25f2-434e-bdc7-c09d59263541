// Ensure Supabase is initialized from config.js
if (typeof supabase === 'undefined') {
    console.error('Supabase client is not initialized. Make sure config.js is loaded correctly.');
    // Optionally display an error message to the user on the page
}

const { createClient } = supabase;
const supabaseClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// DOM Elements
const currentMonthYearEl = document.getElementById('current-month-year');
const totalDriversEl = document.getElementById('total-drivers');
const totalDefaultSalariesEl = document.getElementById('total-default-salaries');
const totalPaidEl = document.getElementById('total-paid');
const totalRemainingEl = document.getElementById('total-remaining');
const totalBankExpensesEl = document.getElementById('total-bank-expenses');
const totalDeductedExpensesEl = document.getElementById('total-deducted-expenses');
const totalDeductionsEl = document.getElementById('total-deductions');
const driverFilesContainer = document.getElementById('driver-files-container');
const listMessageEl = document.getElementById('list-message');
const backToMainDashboardBtn = document.getElementById('back-to-main-dashboard-btn');
const comprehensiveReportBtn = document.getElementById('comprehensive-report-btn');
const comprehensiveReportModal = document.getElementById('comprehensive-report-modal');
const comprehensiveReportMessage = document.getElementById('comprehensive-report-message');
const comprehensiveReportDriverList = document.getElementById('comprehensive-report-driver-list');
const selectAllComprehensiveDriversCheckbox = document.getElementById('select-all-comprehensive-drivers');
const generateComprehensiveReportSubmitBtn = document.getElementById('generate-comprehensive-report-submit');
const compareWithPreviousMonthCheckbox = document.getElementById('compare-with-previous-month'); // Added checkbox element

// Function to display messages
function showMessage(element, message, type = 'error') {
    element.textContent = message;
    element.className = `message ${type}`;
    element.style.display = 'block';
}

// Function to hide messages
function hideMessage(element) {
    element.style.display = 'none';
    element.textContent = '';
}

// Function to display current month and year
function displayCurrentDate() {
    const now = new Date();
    const month = now.toLocaleString('ar-SA', { month: 'long' });
    const year = now.getFullYear();
    if (currentMonthYearEl) {
        currentMonthYearEl.textContent = `${month} ${year}`;
    }
}

// Function to format currency
function formatCurrency(amount) {
    const value = parseFloat(amount);
    return isNaN(value) ? '0.00 ريال' : `${value.toFixed(2)} ريال`;
}

// --- Added: Function to get month name ---
function getMonthName(monthNumber) {
    const names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"];
    const index = parseInt(monthNumber, 10) - 1;
    return names[index] || `شهر ${monthNumber}`;
}
// --- End Added Function ---

// --- Added: Function to translate expense type ---
function translateExpenseType(type, isDeduction) {
    switch (type) {
        case 'advance': return 'سلفة';
        case 'fine': return 'مخالفة';
        case 'accident': return 'حادث';
        case 'commission': return 'عمولة';
        case 'payment': return 'دفعة راتب';
        // Add other types as needed
        default: return type || 'غير معروف';
    }
}
// --- End Added Function ---

// Function to fetch and display summary statistics
async function loadSummaryData() {
    // Reset placeholders
    if (totalDriversEl) totalDriversEl.textContent = '0';
    if (totalDefaultSalariesEl) totalDefaultSalariesEl.textContent = formatCurrency(0);
    if (totalPaidEl) totalPaidEl.textContent = formatCurrency(0);
    if (totalRemainingEl) totalRemainingEl.textContent = formatCurrency(0);
    if (totalBankExpensesEl) totalBankExpensesEl.textContent = formatCurrency(0); // Placeholder for non-deducted expenses
    if (totalDeductedExpensesEl) totalDeductedExpensesEl.textContent = formatCurrency(0);
    if (totalDeductionsEl) totalDeductionsEl.textContent = formatCurrency(0); // Central card

    const selectedMonthId = sessionStorage.getItem('selectedBudgetMonthId');
    let totalDefaultSalaries = 0; // Variable to store the sum

    try {
        // Fetch total drivers count and sum of default salaries (always fetched)
        const [countResult, defaultSalarySumResult] = await Promise.all([
            supabaseClient
                .from('driver_default_settings')
                .select('*', { count: 'exact', head: true }),
            supabaseClient
                .from('driver_default_settings')
                .select('default_base_salary')
        ]);

        // Handle driver count
        const { count, error: countError } = countResult;
        if (countError) {
            console.error('Error fetching driver count:', countError);
        } else {
            if (totalDriversEl) totalDriversEl.textContent = count || 0;
        }

        // Handle default salary sum
        const { data: defaultSalariesData, error: defaultSalaryError } = defaultSalarySumResult;
        if (defaultSalaryError) {
            console.error('Error fetching default salaries:', defaultSalaryError);
            if (totalDefaultSalariesEl) totalDefaultSalariesEl.textContent = 'خطأ';
        } else {
            // Calculate and store the sum of default salaries
            totalDefaultSalaries = (defaultSalariesData || []).reduce((sum, setting) => {
                return sum + (parseFloat(setting.default_base_salary) || 0);
            }, 0);
            if (totalDefaultSalariesEl) {
                totalDefaultSalariesEl.textContent = formatCurrency(totalDefaultSalaries);
            }
        }

        // --- Fetch Monthly Data if month is selected ---
        if (selectedMonthId) {
            console.log(`Fetching monthly summary data for month ID: ${selectedMonthId}`);

            // Fetch ONLY expenses for the selected month
            const monthlyExpensesRes = await supabaseClient
                .from('driver_expenses')
                .select('total_amount, is_deduction')
                .eq('budget_month_id', selectedMonthId);

            // Calculate totals from monthly expenses
            let totalDeductions = 0;
            let totalNonDeductedExpenses = 0; // Represents "مصاريف بنكية"
            if (monthlyExpensesRes.error) {
                console.error('Error fetching monthly expenses:', monthlyExpensesRes.error);
                 showMessage(listMessageEl, `خطأ في جلب بيانات المصروفات الشهرية: ${monthlyExpensesRes.error.message}`, 'error');
            } else {
                (monthlyExpensesRes.data || []).forEach(expense => {
                    const amount = parseFloat(expense.total_amount) || 0;
                    if (expense.is_deduction) {
                        totalDeductions += amount;
                    } else {
                        // Assuming non-deducted expenses are "Bank Expenses"
                        totalNonDeductedExpenses += amount;
                    }
                });
            }

            // --- New Calculation for "Total Paid" ---
            const totalPaid = totalDeductions + totalNonDeductedExpenses;
            // --- End New Calculation ---

            // --- Updated Calculation for "Total Remaining for Month" ---
            // Start with (Default Salaries - Deductions), then subtract Total Paid
            const calculatedRemaining = (totalDefaultSalaries - totalDeductions) - totalPaid;
            // --- End Updated Calculation ---

            // Update Monthly Summary Cards
            if (totalPaidEl) totalPaidEl.textContent = formatCurrency(totalPaid); // <-- Use the new calculated value
            if (totalRemainingEl) totalRemainingEl.textContent = formatCurrency(calculatedRemaining); // <-- Use the new calculated value
            if (totalDeductionsEl) totalDeductionsEl.textContent = formatCurrency(totalDeductions); // Central card
            if (totalDeductedExpensesEl) totalDeductedExpensesEl.textContent = formatCurrency(totalDeductions); // "مصاريف مخصومة" card
            if (totalBankExpensesEl) totalBankExpensesEl.textContent = formatCurrency(totalNonDeductedExpenses); // "مصاريف بنكية" card (non-deducted)

        } else {
            console.log('No month selected. Monthly summary cards will show 0.');
            // Ensure monthly cards are explicitly zero if no month is selected
            if (totalPaidEl) totalPaidEl.textContent = formatCurrency(0); // <-- Set to 0 if no month
            // --- Update: Calculate remaining based on default salaries even if no month selected ---
            const calculatedRemainingNoMonth = totalDefaultSalaries - 0; // No deductions if no month
            if (totalRemainingEl) totalRemainingEl.textContent = formatCurrency(calculatedRemainingNoMonth);
            // --- End Update ---
            if (totalBankExpensesEl) totalBankExpensesEl.textContent = formatCurrency(0);
            if (totalDeductedExpensesEl) totalDeductedExpensesEl.textContent = formatCurrency(0);
            if (totalDeductionsEl) totalDeductionsEl.textContent = formatCurrency(0);
        }
        // --- End Fetch Monthly Data ---

    } catch (error) {
        console.error('Error loading summary data:', error);
        showMessage(listMessageEl, `خطأ في تحميل بيانات الملخص: ${error.message}`, 'error');
        // Optionally reset all cards to 'Error' or 0 on failure
    }
}

// --- Modified: Function to fetch and display drivers ---
async function loadDrivers() {
    if (!driverFilesContainer) return;
    driverFilesContainer.innerHTML = '<p class="loading-message">جاري تحميل قائمة السائقين...</p>';
    hideMessage(listMessageEl);

    const selectedMonthId = sessionStorage.getItem('selectedBudgetMonthId');
    const selectedMonthNumber = sessionStorage.getItem('selectedBudgetMonthNumber'); // Get month number
    const selectedYearNumber = sessionStorage.getItem('selectedBudgetYearNumber'); // Get year number
    console.log("Selected Month ID for checking closure:", selectedMonthId);

    // --- Fetch global month closure status ---
    let isSelectedMonthGloballyClosed = false;
    if (selectedMonthId) {
        try {
            const { data: monthStatusData, error: monthStatusError } = await supabaseClient
                .from('budget_months')
                .select('is_closed')
                .eq('id', selectedMonthId)
                .single();

            if (monthStatusError && monthStatusError.code !== 'PGRST116') { // Ignore 'No rows found' error
                console.error("Error fetching selected month status:", monthStatusError);
            } else if (monthStatusData) {
                isSelectedMonthGloballyClosed = monthStatusData.is_closed;
                console.log(`Selected month (${selectedMonthId}) is globally closed: ${isSelectedMonthGloballyClosed}`);
            }
        } catch (err) {
            console.error("Exception fetching selected month status:", err);
        }
    }
    // --- End Fetch global month closure status ---

    // --- Fetch driver monthly closings data for the selected month ---
    let monthlyClosingsDataMap = new Map();
    if (selectedMonthId) {
        try {
            const { data: closingsData, error: closingsError } = await supabaseClient
                .from('driver_monthly_closings')
                .select('driver_id, base_salary_at_closing')
                .eq('budget_month_id', selectedMonthId);

            if (closingsError) {
                console.error("Error fetching driver monthly closings:", closingsError);
            } else if (closingsData) {
                closingsData.forEach(closing => {
                    monthlyClosingsDataMap.set(closing.driver_id, {
                        base_salary_at_closing: closing.base_salary_at_closing
                    });
                });
                console.log("Fetched driver monthly closings data for selected month:", monthlyClosingsDataMap);
            }
        } catch (err) {
            console.error("Exception fetching driver monthly closings:", err);
        }
    }
    // --- End Fetch driver monthly closings data ---

    try {
        // Fetch driver default settings and driver names
        const { data: driverSettingsList, error: settingsError } = await supabaseClient
            .from('driver_default_settings')
            .select(`
                driver_id,
                default_base_salary,
                drivers!inner ( name )
            `);

        if (settingsError) throw settingsError;

        const validDriversData = driverSettingsList.filter(setting => setting.drivers); 
        const sortedDrivers = validDriversData.sort((a, b) => {
            const nameA = a.drivers.name.toLowerCase();
            const nameB = b.drivers.name.toLowerCase();
            if (nameA < nameB) return -1;
            if (nameA > nameB) return 1;
            return 0;
        });

        if (sortedDrivers && sortedDrivers.length > 0) {
            driverFilesContainer.innerHTML = '';
            sortedDrivers.forEach(setting => {
                const driverId = setting.driver_id;
                const driverName = setting.drivers.name;
                const defaultSalaryFromSettings = setting.default_base_salary || 0;

                const monthlyClosingInfo = monthlyClosingsDataMap.get(driverId);                const isDriverIndividuallyClosedForMonth = monthlyClosingInfo !== undefined;

                // Determines if the card looks "closed" (greyed out, lock icon)
                const isCardAppearanceClosed = isSelectedMonthGloballyClosed || isDriverIndividuallyClosedForMonth;

                let salaryForDisplay = defaultSalaryFromSettings;
                if (monthlyClosingInfo && monthlyClosingInfo.base_salary_at_closing !== null && monthlyClosingInfo.base_salary_at_closing !== undefined) {
                    salaryForDisplay = monthlyClosingInfo.base_salary_at_closing;
                }

                const driverCard = document.createElement('div');
                driverCard.classList.add('driver-card');
                driverCard.dataset.driverId = driverId;
                driverCard.dataset.driverName = driverName;

                if (isCardAppearanceClosed) {
                    driverCard.classList.add('closed');
                }

                let cardHTML = '';

                if (isCardAppearanceClosed) {
                    cardHTML += `<div class="lock-icon"><i class="fas fa-lock"></i></div>`;
                }

                cardHTML += `
                    <div class="driver-icon"><i class="fas fa-user"></i></div>
                    <h4 class="driver-name">${driverName}</h4>
                    <p class="driver-salary">الراتب: ${formatCurrency(salaryForDisplay)}</p>
                `;

                const showReopenButton = selectedMonthId && !isSelectedMonthGloballyClosed && isDriverIndividuallyClosedForMonth;

                if (showReopenButton) {
                    cardHTML += `
                        <button class="reopen-driver-btn" data-driver-id="${driverId}" data-month-id="${selectedMonthId}" data-driver-name="${driverName}">
                            <i class="fas fa-unlock-alt"></i> إعادة فتح الشهر للسائق
                        </button>
                    `;
                }

                driverCard.innerHTML = cardHTML;

                driverCard.addEventListener('click', async (event) => {
                    if (event.target.closest('.reopen-driver-btn')) {
                        return; // Action handled by the button's own listener
                    }

                    if (isSelectedMonthGloballyClosed) {
                        showMessage(listMessageEl, 'هذا الشهر مغلق بشكل عام ولا يمكن عرض أو تعديل بيانات السائقين.', 'info');
                        return;
                    }

                    if (isDriverIndividuallyClosedForMonth) {
                        // If individually closed, and not globally closed, the reopen button should be present.
                        // Clicking the card itself should prompt to use the button.
                        showMessage(listMessageEl, `حساب السائق ${driverName} مغلق لهذا الشهر. استخدم زر "إعادة فتح الشهر" إذا كنت ترغب في تعديل البيانات.`, 'info');
                        return;
                    }

                    // If none of the above, proceed to driver financials
                    sessionStorage.setItem('selectedDriverId', driverId);
                    sessionStorage.setItem('selectedDriverName', driverName);
                    window.location.href = '../driver_financials/driver_financials.html';
                });

                if (showReopenButton) {
                    const reopenBtn = driverCard.querySelector('.reopen-driver-btn');
                    if (reopenBtn) {
                        reopenBtn.addEventListener('click', (event) => {
                            event.stopPropagation(); // Prevent card click listener
                            const dId = reopenBtn.dataset.driverId;
                            const mId = reopenBtn.dataset.monthId;
                            const dName = reopenBtn.dataset.driverName;
                            reopenDriverMonth(dId, mId, dName);
                        });
                    }
                }

                driverFilesContainer.appendChild(driverCard);
            });
        } else {
            driverFilesContainer.innerHTML = '<p class="loading-message">لم يتم العثور على سائقين لديهم إعدادات افتراضية.</p>';
        }

    } catch (error) {
        console.error('Error loading drivers:', error);
        driverFilesContainer.innerHTML = ''; 
        let errorMessage = `حدث خطأ أثناء تحميل السائقين: ${error.message}`;
        if (error.message.includes("relation \"drivers\" does not exist")) {
             errorMessage = "خطأ في الاتصال بقاعدة البيانات أو جدول السائقين غير موجود. يرجى مراجعة مسؤول النظام.";
        } else if (error.message.includes("JWT")) {
             errorMessage = "جلسة المستخدم غير صالحة أو منتهية. يرجى تسجيل الخروج ثم الدخول مرة أخرى.";
        }
        showMessage(listMessageEl, errorMessage, 'error');
    }
}

// --- Added: Function to reopen a driver's month ---

// --- Added: Function to update subsequent monthly closings after reopening a month ---
async function updateSubsequentMonthlyClosings(driverId, reopenedMonthId, restoredBalanceForReopenedMonth) {
    console.log(`Starting cascade update for driver ${driverId} from month ${reopenedMonthId} with restored balance ${restoredBalanceForReopenedMonth}`);
    let lastCalculatedBalance = restoredBalanceForReopenedMonth;
    // This variable will hold the final balance of the last processed month in the cascade,
    // or the restoredBalanceForReopenedMonth if no subsequent months are processed.
    let finalBalanceFromCascade = restoredBalanceForReopenedMonth;

    try {
        // 1. Fetch the budget_month_order for the reopened month
        const { data: reopenedMonthOrderData, error: reopenedMonthOrderError } = await supabaseClient
            .from('budget_months')
            .select('budget_month_order')
            .eq('id', reopenedMonthId)
            .single();

        if (reopenedMonthOrderError || !reopenedMonthOrderData) {
            console.error(`Error fetching reopened month order for ID ${reopenedMonthId}:`, reopenedMonthOrderError);
            return { success: false, error: `Failed to fetch reopened month order: ${reopenedMonthOrderError?.message || 'No data returned.'}`, finalBalance: null };
        }
        const reopenedMonthOrder = reopenedMonthOrderData.budget_month_order;
        console.log(`Reopened month ID ${reopenedMonthId} has order ${reopenedMonthOrder}`);

        // 2. Fetch all subsequent monthly closings for the driver, ordered chronologically
        // Ensure to select all necessary fields from driver_monthly_closings and the budget_month_order
        const { data: subsequentClosings, error: fetchSubsequentError } = await supabaseClient
            .from('driver_monthly_closings')
            .select(`
                id, budget_month_id,
                previous_balance_at_closing, base_salary_at_closing,
                total_commissions_at_closing, total_deductions_at_closing,
                total_payments_at_closing, final_balance_at_closing,
                budget_months!inner (budget_month_order)
            `)
            .eq('driver_id', driverId)
            .gt('budget_months.budget_month_order', reopenedMonthOrder)
            .order('budget_months(budget_month_order)', { ascending: true });

        if (fetchSubsequentError) {
            console.error('Error fetching subsequent closings:', fetchSubsequentError);
            return { success: false, error: `Failed to fetch subsequent closings: ${fetchSubsequentError.message}`, finalBalance: null };
        }

        if (!subsequentClosings || subsequentClosings.length === 0) {
            console.log('No subsequent closings found to update for this driver after the reopened month.');
            // If no subsequent months, the final balance to set for the driver is the restored balance of the reopened month.
            return { success: true, error: null, finalBalance: restoredBalanceForReopenedMonth };
        }

        console.log(`Found ${subsequentClosings.length} subsequent closings to update.`);

        for (const closing of subsequentClosings) {
            const currentProcessingMonthId = closing.budget_month_id;
            console.log(`Processing subsequent month ID: ${currentProcessingMonthId} (Order: ${closing.budget_months.budget_month_order}). Previous month's final balance (new PBoC): ${lastCalculatedBalance}`);

            const newPreviousBalanceAtClosing = lastCalculatedBalance;
            const baseSalary = closing.base_salary_at_closing || 0;
            const commissions = closing.total_commissions_at_closing || 0;
            const deductions = closing.total_deductions_at_closing || 0;
            const payments = closing.total_payments_at_closing || 0;

            const newTotalDue = newPreviousBalanceAtClosing + baseSalary + commissions - deductions;
            const newFinalBalance = newTotalDue - payments;

            console.log(`Month ${currentProcessingMonthId} - Old PBoC: ${closing.previous_balance_at_closing}, New PBoC: ${newPreviousBalanceAtClosing}`);
            console.log(`Month ${currentProcessingMonthId} - Old FBaC: ${closing.final_balance_at_closing}, New FBaC: ${newFinalBalance}`);

            // Update driver_monthly_closings for the current iterated month
            const { error: updateClosingError } = await supabaseClient
                .from('driver_monthly_closings')
                .update({
                    previous_balance_at_closing: newPreviousBalanceAtClosing,
                    final_balance_at_closing: newFinalBalance
                })
                .eq('id', closing.id); // Use the primary key 'id' of the driver_monthly_closings record

            if (updateClosingError) {
                console.error(`Error updating driver_monthly_closings for month ${currentProcessingMonthId}:`, updateClosingError);
                return { success: false, error: `Failed to update closing for month ${currentProcessingMonthId}: ${updateClosingError.message}`, finalBalance: null };
            }
            console.log(`Updated driver_monthly_closings for month ${currentProcessingMonthId}.`);

            // Update corresponding driver_salaries record
            const { error: updateSalaryError } = await supabaseClient
                .from('driver_salaries')
                .update({
                    previous_month_balance: newPreviousBalanceAtClosing,
                    total_due: newTotalDue,
                    remaining_amount: newFinalBalance
                })
                .eq('driver_id', driverId)
                .eq('budget_month_id', currentProcessingMonthId);

            if (updateSalaryError) {
                console.warn(`Warning: Error updating driver_salaries for month ${currentProcessingMonthId}:`, updateSalaryError);
                // Depending on requirements, this could be a critical error.
                // For now, returning as failure to ensure data integrity.
                return { success: false, error: `Failed to update salary for month ${currentProcessingMonthId}: ${updateSalaryError.message}`, finalBalance: null };
            }
            console.log(`Updated driver_salaries for month ${currentProcessingMonthId}.`);

            lastCalculatedBalance = newFinalBalance; 
        }

        // After the loop, lastCalculatedBalance holds the final balance of the *last processed subsequent month*.
        finalBalanceFromCascade = lastCalculatedBalance;
        console.log(`Cascade update completed. Final balance from cascade: ${finalBalanceFromCascade}`);
        return { success: true, error: null, finalBalance: finalBalanceFromCascade };

    } catch (error) {
        console.error('General error in updateSubsequentMonthlyClosings:', error);
        return { success: false, error: `General error: ${error.message}`, finalBalance: null };
    }
}
// --- End Added Function ---

async function reopenDriverMonth(driverId, monthId, driverName) {
    const selectedMonthElement = document.getElementById('month-year');
    const selectedMonthText = selectedMonthElement ? selectedMonthElement.textContent : 'البيانات الحالية';

    const confirmation = await Swal.fire({
        title: `إعادة فتح شهر ${selectedMonthText}`,
        html: `هل أنت متأكد أنك تريد إعادة فتح الشهر مالياً للسائق <strong>${driverName}</strong>؟<br><small>سيتم حذف سجل الإغلاق المالي لهذا السائق لهذا الشهر، وكذلك الراتب النهائي المسجل له، وسيتم استرجاع رصيده إلى ما قبل عملية الإغلاق.</small>`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، قم بإعادة الفتح',
        cancelButtonText: 'إلغاء'
    });

    if (confirmation.isConfirmed) {
        try {
            Swal.fire({
                title: 'جاري إعادة فتح الشهر...',
                text: 'الرجاء الانتظار.',
                icon: 'info',
                allowOutsideClick: false,
                allowEscapeKey: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // 1. Fetch the previous_balance_at_closing from driver_monthly_closings
            const { data: closingData, error: fetchClosingError } = await supabaseClient
                .from('driver_monthly_closings')
                .select('previous_balance_at_closing')
                .eq('driver_id', driverId)
                .eq('budget_month_id', monthId)
                .single();

            if (fetchClosingError) {
                console.error('Error fetching closing data:', fetchClosingError);
                Swal.fire('خطأ!', `حدث خطأ أثناء محاولة جلب بيانات الإغلاق: ${fetchClosingError.message}`, 'error');
                return;
            }

            if (!closingData) {
                Swal.fire('خطأ!', 'لم يتم العثور على سجل الإغلاق لهذا السائق والشهر المحدد. لا يمكن المتابعة.', 'error');
                return;
            }
            const previousBalanceToRestore = closingData.previous_balance_at_closing;

            // 2. Delete the driver's monthly closing record
            const { error: deleteClosingError } = await supabaseClient
                .from('driver_monthly_closings')
                .delete()
                .eq('driver_id', driverId)
                .eq('budget_month_id', monthId);

            if (deleteClosingError) {
                console.error('Error deleting driver monthly closing:', deleteClosingError);
                Swal.fire('خطأ!', `حدث خطأ أثناء حذف سجل الإغلاق الشهري: ${deleteClosingError.message}`, 'error');
                return; 
            }

            // 3. Delete the driver's salary record for that month
            const { error: deleteSalaryError } = await supabaseClient
                .from('driver_salaries')
                .delete()
                .eq('driver_id', driverId)
                .eq('budget_month_id', monthId); 

            if (deleteSalaryError) {
                console.error('Error deleting driver salary:', deleteSalaryError);
                // Log the error and inform the user, but proceed with balance update as it's critical.
                Swal.fire('تحذير!', `تم حذف سجل الإغلاق، ولكن حدث خطأ أثناء حذف سجل الراتب: ${deleteSalaryError.message}. يرجى التحقق من البيانات.`, 'warning');
            }

            // 4. Update the driver's current_balance in driver_default_settings
            const { error: updateBalanceError } = await supabaseClient
                .from('driver_default_settings')
                .update({ current_balance: previousBalanceToRestore })
                .eq('driver_id', driverId);

            if (updateBalanceError) {
                console.error('Error updating driver balance:', updateBalanceError);
                Swal.fire('خطأ حرج!', `تم حذف سجلات الإغلاق والراتب، ولكن فشلت عملية تحديث رصيد السائق: ${updateBalanceError.message}. قد يكون الرصيد غير صحيح. يرجى مراجعة البيانات فوراً والقيام بالتعديل اليدوي إذا لزم الأمر.`, 'error');
                return;
            }

            // 5. Update subsequent monthly closings
            const { success: cascadeSuccess, error: cascadeError, finalBalance: newCurrentBalance } = await updateSubsequentMonthlyClosings(driverId, monthId, previousBalanceToRestore);

            if (!cascadeSuccess) {
                console.error('Error during cascading update:', cascadeError);
                Swal.fire('تحذير!', `تمت إعادة فتح الشهر بنجاح وتحديث الرصيد الأولي، ولكن حدث خطأ أثناء تحديث الأشهر اللاحقة: ${cascadeError}. يرجى مراجعة البيانات.`, 'warning');
                // Proceed to success message for the reopened month, but warn about cascade
            } else {
                 // If cascade was successful and a new final balance was determined, update driver_default_settings
                if (newCurrentBalance !== null && newCurrentBalance !== undefined) {
                    const { error: finalBalanceUpdateError } = await supabaseClient
                        .from('driver_default_settings')
                        .update({ current_balance: newCurrentBalance })
                        .eq('driver_id', driverId);

                    if (finalBalanceUpdateError) {
                        console.error('Error updating driver current_balance after cascade:', finalBalanceUpdateError);
                        Swal.fire('تحذير!', `تمت إعادة فتح الشهر وتحديث الأشهر اللاحقة، ولكن فشل تحديث الرصيد النهائي للسائق: ${finalBalanceUpdateError.message}.`, 'warning');
                    }
                }
            }


            Swal.fire(
                'تم بنجاح!',
                `تمت إعادة فتح الشهر المالي للسائق ${driverName} بنجاح، وتم استرجاع رصيده السابق.`,
                'success'
            );

            loadDrivers();
            loadSummaryData();

        } catch (error) {
            console.error('Error reopening driver month:', error);
            Swal.fire('خطأ!', `حدث خطأ عام أثناء محاولة إعادة فتح الشهر: ${error.message}`, 'error');
        }
    }
}
// --- End Added Function ---

// --- Added: Function to fetch data for the report ---
async function fetchDriverMonthDataForReport(driverId, monthId) {
    if (!supabaseClient || !driverId || !monthId) {
        throw new Error("بيانات ناقصة لجلب تقرير السائق.");
    }
    try {
        // Fetch salary record, expenses, and default settings in parallel
        const [salaryRes, expensesRes, settingsRes] = await Promise.all([
            supabaseClient
                .from('driver_salaries') // Fetch the specific salary record for the closed month
                .select('*')
                .eq('driver_id', driverId)
                .eq('budget_month_id', monthId)
                .maybeSingle(), // Use maybeSingle() to handle cases where the record might not exist gracefully
            supabaseClient
                .from('driver_expenses') // Fetch expenses for the month
                .select('*')
                .eq('driver_id', driverId)
                .eq('budget_month_id', monthId)
                .order('expense_date', { ascending: true }),
            supabaseClient
                .from('driver_default_settings') // Fetch default settings for fallback salary AND balance
                .select('default_base_salary, current_balance') // Fetch both fields
                .eq('driver_id', driverId)
                .maybeSingle() // Changed from .single() to .maybeSingle() to prevent 406 error on duplicates
        ]);

        // Check for critical errors (expenses and settings fetch)
        if (expensesRes.error) {
            console.error("Expenses fetch error:", expensesRes.error);
            throw new Error(`خطأ في جلب حركات المصروفات: ${expensesRes.error.message}`);
        }
        if (settingsRes.error) {
             console.error("Default Settings fetch error:", settingsRes.error);
             // If settings are missing OR multiple rows were found (maybeSingle returns null), log a warning
             // PGRST116 is 'No rows found'
             if (settingsRes.error.code === 'PGRST116' || settingsRes.data === null) {
                 console.warn(`No unique default settings found for driver ${driverId}. Report might use 0 as fallback salary/balance. Error: ${settingsRes.error?.message}`);
             } else {
                // For other errors (like network issues)
                throw new Error(`خطأ في جلب الإعدادات الافتراضية: ${settingsRes.error.message}`);
             }
        }
        // Check for salary record fetch error (non-critical if maybeSingle is used)
        if (salaryRes.error) {
             console.error("Salary record fetch error:", salaryRes.error);
             // Log the error but proceed, as we might use default values
        }


        return {
            salaryRecord: salaryRes.data, // Might be null
            transactions: expensesRes.data || [],
            // Use settingsRes.data which might be null if no unique row found
            defaultSalary: settingsRes.data?.default_base_salary ?? 0,
            currentBalanceFromSettings: settingsRes.data?.current_balance ?? 0
        };
    } catch (error) {
        console.error("Error fetching report data:", error);
        throw error; // Re-throw to be caught by the calling function
    }
}

// --- Added: Function to format the report as HTML ---
function formatReportAsHTML(driverName, monthNumber, yearNumber, reportData) {
    const { salaryRecord, transactions, defaultSalary, currentBalanceFromSettings } = reportData; // Destructure new balance
    const monthName = getMonthName(monthNumber);

    // Separate transactions
    const commissions = transactions.filter(tx => tx.expense_type === 'commission');
    const deductions = transactions.filter(tx => tx.is_deduction);
    const payments = transactions.filter(tx => tx.expense_type === 'payment');

    // --- Updated Base Salary Logic ---
    const baseSalary = salaryRecord?.base_salary ?? defaultSalary ?? 0;
    // --- End Updated Base Salary Logic ---

    // --- Updated Previous Balance Logic ---
    // Prioritize balance from the specific month's record, fallback to current balance from settings
    const prevBalance = salaryRecord?.previous_month_balance ?? currentBalanceFromSettings ?? 0;
    // --- End Updated Previous Balance Logic ---

    // Recalculate totalDue and other derived values using the determined baseSalary and prevBalance
    const totalCommissions = commissions.reduce((sum, tx) => sum + (parseFloat(tx.total_amount) || 0), 0);
    const totalDeductions = deductions.reduce((sum, tx) => sum + (parseFloat(tx.total_amount) || 0), 0);
    const totalPayments = payments.reduce((sum, tx) => sum + (parseFloat(tx.total_amount) || 0), 0);

    // Use salaryRecord values if available, otherwise calculate
    const totalDue = salaryRecord?.total_due ?? (prevBalance + baseSalary + totalCommissions - totalDeductions);
    const totalPaid = salaryRecord?.paid_amount ?? totalPayments;
    const finalBalance = salaryRecord?.remaining_amount ?? (totalDue - totalPaid);

    let html = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير حساب السائق: ${driverName} - ${monthName} ${yearNumber}</title>
            <style>
                body {
                    font-family: 'Tajawal', sans-serif;
                    direction: rtl;
                    padding: 20px;
                    background-color: #f9f9f9; /* Lighter background */
                    color: #333;
                    font-size: 14px; /* Base font size */
                }
                .report-container {
                    background-color: #fff;
                    padding: 30px;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
                    max-width: 800px;
                    margin: 20px auto;
                    border: 1px solid #e0e0e0;
                }
                .report-header {
                    text-align: center;
                    margin-bottom: 30px;
                    padding-bottom: 15px;
                    border-bottom: 1px solid #eee;
                }
                .report-header h1 {
                    font-size: 1.8em;
                    color: #3c4b64; /* Primary color */
                    margin-bottom: 10px;
                }
                .report-header p {
                    font-size: 1.1em;
                    color: #555;
                }
                .report-section {
                    margin-bottom: 30px;
                }
                .report-section h2 {
                    font-size: 1.4em;
                    color: #698264; /* Accent color */
                    border-bottom: 2px solid #e0e0e0;
                    padding-bottom: 8px;
                    margin-bottom: 15px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }
                .report-section h2 i {
                    font-size: 0.9em; /* Slightly smaller icon */
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 10px 12px; /* Adjusted padding */
                    text-align: right;
                    vertical-align: middle;
                }
                th {
                    background-color: #f8f9fa;
                    font-weight: 600; /* Slightly bolder */
                    color: #495057;
                }
                /* Summary Table Specific Styles */
                .summary-table td:first-child {
                    font-weight: 600;
                    width: 40%; /* Adjust width for label */
                    color: #555;
                }
                .summary-table td:last-child {
                    text-align: left; /* Align amount left */
                    direction: ltr; /* Ensure LTR for currency */
                    font-weight: bold;
                    font-size: 1.1em;
                }
                /* Transactions Table Specific Styles */
                .transactions-table th:last-child,
                .transactions-table td:last-child {
                    text-align: left; /* Align amount left */
                    direction: ltr; /* Ensure LTR for currency */
                    font-weight: 500;
                    width: 150px; /* Fixed width for amount */
                }
                .transactions-table td:nth-child(2) { /* Description column */
                    word-break: break-word; /* Allow long descriptions to wrap */
                }
                .positive { color: #28a745; font-weight: bold; } /* Green for positive */
                .negative { color: #dc3545; font-weight: bold; } /* Red for negative */
                .print-button {
                    display: block;
                    width: 120px; /* Smaller button */
                    margin: 30px auto 0;
                    padding: 8px 12px;
                    background-color: #698264;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                    text-align: center;
                    font-size: 1em;
                    transition: background-color 0.2s;
                }
                .print-button i { margin-left: 5px; }
                .print-button:hover { background-color: #556d53; }
                @media print {
                    body { background-color: #fff; padding: 0; margin: 0; font-size: 12px; } /* Smaller font for print */
                    .report-container { box-shadow: none; border: none; margin: 0; max-width: 100%; padding: 10px;}
                    .print-button { display: none; }
                    h1, h2 { margin-top: 15px; }
                    th, td { padding: 6px 8px; } /* Reduce padding for print */
                }
            </style>
            <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"> <!-- Font Awesome for icons -->
        </head>
        <body>
            <div class="report-container">
                <div class="report-header">
                    <h1>تقرير حساب السائق</h1>
                    <p>
                        <strong>السائق:</strong> ${driverName}<br>
                        <strong>الشهر:</strong> ${monthName} ${yearNumber}
                    </p>
                </div>

                <div class="report-section">
                    <h2><i class="fas fa-file-invoice-dollar"></i> الملخص المالي</h2>
                    <table class="summary-table">
                        <tr><td>الراتب الأساسي المعتمد</td><td>${formatCurrency(baseSalary)}</td></tr>
                        <tr><td>الرصيد السابق</td><td>${formatCurrency(prevBalance)}</td></tr> <!-- Uses updated prevBalance -->
                        <tr><td>إجمالي العمولات (+)</td><td class="positive">${formatCurrency(totalCommissions)}</td></tr>
                        <tr><td>إجمالي الخصومات (-)</td><td class="negative">${formatCurrency(totalDeductions)}</td></tr>
                        <tr><td><strong>الإجمالي المستحق</strong></td><td><strong>${formatCurrency(totalDue)}</strong></td></tr> <!-- Uses updated totalDue -->
                        <tr><td>إجمالي المدفوع (-)</td><td class="negative">${formatCurrency(totalPaid)}</td></tr> <!-- Uses updated totalPaid -->
                        <tr><td><strong>الرصيد النهائي</strong></td><td><strong>${formatCurrency(finalBalance)}</strong></td></tr> <!-- Uses updated finalBalance -->
                    </table>
                </div>

                ${commissions.length > 0 ? `
                <div class="report-section">
                    <h2><i class="fas fa-hand-holding-usd"></i> تفاصيل العمولات</h2>
                    <table class="transactions-table">
                        <thead><tr><th>التاريخ</th><th>الوصف/النوع</th><th>المبلغ</th></tr></thead>
                        <tbody>
                            ${commissions.map(tx => `<tr><td>${tx.expense_date || '---'}</td><td>${tx.commission_type || tx.notes || 'عمولة'}</td><td class="positive">${formatCurrency(tx.total_amount)}</td></tr>`).join('')}
                        </tbody>
                    </table>
                </div>` : ''}

                ${deductions.length > 0 ? `
                <div class="report-section">
                    <h2><i class="fas fa-minus-circle"></i> تفاصيل الخصومات</h2>
                    <table class="transactions-table">
                        <thead><tr><th>التاريخ</th><th>النوع</th><th>الوصف/الملاحظات</th><th>المبلغ</th></tr></thead>
                        <tbody>
                            ${deductions.map(tx => `<tr><td>${tx.expense_date || '---'}</td><td>${translateExpenseType(tx.expense_type, true)}</td><td>${tx.fine_or_accident_type || tx.notes || '---'}</td><td class="negative">${formatCurrency(tx.total_amount)}</td></tr>`).join('')}
                        </tbody>
                    </table>
                </div>` : ''}

                 ${payments.length > 0 ? `
                 <div class="report-section">
                    <h2><i class="fas fa-money-check-alt"></i> تفاصيل الدفعات</h2>
                    <table class="transactions-table">
                        <thead><tr><th>التاريخ</th><th>الوصف/الملاحظات</th><th>المبلغ</th></tr></thead>
                        <tbody>
                            ${payments.map(tx => `<tr><td>${tx.expense_date || '---'}</td><td>${tx.notes || 'دفعة راتب'}</td><td class="negative">${formatCurrency(tx.total_amount)}</td></tr>`).join('')}
                        </tbody>
                    </table>
                 </div>` : ''}

                <button class="print-button" onclick="window.print()"><i class="fas fa-print"></i> طباعة</button>
            </div>
            <!-- Removed Font Awesome script tag, included CSS link in head instead -->
        </body>
        </html>
    `;
    return html;
}

// --- Added: Function to generate and display the report ---
async function generateDriverReport(driverId, driverName, monthId, monthNumber, yearNumber) {
    try {
        const reportData = await fetchDriverMonthDataForReport(driverId, monthId);
        const reportHTML = formatReportAsHTML(driverName, monthNumber, yearNumber, reportData);

        // Open report in a new window/tab
        const reportWindow = window.open('', '_blank');
        if (reportWindow) {
            reportWindow.document.open();
            reportWindow.document.write(reportHTML);
            reportWindow.document.close();
        } else {
            throw new Error("فشل فتح نافذة التقرير. قد يكون متصفحك يمنع النوافذ المنبثقة.");
        }

    } catch (error) {
        console.error("Error generating driver report:", error);
        showMessage(listMessageEl, `فشل إنشاء التقرير: ${error.message}`, 'error');
    }
}

// --- Modal Helper ---
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

// --- Comprehensive Report Logic ---

// Function to open the comprehensive report modal
async function openComprehensiveReportModal() {
    if (!comprehensiveReportModal || !comprehensiveReportDriverList) return;
    comprehensiveReportDriverList.innerHTML = '<p class="loading-message">جاري تحميل السائقين...</p>';
    hideMessage(comprehensiveReportMessage);
    selectAllComprehensiveDriversCheckbox.checked = false; // Reset select all

    const selectedMonthId = sessionStorage.getItem('selectedBudgetMonthId');
    if (!selectedMonthId) {
        showMessage(comprehensiveReportMessage, 'الرجاء تحديد شهر أولاً من لوحة التحكم الرئيسية.', 'error');
        comprehensiveReportModal.style.display = 'block';
        return;
    }

    try {
        // Fetch only active drivers' names and IDs
        const { data: drivers, error } = await supabaseClient
            .from('drivers')
            .select('id, name')
            .eq('is_active', true) // Assuming you only want active drivers
            .order('name', { ascending: true });

        if (error) throw error;

        comprehensiveReportDriverList.innerHTML = ''; // Clear loading message

        if (!drivers || drivers.length === 0) {
            comprehensiveReportDriverList.innerHTML = '<p class="loading-message">لم يتم العثور على سائقين نشطين.</p>';
            selectAllComprehensiveDriversCheckbox.disabled = true;
            generateComprehensiveReportSubmitBtn.disabled = true;
        } else {
            drivers.forEach(driver => {
                const listItem = document.createElement('div');
                listItem.classList.add('list-item');
                listItem.innerHTML = `
                    <input type="checkbox" class="comprehensive-driver-checkbox" value="${driver.id}" id="comp-driver-${driver.id}">
                    <label for="comp-driver-${driver.id}">${driver.name}</label>
                `;
                comprehensiveReportDriverList.appendChild(listItem);
            });
            selectAllComprehensiveDriversCheckbox.disabled = false;
            generateComprehensiveReportSubmitBtn.disabled = false;
        }

        comprehensiveReportModal.style.display = 'block';

    } catch (error) {
        console.error('Error fetching drivers for comprehensive report:', error);
        comprehensiveReportDriverList.innerHTML = '<p class="loading-message error">خطأ في تحميل السائقين.</p>';
        showMessage(comprehensiveReportMessage, `خطأ: ${error.message}`, 'error');
        selectAllComprehensiveDriversCheckbox.disabled = true;
        generateComprehensiveReportSubmitBtn.disabled = true;
        comprehensiveReportModal.style.display = 'block'; // Show modal even with error to display message
    }
}

// Function to handle the generation of the comprehensive report
async function handleGenerateComprehensiveReport() {
    const selectedCheckboxes = comprehensiveReportDriverList.querySelectorAll('.comprehensive-driver-checkbox:checked');
    const selectedDriverIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    const selectedMonthId = sessionStorage.getItem('selectedBudgetMonthId');
    const selectedMonthNumber = parseInt(sessionStorage.getItem('selectedBudgetMonthNumber'), 10);
    const selectedYearNumber = parseInt(sessionStorage.getItem('selectedBudgetYearNumber'), 10);
    const compareWithPrevious = compareWithPreviousMonthCheckbox.checked;

    let previousMonthId = null;
    let previousMonthNumber = null;
    let previousYearNumber = null;

    if (!selectedMonthId || isNaN(selectedMonthNumber) || isNaN(selectedYearNumber)) {
        showMessage(comprehensiveReportMessage, 'لم يتم تحديد الشهر أو السنة بشكل صحيح. لا يمكن إنشاء التقرير.', 'error');
        return;
    }

    if (selectedDriverIds.length === 0) {
        showMessage(comprehensiveReportMessage, 'الرجاء تحديد سائق واحد على الأقل.', 'warning');
        return;
    }

    hideMessage(comprehensiveReportMessage);
    generateComprehensiveReportSubmitBtn.disabled = true;
    generateComprehensiveReportSubmitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإنشاء...';

    try {
        // --- Determine Previous Month ID if comparison is enabled ---
        if (compareWithPrevious) {
            previousMonthNumber = selectedMonthNumber === 1 ? 12 : selectedMonthNumber - 1;
            previousYearNumber = selectedMonthNumber === 1 ? selectedYearNumber - 1 : selectedYearNumber;

            // Fetch the budget_year_id for the previous year if needed
            let previousBudgetYearId = sessionStorage.getItem('selectedBudgetYearId'); // Assume same year initially
            if (selectedMonthNumber === 1) {
                // Use supabaseClient instead of _supabase
                const { data: prevYearData, error: prevYearError } = await supabaseClient
                    .from('budget_years')
                    .select('id')
                    .eq('year_number', previousYearNumber)
                    .single();
                if (prevYearError || !prevYearData) {
                    console.warn(`Previous budget year (${previousYearNumber}) not found.`);
                    // Proceed without comparison if previous year doesn't exist
                } else {
                    previousBudgetYearId = prevYearData.id;
                }
            }

            // Fetch the previous month's ID using the correct year ID
            if (previousBudgetYearId) {
                 // Use supabaseClient instead of _supabase
                const { data: prevMonthData, error: prevMonthError } = await supabaseClient
                    .from('budget_months')
                    .select('id')
                    .eq('budget_year_id', previousBudgetYearId)
                    .eq('month_number', previousMonthNumber)
                    .single();

                if (prevMonthError || !prevMonthData) {
                    showMessage(comprehensiveReportMessage, `لم يتم العثور على بيانات للشهر السابق (${previousMonthNumber}/${previousYearNumber}). سيتم إنشاء التقرير بدون مقارنة.`, 'warning');
                    // Disable comparison flag if previous month not found
                    // compareWithPrevious = false; // Keep compareWithPrevious true to indicate intent, handle missing data in formatting
                } else {
                    previousMonthId = prevMonthData.id;
                    console.log(`Previous month found: ID=${previousMonthId}, Num=${previousMonthNumber}, Year=${previousYearNumber}`);
                }
            } else if (selectedMonthNumber === 1) {
                 showMessage(comprehensiveReportMessage, `لم يتم العثور على السنة المالية السابقة (${previousYearNumber}). سيتم إنشاء التقرير بدون مقارنة.`, 'warning');
            }
        }
        // --- End Determine Previous Month ID ---

        // --- Fetch Data for Selected Drivers ---
        const reportDataPromises = selectedDriverIds.map(async (driverId) => {
            try {
                const currentMonthData = await fetchDriverMonthDataForReport(driverId, selectedMonthId);
                let previousMonthData = null;
                if (compareWithPrevious && previousMonthId) {
                    // Fetch previous month data only if ID exists
                    previousMonthData = await fetchDriverMonthDataForReport(driverId, previousMonthId);
                }
                return {
                    driverId: driverId,
                    driverName: comprehensiveReportDriverList.querySelector(`label[for="comp-driver-${driverId}"]`)?.textContent || `سائق ${driverId}`,
                    currentMonthData: currentMonthData,
                    previousMonthData: previousMonthData // Will be null if comparison disabled or previous month not found/fetched
                };
            } catch (error) {
                console.error(`Failed to fetch data for driver ${driverId}:`, error);
                return { driverId: driverId, error: error.message };
            }
        });

        const allDriversData = await Promise.all(reportDataPromises);
        // --- End Fetch Data ---

        const successfulDriversData = allDriversData.filter(data => !data.error);
        const failedDriversData = allDriversData.filter(data => data.error);

        if (successfulDriversData.length === 0) {
            throw new Error("فشل جلب بيانات جميع السائقين المحددين.");
        }

        // --- Generate HTML Report ---
        const reportHTML = formatComprehensiveReportAsHTML(
            selectedMonthNumber,
            selectedYearNumber,
            successfulDriversData,
            failedDriversData,
            compareWithPrevious && previousMonthId ? { month: previousMonthNumber, year: previousYearNumber } : null // Pass previous month details if comparison happened
        );
        // --- End Generate HTML Report ---

        // Open report in a new window/tab
        const reportWindow = window.open('', '_blank');
        if (reportWindow) {
            reportWindow.document.open();
            reportWindow.document.write(reportHTML);
            reportWindow.document.close();
            closeModal('comprehensive-report-modal');
        } else {
            throw new Error("فشل فتح نافذة التقرير. قد يكون متصفحك يمنع النوافذ المنبثقة.");
        }

        // Show success/warning message
        if (failedDriversData.length > 0) {
             showMessage(listMessageEl, `تم إنشاء التقرير بنجاح، لكن فشل تحميل بيانات ${failedDriversData.length} سائق/سائقين.`, 'warning');
        } else {
             showMessage(listMessageEl, 'تم إنشاء التقرير الشامل بنجاح!', 'success');
        }

    } catch (error) {
        console.error("Error generating comprehensive report:", error);
        showMessage(comprehensiveReportMessage, `فشل إنشاء التقرير الشامل: ${error.message}`, 'error');
    } finally {
        generateComprehensiveReportSubmitBtn.disabled = false;
        generateComprehensiveReportSubmitBtn.innerHTML = '<i class="fas fa-cogs"></i> توليد التقرير';
    }
}


// Function to format the comprehensive report as HTML (Updated for More Analysis & Charts)
function formatComprehensiveReportAsHTML(monthNumber, yearNumber, driversData, failedDriversData = [], previousMonthDetails = null) {
    const monthName = getMonthName(monthNumber);
    const prevMonthName = previousMonthDetails ? getMonthName(previousMonthDetails.month) : null;
    const comparisonEnabled = previousMonthDetails !== null;

    // --- Data Aggregation & Preparation ---
    let currentTotals = { base: 0, prevBal: 0, comm: 0, deduc: 0, due: 0, paid: 0, finalBal: 0 };
    let previousTotals = { base: 0, prevBal: 0, comm: 0, deduc: 0, due: 0, paid: 0, finalBal: 0 };
    let driverDetails = []; // For charts and analysis

    driversData.forEach(data => {
        const { currentMonthData, previousMonthData, driverName } = data;

        // Process Current Month
        const current = processDriverMonthData(currentMonthData);
        currentTotals.base += current.baseSalary;
        currentTotals.prevBal += current.prevBalance;
        currentTotals.comm += current.totalCommissions;
        currentTotals.deduc += current.totalDeductions;
        currentTotals.due += current.totalDue;
        currentTotals.paid += current.totalPaid;
        currentTotals.finalBal += current.finalBalance;

        // Process Previous Month (if comparison enabled and data exists)
        let previous = null;
        if (comparisonEnabled && previousMonthData) {
            previous = processDriverMonthData(previousMonthData);
            previousTotals.base += previous.baseSalary;
            previousTotals.prevBal += previous.prevBalance;
            previousTotals.comm += previous.totalCommissions;
            previousTotals.deduc += previous.totalDeductions;
            previousTotals.due += previous.totalDue;
            previousTotals.paid += previous.totalPaid;
            previousTotals.finalBal += previous.finalBalance;
        }

        driverDetails.push({
            name: driverName,
            current: current,
            previous: previous // Will be null if no comparison data
        });
    });

    // --- Enhanced Analysis ---
    let analysisText = "<ul>";
    analysisText += `<li>إجمالي السائقين في التقرير: ${driverDetails.length}.</li>`;

    // Find drivers with min/max values for the current month
    const findMinMax = (key) => {
        if (driverDetails.length === 0) return { min: { name: 'N/A', value: 0 }, max: { name: 'N/A', value: 0 } };
        let min = { name: driverDetails[0].name, value: driverDetails[0].current[key] };
        let max = { name: driverDetails[0].name, value: driverDetails[0].current[key] };
        driverDetails.slice(1).forEach(d => {
            if (d.current[key] < min.value) min = { name: d.name, value: d.current[key] };
            if (d.current[key] > max.value) max = { name: d.name, value: d.current[key] };
        });
        return { min, max };
    };

    const finalBalanceStats = findMinMax('finalBalance');
    const commissionStats = findMinMax('totalCommissions');
    const deductionStats = findMinMax('totalDeductions'); // Used for "best" driver
    const paidStats = findMinMax('totalPaid');

    analysisText += `<li><strong>الشهر الحالي (${monthName}):</strong><ul>`;
    analysisText += `<li>أعلى رصيد نهائي: ${finalBalanceStats.max.name} (${formatCurrency(finalBalanceStats.max.value)}).</li>`;
    analysisText += `<li>أدنى رصيد نهائي: ${finalBalanceStats.min.name} (${formatCurrency(finalBalanceStats.min.value)}).</li>`;
    analysisText += `<li>أعلى عمولات: ${commissionStats.max.name} (${formatCurrency(commissionStats.max.value)}).</li>`;
    analysisText += `<li>أعلى خصومات: ${deductionStats.max.name} (${formatCurrency(deductionStats.max.value)}).</li>`;
    analysisText += `<li>أقل خصومات (الأفضل): ${deductionStats.min.name} (${formatCurrency(deductionStats.min.value)}).</li>`;
    analysisText += `<li>أعلى مبلغ مدفوع: ${paidStats.max.name} (${formatCurrency(paidStats.max.value)}).</li>`;
    analysisText += `</ul></li>`;


    if (comparisonEnabled) {
        const deductionChange = currentTotals.deduc - previousTotals.deduc;
        const paidChange = currentTotals.paid - previousTotals.paid;
        const balanceChange = currentTotals.finalBal - previousTotals.finalBal;

        // Find drivers with biggest changes
        const findChangeStats = (key) => {
            if (driverDetails.length === 0) return { biggestIncrease: { name: 'N/A', value: 0 }, biggestDecrease: { name: 'N/A', value: 0 } };
            let biggestIncrease = { name: 'N/A', value: -Infinity };
            let biggestDecrease = { name: 'N/A', value: Infinity };

            driverDetails.forEach(d => {
                if (d.previous) { // Only consider drivers with previous data
                    const change = d.current[key] - d.previous[key];
                    if (change > biggestIncrease.value) biggestIncrease = { name: d.name, value: change };
                    if (change < biggestDecrease.value) biggestDecrease = { name: d.name, value: change };
                }
            });
             // Handle cases where no change occurred or only one driver with previous data
            if (biggestIncrease.value === -Infinity) biggestIncrease = { name: 'لا يوجد', value: 0 };
            if (biggestDecrease.value === Infinity) biggestDecrease = { name: 'لا يوجد', value: 0 };

            return { biggestIncrease, biggestDecrease };
        };

        const deductionChangeStats = findChangeStats('totalDeductions');
        const paidChangeStats = findChangeStats('totalPaid');
        const balanceChangeStats = findChangeStats('finalBalance'); // Improvement/Decline

        analysisText += `<li><strong>مقارنة بالشهر السابق (${prevMonthName} ${previousMonthDetails.year}):</strong><ul>`;
        analysisText += `<li>تغير إجمالي الخصومات: <span class="${deductionChange >= 0 ? 'negative' : 'positive'}">${formatCurrency(deductionChange)}</span>. (أكبر زيادة: ${deductionChangeStats.biggestIncrease.name} [${formatCurrency(deductionChangeStats.biggestIncrease.value)}], أكبر نقصان: ${deductionChangeStats.biggestDecrease.name} [${formatCurrency(deductionChangeStats.biggestDecrease.value)}])</li>`;
        analysisText += `<li>تغير إجمالي المدفوع: <span class="${paidChange >= 0 ? 'negative' : 'positive'}">${formatCurrency(paidChange)}</span>. (أكبر زيادة: ${paidChangeStats.biggestIncrease.name} [${formatCurrency(paidChangeStats.biggestIncrease.value)}], أكبر نقصان: ${paidChangeStats.biggestDecrease.name} [${formatCurrency(paidChangeStats.biggestDecrease.value)}])</li>`;
        analysisText += `<li>تغير الرصيد النهائي الإجمالي: <span class="${balanceChange >= 0 ? 'positive' : 'negative'}">${formatCurrency(balanceChange)}</span>. (أكبر تحسن: ${balanceChangeStats.biggestIncrease.name} [${formatCurrency(balanceChangeStats.biggestIncrease.value)}], أكبر تراجع: ${balanceChangeStats.biggestDecrease.name} [${formatCurrency(balanceChangeStats.biggestDecrease.value)}])</li>`;
        analysisText += `</ul></li>`;
    }

    // Find highest overall category value (already present, kept for consistency)
    let highestCategory = { name: 'لا يوجد', value: 0 };
    if (currentTotals.comm > highestCategory.value) highestCategory = { name: 'العمولات', value: currentTotals.comm };
    if (currentTotals.deduc > highestCategory.value) highestCategory = { name: 'الخصومات', value: currentTotals.deduc };
    if (currentTotals.paid > highestCategory.value) highestCategory = { name: 'المدفوعات', value: currentTotals.paid };
    analysisText += `<li>الفئة الأعلى قيمة إجمالية هذا الشهر: ${highestCategory.name} (${formatCurrency(highestCategory.value)}).</li>`;

    analysisText += "</ul>";


    // --- Chart Data Preparation ---
    const chartLabels = driverDetails.map(d => d.name);
    const currentCommissionsData = driverDetails.map(d => d.current.totalCommissions); // New data for chart
    const currentDeductionsData = driverDetails.map(d => d.current.totalDeductions);
    const currentPaidData = driverDetails.map(d => d.current.totalPaid);
    const currentFinalBalanceData = driverDetails.map(d => d.current.finalBalance);
    let previousDeductionsData = comparisonEnabled ? driverDetails.map(d => d.previous?.totalDeductions ?? 0) : [];
    let previousPaidData = comparisonEnabled ? driverDetails.map(d => d.previous?.totalPaid ?? 0) : [];
    let previousFinalBalanceData = comparisonEnabled ? driverDetails.map(d => d.previous?.finalBalance ?? 0) : []; // New data for chart

    // --- HTML Structure ---
    let html = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير السائقين الشامل - ${monthName} ${yearNumber}${comparisonEnabled ? ' (مقارنة مع ' + prevMonthName + ' ' + previousMonthDetails.year + ')' : ''}</title>
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <!-- Include Chart.js -->
            <style>
                /* ... (Existing styles - ensure .report-charts grid handles more charts) ... */
                 body { font-family: 'Tajawal', sans-serif; direction: rtl; padding: 15px; background-color: #f9f9f9; color: #333; font-size: 12px; }
                .report-container { background-color: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.08); max-width: 1400px; /* Wider container */ margin: 15px auto; border: 1px solid #e0e0e0; }
                .report-header { text-align: center; margin-bottom: 25px; padding-bottom: 15px; border-bottom: 1px solid #eee; }
                .report-header h1 { font-size: 1.6em; color: #3c4b64; margin-bottom: 8px; }
                .report-header p { font-size: 1em; color: #555; }
                .report-section { margin-bottom: 25px; } /* Slightly reduced margin */
                .report-section h2 { font-size: 1.3em; color: #698264; border-bottom: 2px solid #e0e0e0; padding-bottom: 6px; margin-bottom: 15px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; table-layout: fixed; }
                th, td { border: 1px solid #ddd; padding: 7px 5px; /* Slightly reduced padding */ text-align: right; vertical-align: middle; word-wrap: break-word; font-size: 0.85em; /* Smaller font */ }
                th { background-color: #f8f9fa; font-weight: 600; color: #495057; }
                .summary-table td:first-child { font-weight: 600; width: 25%; color: #555; }
                .summary-table td:last-child, .details-table td:not(:first-child) { text-align: left; direction: ltr; font-weight: bold; }
                .details-table td:first-child { font-weight: 500; width: 10%; } /* Adjust driver name width */
                .positive { color: #28a745; font-weight: bold; }
                .negative { color: #dc3545; font-weight: bold; }
                .comparison-col { background-color: #f0f8ff; }
                .print-button { display: block; width: 100px; margin: 25px auto 0; padding: 8px 12px; background-color: #698264; color: white; border: none; border-radius: 5px; cursor: pointer; text-align: center; font-size: 0.9em; transition: background-color 0.2s; }
                .print-button i { margin-left: 5px; }
                .print-button:hover { background-color: #556d53; }
                /* Chart and Analysis Styles */
                .report-charts { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin-bottom: 20px; } /* Reduced gap */
                .chart-container { background-color: #f8f9fa; padding: 10px; border-radius: 8px; border: 1px solid #dee2e6; }
                .report-analysis { background-color: #e9ecef; padding: 15px; border-radius: 8px; border: 1px solid #ced4da; margin-top: 20px; }
                .report-analysis h3 { margin-top: 0; margin-bottom: 10px; color: #556d53; font-size: 1.1em; }
                .report-analysis ul { padding-right: 20px; margin: 0; list-style-type: none; /* Remove default bullets */ }
                .report-analysis > ul > li { margin-bottom: 12px; font-size: 0.9em; } /* Space between main points */
                .report-analysis > ul > li > strong { color: var(--primary-dark); }
                .report-analysis ul ul { padding-right: 15px; margin-top: 5px; list-style-type: circle; /* Indented bullets */ }
                .report-analysis ul ul li { margin-bottom: 5px; font-size: 0.95em; }
                @media print {
                    body { background-color: #fff; padding: 0; margin: 0; font-size: 8px; } /* Even smaller for print */
                    .report-container { box-shadow: none; border: none; margin: 0; max-width: 100%; padding: 5px;}
                    .print-button, .report-charts, .report-analysis { display: none; }
                    h1, h2 { margin-top: 8px; font-size: 1.1em; }
                    th, td { padding: 2px 3px; }
                    table { table-layout: auto; }
                }
            </style>
            <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
        </head>
        <body>
            <div class="report-container">
                <div class="report-header">
                    <h1>تقرير السائقين الشامل</h1>
                    <p>الشهر: ${monthName} ${yearNumber} ${comparisonEnabled ? `(مقارنة مع ${prevMonthName} ${previousMonthDetails.year})` : ''}</p>
                </div>

                <!-- Overall Summary Section -->
                <div class="report-section">
                    <h2>الملخص الإجمالي (${driversData.length} سائق/سائقين)</h2>
                    <table class="summary-table">
                        <thead>
                            <tr>
                                <th>البند</th>
                                <th>الشهر الحالي (${monthName})</th>
                                ${comparisonEnabled ? `<th>الشهر السابق (${prevMonthName})</th><th>التغير</th>` : ''}
                            </tr>
                        </thead>
                        <tbody>
                            <!-- ... existing summary rows ... -->
                             <tr><td>إجمالي الرواتب الأساسية</td><td>${formatCurrency(currentTotals.base)}</td> ${comparisonEnabled ? `<td class="comparison-col">${formatCurrency(previousTotals.base)}</td><td class="comparison-col">${formatCurrency(currentTotals.base - previousTotals.base)}</td>` : ''}</tr>
                            <tr><td>إجمالي الأرصدة السابقة</td><td>${formatCurrency(currentTotals.prevBal)}</td> ${comparisonEnabled ? `<td class="comparison-col">${formatCurrency(previousTotals.prevBal)}</td><td class="comparison-col">${formatCurrency(currentTotals.prevBal - previousTotals.prevBal)}</td>` : ''}</tr>
                            <tr><td>إجمالي العمولات (+)</td><td class="positive">${formatCurrency(currentTotals.comm)}</td> ${comparisonEnabled ? `<td class="comparison-col positive">${formatCurrency(previousTotals.comm)}</td><td class="comparison-col">${formatCurrency(currentTotals.comm - previousTotals.comm)}</td>` : ''}</tr>
                            <tr><td>إجمالي الخصومات (-)</td><td class="negative">${formatCurrency(currentTotals.deduc)}</td> ${comparisonEnabled ? `<td class="comparison-col negative">${formatCurrency(previousTotals.deduc)}</td><td class="comparison-col">${formatCurrency(currentTotals.deduc - previousTotals.deduc)}</td>` : ''}</tr>
                            <tr><td><strong>الإجمالي المستحق العام</strong></td><td><strong>${formatCurrency(currentTotals.due)}</strong></td> ${comparisonEnabled ? `<td class="comparison-col"><strong>${formatCurrency(previousTotals.due)}</strong></td><td class="comparison-col"><strong>${formatCurrency(currentTotals.due - previousTotals.due)}</strong></td>` : ''}</tr>
                            <tr><td>إجمالي المدفوع العام (-)</td><td class="negative">${formatCurrency(currentTotals.paid)}</td> ${comparisonEnabled ? `<td class="comparison-col negative">${formatCurrency(previousTotals.paid)}</td><td class="comparison-col">${formatCurrency(currentTotals.paid - previousTotals.paid)}</td>` : ''}</tr>
                            <tr><td><strong>الرصيد النهائي العام</strong></td><td><strong>${formatCurrency(currentTotals.finalBal)}</strong></td> ${comparisonEnabled ? `<td class="comparison-col"><strong>${formatCurrency(previousTotals.finalBal)}</strong></td><td class="comparison-col"><strong>${formatCurrency(currentTotals.finalBal - previousTotals.finalBal)}</strong></td>` : ''}</tr>
                        </tbody>
                    </table>
                    ${failedDriversData.length > 0 ? `<p style="color: red; text-align: center;">فشل تحميل بيانات ${failedDriversData.length} سائق/سائقين.</p>` : ''}
                </div>

                 <!-- Charts Section -->
                <div class="report-section report-charts">
                    <div class="chart-container">
                        <canvas id="finalBalanceChart"></canvas> <!-- Existing -->
                    </div>
                     <div class="chart-container">
                        <canvas id="commissionsChart"></canvas> <!-- New -->
                    </div>
                    ${comparisonEnabled ? `
                    <div class="chart-container">
                        <canvas id="deductionsComparisonChart"></canvas> <!-- Existing -->
                    </div>
                    <div class="chart-container">
                        <canvas id="paidComparisonChart"></canvas> <!-- Existing -->
                    </div>
                     <div class="chart-container">
                        <canvas id="finalBalanceComparisonChart"></canvas> <!-- New -->
                    </div>
                    ` : ''}
                 </div>

                 <!-- Analysis Section -->
                 <div class="report-section report-analysis">
                     <h3><i class="fas fa-lightbulb"></i> تحليل وملاحظات</h3>
                     ${analysisText}
                 </div>


                <!-- Detailed Table Section -->
                <div class="report-section">
                    <h2>التفاصيل لكل سائق</h2>
                    <div class="table-responsive">
                        <table class="details-table">
                             <thead>
                                <tr>
                                    <th rowspan="2">اسم السائق</th>
                                    <th colspan="${comparisonEnabled ? 2 : 1}">الراتب الأساسي</th>
                                    <th colspan="${comparisonEnabled ? 2 : 1}">الرصيد السابق</th>
                                    <th colspan="${comparisonEnabled ? 2 : 1}">العمولات</th>
                                    <th colspan="${comparisonEnabled ? 2 : 1}">الخصومات</th>
                                    <th colspan="${comparisonEnabled ? 2 : 1}">الإجمالي المستحق</th>
                                    <th colspan="${comparisonEnabled ? 2 : 1}">المدفوع</th>
                                    <th colspan="${comparisonEnabled ? 3 : 1}">الرصيد النهائي</th>
                                </tr>
                                ${comparisonEnabled ? `
                                <tr>
                                    <th>الحالي</th><th class="comparison-col">السابق</th>
                                    <th>الحالي</th><th class="comparison-col">السابق</th>
                                    <th>الحالي</th><th class="comparison-col">السابق</th>
                                    <th>الحالي</th><th class="comparison-col">السابق</th>
                                    <th>الحالي</th><th class="comparison-col">السابق</th>
                                    <th>الحالي</th><th class="comparison-col">السابق</th>
                                    <th>الحالي</th><th class="comparison-col">السابق</th>
                                    <th>الحالي</th><th class="comparison-col">السابق</th><th class="comparison-col">التغير</th>
                                </tr>
                                ` : ''}
                            </thead>
                            <tbody>
                                <!-- ... existing detail rows ... -->
                                 ${driverDetails.map(d => `
                                    <tr>
                                        <td>${d.name}</td>
                                        <td>${formatCurrency(d.current.baseSalary)}</td>
                                        ${comparisonEnabled ? `<td class="comparison-col">${formatCurrency(d.previous?.baseSalary ?? 0)}</td>` : ''}
                                        <td>${formatCurrency(d.current.prevBalance)}</td>
                                        ${comparisonEnabled ? `<td class="comparison-col">${formatCurrency(d.previous?.prevBalance ?? 0)}</td>` : ''}
                                        <td class="positive">${formatCurrency(d.current.totalCommissions)}</td>
                                        ${comparisonEnabled ? `<td class="comparison-col positive">${formatCurrency(d.previous?.totalCommissions ?? 0)}</td>` : ''}
                                        <td class="negative">${formatCurrency(d.current.totalDeductions)}</td>
                                        ${comparisonEnabled ? `<td class="comparison-col negative">${formatCurrency(d.previous?.totalDeductions ?? 0)}</td>` : ''}
                                        <td><strong>${formatCurrency(d.current.totalDue)}</strong></td>
                                        ${comparisonEnabled ? `<td class="comparison-col"><strong>${formatCurrency(d.previous?.totalDue ?? 0)}</strong></td>` : ''}
                                        <td class="negative">${formatCurrency(d.current.totalPaid)}</td>
                                        ${comparisonEnabled ? `<td class="comparison-col negative">${formatCurrency(d.previous?.totalPaid ?? 0)}</td>` : ''}
                                        <td><strong>${formatCurrency(d.current.finalBalance)}</strong></td>
                                        ${comparisonEnabled ? `<td class="comparison-col"><strong>${formatCurrency(d.previous?.finalBalance ?? 0)}</td>` : ''}
                                        ${comparisonEnabled ? `<td class="comparison-col"><strong>${formatCurrency(d.current.finalBalance - (d.previous?.finalBalance ?? 0))}</strong></td>` : ''}
                                    </tr>
                                `).join('')}
                                <!-- Failed Driver Rows -->
                                ${failedDriversData.map(failData => {
                                    const driverName = comprehensiveReportDriverList.querySelector(`label[for="comp-driver-${failData.driverId}"]`)?.textContent || `سائق ${failData.driverId}`;
                                    return `<tr><td>${driverName}</td><td colspan="${comparisonEnabled ? 15 : 7}" style="text-align: center; color: red;">فشل تحميل البيانات: ${failData.error || 'خطأ غير معروف'}</td></tr>`;
                                }).join('')}
                            </tbody>
                             <tfoot>
                                <!-- ... existing footer row ... -->
                                 <tr style="background-color: #f8f9fa; font-weight: bold;">
                                    <td>الإجمالي</td>
                                    <td>${formatCurrency(currentTotals.base)}</td>
                                    ${comparisonEnabled ? `<td class="comparison-col">${formatCurrency(previousTotals.base)}</td>` : ''}
                                    <td>${formatCurrency(currentTotals.prevBal)}</td>
                                    ${comparisonEnabled ? `<td class="comparison-col">${formatCurrency(previousTotals.prevBal)}</td>` : ''}
                                    <td class="positive">${formatCurrency(currentTotals.comm)}</td>
                                    ${comparisonEnabled ? `<td class="comparison-col positive">${formatCurrency(previousTotals.comm)}</td>` : ''}
                                    <td class="negative">${formatCurrency(currentTotals.deduc)}</td>
                                    ${comparisonEnabled ? `<td class="comparison-col negative">${formatCurrency(previousTotals.deduc)}</td>` : ''}
                                    <td>${formatCurrency(currentTotals.due)}</td>
                                    ${comparisonEnabled ? `<td class="comparison-col">${formatCurrency(previousTotals.due)}</td>` : ''}
                                    <td class="negative">${formatCurrency(currentTotals.paid)}</td>
                                    ${comparisonEnabled ? `<td class="comparison-col negative">${formatCurrency(previousTotals.paid)}</td>` : ''}
                                    <td>${formatCurrency(currentTotals.finalBal)}</td>
                                    ${comparisonEnabled ? `<td class="comparison-col">${formatCurrency(previousTotals.finalBal)}</td>` : ''}
                                    ${comparisonEnabled ? `<td class="comparison-col">${formatCurrency(currentTotals.finalBal - previousTotals.finalBal)}</td>` : ''}
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>

                <button class="print-button" onclick="window.print()"><i class="fas fa-print"></i> طباعة</button>
            </div>

            <script>
                // Chart Rendering Script
                document.addEventListener('DOMContentLoaded', () => {
                    const ctxBalance = document.getElementById('finalBalanceChart')?.getContext('2d');
                    const ctxCommissions = document.getElementById('commissionsChart')?.getContext('2d'); // New
                    const ctxDeductions = document.getElementById('deductionsComparisonChart')?.getContext('2d');
                    const ctxPaid = document.getElementById('paidComparisonChart')?.getContext('2d');
                    const ctxBalanceComparison = document.getElementById('finalBalanceComparisonChart')?.getContext('2d'); // New
                    const comparisonEnabled = ${comparisonEnabled};

                    const chartLabels = ${JSON.stringify(chartLabels)};
                    const currentFinalBalanceData = ${JSON.stringify(currentFinalBalanceData)};
                    const currentCommissionsData = ${JSON.stringify(currentCommissionsData)}; // New
                    const currentDeductionsData = ${JSON.stringify(currentDeductionsData)};
                    const currentPaidData = ${JSON.stringify(currentPaidData)};
                    const previousDeductionsData = ${JSON.stringify(previousDeductionsData)};
                    const previousPaidData = ${JSON.stringify(previousPaidData)};
                    const previousFinalBalanceData = ${JSON.stringify(previousFinalBalanceData)}; // New

                    const positiveColor = 'rgba(40, 167, 69, 0.6)'; // Green
                    const negativeColor = 'rgba(220, 53, 69, 0.6)'; // Red
                    const primaryColor = 'rgba(54, 162, 235, 0.6)'; // Blue
                    const secondaryColor = 'rgba(255, 159, 64, 0.6)'; // Orange
                    const tertiaryColor = 'rgba(75, 192, 192, 0.6)'; // Teal

                    const getBorderColor = (rgbaColor) => rgbaColor.replace('0.6', '1');

                    // Chart 1: Final Balance (Current Month) - Existing
                    if (ctxBalance) {
                        const barBgColors = currentFinalBalanceData.map(val => val >= 0 ? positiveColor : negativeColor);
                        const barBorderColors = barBgColors.map(getBorderColor);
                        new Chart(ctxBalance, {
                            type: 'bar',
                            data: { labels: chartLabels, datasets: [{ label: 'الرصيد النهائي (الحالي)', data: currentFinalBalanceData, backgroundColor: barBgColors, borderColor: barBorderColors, borderWidth: 1 }] },
                            options: { responsive: true, maintainAspectRatio: false, plugins: { legend: { display: false }, title: { display: true, text: 'الرصيد النهائي (الشهر الحالي)' } }, scales: { y: { beginAtZero: false } } }
                        });
                    }

                    // Chart 2: Commissions (Current Month) - New
                    if (ctxCommissions) {
                         new Chart(ctxCommissions, {
                            type: 'bar',
                            data: { labels: chartLabels, datasets: [{ label: 'العمولات (الحالي)', data: currentCommissionsData, backgroundColor: positiveColor, borderColor: getBorderColor(positiveColor), borderWidth: 1 }] },
                            options: { responsive: true, maintainAspectRatio: false, plugins: { legend: { display: false }, title: { display: true, text: 'إجمالي العمولات (الشهر الحالي)' } }, scales: { y: { beginAtZero: true } } }
                        });
                    }

                    // Chart 3: Deductions Comparison - Existing
                    if (comparisonEnabled && ctxDeductions) {
                         new Chart(ctxDeductions, {
                            type: 'bar',
                            data: { labels: chartLabels, datasets: [ { label: 'الخصومات (السابق)', data: previousDeductionsData, backgroundColor: secondaryColor, borderColor: getBorderColor(secondaryColor), borderWidth: 1 }, { label: 'الخصومات (الحالي)', data: currentDeductionsData, backgroundColor: negativeColor, borderColor: getBorderColor(negativeColor), borderWidth: 1 } ] },
                            options: { responsive: true, maintainAspectRatio: false, plugins: { title: { display: true, text: 'مقارنة إجمالي الخصومات' } }, scales: { y: { beginAtZero: true } } }
                        });
                    }

                     // Chart 4: Paid Amount Comparison - Existing
                    if (comparisonEnabled && ctxPaid) {
                         new Chart(ctxPaid, {
                            type: 'bar',
                            data: { labels: chartLabels, datasets: [ { label: 'المدفوع (السابق)', data: previousPaidData, backgroundColor: primaryColor, borderColor: getBorderColor(primaryColor), borderWidth: 1 }, { label: 'المدفوع (الحالي)', data: currentPaidData, backgroundColor: tertiaryColor, borderColor: getBorderColor(tertiaryColor), borderWidth: 1 } ] },
                            options: { responsive: true, maintainAspectRatio: false, plugins: { title: { display: true, text: 'مقارنة إجمالي المدفوع' } }, scales: { y: { beginAtZero: true } } }
                        });
                    }

                     // Chart 5: Final Balance Comparison - New
                    if (comparisonEnabled && ctxBalanceComparison) {
                         new Chart(ctxBalanceComparison, {
                            type: 'bar',
                            data: { labels: chartLabels, datasets: [ { label: 'الرصيد النهائي (السابق)', data: previousFinalBalanceData, backgroundColor: primaryColor, borderColor: getBorderColor(primaryColor), borderWidth: 1 }, { label: 'الرصيد النهائي (الحالي)', data: currentFinalBalanceData, backgroundColor: positiveColor, borderColor: getBorderColor(positiveColor), borderWidth: 1 } ] },
                            options: { responsive: true, maintainAspectRatio: false, plugins: { title: { display: true, text: 'مقارنة الرصيد النهائي' } }, scales: { y: { beginAtZero: false } } } // Allow negative
                        });
                    }
                });
            </script>
        </body>
        </html>
    `;
    return html;
}

// Helper function to process driver month data (avoids repetition)
function processDriverMonthData(monthData) {
    if (!monthData) return { baseSalary: 0, prevBalance: 0, totalCommissions: 0, totalDeductions: 0, totalPayments: 0, totalDue: 0, totalPaid: 0, finalBalance: 0 };

    const { salaryRecord, transactions, defaultSalary, currentBalanceFromSettings } = monthData;
    const commissions = transactions.filter(tx => tx.expense_type === 'commission');
    const deductions = transactions.filter(tx => tx.is_deduction);
    const payments = transactions.filter(tx => tx.expense_type === 'payment');

    const baseSalary = salaryRecord?.base_salary ?? defaultSalary ?? 0;
    const prevBalance = salaryRecord?.previous_month_balance ?? currentBalanceFromSettings ?? 0;
    const totalCommissions = commissions.reduce((sum, tx) => sum + (parseFloat(tx.total_amount) || 0), 0);
    const totalDeductions = deductions.reduce((sum, tx) => sum + (parseFloat(tx.total_amount) || 0), 0);
    const totalPayments = payments.reduce((sum, tx) => sum + (parseFloat(tx.total_amount) || 0), 0);

    const totalDue = salaryRecord?.total_due ?? (prevBalance + baseSalary + totalCommissions - totalDeductions);
    const totalPaid = salaryRecord?.paid_amount ?? totalPayments;
    const finalBalance = salaryRecord?.remaining_amount ?? (totalDue - totalPaid);

    return { baseSalary, prevBalance, totalCommissions, totalDeductions, totalPayments, totalDue, totalPaid, finalBalance };
}


// Event Listener for DOM Content Loaded
document.addEventListener('DOMContentLoaded', () => {
    // Check Auth status (ensure auth.js runs first)
    if (typeof checkAuth === 'function') {
        try {
            checkAuth('../../login.html'); // Call checkAuth here, adjust path if needed
        } catch (error) {
            console.error("Authentication check failed:", error);
            // Optionally display a message or halt execution if auth is mandatory
            showMessage(listMessageEl, 'خطأ في التحقق من المصادقة. لا يمكن تحميل الصفحة.', 'error');
            return; // Stop further execution
        }
    } else {
        console.error('Auth check function (checkAuth) not found. Make sure auth.js is loaded before script.js.');
        // Display error to user
        showMessage(listMessageEl, 'خطأ حرج: فشل تحميل نظام المصادقة.', 'error');
        // Optionally disable functionality or redirect
        return; // Stop further execution
    }

    displayCurrentDate();
    loadSummaryData(); // Load placeholder/initial summary data
    loadDrivers(); // Load the list of drivers

    // Back button functionality
    if (backToMainDashboardBtn) {
        backToMainDashboardBtn.addEventListener('click', () => {
            // Adjust path if needed, assuming dashboard.html is two levels up
            // Corrected path to financial dashboard
            window.location.href = '../financial_dashboard.html';
        });
    }

    // --- Add Event Listeners for Comprehensive Report ---
    if (comprehensiveReportBtn) {
        comprehensiveReportBtn.addEventListener('click', openComprehensiveReportModal);
    }

    if (selectAllComprehensiveDriversCheckbox) {
        selectAllComprehensiveDriversCheckbox.addEventListener('change', (event) => {
            const isChecked = event.target.checked;
            comprehensiveReportDriverList.querySelectorAll('.comprehensive-driver-checkbox').forEach(checkbox => {
                checkbox.checked = isChecked;
            });
        });
    }

    if (generateComprehensiveReportSubmitBtn) {
        generateComprehensiveReportSubmitBtn.addEventListener('click', handleGenerateComprehensiveReport);
    }
    // --- End Add Event Listeners ---
});
ById('save-driver-balance-btn');
// const messageEl = document.getElementById('edit-balance-message');

// if (!modal || !driverSelect || !currentBalanceInput || !newBalanceInput || !saveBalanceBtn || !messageEl) {
// console.error('Modal elements not found for editing driver balance.');
// return;
// }

// // Reset form
// driverSelect.innerHTML = '<option value="">-- جاري تحميل السائقين --</option>';
// currentBalanceInput.value = '';
// newBalanceInput.value = '';
// newBalanceInput.disabled = true;
// saveBalanceBtn.disabled = true;
// hideMessage(messageEl);

// modal.style.display = 'block';

// try {
// const { data: drivers, error } = await supabaseClient
// .from('drivers')
// .select('id, name, driver_default_settings(current_balance)')
// .eq('is_active', true)
// .order('name', { ascending: true });

// if (error) throw error;

// driverSelect.innerHTML = '<option value="">-- اختر سائق --</option>';
// if (drivers && drivers.length > 0) {
// drivers.forEach(driver => {
// const option = document.createElement('option');
// option.value = driver.id;
// option.textContent = driver.name;
// // Store balance in a data attribute if needed, or fetch on select
// option.dataset.balance = driver.driver_default_settings[0]?.current_balance ?? '0';
// driverSelect.appendChild(option);
// });
// } else {
// driverSelect.innerHTML = '<option value="">-- لم يتم العثور على سائقين --</option>';
// }
// } catch (err) {
// console.error('Error loading drivers for balance edit:', err);
// showMessage(messageEl, `خطأ في تحميل السائقين: ${err.message}`, 'error');
// driverSelect.innerHTML = '<option value="">-- خطأ في التحميل --</option>';
// }

// // Add event listener for driver selection
// driverSelect.onchange = () => {
// const selectedOption = driverSelect.options[driverSelect.selectedIndex];
// if (selectedOption && selectedOption.value) {
// currentBalanceInput.value = formatCurrency(selectedOption.dataset.balance);
// newBalanceInput.disabled = false;
// saveBalanceBtn.disabled = false;
// } else {
// currentBalanceInput.value = '';
// newBalanceInput.disabled = true;
// saveBalanceBtn.disabled = true;
// }
// };

// // Add event listener for save button
// saveBalanceBtn.onclick = async () => {
// const driverId = driverSelect.value;
// const newBalance = parseFloat(newBalanceInput.value);

// if (!driverId) {
// showMessage(messageEl, 'الرجاء اختيار سائق.', 'warning');
// return;
// }
// if (isNaN(newBalance)) {
// showMessage(messageEl, 'الرجاء إدخال رصيد صحيح.', 'warning');
// return;
// }

// saveBalanceBtn.disabled = true;
// saveBalanceBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

// try {
// // Check if any month is closed for this driver
// const { data: closedMonths, error: closedMonthsError } = await supabaseClient
// .from('driver_monthly_closings')
// .select('budget_month_id, budget_months(month_number, budget_years(year_number))')
// .eq('driver_id', driverId)
// .limit(1); // We only need to know if *any* month is closed

// if (closedMonthsError) throw closedMonthsError;

// if (closedMonths && closedMonths.length > 0) {
// const closedMonthInfo = closedMonths[0];
// const monthName = getMonthName(closedMonthInfo.budget_months.month_number);
// const year = closedMonthInfo.budget_months.budget_years.year_number;
// showMessage(messageEl, `لا يمكن تعديل الرصيد. يوجد شهر مغلق (${monthName} ${year}) لهذا السائق. يرجى إعادة فتح الشهر أولاً.`, 'error');
// saveBalanceBtn.disabled = false;
// saveBalanceBtn.innerHTML = '<i class="fas fa-save"></i> حفظ الرصيد';
// return;
// }

// // If no closed months, proceed to update balance
// const { error: updateError } = await supabaseClient
// .from('driver_default_settings')
// .update({ current_balance: newBalance })
// .eq('driver_id', driverId);

// if (updateError) throw updateError;

// showMessage(messageEl, 'تم تحديث رصيد السائق بنجاح!', 'success');
// currentBalanceInput.value = formatCurrency(newBalance); // Update displayed current balance
// newBalanceInput.value = ''; // Clear new balance input
// // Optionally, reload summary data or driver list if this change affects them
// loadSummaryData();
// loadDrivers(); // To reflect new balance if shown in driver list

// setTimeout(() => {
// closeModal('edit-driver-balance-modal');
// }, 1500);

// } catch (err) {
// console.error('Error saving driver balance:', err);
// showMessage(messageEl, `خطأ في حفظ الرصيد: ${err.message}`, 'error');
// } finally {
// if (saveBalanceBtn.innerHTML.includes('spinner')) { // Only re-enable if it was spinning
// saveBalanceBtn.disabled = false;
// saveBalanceBtn.innerHTML = '<i class="fas fa-save"></i> حفظ الرصيد';
// }
// }
// };
//