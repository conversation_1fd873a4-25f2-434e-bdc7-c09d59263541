-- تشخيص شامل لحالة التريجرات في قاعدة البيانات
-- هذا الملف سيساعدنا في فهم لماذا يعمل تريجر الطلاب ولا تعمل التريجرات الأخرى

-- الخطوة 1: عرض جميع التريجرات المالية الموجودة
SELECT '=== جميع التريجرات المالية الموجودة ===' as section;
SELECT 
    trigger_name,
    event_object_table as table_name,
    action_timing,
    event_manipulation,
    action_condition,
    action_statement
FROM information_schema.triggers 
WHERE trigger_name LIKE '%financial%' 
   OR trigger_name LIKE '%payment%' 
   OR trigger_name LIKE '%student%'
   OR trigger_name LIKE '%trigger%'
ORDER BY event_object_table, trigger_name;

-- الخطوة 2: التحقق من وجود الدوال المطلوبة
SELECT '=== الدوال المطلوبة للتريجرات ===' as section;
SELECT 
    routine_name as function_name,
    routine_type,
    data_type as return_type
FROM information_schema.routines 
WHERE routine_name LIKE '%payment%' 
   OR routine_name LIKE '%financial%'
   OR routine_name LIKE '%trigger%'
   OR routine_name LIKE '%generic%'
ORDER BY routine_name;

-- الخطوة 3: فحص تريجر الطلاب العامل
SELECT '=== تفاصيل تريجر الطلاب العامل ===' as section;
SELECT 
    trigger_name,
    event_object_table,
    action_timing,
    event_manipulation,
    action_condition,
    action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'student_payments';

-- الخطوة 4: التحقق من وجود الجداول المطلوبة
SELECT '=== الجداول المطلوبة ===' as section;
SELECT 
    table_name,
    CASE 
        WHEN table_name IN (
            'student_payments', 'enterprise_group_payments', 'enterprise_subscriptions',
            'nathriyat_transactions', 'bus_expenses', 'driver_expenses'
        ) THEN 'مطلوب للتريجرات'
        ELSE 'جدول آخر'
    END as table_purpose
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN (
    'student_payments', 'enterprise_group_payments', 'enterprise_subscriptions',
    'nathriyat_transactions', 'bus_expenses', 'driver_expenses',
    'financial_transactions_log', 'bank_transactions'
  )
ORDER BY table_name;

-- الخطوة 5: فحص بنية الجداول المطلوبة
SELECT '=== بنية جدول student_payments ===' as section;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'student_payments' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

SELECT '=== بنية جدول bus_expenses ===' as section;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'bus_expenses' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

SELECT '=== بنية جدول nathriyat_transactions ===' as section;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'nathriyat_transactions' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

SELECT '=== بنية جدول driver_expenses ===' as section;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'driver_expenses' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

SELECT '=== بنية جدول enterprise_subscriptions ===' as section;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'enterprise_subscriptions' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

SELECT '=== بنية جدول enterprise_group_payments ===' as section;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'enterprise_group_payments' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- الخطوة 6: فحص جدول السجلات المالية
SELECT '=== بنية جدول financial_transactions_log ===' as section;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'financial_transactions_log' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- الخطوة 7: اختبار عمل تريجر الطلاب
SELECT '=== اختبار آخر دفعات الطلاب ===' as section;
SELECT 
    id,
    student_id,
    amount,
    payment_date,
    bank_id,
    budget_month_id,
    created_at
FROM public.student_payments 
ORDER BY created_at DESC 
LIMIT 5;

-- الخطوة 8: التحقق من السجلات المالية المقابلة
SELECT '=== السجلات المالية لدفعات الطلاب ===' as section;
SELECT 
    ftl.id,
    ftl.source_table,
    ftl.source_record_id,
    ftl.amount,
    ftl.transaction_type,
    ftl.description,
    ftl.created_at
FROM public.financial_transactions_log ftl
WHERE ftl.source_table = 'student_payments'
ORDER BY ftl.created_at DESC 
LIMIT 5;

-- الخطوة 9: التحقق من المعاملات البنكية المقابلة
SELECT '=== المعاملات البنكية لدفعات الطلاب ===' as section;
SELECT 
    bt.id,
    bt.reference_table,
    bt.reference_id,
    bt.amount,
    bt.transaction_type,
    bt.description,
    bt.financial_transaction_log_id,
    bt.created_at
FROM public.bank_transactions bt
WHERE bt.reference_table = 'student_payments'
ORDER BY bt.created_at DESC 
LIMIT 5;

-- الخطوة 10: اختبار الجداول الأخرى
SELECT '=== آخر مصروفات الحافلات ===' as section;
SELECT 
    id,
    bus_id,
    expense_type,
    amount,
    total_with_tax,
    expense_date,
    bank_id,
    budget_month_id,
    created_at
FROM public.bus_expenses 
ORDER BY created_at DESC 
LIMIT 3;

SELECT '=== السجلات المالية لمصروفات الحافلات ===' as section;
SELECT 
    ftl.id,
    ftl.source_table,
    ftl.source_record_id,
    ftl.amount,
    ftl.transaction_type,
    ftl.description,
    ftl.created_at
FROM public.financial_transactions_log ftl
WHERE ftl.source_table = 'bus_expenses'
ORDER BY ftl.created_at DESC 
LIMIT 3;

-- الخطوة 11: فحص أنواع المعاملات المُعرفة
SELECT '=== أنواع المعاملات المُعرفة ===' as section;
SELECT 
    t.typname as type_name,
    e.enumlabel as enum_value
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid 
WHERE t.typname = 'transaction_type'
ORDER BY e.enumsortorder;

-- الخطوة 12: عرض ملخص التشخيص
SELECT '=== ملخص التشخيص ===' as section;
SELECT 
    'عدد التريجرات المالية' as metric,
    COUNT(*)::text as value
FROM information_schema.triggers 
WHERE trigger_name LIKE '%financial%' 
   OR trigger_name LIKE '%payment%'

UNION ALL

SELECT 
    'عدد دفعات الطلاب' as metric,
    COUNT(*)::text as value
FROM public.student_payments

UNION ALL

SELECT 
    'عدد السجلات المالية للطلاب' as metric,
    COUNT(*)::text as value
FROM public.financial_transactions_log
WHERE source_table = 'student_payments'

UNION ALL

SELECT 
    'عدد مصروفات الحافلات' as metric,
    COUNT(*)::text as value
FROM public.bus_expenses

UNION ALL

SELECT 
    'عدد السجلات المالية للحافلات' as metric,
    COUNT(*)::text as value
FROM public.financial_transactions_log
WHERE source_table = 'bus_expenses';

-- تعليمات للمطور
SELECT '=== تعليمات للمطور ===' as section;
SELECT 'انسخ نتائج هذا الاستعلام وأرسلها لي لتحليل المشكلة وإنشاء الإصلاح المناسب' as instruction;
