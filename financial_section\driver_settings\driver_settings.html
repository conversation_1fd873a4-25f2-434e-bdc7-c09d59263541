<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات السائقين - نظام النقل المدرسي</title>

    <!-- الترتيب مهم جداً -->
    <!-- Shared Styles -->
    <link rel="stylesheet" href="../../shared_styles.css">
    <!-- Page Specific Styles -->
    <link rel="stylesheet" href="style.css">
    <!-- Sidebar Component Styles -->
    <link rel="stylesheet" href="../../shared_components/sidebar.css">

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="../../config.js"></script>
    <script src="../../auth.js"></script>
    <!-- Sidebar Component Script -->
    <script src="../../shared_components/sidebar.js"></script>
    <!-- Page Script -->
    <script defer src="script.js"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body class="dashboard-layout" data-load-sidebar="true">
    <!-- Top Navigation Bar -->
    <nav class="top-navbar">
        <div class="navbar-left">
            <button id="sidebar-toggle" class="sidebar-toggle-btn">
                <i class="fas fa-bars"></i>
            </button>
            <div class="navbar-brand">
                <i class="fas fa-user-tie"></i>
                <span>إعدادات السائقين</span>
            </div>
        </div>
        <div class="navbar-right">
            <div class="navbar-user">
                <span id="navbar-username"></span>
                <button id="logout-btn" class="logout-btn" style="display: none;">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Sidebar Container (will be populated by sidebar.js) -->
    <div id="sidebar-container"></div>

    <!-- Main Content Area -->
    <main class="main-content">
        <div class="content-wrapper">
            <!-- Dashboard Header -->
            <header class="dashboard-header">
                <div class="header-content">
                    <h1>
                        <i class="fas fa-user-tie"></i>
                        إعدادات السائقين
                    </h1>
                    <p class="header-description">إدارة الرواتب الأساسية والأرصدة الافتراضية لجميع السائقين</p>
                </div>
                <div class="header-actions">
                    <button id="refresh-btn" class="btn btn-secondary">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
                <div id="dashboard-message" class="message" style="display: none;"></div>
            </header>

            <!-- Statistics Cards -->
            <section class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-drivers-count">0</h3>
                            <p>إجمالي السائقين</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="configured-drivers-count">0</h3>
                            <p>سائقين مُعدين</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-salaries">0.00</h3>
                            <p>إجمالي الرواتب (ريال)</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Search and Filters -->
            <section class="filters-section">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-search"></i> البحث والتصفية</h2>
                    </div>
                    <div class="card-body">
                        <div class="filters-grid">
                            <div class="form-group">
                                <label for="driver-search">البحث عن سائق</label>
                                <div class="search-input-container">
                                    <input type="text" id="driver-search" placeholder="ابحث بالاسم...">
                                    <i class="fas fa-search"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="work-shift-filter">تصفية حسب الوردية</label>
                                <select id="work-shift-filter">
                                    <option value="">جميع الورديات</option>
                                    <option value="صباحي">صباحي</option>
                                    <option value="مسائي">مسائي</option>
                                    <option value="دوام كامل">دوام كامل</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Drivers Table -->
            <section class="table-section">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-table"></i> قائمة السائقين</h2>
                        <div class="header-actions">
                            <span class="badge" id="displayed-count">0 سائق</span>
                            <button class="btn btn-success save-all-btn" onclick="saveAllChanges()" disabled>
                                <i class="fas fa-save"></i>
                                حفظ جميع التغييرات
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Message Area -->
                        <div id="list-message" class="message" style="display: none;"></div>

                        <!-- Table Container -->
                        <div class="table-responsive">
                            <table id="drivers-table" class="modern-table">
                                <thead>
                                    <tr>
                                        <th class="select-col">
                                            <input type="checkbox" id="select-all" onchange="toggleSelectAll()">
                                        </th>
                                        <th>اسم السائق</th>
                                        <th>فترة العمل</th>
                                        <th>الراتب الأساسي (ريال)</th>
                                        <th>الرصيد الافتتاحي (ريال)</th>
                                        <th>الرصيد الحالي (ريال)</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="settings-table-body">
                                    <tr>
                                        <td colspan="8" class="loading-message">
                                            <i class="fas fa-spinner fa-spin"></i>
                                            جاري تحميل بيانات السائقين...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Balance Info Modal -->
    <div id="balance-info-modal" class="balance-info-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تفاصيل الرصيد - <span id="balance-info-driver-name">...</span></h2>
                <button type="button" class="close-modal-btn" id="close-balance-info-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div id="balance-info-message" class="message" style="display: none;"></div>
                <table class="balance-breakdown-table">
                    <thead>
                        <tr>
                            <th>البيان</th>
                            <th>المبلغ</th>
                            <th>الملاحظات</th>
                        </tr>
                    </thead>
                    <tbody id="balance-breakdown-tbody">
                        <!-- Balance breakdown will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

</body>
</html>
