// Sidebar Component JavaScript

// --- Sidebar Functions ---
const toggleSidebar = () => {
    const sidebar = document.getElementById('sidebar');
    const sidebarOverlay = document.getElementById('sidebar-overlay');

    if (sidebar && sidebarOverlay) {
        sidebar.classList.toggle('active');
        sidebarOverlay.classList.toggle('active');
    }
};

const closeSidebar = () => {
    const sidebar = document.getElementById('sidebar');
    const sidebarOverlay = document.getElementById('sidebar-overlay');

    if (sidebar && sidebarOverlay) {
        sidebar.classList.remove('active');
        sidebarOverlay.classList.remove('active');
    }
};

// --- Modal Functions ---
const openSectionModal = (sectionName) => {
    console.log('Opening modal for section:', sectionName); // Debug log
    const modal = document.getElementById(`${sectionName}-modal`);
    console.log('Modal element found:', modal); // Debug log
    if (modal) {
        modal.classList.add('active');
        console.log('Modal activated'); // Debug log
    } else {
        console.error('Modal not found for section:', sectionName); // Error log
    }
    closeSidebar();
};

const closeSectionModal = (modalId) => {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('active');
    }
};

// --- Initialize Sidebar ---
const initializeSidebar = () => {
    console.log('Initializing sidebar...'); // Debug log

    // Sidebar toggle button
    const sidebarToggle = document.getElementById('sidebar-toggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
        console.log('Sidebar toggle button found and event added'); // Debug log
    } else {
        console.warn('Sidebar toggle button not found'); // Warning log
    }

    // Sidebar overlay click
    const sidebarOverlay = document.getElementById('sidebar-overlay');
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', closeSidebar);
    }

    // Sidebar navigation links
    const navLinks = document.querySelectorAll('.nav-link[data-section]');
    console.log('Found nav links:', navLinks.length); // Debug log
    navLinks.forEach((link, index) => {
        const section = link.getAttribute('data-section');
        console.log(`Link ${index + 1}: section = ${section}`); // Debug log
        link.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Clicked section:', section); // Debug log
            openSectionModal(section);
        });
    });

    // Modal close buttons
    const closeButtons = document.querySelectorAll('.close-modal');
    console.log('Found close buttons:', closeButtons.length); // Debug log
    closeButtons.forEach((btn, index) => {
        const modalId = btn.getAttribute('data-modal');
        console.log(`Close button ${index + 1}: modal = ${modalId}`); // Debug log
        btn.addEventListener('click', () => {
            console.log('Closing modal:', modalId); // Debug log
            closeSectionModal(modalId);
        });
    });

    // Close section modals when clicking outside
    const sectionModals = document.querySelectorAll('.section-modal');
    console.log('Found section modals:', sectionModals.length); // Debug log
    sectionModals.forEach((modal, index) => {
        console.log(`Modal ${index + 1}: id = ${modal.id}`); // Debug log
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.remove('active');
                console.log('Modal closed by outside click:', modal.id); // Debug log
            }
        });
    });

    // Close modals on Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            // Close sidebar
            closeSidebar();

            // Close section modals
            document.querySelectorAll('.section-modal.active').forEach(modal => {
                modal.classList.remove('active');
            });
        }
    });

    // Bank transactions link (special handling)
    const bankTransactionsLink = document.getElementById('bank-transactions-link');
    if (bankTransactionsLink) {
        bankTransactionsLink.addEventListener('click', (event) => {
            event.preventDefault();
            // Check if bank type modal exists and open it
            const bankTypeModal = document.getElementById('bank-type-modal');
            if (bankTypeModal) {
                bankTypeModal.classList.add('show');
            }
        });
    }

    console.log('Sidebar initialization completed'); // Debug log
};

// --- Helper Functions ---
// Calculate the correct path to root from current location
const getPathToRoot = () => {
    const currentPath = window.location.pathname;
    console.log('Current path:', currentPath);

    // Handle local file paths (file://) and web paths differently
    if (currentPath.includes('file://') || currentPath.includes('C:') || currentPath.includes('\\')) {
        // For local files, use relative paths based on known structure
        const pathSegments = currentPath.split(/[\/\\]/).filter(segment => segment !== '');
        console.log('Path segments:', pathSegments);

        // Find the project root by looking for common folders
        let rootIndex = -1;
        const projectFolders = ['financial_section', 'students_section', 'drivers_section', 'buses_section', 'institutions_section', 'banks_section', 'shared_components'];

        for (let i = pathSegments.length - 1; i >= 0; i--) {
            if (projectFolders.includes(pathSegments[i])) {
                rootIndex = i;
                console.log('Found project folder:', pathSegments[i], 'at index:', i);
                break;
            }
        }

        if (rootIndex === -1) {
            // If we can't find project folders, assume we're in root
            console.log('No project folders found, assuming root');
            return './';
        }

        // Calculate how many levels up we need to go
        const levelsUp = pathSegments.length - rootIndex;
        const pathToRoot = '../'.repeat(levelsUp - 1) || './';

        console.log('Levels up needed:', levelsUp);
        console.log('Calculated path to root:', pathToRoot);

        return pathToRoot;
    } else {
        // For web paths, use the original logic
        const pathSegments = currentPath.split('/').filter(segment => segment !== '');
        console.log('Path segments:', pathSegments);

        // Find the project root by looking for common folders
        let rootIndex = -1;
        const projectFolders = ['financial_section', 'students_section', 'drivers_section', 'buses_section', 'institutions_section', 'banks_section', 'shared_components'];

        for (let i = pathSegments.length - 1; i >= 0; i--) {
            if (projectFolders.includes(pathSegments[i])) {
                rootIndex = i;
                console.log('Found project folder:', pathSegments[i], 'at index:', i);
                break;
            }
        }

        if (rootIndex === -1) {
            // If we can't find project folders, assume we're in root
            console.log('No project folders found, assuming root');
            return './';
        }

        // Calculate how many levels up we need to go
        const levelsUp = pathSegments.length - rootIndex;
        const pathToRoot = '../'.repeat(levelsUp - 1) || './';

        console.log('Levels up needed:', levelsUp);
        console.log('Calculated path to root:', pathToRoot);

        return pathToRoot;
    }
};

// --- Load Sidebar Component ---
const loadSidebar = async () => {
    try {
        // Get the correct path to root
        const pathToRoot = getPathToRoot();
        console.log('Path to root calculated as:', pathToRoot);

        // Define sidebar HTML directly to avoid CORS issues
        const sidebarHTML = `
            <!-- Sidebar -->
            <aside id="sidebar" class="sidebar">
                <div class="sidebar-header">
                    <h3><i class="fas fa-cogs"></i> القوائم الرئيسية</h3>
                </div>
                <nav class="sidebar-nav">
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-section="students">
                                <i class="fas fa-user-graduate"></i>
                                <span>إدارة الطلاب</span>
                                <i class="fas fa-chevron-left arrow"></i>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-section="drivers">
                                <i class="fas fa-id-card-alt"></i>
                                <span>إدارة السائقين</span>
                                <i class="fas fa-chevron-left arrow"></i>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-section="buses">
                                <i class="fas fa-bus"></i>
                                <span>إدارة الباصات</span>
                                <i class="fas fa-chevron-left arrow"></i>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-section="expenses">
                                <i class="fas fa-coins"></i>
                                <span>إدارة النثريات</span>
                                <i class="fas fa-chevron-left arrow"></i>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-section="enterprises">
                                <i class="fas fa-building"></i>
                                <span>إدارة المؤسسات</span>
                                <i class="fas fa-chevron-left arrow"></i>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-section="banking">
                                <i class="fas fa-university"></i>
                                <span>الإدارة البنكية</span>
                                <i class="fas fa-chevron-left arrow"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
            </aside>

            <!-- Sidebar Overlay -->
            <div id="sidebar-overlay" class="sidebar-overlay"></div>

            <!-- Section Modals -->
            <!-- Students Modal -->
            <div id="students-modal" class="section-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-user-graduate"></i> إدارة الطلاب</h3>
                        <button class="close-modal" data-modal="students-modal">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="modal-links">
                            <a href="${pathToRoot}students_section/register_student.html" class="modal-link">
                                <i class="fas fa-user-plus"></i>
                                <span>تسجيل طالب جديد</span>
                            </a>
                            <a href="${pathToRoot}financial_section/student_subscriptions/student_subscriptions.html" class="modal-link">
                                <i class="fas fa-users"></i>
                                <span>إدارة المجموعات والاشتراكات</span>
                            </a>
                            <a href="${pathToRoot}financial_section/defaults/student_defaults.html" class="modal-link">
                                <i class="fas fa-graduation-cap"></i>
                                <span>الاشتراكات الافتراضية</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Drivers Modal -->
            <div id="drivers-modal" class="section-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-id-card-alt"></i> إدارة السائقين</h3>
                        <button class="close-modal" data-modal="drivers-modal">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="modal-links">
                            <a href="${pathToRoot}drivers_section/drivers.html" class="modal-link">
                                <i class="fas fa-id-card-alt"></i>
                                <span>إدارة السائقين</span>
                            </a>
                            <a href="${pathToRoot}financial_section/drivers_management/drivers_management.html" class="modal-link">
                                <i class="fas fa-users-cog"></i>
                                <span>الإدارة المالية للسائقين</span>
                            </a>
                            <a href="${pathToRoot}financial_section/driver_settings/driver_settings.html" class="modal-link">
                                <i class="fas fa-user-plus"></i>
                                <span>إضافة إعدادات السائقين</span>
                            </a>
                            <a href="${pathToRoot}financial_section/defaults/driver_settings.html" class="modal-link">
                                <i class="fas fa-users-cog"></i>
                                <span>عرض إعدادات السائقين</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Buses Modal -->
            <div id="buses-modal" class="section-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-bus"></i> إدارة الباصات</h3>
                        <button class="close-modal" data-modal="buses-modal">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="modal-links">
                            <a href="${pathToRoot}buses_section/buses.html" class="modal-link">
                                <i class="fas fa-bus-alt"></i>
                                <span>إدارة الحافلات</span>
                            </a>
                            <a href="${pathToRoot}financial_section/bus_expenses/bus_expenses_dashboard.html" class="modal-link">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>لوحة معلومات مصاريف الحافلات</span>
                            </a>
                            <a href="${pathToRoot}financial_section/bus_expenses/bus_expenses.html" class="modal-link">
                                <i class="fas fa-bus"></i>
                                <span>تفاصيل مصاريف الحافلات</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Expenses Modal -->
            <div id="expenses-modal" class="section-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-coins"></i> إدارة النثريات</h3>
                        <button class="close-modal" data-modal="expenses-modal">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="modal-links">
                            <a href="${pathToRoot}financial_section/nathriyat_section/nathriyat.html" class="modal-link">
                                <i class="fas fa-coins"></i>
                                <span>النثريات</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enterprises Modal -->
            <div id="enterprises-modal" class="section-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-building"></i> إدارة المؤسسات</h3>
                        <button class="close-modal" data-modal="enterprises-modal">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="modal-links">
                            <a href="${pathToRoot}institutions_section/institutions.html" class="modal-link">
                                <i class="fas fa-building"></i>
                                <span>إدارة المنشآت</span>
                            </a>
                            <a href="${pathToRoot}financial_section/institution_subscriptions/index.html" class="modal-link">
                                <i class="fas fa-building-columns"></i>
                                <span>اشتراكات المنشآت</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Banking Modal -->
            <div id="banking-modal" class="section-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-university"></i> الإدارة البنكية</h3>
                        <button class="close-modal" data-modal="banking-modal">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="modal-links">
                            <a href="${pathToRoot}banks_section/banks.html" class="modal-link">
                                <i class="fas fa-university"></i>
                                <span>إدارة البنوك</span>
                            </a>
                            <a href="javascript:void(0);" id="bank-transactions-link" class="modal-link">
                                <i class="fas fa-money-check-alt"></i>
                                <span>المعاملات البنكية</span>
                            </a>
                            <a href="${pathToRoot}financial_section/monthly_budget/monthly_budget.html" class="modal-link">
                                <i class="fas fa-calendar-alt"></i>
                                <span>نظرة عامة الشهور</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Insert sidebar into the page
        const sidebarContainer = document.getElementById('sidebar-container');
        if (sidebarContainer) {
            sidebarContainer.innerHTML = sidebarHTML;
        } else {
            // If no container, append to body
            document.body.insertAdjacentHTML('beforeend', sidebarHTML);
        }

        // Initialize sidebar functionality
        initializeSidebar();

        // Debug: Check if modals are loaded
        console.log('Checking modals after load:');
        console.log('Students modal:', document.getElementById('students-modal'));
        console.log('Drivers modal:', document.getElementById('drivers-modal'));
        console.log('Buses modal:', document.getElementById('buses-modal'));
        console.log('Expenses modal:', document.getElementById('expenses-modal'));
        console.log('Enterprises modal:', document.getElementById('enterprises-modal'));
        console.log('Banking modal:', document.getElementById('banking-modal'));

        console.log('Sidebar component loaded successfully');

    } catch (error) {
        console.error('Error loading sidebar component:', error);
    }
};

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Check if sidebar should be auto-loaded
    const autoLoadSidebar = document.querySelector('[data-load-sidebar="true"]');
    if (autoLoadSidebar) {
        loadSidebar();
    }
});

// Export functions for manual use
window.SidebarComponent = {
    load: loadSidebar,
    toggle: toggleSidebar,
    close: closeSidebar,
    openModal: openSectionModal,
    closeModal: closeSectionModal,
    initialize: initializeSidebar
};
