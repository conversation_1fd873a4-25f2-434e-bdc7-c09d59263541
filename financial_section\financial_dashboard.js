// --- Auth Check ---
checkAuth('../login.html');

// --- Supabase Initialization ---
let _supabase;
try {
    if (typeof supabase !== 'undefined' && supabase.createClient) {
        _supabase = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('Supabase client initialized for dashboard.');
    } else {
        console.error('Supabase client not found.');
    }
} catch (error) {
    console.error('Error initializing Supabase client:', error);
}

// --- DOM Elements ---
const dashboardMonthYear = document.getElementById('dashboard-month-year');
const dashboardMessage = document.getElementById('dashboard-message');
const totalRevenueEl = document.getElementById('total-revenue');
const totalExpensesEl = document.getElementById('total-expenses');
const totalTaxEl = document.getElementById('total-tax');
const netProfitEl = document.getElementById('net-profit');
const homeBtn = document.getElementById('home-btn');
const monthSelector = document.getElementById('month-selector');
const notificationsCount = document.getElementById('notifications-count');
const notificationsList = document.getElementById('notifications-list');

// --- State ---
let selectedMonthId = null;
let selectedMonthNumber = null;
let selectedYearNumber = null;
let revenueChart = null;
let expensesChart = null;

// --- Helper Functions ---
const showDashboardMessage = (message, type = 'info') => {
    if (!dashboardMessage) return;
    dashboardMessage.textContent = message;
    dashboardMessage.className = `message ${type} show`;
    dashboardMessage.style.display = 'block';
};

const formatCurrency = (amount) => {
    const value = parseFloat(amount);
    return isNaN(value) ? '0.00' : value.toFixed(2);
};

const getMonthName = (monthNumber) => {
    const names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"];
    const index = parseInt(monthNumber, 10) - 1;
    return names[index] || `شهر ${monthNumber}`;
};

// Sidebar functions are now handled by shared_components/sidebar.js

// --- Data Fetching Functions ---

// Fetch student subscriptions revenue (without tax)
const fetchStudentRevenue = async (monthId) => {
    if (!_supabase || !monthId) return 0;

    try {
        const { data, error } = await _supabase
            .from('student_payments')
            .select('amount')
            .eq('budget_month_id', monthId);

        if (error) throw error;

        return data.reduce((sum, payment) => sum + (parseFloat(payment.amount) || 0), 0);
    } catch (error) {
        console.error('Error fetching student revenue:', error);
        return 0;
    }
};

// Fetch enterprise subscriptions revenue (without tax)
const fetchEnterpriseRevenue = async (monthId) => {
    if (!_supabase || !monthId) return 0;

    try {
        const { data, error } = await _supabase
            .from('enterprise_subscriptions')
            .select('total_amount')
            .eq('budget_month_id', monthId);

        if (error) throw error;

        return data.reduce((sum, sub) => sum + (parseFloat(sub.total_amount) || 0), 0);
    } catch (error) {
        console.error('Error fetching enterprise revenue:', error);
        return 0;
    }
};

// Fetch driver expenses (without tax)
const fetchDriverExpenses = async (monthId) => {
    if (!_supabase || !monthId) return { amount: 0, tax: 0 };

    try {
        const { data, error } = await _supabase
            .from('driver_expenses')
            .select('total_amount, vat_amount')
            .eq('budget_month_id', monthId);

        if (error) throw error;

        const amount = data.reduce((sum, exp) => sum + (parseFloat(exp.total_amount) || 0), 0);
        const tax = data.reduce((sum, exp) => sum + (parseFloat(exp.vat_amount) || 0), 0);

        return { amount: amount - tax, tax };
    } catch (error) {
        console.error('Error fetching driver expenses:', error);
        return { amount: 0, tax: 0 };
    }
};

// Fetch bus expenses (without tax)
const fetchBusExpenses = async (monthId) => {
    if (!_supabase || !monthId) return { amount: 0, tax: 0 };

    try {
        const { data, error } = await _supabase
            .from('bus_expenses')
            .select('amount, tax_amount')
            .eq('budget_month_id', monthId);

        if (error) throw error;

        const amount = data.reduce((sum, exp) => sum + (parseFloat(exp.amount) || 0), 0);
        const tax = data.reduce((sum, exp) => sum + (parseFloat(exp.tax_amount) || 0), 0);

        return { amount, tax };
    } catch (error) {
        console.error('Error fetching bus expenses:', error);
        return { amount: 0, tax: 0 };
    }
};

// Fetch nathriyat expenses
const fetchNathriyatExpenses = async (monthId) => {
    if (!_supabase || !monthId) return 0;

    try {
        const { data, error } = await _supabase
            .from('nathriyat_transactions')
            .select('amount')
            .eq('budget_month_id', monthId);

        if (error) throw error;

        return data.reduce((sum, trans) => sum + (parseFloat(trans.amount) || 0), 0);
    } catch (error) {
        console.error('Error fetching nathriyat expenses:', error);
        return 0;
    }
};

// Update financial summary cards
const updateFinancialSummary = async (monthId) => {
    if (!monthId) return;

    try {
        // Fetch all financial data in parallel
        const [
            studentRevenue,
            enterpriseRevenue,
            driverExpenses,
            busExpenses,
            nathriyatExpenses
        ] = await Promise.all([
            fetchStudentRevenue(monthId),
            fetchEnterpriseRevenue(monthId),
            fetchDriverExpenses(monthId),
            fetchBusExpenses(monthId),
            fetchNathriyatExpenses(monthId)
        ]);

        // Calculate totals
        const totalRevenue = studentRevenue + enterpriseRevenue;
        const totalExpensesAmount = driverExpenses.amount + busExpenses.amount + nathriyatExpenses;
        const totalTax = driverExpenses.tax + busExpenses.tax;

        // Calculate net profit (revenue - expenses + tax)
        // Tax is added because it's income when it's positive (from customers)
        // and expense when negative (paid to government)
        const netProfit = totalRevenue - totalExpensesAmount + totalTax;

        // Update UI
        if (totalRevenueEl) totalRevenueEl.textContent = formatCurrency(totalRevenue);
        if (totalExpensesEl) totalExpensesEl.textContent = formatCurrency(totalExpensesAmount);
        if (totalTaxEl) totalTaxEl.textContent = formatCurrency(totalTax);
        if (netProfitEl) {
            netProfitEl.textContent = formatCurrency(netProfit);
            // Change color based on profit/loss
            const profitCard = netProfitEl.closest('.summary-card');
            if (profitCard) {
                profitCard.style.borderTopColor = netProfit >= 0 ? '#28a745' : '#dc3545';
            }
        }

        // Update charts
        await updateCharts(monthId, {
            revenue: { students: studentRevenue, enterprises: enterpriseRevenue },
            expenses: {
                drivers: driverExpenses.amount,
                buses: busExpenses.amount,
                nathriyat: nathriyatExpenses
            },
            tax: { drivers: driverExpenses.tax, buses: busExpenses.tax }
        });

    } catch (error) {
        console.error('Error updating financial summary:', error);
        showDashboardMessage('خطأ في تحديث الملخص المالي', 'error');
    }
};

// Update charts
const updateCharts = async (monthId, data) => {
    // Destroy existing charts
    if (revenueChart) {
        revenueChart.destroy();
    }
    if (expensesChart) {
        expensesChart.destroy();
    }

    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart');
    if (revenueCtx) {
        revenueChart = new Chart(revenueCtx, {
            type: 'bar',
            data: {
                labels: ['اشتراكات الطلاب', 'اشتراكات المؤسسات'],
                datasets: [{
                    label: 'الإيرادات (ريال)',
                    data: [data.revenue.students, data.revenue.enterprises],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(23, 162, 184, 0.8)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(23, 162, 184, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value) + ' ريال';
                            }
                        }
                    }
                }
            }
        });
    }

    // Expenses Chart
    const expensesCtx = document.getElementById('expensesChart');
    if (expensesCtx) {
        expensesChart = new Chart(expensesCtx, {
            type: 'doughnut',
            data: {
                labels: ['مصروفات السائقين', 'مصروفات الباصات', 'النثريات'],
                datasets: [{
                    data: [data.expenses.drivers, data.expenses.buses, data.expenses.nathriyat],
                    backgroundColor: [
                        'rgba(220, 53, 69, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(108, 117, 125, 0.8)'
                    ],
                    borderColor: [
                        'rgba(220, 53, 69, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(108, 117, 125, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + formatCurrency(context.parsed) + ' ريال';
                            }
                        }
                    }
                }
            }
        });
    }
};

// Load and display notifications
const loadNotifications = async () => {
    if (!notificationsList) return;

    try {
        // Get notifications from database
        const { data: dbNotifications, error } = await _supabase
            .from('notifications')
            .select('*')
            .or(`budget_month_id.eq.${selectedMonthId},budget_month_id.is.null`)
            .order('created_at', { ascending: false })
            .limit(10);

        if (error) throw error;

        const notifications = [];

        // Add database notifications
        if (dbNotifications && dbNotifications.length > 0) {
            dbNotifications.forEach(notif => {
                notifications.push({
                    type: notif.notification_type,
                    icon: getNotificationIcon(notif.notification_type),
                    message: notif.message,
                    time: formatNotificationTime(notif.created_at)
                });
            });
        }

        // Check for students without payments this month (additional check)
        if (selectedMonthId) {
            try {
                const { data: allStudents } = await _supabase
                    .from('students')
                    .select('id')
                    .eq('is_active', true);

                const { data: studentsWithPayments } = await _supabase
                    .from('student_payments')
                    .select('student_id')
                    .eq('budget_month_id', selectedMonthId);

                const totalStudents = allStudents?.length || 0;
                const studentsWithPaymentsCount = new Set(studentsWithPayments?.map(p => p.student_id)).size;
                const unpaidCount = totalStudents - studentsWithPaymentsCount;

                if (unpaidCount > 0) {
                    notifications.unshift({
                        type: 'warning',
                        icon: 'fas fa-exclamation-triangle',
                        message: `يوجد ${unpaidCount} طالب لم يدفع هذا الشهر`,
                        time: 'الآن'
                    });
                }
            } catch (error) {
                console.error('Error checking unpaid students:', error);
            }
        }

        // Add success notification if no issues
        if (notifications.length === 0) {
            notifications.push({
                type: 'success',
                icon: 'fas fa-check-circle',
                message: 'جميع المعاملات المالية تبدو طبيعية',
                time: 'الآن'
            });
        }

        // Update notifications UI
        if (notificationsCount) {
            notificationsCount.textContent = notifications.length;
        }

        if (notificationsList) {
            notificationsList.innerHTML = notifications.map(notif => `
                <div class="notification-item ${notif.type}">
                    <i class="${notif.icon}"></i>
                    <div class="notification-content">
                        <span class="notification-message">${notif.message}</span>
                        ${notif.time ? `<span class="notification-time">${notif.time}</span>` : ''}
                    </div>
                </div>
            `).join('');
        }

    } catch (error) {
        console.error('Error loading notifications:', error);

        // Fallback notification
        if (notificationsList) {
            notificationsList.innerHTML = `
                <div class="notification-item error">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>خطأ في تحميل الإشعارات</span>
                </div>
            `;
        }
    }
};

// Helper functions for notifications
const getNotificationIcon = (type) => {
    switch (type) {
        case 'success': return 'fas fa-check-circle';
        case 'warning': return 'fas fa-exclamation-triangle';
        case 'error': return 'fas fa-exclamation-circle';
        case 'info':
        default: return 'fas fa-info-circle';
    }
};

const formatNotificationTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'الآن';
    if (diffMins < 60) return `منذ ${diffMins} دقيقة`;
    if (diffHours < 24) return `منذ ${diffHours} ساعة`;
    if (diffDays < 7) return `منذ ${diffDays} يوم`;

    return date.toLocaleDateString('ar-SA');
};

// Load available months for selector
const loadMonthSelector = async () => {
    if (!monthSelector || !_supabase) return;

    try {
        const { data: months, error } = await _supabase
            .from('budget_months')
            .select('id, month_number, budget_years(year_number)')
            .order('budget_years(year_number)', { ascending: false })
            .order('month_number', { ascending: false });

        if (error) throw error;

        monthSelector.innerHTML = '<option value="">اختر شهر...</option>';

        months.forEach(month => {
            const option = document.createElement('option');
            option.value = month.id;
            option.textContent = `${getMonthName(month.month_number)} ${month.budget_years.year_number}`;
            if (month.id === selectedMonthId) {
                option.selected = true;
            }
            monthSelector.appendChild(option);
        });

    } catch (error) {
        console.error('Error loading month selector:', error);
    }
};

// --- Event Handlers ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log('Financial Dashboard page loaded.');

    // Get selected month and year from sessionStorage
    selectedMonthId = sessionStorage.getItem('selectedBudgetMonthId');
    selectedMonthNumber = sessionStorage.getItem('selectedBudgetMonthNumber');
    selectedYearNumber = sessionStorage.getItem('selectedBudgetYearNumber');

    console.log('Retrieved from sessionStorage:', { selectedMonthId, selectedMonthNumber, selectedYearNumber });

    // Update month display
    if (selectedMonthNumber && selectedYearNumber && dashboardMonthYear) {
        dashboardMonthYear.textContent = `${getMonthName(selectedMonthNumber)} ${selectedYearNumber}`;
    } else {
        dashboardMonthYear.textContent = 'غير محدد';
        console.warn('Month number or year number missing from sessionStorage.');
    }

    // Load month selector
    await loadMonthSelector();

    if (!selectedMonthId) {
        console.error('Selected Budget Month ID not found in sessionStorage.');
        showDashboardMessage('لم يتم تحديد الشهر المالي. الرجاء العودة واختيار شهر أولاً.', 'error');
    } else {
        // Load financial data
        await updateFinancialSummary(selectedMonthId);
        await loadNotifications();
    }

    // Sidebar functionality is handled by shared_components/sidebar.js

    // Month selector change
    if (monthSelector) {
        monthSelector.addEventListener('change', async (e) => {
            const newMonthId = e.target.value;
            if (newMonthId && newMonthId !== selectedMonthId) {
                selectedMonthId = newMonthId;
                sessionStorage.setItem('selectedBudgetMonthId', newMonthId);

                // Update financial data
                await updateFinancialSummary(newMonthId);
                await loadNotifications();

                // Update month display
                const selectedOption = e.target.selectedOptions[0];
                if (selectedOption && dashboardMonthYear) {
                    dashboardMonthYear.textContent = selectedOption.textContent;
                }
            }
        });
    }

    // Home button
    if (homeBtn) {
        homeBtn.addEventListener('click', () => {
            window.location.href = 'select_budget_month.html';
        });
    }

    // --- Bank Type Modal Logic ---
    const bankTransactionsLink = document.getElementById('bank-transactions-link');
    const bankTypeModal = document.getElementById('bank-type-modal');
    const closeBankTypeModalBtn = document.getElementById('close-bank-type-modal');
    const selectMarkaziBtn = document.getElementById('select-bank-type-markazi');
    const selectAjelBtn = document.getElementById('select-bank-type-ajel');

    const openBankTypeModal = () => {
        if (bankTypeModal) {
            bankTypeModal.classList.add('show');
        }
    };

    const closeBankTypeModal = () => {
        if (bankTypeModal) {
            bankTypeModal.classList.remove('show');
        }
    };

    const navigateToBankTransactions = (type) => {
        if (type) {
            // Construct the URL with the type parameter
            const url = `../financial_section/bank_transactions/bank_transactions.html?type=${encodeURIComponent(type)}`;
            window.location.href = url;
        } else {
            // Fallback or default behavior if needed
            window.location.href = '../financial_section/bank_transactions/bank_transactions.html';
        }
        closeBankTypeModal(); // Close modal after selection
    };

    if (bankTransactionsLink) {
        bankTransactionsLink.addEventListener('click', (event) => {
            event.preventDefault(); // Prevent default link behavior
            openBankTypeModal();
        });
    }

    if (closeBankTypeModalBtn) {
        closeBankTypeModalBtn.addEventListener('click', closeBankTypeModal);
    }

    if (selectMarkaziBtn) {
        selectMarkaziBtn.addEventListener('click', () => {
            navigateToBankTransactions(selectMarkaziBtn.dataset.type);
        });
    }

    if (selectAjelBtn) {
        selectAjelBtn.addEventListener('click', () => {
            navigateToBankTransactions(selectAjelBtn.dataset.type);
        });
    }

    // Close modal if clicking outside the modal content
    if (bankTypeModal) {
        bankTypeModal.addEventListener('click', (event) => {
            if (event.target === bankTypeModal) {
                closeBankTypeModal();
            }
        });
    }

    // Close modal on Escape key press
    document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape' && bankTypeModal?.classList.contains('show')) {
            closeBankTypeModal();
        }
    });

    // --- End Bank Type Modal Logic ---

    // Escape key handling for bank type modal
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            // Close bank type modal
            const bankTypeModal = document.getElementById('bank-type-modal');
            if (bankTypeModal?.classList.contains('show')) {
                bankTypeModal.classList.remove('show');
            }
        }
    });
});
