<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المؤسسات</title>
    <link rel="stylesheet" href="style.css">
    <!-- Include Supabase JS library -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Include Config and Main Script -->
    <script src="../config.js"></script>
    <script defer src="script.js"></script>
    <!-- Add Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <header class="dashboard-header">
            <div class="header-content">
                <h1><i class="fas fa-building"></i> نظام إدارة المؤسسات</h1>
                <p>إدارة بيانات المؤسسات المتعاقدة</p>
            </div>
        </header>

        <main class="dashboard-main">
            <!-- Stats Cards -->
            <section class="stats-section">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-city"></i></div>
                    <div class="stat-info">
                        <h3 id="total-institutions">0</h3>
                        <p>إجمالي المؤسسات</p>
                    </div>
                </div>
                <!-- Add more relevant stats if needed -->
                 <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-file-contract"></i></div>
                    <div class="stat-info">
                        <h3 id="active-contracts">0</h3> <!-- Example Placeholder -->
                        <p>العقود النشطة (مثال)</p>
                    </div>
                </div>
            </section>

            <!-- Controls Section -->
            <section class="controls-section">
                <div class="control-card">
                    <div class="card-header">
                        <h2>أدوات التحكم</h2>
                    </div>
                    <div class="card-body">
                        <div class="controls-grid">
                            <!-- Add Institution Button -->
                            <button id="add-institution-btn" class="control-btn add-btn">
                                <i class="fas fa-plus-circle"></i> إضافة مؤسسة
                            </button>
                            <div class="search-container">
                                <input type="text" id="search-input" placeholder="ابحث باسم المؤسسة أو رقم السجل...">
                                <button id="search-btn" class="search-btn"><i class="fas fa-search"></i></button>
                            </div>
                            <button id="print-report-btn" class="control-btn print-btn">
                                <i class="fas fa-print"></i> طباعة تقرير
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Institutions Table Section -->
            <section class="table-section"> <!-- Keep section, rename if desired -->
                <div class="table-card"> <!-- Keep outer card -->
                    <div class="card-header">
                        <h2>قائمة المؤسسات</h2>
                        <span class="badge" id="institutions-count">0</span>
                    </div>
                    <div class="card-body">
                        <!-- New Card Container -->
                        <div id="institutions-cards-container" class="institutions-cards-container">
                            <div class="loading-placeholder"> <!-- Loading Placeholder -->
                                <i class="fas fa-spinner fa-spin"></i> جاري تحميل بيانات المؤسسات...
                            </div>
                        </div>
                        <div id="list-message" class="message" style="display: none;"></div>
                        <div class="pagination" id="pagination-controls">
                            <!-- Pagination will be added by JavaScript -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Institution Form Section (Modal) -->
            <section id="add-institution-section" class="form-section">
                <div class="form-card">
                    <div class="card-header">
                        <h2 id="form-title">إضافة مؤسسة جديدة</h2>
                        <button id="close-form-btn" class="close-btn"><i class="fas fa-times"></i></button>
                    </div>
                    <div class="card-body">
                        <form id="institution-form">
                            <input type="hidden" id="institution_id" name="institution_id"> <!-- Hidden input for institution ID (PK) -->

                            <!-- Institution Information Section -->
                            <fieldset class="form-fieldset">
                                <legend>بيانات المؤسسة</legend>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="institution_name">اسم المؤسسة <span class="required">*</span></label>
                                        <input type="text" id="institution_name" name="institution_name" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="commercial_reg_number">رقم السجل التجاري</label>
                                        <input type="text" id="commercial_reg_number" name="commercial_reg_number">
                                    </div>
                                    <div class="form-group">
                                        <label for="tax_number">الرقم الضريبي</label>
                                        <input type="text" id="tax_number" name="tax_number">
                                    </div>
                                </div>
                            </fieldset>

                            <div class="form-actions">
                                <button type="submit" class="submit-btn">حفظ</button>
                                <button type="button" id="cancel-edit-btn" class="cancel-btn">إلغاء</button>
                            </div>
                        </form>
                        <div id="form-message" class="message"></div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Report Selection Modal -->
        <div id="report-selection-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>اختيار مؤسسة للتقرير</h2>
                    <button id="close-report-modal-btn" class="close-modal-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="report-modal-message" class="message" style="display: none;"></div>
                    <div class="form-group">
                        <label for="report-institution-select">اختر المؤسسة:</label>
                        <select id="report-institution-select" name="report_institution_id" required>
                            <option value="" disabled selected>-- اختر مؤسسة --</option>
                            <!-- Options populated by JS -->
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" id="generate-report-btn" class="btn btn-primary">
                        <i class="fas fa-file-alt"></i> إنشاء التقرير
                    </button>
                    <button type="button" id="cancel-report-btn" class="btn btn-secondary">إلغاء</button>
                </div>
            </div>
        </div>
        <!-- End Report Selection Modal -->

        <footer class="dashboard-footer">
        </footer>
    </div>
</body>
</html>
