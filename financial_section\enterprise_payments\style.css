/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f7fc;
    color: #333;
    direction: rtl; /* Right-to-left for Arabic */
}

a {
    text-decoration: none;
    color: #007bff;
}

a:hover {
    text-decoration: underline;
}

/* Layout */
.dashboard-container {
    display: flex; /* Restore flex display */
    min-height: 100vh;
    /* padding: 20px; */ /* Remove padding if sidebar handles it */
    box-sizing: border-box;
}

/* Restore sidebar styles */
.sidebar {
    width: 250px;
    background-color: #2c3e50; /* Dark blue */
    color: #ecf0f1; /* Light grey */
    padding: 20px;
    box-sizing: border-box;
    flex-shrink: 0; /* Prevent sidebar from shrinking */
}

.sidebar h2 {
    text-align: center;
    margin-bottom: 20px; /* Adjusted margin */
    margin-top: 0;
    color: #fff;
    font-size: 1.2em; /* Slightly smaller heading */
    padding-bottom: 10px;
    border-bottom: 1px solid #34495e;
}

.sidebar nav ul {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0; /* Add margin below nav lists */
}

.sidebar nav ul li {
    margin-bottom: 10px; /* Slightly reduced margin */
}

.sidebar nav ul li a {
    color: #bdc3c7; /* Lighter grey */
    display: block;
    padding: 8px 15px; /* Adjusted padding */
    border-radius: 4px;
    transition: background-color 0.3s ease, color 0.3s ease;
    font-size: 0.95em;
}

.sidebar nav ul li a:hover,
.sidebar nav ul li.active a { /* Style for active link */
    background-color: #34495e; /* Slightly lighter blue */
    color: #fff;
    text-decoration: none;
}

.sidebar-divider {
    border: 0;
    height: 1px;
    background-color: #34495e;
    margin: 20px 0;
}

/* Removed Enterprise Selector Section styles */
/* .enterprise-selector { ... } */
/* .enterprise-selector h2 { ... } */
/* .enterprise-selector table { ... } */
/* .enterprise-selector th, */
/* .enterprise-selector td { ... } */
/* .enterprise-selector thead th { ... } */
/* .enterprise-selector tbody tr:hover { ... } */
/* .btn-select { ... } */
/* .btn-select:hover { ... } */

.main-content {
    flex-grow: 1; /* Restore flex-grow */
    padding: 30px;
    background-color: #fff;
    box-sizing: border-box;
    /* Remove optional styles if they were added */
    /* border-radius: 8px; */
    /* box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); */
}

/* Header */
.main-content header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    border-bottom: 1px solid #eee;
    padding-bottom: 20px;
}

.main-content header h1 {
    margin: 0;
    font-size: 1.8em;
    color: #2c3e50;
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.3s ease;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

/* Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.card {
    background-color: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.card h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #555;
    font-size: 1.1em;
}

.card p {
    font-size: 1.5em;
    font-weight: bold;
    color: #2c3e50;
    margin: 0;
}

/* Payment Table */
.payment-table {
    background-color: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-top: 30px; /* Ensure spacing if needed */
}

.payment-table h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #2c3e50;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

th, td {
    padding: 12px 15px;
    text-align: right; /* Right align for Arabic */
    border-bottom: 1px solid #eee;
}

thead th {
    background-color: #f8f9fa;
    color: #555;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 0.9em;
}

tbody tr:hover {
    background-color: #f1f1f1;
}

/* Status Badges */
.status {
    padding: 5px 10px;
    border-radius: 12px;
    font-size: 0.85em;
    font-weight: bold;
    color: #fff;
    display: inline-block;
}

.status-paid {
    background-color: #28a745; /* Green */
}

.status-pending {
    background-color: #ffc107; /* Yellow */
    color: #333;
}

.status-overdue {
    background-color: #dc3545; /* Red */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column; /* Stack sidebar and content */
    }

    .sidebar {
        width: 100%; /* Full width sidebar */
        height: auto; /* Auto height */
        position: relative; /* Or static, depending on desired behavior */
    }

    /* Adjust sidebar content alignment if needed for horizontal layout */
    .sidebar h2 {
         text-align: right;
    }

    .main-content header {
        flex-direction: column;
        align-items: flex-start;
    }

    .main-content header .btn {
        margin-top: 10px;
    }

    .summary-cards {
        grid-template-columns: 1fr; /* Stack cards on smaller screens */
    }

    /* Make tables scrollable horizontally on small screens */
     .payment-table { /* Only payment table needs this now */
        overflow-x: auto;
    }
}
