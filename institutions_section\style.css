/* Content copied from c:\Users\<USER>\OneDrive\سطح المكتب\مشروعي\banks_section\style.css */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');
@import url('../shared_styles.css');

:root {
    /* Main Colors */
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;

    /* Backgrounds */
    --body-bg: #f5f7fa;
    --card-bg: #ffffff;

    /* Borders */
    --border-color: #e1e8ed;
    --border-radius: 8px;

    /* Typography */
    --text-dark: #34495e;
    --text-muted: #7f8c8d;
    --text-light: #ffffff;

    /* Shadow */
    --card-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    --hover-shadow: 0 5px 25px rgba(0, 0, 0, 0.12);
    --button-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--body-bg);
    color: var (--text-dark);
    line-height: 1.6;
}

/* Dashboard Container */
.dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.dashboard-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-light);
    padding: 20px;
    text-align: center;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
}

.header-content h1 {
    font-size: 1.8rem;
    margin-bottom: 5px;
    font-weight: 700;
}

.header-content p {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 300;
}

.header-content h1 i {
    margin-left: 10px;
}

/* Main Content */
.dashboard-main {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    width: 100%;
}

/* Stats Cards */
.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.stat-icon {
    font-size: 2rem;
    margin-left: 15px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-card:nth-child(1) .stat-icon {
    color: var(--primary-color);
    background-color: rgba(52, 152, 219, 0.1);
}

.stat-card:nth-child(2) .stat-icon {
    color: var(--success-color);
    background-color: rgba(46, 204, 113, 0.1);
}

.stat-card:nth-child(3) .stat-icon {
    color: var(--warning-color);
    background-color: rgba(243, 156, 18, 0.1);
}

.stat-info h3 {
    font-size: 1.8rem;
    margin-bottom: 5px;
    font-weight: 700;
}

.stat-info p {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Controls Section */
.controls-section {
    margin-bottom: 20px;
}

.control-card, .table-card, .form-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa; /* Light header background */
}

.card-header h2 {
    font-size: 1.2rem;
    font-weight: 500;
    margin: 0;
}

.card-body {
    padding: 20px 30px; /* Adjust padding */
    max-height: 75vh; /* Limit height and allow scrolling */
    overflow-y: auto;
}

.controls-grid {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 15px;
    align-items: center;
}

.control-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var(--text-light);
    transition: all 0.3s ease;
    box-shadow: var(--button-shadow);
}

.control-btn i {
    font-size: 1rem;
}

.add-btn {
    background-color: var(--success-color);
}

.add-btn:hover {
    background-color: #27ae60;
}

.print-btn {
    background-color: var(--primary-color);
}

.print-btn:hover {
    background-color: var(--primary-dark);
}

.search-container {
    display: flex;
    max-width: 100%;
    position: relative;
}

#search-input {
    width: 100%;
    padding: 10px 45px 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

#search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.25);
}

.search-btn {
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 1rem;
    padding: 5px;
    cursor: pointer;
}

/* Table */
.table-section {
    margin-bottom: 20px;
}

.badge {
    background-color: var(--primary-color);
    color: var(--text-light);
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.table-responsive {
    overflow-x: auto;
}

/* --- New Card Styles --- */
.institutions-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* Adjust minmax for desired card size */
    gap: 25px; /* Gap between cards */
    padding: 10px 0;
}

.institution-card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden; /* Ensure content stays within rounded corners */
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    flex-direction: column; /* Stack content and actions */
}

.institution-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--hover-shadow);
}

.card-content {
    padding: 20px;
    display: flex;
    align-items: center; /* Align icon and details vertically */
    gap: 20px; /* Space between icon and details */
    flex-grow: 1; /* Allow content to take available space */
}

.card-icon {
    font-size: 2.5rem; /* Adjust icon size */
    color: var(--primary-color);
    width: 50px; /* Fixed width for alignment */
    text-align: center;
}

.card-details {
    flex-grow: 1; /* Allow details to take remaining space */
}

.institution-name {
    margin: 0 0 8px 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-dark);
}

.institution-info {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin: 0 0 10px 0; /* Increased bottom margin */
    display: flex;
    flex-wrap: wrap; /* Allow wrapping if needed */
    gap: 5px 10px; /* Row and column gap */
    padding-bottom: 10px; /* Add padding below */
    border-bottom: 1px dashed var(--border-color); /* Separator */
}

.institution-info span {
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.institution-info i {
    width: 14px; /* Align icons */
    text-align: center;
}

/* --- New Styles for Group Counts --- */
.institution-group-counts {
    display: flex;
    justify-content: space-around; /* Distribute items */
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    padding: 8px 5px; /* Add some padding */
    background-color: #f8f9fa; /* Light background */
    border-radius: 4px;
    font-size: 0.9rem;
    color: var(--text-dark);
}

.institution-group-counts span {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-weight: 500;
}

.institution-group-counts i {
    color: var(--primary-color);
    font-size: 0.9em;
}

/* --- New Styles for Financials --- */
.institution-financials {
    display: grid; /* Use grid for better alignment */
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); /* Responsive columns */
    gap: 8px 12px; /* Row and column gap */
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px dashed var(--border-color); /* Separator */
    font-size: 0.85rem; /* Slightly smaller font */
}

.institution-financials span {
    display: flex; /* Use flex for icon and text alignment */
    align-items: center;
    gap: 6px;
    background-color: #f0f4f8; /* Light blue-grey background */
    padding: 5px 8px;
    border-radius: 4px;
    color: var(--secondary-color); /* Use secondary color for text */
    font-weight: 500;
    white-space: nowrap; /* Prevent wrapping */
}

.institution-financials i {
    color: var(--accent-color); /* Use accent color for icons */
    width: 14px;
    text-align: center;
}

/* Specific icon colors if needed */
.institution-financials .fa-money-bill-wave { color: var (--success-color); }
.institution-financials .fa-percentage { color: var(--warning-color); }
.institution-financials .fa-file-invoice-dollar { color: var(--primary-dark); }

.card-actions {
    border-top: 1px solid var(--border-color);
    background-color: #fdfdfd;
    padding: 10px 15px;
    display: flex;
    justify-content: space-around; /* Distribute buttons evenly */
    gap: 10px;
}

.card-actions .action-btn {
    flex-grow: 1; /* Allow buttons to share space */
    padding: 6px 10px;
    font-size: 0.85rem;
    border-radius: 4px;
    border: 1px solid transparent;
    transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.card-actions .action-btn i {
    font-size: 0.9em;
}

/* Specific button styles */
.card-actions .details-btn {
    background-color: var(--info-light);
    color: var(--info-dark);
    border-color: var(--info-border);
}
.card-actions .details-btn:hover { background-color: var(--info-hover); }

.card-actions .edit-btn {
    background-color: var(--warning-light);
    color: var(--warning-dark);
    border-color: var(--warning-border);
}
.card-actions .edit-btn:hover { background-color: var(--warning-hover); }

.card-actions .delete-btn {
    background-color: var(--danger-light);
    color: var(--danger-dark);
    border-color: var(--danger-border);
}
.card-actions .delete-btn:hover { background-color: var(--danger-hover); }


/* Loading Placeholder Styles (if not already defined well) */
.loading-placeholder {
    grid-column: 1 / -1; /* Span full width if using grid */
    text-align: center;
    padding: 40px 20px;
    font-size: 1.1rem;
    color: var(--text-muted);
}

.loading-placeholder i {
    margin-left: 10px;
    font-size: 1.4rem;
    color: var(--primary-color);
}
.loading-placeholder.error {
    color: var(--danger-color);
}
.loading-placeholder.error i {
    color: var(--danger-color);
}

/* Responsive adjustments for cards */
@media (max-width: 768px) {
    .institutions-cards-container {
        grid-template-columns: 1fr; /* Stack cards on smaller screens */
    }
}

/* Enhanced Form Section - Modal Styling */
.form-section {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px); /* Add blur effect to background */
    -webkit-backdrop-filter: blur(4px); /* For Safari */
    justify-content: center;
    align-items: center;
    padding: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* Use .active to show the modal */
.form-section.active {
    display: flex;
    opacity: 1;
}

.form-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    width: 100%;
    max-width: 700px;
    margin: auto;
    position: relative;
    transform: translateY(30px);
    transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1), opacity 0.3s ease; /* Added opacity transition */
    overflow: hidden;
}

/* Use .active to trigger the transform */
.form-section.active .form-card {
    transform: translateY(0);
}

.card-header {
    padding: 15px 25px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-btn:hover {
    color: var(--danger-color);
    background-color: rgba(231, 76, 60, 0.1);
}

.card-body {
    padding: 20px 30px; /* Adjust padding */
    max-height: 75vh; /* Limit height and allow scrolling */
    overflow-y: auto;
}

/* Ensure this .form-row style is used inside the modal */
.form-row {
    display: grid;
    /* Adjust grid columns based on content, allow flexibility */
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 15px;
}

/* Style for fieldsets */
.form-fieldset {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px 20px 20px 20px; /* Add padding */
    margin-bottom: 25px; /* Space between fieldsets */
    position: relative;
}

.form-fieldset legend {
    font-weight: 600;
    color: var(--primary-dark);
    padding: 0 10px; /* Padding around legend text */
    margin-right: 10px; /* Position legend correctly in RTL */
    font-size: 1.1rem;
}

/* Style for required fields */
.required {
    color: var(--danger-color);
    margin-right: 3px;
}

/* Checkbox styling */
.form-group-checkbox {
    display: flex;
    align-items: center; /* Align checkbox and label vertically */
    padding-top: 25px; /* Align with input fields */
}

.form-group-checkbox label {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 0; /* Remove default margin */
}

.form-group-checkbox input[type="checkbox"] {
    width: auto; /* Override default width */
    margin-left: 8px; /* Space between checkbox and text */
    accent-color: var(--primary-color); /* Style checkbox color */
    height: 18px;
    width: 18px;
}

/* File input styling */
.form-group input[type="file"] {
    padding: 8px; /* Adjust padding */
    border: 1px dashed var(--border-color); /* Dashed border */
    background-color: #f9f9f9;
}

.form-group input[type="file"]::file-selector-button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    background-color: var(--primary-color);
    color: var(--text-light);
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-left: 10px; /* Space in RTL */
}

.form-group input[type="file"]::file-selector-button:hover {
    background-color: var(--primary-dark);
}

/* Small text hint */
.form-group small {
    display: block;
    margin-top: 5px;
    font-size: 0.85rem;
    color: var (--text-muted);
}

/* Responsive adjustments for modal */
@media (max-width: 768px) {
    /* ... existing responsive styles ... */
    .form-row {
        grid-template-columns: 1fr; /* Stack form elements on smaller screens */
        gap: 15px; /* Adjusted gap for stacked */
    }
    .form-group-checkbox {
        padding-top: 0; /* Adjust alignment when stacked */
        margin-top: 10px;
    }
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea { /* Added textarea */
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: all 0.3s ease;
    font-family: inherit; /* Ensure textarea uses the same font */
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus { /* Added textarea */
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.25);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.submit-btn,
.cancel-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.submit-btn {
    background-color: var(--success-color);
    color: var(--text-light);
}

.submit-btn:hover {
    background-color: #27ae60;
}

.cancel-btn {
    background-color: var(--light-color);
    color: var(--text-dark);
}

.cancel-btn:hover {
    background-color: #bdc3c7;
}

/* Message Styles */
.message {
    margin-top: 15px;
    padding: 12px;
    border-radius: var(--border-radius);
    text-align: center;
    font-weight: 500;
    display: none; /* Keep hidden by default */
    opacity: 0; /* Start hidden for transition */
    transition: opacity 0.3s ease;
}

.message.show {
    display: block; /* Use display block to take space */
    opacity: 1; /* Fade in */
}

.message.info {
    background-color: rgba(52, 152, 219, 0.1);
    color: var (--primary-color);
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.message.success {
    background-color: rgba(46, 204, 113, 0.1);
    color: var (--success-color);
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.message.error {
    background-color: rgba(231, 76, 60, 0.1);
    color: var (--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

/* Footer */
.dashboard-footer {
    background-color: var(--card-bg);
    text-align: center;
    padding: 15px;
    color: var(--text-muted);
    border-top: 1px solid var(--border-color);
    margin-top: auto;
}

/* Responsive */
@media (max-width: 992px) {
    .dashboard-main {
        padding: 15px;
    }
}

@media (max-width: 768px) {
    .stats-section {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .controls-grid {
        grid-template-columns: 1fr;
    }

    .search-container {
        order: -1;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

@media (max-width: 576px) {
    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .badge {
        margin-top: 5px;
        align-self: flex-start;
    }

    .form-actions {
        flex-direction: column;
    }

    .submit-btn,
    .cancel-btn {
        width: 100%;
    }

    .header-content h1 {
        font-size: 1.5rem;
    }
}

/* Mini form styles (for potential future use like adding buses) */
.mini-form-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.mini-form {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    width: 100%;
    max-width: 450px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    direction: rtl;
}

.mini-form h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: var(--primary-dark);
    font-size: 1.2rem;
    text-align: center;
}

.mini-form-group {
    margin-bottom: 15px;
}

.mini-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.mini-form-group input,
.mini-form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.95rem;
}

.mini-form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 25px;
}

.mini-form-actions button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.mini-form-save {
    background-color: var(--success-color);
    color: white;
}

.mini-form-cancel {
    background-color: var(--light-color);
    color: var(--text-dark);
}

.add-new-option {
    color: var(--primary-dark);
    font-weight: bold;
    background-color: #f0f8ff;
}

/* Status indicator styles */
.status-active { color: #2ecc71; font-weight: bold; }
.status-inactive { color: #e74c3c; font-weight: bold; }
/* Add specific status styles if needed */
.status-maintenance { color: var(--warning-color); font-weight: bold; }
.status-stopped { color: var(--danger-color); font-weight: bold; }


/* Additional pagination styles */
.pagination-ellipsis {
    padding: 8px 5px;
    color: var(--text-muted);
}
.pagination button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
.pagination button.active {
    background-color: var(--primary-color);
    color: var(--text-light);
    font-weight: bold;
}

/* Style for location button (if needed later) */
.location-btn {
    color: var(--primary-color);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border: 1px solid var(--primary-color);
    border-radius: 4px;
}
.location-btn:hover {
    background-color: rgba(52, 152, 219, 0.1);
    text-decoration: none;
}
.location-btn i {
     margin-left: 4px; /* RTL */
}

/* --- Modal Styles (Ensure these are generic enough or add specific ones) --- */
.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1050; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto; /* Enable scroll if needed */
    background-color: rgba(0, 0, 0, 0.6); /* Darker overlay */
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    align-items: center; /* Vertical center */
    justify-content: center; /* Horizontal center */
}

.modal.active { /* Use 'active' class to show */
    display: flex;
}

.modal-content {
    background-color: #fff;
    margin: auto;
    padding: 0; /* Remove padding, handle in header/body/footer */
    border-radius: var(--border-radius);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.25);
    width: 90%;
    max-width: 500px; /* Adjust width as needed */
    position: relative;
    animation: fadeInModal 0.3s ease-out;
    display: flex;
    flex-direction: column;
    max-height: 80vh; /* Limit height */
}

@keyframes fadeInModal {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
}

.modal-header h2 {
    margin: 0;
    font-size: 1.3rem;
    color: var(--primary-dark);
}

.close-modal-btn {
    background: none;
    border: none;
    font-size: 1.8rem;
    line-height: 1;
    color: #aaa;
    cursor: pointer;
    padding: 0 5px;
}
.close-modal-btn:hover {
    color: #333;
}

.modal-body {
    padding: 20px 25px;
    overflow-y: auto; /* Allow body scrolling if content overflows */
}

.modal-body .form-group {
    margin-bottom: 1rem;
}

.modal-body .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.modal-body .form-group select {
    width: 100%;
    padding: 0.7rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
}

.modal-footer {
    display: flex;
    justify-content: center; /* Center buttons */
    gap: 15px;
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    background-color: #f8f9fa;
    border-bottom-left-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
}

/* Use shared button styles if available */
.modal-footer .btn {
    padding: 8px 18px;
    font-size: 0.95rem;
    border-radius: 6px;
    cursor: pointer;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}
.modal-footer .btn-primary { background-color: var(--primary-color); color: white; }
.modal-footer .btn-primary:hover { background-color: var(--primary-dark); }
.modal-footer .btn-secondary { background-color: var(--secondary-color); color: white; }
.modal-footer .btn-secondary:hover { background-color: #5a6268; }
.modal-footer .btn:disabled { background-color: #ccc; cursor: not-allowed; }
