console.log("Bus Expenses (Single View) script loaded.");

// --- Auth Check ---
checkAuth('../../login.html'); // Adjusted path

// Supabase Initialization
let _supabase;
try {
    if (typeof supabase !== 'undefined' && supabase.createClient && typeof SUPABASE_URL !== 'undefined' && typeof SUPABASE_ANON_KEY !== 'undefined') {
        _supabase = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('Supabase Initialized for Bus Expenses (Single View)');
    } else {
        throw new Error('Supabase client or config variables not available.');
    }
} catch (error) {
    console.error('Error initializing Supabase client:', error);
    alert('خطأ فادح: لا يمكن الاتصال بقاعدة البيانات.');
    // Consider disabling the page or showing a persistent error
}


// --- DOM Elements ---
const pageMonthYearTitle = document.getElementById('page-month-year-title'); // May not be needed if month is in subtitle
const dashboardMessage = document.getElementById('dashboard-message');
const expensesTableBody = document.getElementById('expenses-tbody');
const expensesCountBadge = document.getElementById('expenses-count');
const listMessage = document.getElementById('list-message');
const paginationControls = document.getElementById('pagination-controls');

// Header
const mainHeaderTitle = document.getElementById('main-header-title');
const mainHeaderSubtitle = document.getElementById('main-header-subtitle');
const tableHeaderTitle = document.getElementById('table-header-title');
const activeMonthDisplay = document.getElementById('active-month-display'); // In Modal

// Dashboard Cards (Stats)
/*
const totalMaintenanceCard = document.getElementById('total-maintenance-card');
const totalFuelCard = document.getElementById('total-fuel-card');
const totalOtherCard = document.getElementById('total-other-card');
const totalExpensesAmountCard = document.getElementById('total-expenses-amount-card');
*/

// --- إضافة: تعريفات عناصر الإحصائيات المفصلة الجديدة ---
const statMaintenance = document.getElementById('stat-maintenance');
const statParts = document.getElementById('stat-parts');
const statInsurance = document.getElementById('stat-insurance');
const statOil = document.getElementById('stat-oil');
const statTires = document.getElementById('stat-tires');
const statRent = document.getElementById('stat-rent');
const statFuel = document.getElementById('stat-fuel');
const statLicenses = document.getElementById('stat-licenses');
const statWashing = document.getElementById('stat-washing');
const statInspection = document.getElementById('stat-inspection');
const statOther = document.getElementById('stat-other');
const statGrandTotal = document.getElementById('stat-grand-total');
// --- نهاية الإضافة ---

// Controls & Filters
const openAddExpenseModalBtn = document.getElementById('open-add-expense-modal-btn');
const searchInput = document.getElementById('search-input'); // Search by description
const filterExpenseTypeSelect = document.getElementById('filter-expense-type'); // Filter by type
const filterStartDateInput = document.getElementById('filter-start-date'); // Filter date range start
const filterEndDateInput = document.getElementById('filter-end-date');   // Filter date range end
const filterBtn = document.getElementById('filter-btn');
const resetFilterBtn = document.getElementById('reset-filter-btn');
const backToDashboardBtn = document.getElementById('back-to-dashboard-btn'); // Back button in header

// Expense Form (Modal)
const expenseFormSection = document.getElementById('expense-form-section');
const expenseForm = document.getElementById('expense-form');
const expenseFormTitle = document.getElementById('expense-form-title');
const closeExpenseFormBtn = document.getElementById('close-expense-form-btn');
const expenseFormMessage = document.getElementById('expense-form-message');
const expenseIdField = document.getElementById('expense_id');
const modalBusIdField = document.getElementById('modal_bus_id'); // Hidden field for bus_id
const modalMonthIdField = document.getElementById('modal_month_id'); // Hidden field for month_id

// Modal Form Fields
const expenseDateField = document.getElementById('expense_date');
const expenseTypeSelect = document.getElementById('expense_type'); // Changed to select
const amountNumericInput = document.getElementById('amount_numeric');
const includeTaxSelect = document.getElementById('include_tax');
const taxAmountInput = document.getElementById('tax_amount');
const totalWithTaxInput = document.getElementById('total_with_tax');
const bankIdSelect = document.getElementById('bank_id');
const detailsTextarea = document.getElementById('details');

// --- State ---
let selectedBudgetMonthId = null;
let selectedBudgetMonthInfo = null; // { id, name, year }
let selectedBusId = null; // Bus ID from URL
let selectedBusInfo = null; // { id, bus_number, plate_numbers }
let availableBanks = [];
let currentBusExpenses = []; // All expenses for the selected bus & month (filtered)
let currentPage = 1;
const itemsPerPage = 15;
let totalItems = 0;
const VAT_RATE = 0.15;

// --- Helper Functions ---
const showMessage = (element, message, type = 'info', duration = 5000) => {
    if (!element) return;
    element.textContent = message;
    element.className = `message ${type} show`;
    element.style.display = 'block';
    // Clear previous timeouts if any
    if (element.timeoutId) {
        clearTimeout(element.timeoutId);
    }
    if (duration > 0) {
        element.timeoutId = setTimeout(() => {
            if (element.textContent === message) {
                 element.style.display = 'none';
                 element.classList.remove('show');
            }
            element.timeoutId = null;
        }, duration);
    } else {
         element.timeoutId = null;
    }
};

const formatCurrency = (amount) => {
    if (amount === null || amount === undefined || isNaN(amount)) return '0.00';
    return parseFloat(amount).toFixed(2);
};

const formatDate = (dateString) => {
    if (!dateString) return '';
    try {
        // Adjust for timezone offset before formatting
        const date = new Date(dateString);
        const offset = date.getTimezoneOffset();
        const adjustedDate = new Date(date.getTime() - (offset * 60 * 1000));
        return adjustedDate.toISOString().split('T')[0];
    } catch (e) { return dateString; }
};

const getMonthName = (monthNumber) => {
    const names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"];
    const index = parseInt(monthNumber, 10) - 1;
    return names[index] || `شهر ${monthNumber}`;
};

// --- Get Bus ID from URL ---
const getBusIdFromUrl = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const busId = urlParams.get('bus_id');
    console.log("Bus ID from URL:", busId);
    return busId;
};

// --- Load Selected Month from Storage ---
const loadSelectedMonthFromStorage = () => {
    try {
        console.log('[bus_expenses] Attempting to read selected month/year from sessionStorage...');
        const monthId = sessionStorage.getItem('selectedBudgetMonthId');
        const monthNumber = sessionStorage.getItem('selectedBudgetMonthNumber');
        const year = sessionStorage.getItem('selectedBudgetYearNumber');
        console.log(`[bus_expenses] Read from sessionStorage: ID=${monthId}, MonthNumber=${monthNumber}, Year=${year}`);

        if (monthId && monthNumber && year) {
            selectedBudgetMonthId = monthId;
            selectedBudgetMonthInfo = {
                id: monthId,
                name: getMonthName(monthNumber),
                year: year
            };
            console.log('[bus_expenses] Selected Budget Month:', selectedBudgetMonthInfo);

            // Update display in modal
            if (activeMonthDisplay) {
                activeMonthDisplay.textContent = `${selectedBudgetMonthInfo.name} ${selectedBudgetMonthInfo.year}`;
            }
            // Update subtitle (optional, can be combined with bus info)
            if (mainHeaderSubtitle) {
                 mainHeaderSubtitle.textContent = `ملخص مصاريف الشهر المحدد: ${selectedBudgetMonthInfo.name} ${selectedBudgetMonthInfo.year}`;
            }
            return true; // Success

        } else {
            throw new Error('لم يتم العثور على معلومات الشهر المحدد في sessionStorage. يرجى اختيار شهر أولاً.');
        }
    } catch (error) {
        console.error('Error loading selected budget month from storage:', error);
        showMessage(dashboardMessage, `خطأ في تحديد الشهر: ${error.message}`, 'error', 0);
        selectedBudgetMonthId = null;
        selectedBudgetMonthInfo = null;
        if (activeMonthDisplay) {
            activeMonthDisplay.textContent = 'خطأ';
            activeMonthDisplay.style.color = 'red';
        }
        if (mainHeaderSubtitle) {
            mainHeaderSubtitle.textContent = 'خطأ في تحديد الشهر المحدد';
            mainHeaderSubtitle.style.color = 'red';
        }
        // Disable functionality dependent on month
        if (openAddExpenseModalBtn) openAddExpenseModalBtn.disabled = true;
        if (expensesTableBody) expensesTableBody.innerHTML = `<tr><td colspan="10" class="loading-message error">خطأ: ${error.message}</td></tr>`;
        return false; // Failure
    }
};


// --- Data Fetching ---
const fetchBusInfo = async (busId) => {
    if (!_supabase || !busId) return;
    console.log("Fetching info for Bus ID:", busId);
    try {
        const { data, error } = await _supabase
            .from('buses')
            .select('id, bus_number, plate_numbers')
            .eq('id', busId)
            .single();

        if (error) throw error;

        if (data) {
            selectedBusInfo = data;
            console.log("Bus Info:", selectedBusInfo);
            // Update Header
            const busIdentifier = selectedBusInfo.bus_number || selectedBusInfo.plate_numbers || 'غير معروف';
            if (mainHeaderTitle) mainHeaderTitle.innerHTML = `<i class="fas fa-bus"></i> مصاريف الحافلة: ${busIdentifier}`;
            if (mainHeaderSubtitle && selectedBudgetMonthInfo) { // Combine bus and month info
                 mainHeaderSubtitle.textContent = `عرض وإدارة مصاريف الحافلة ${busIdentifier} لشهر ${selectedBudgetMonthInfo.name} ${selectedBudgetMonthInfo.year}.`;
            } else if (mainHeaderSubtitle) {
                 mainHeaderSubtitle.textContent = `عرض وإدارة مصاريف الحافلة ${busIdentifier}.`;
            }
            if (tableHeaderTitle) tableHeaderTitle.textContent = `قائمة مصاريف الحافلة ${busIdentifier}`;
        } else {
            throw new Error('لم يتم العثور على بيانات الحافلة.');
        }
    } catch (error) {
        console.error('Error fetching bus info:', error);
        showMessage(dashboardMessage, `خطأ في جلب بيانات الحافلة: ${error.message}`, 'error', 0);
        if (mainHeaderTitle) mainHeaderTitle.textContent = 'خطأ في تحميل بيانات الحافلة';
        selectedBusInfo = null;
        // Disable add button if bus info fails
        if (openAddExpenseModalBtn) openAddExpenseModalBtn.disabled = true;
    }
};

const fetchBanks = async () => {
    // ... (Keep existing fetchBanks function) ...
    if (!_supabase) return;
    try {
        const { data, error } = await _supabase
            .from('banks')
            .select('id, name')
            .order('name', { ascending: true });
        if (error) throw error;
        availableBanks = data || [];
        populateBankDropdown();
    } catch (error) {
        console.error('Error fetching banks:', error);
        showMessage(dashboardMessage, `خطأ في جلب قائمة البنوك: ${error.message}`, 'error');
    }
};

const fetchExpenses = async () => {
    // Ensure bus and month are selected
    if (!_supabase || !selectedBusId || !selectedBudgetMonthId) {
        console.warn("Skipping fetchExpenses: Missing busId or selectedBudgetMonthId.");
        expensesTableBody.innerHTML = `<tr><td colspan="10" class="loading-message">الرجاء التأكد من تحديد الحافلة والشهر.</td></tr>`;
        if (paginationControls) paginationControls.innerHTML = '';
        if (expensesCountBadge) expensesCountBadge.textContent = '0';
        resetBusStats();
        return;
    }

    const busIdentifier = selectedBusInfo?.bus_number || selectedBusInfo?.plate_numbers || 'المحددة';
    expensesTableBody.innerHTML = `<tr><td colspan="10" class="loading-message"><i class="fas fa-spinner fa-spin"></i> جاري تحميل مصاريف الحافلة ${busIdentifier}...</td></tr>`;
    if (paginationControls) paginationControls.innerHTML = '';
    if (listMessage) listMessage.style.display = 'none';
    resetBusStats();

    // Get filter values
    const searchTerm = searchInput.value.trim();
    const filterType = filterExpenseTypeSelect.value;
    const filterStart = filterStartDateInput.value;
    const filterEnd = filterEndDateInput.value;

    console.log("Fetching expenses with filters:", { selectedBusId, selectedBudgetMonthId, filterType, filterStart, filterEnd, searchTerm });

    try {
        let query = _supabase
            .from('bus_expenses')
            .select('*, banks(name)', { count: 'exact' })
            .eq('bus_id', selectedBusId)
            .eq('budget_month_id', selectedBudgetMonthId);

        // Apply Filters
        if (filterType) {
            query = query.eq('expense_type', filterType);
        }
        if (filterStart) {
            query = query.gte('expense_date', filterStart);
        }
        if (filterEnd) {
            // Add 1 day to include the end date fully if needed, or adjust based on DB behavior
            // query = query.lte('expense_date', filterEnd);
             const endDate = new Date(filterEnd);
             endDate.setDate(endDate.getDate() + 1); // Include the whole end day
             query = query.lt('expense_date', endDate.toISOString().split('T')[0]);
        }
        if (searchTerm) {
            // Search in details (case-insensitive)
            query = query.ilike('details', `%${searchTerm}%`);
        }

        // Ordering and Pagination
        const startIndex = (currentPage - 1) * itemsPerPage;
        query = query.order('expense_date', { ascending: false })
                     .order('created_at', { ascending: false })
                     .range(startIndex, startIndex + itemsPerPage - 1);

        const { data, error, count } = await query;

        if (error) throw error;

        currentBusExpenses = data || []; // Store only the paginated items for rendering
        totalItems = count || 0; // Total count matching filters

        if (expensesCountBadge) expensesCountBadge.textContent = totalItems;

        // Fetch summary based on *filtered* results if needed, or fetch separately without pagination
        // For simplicity, let's calculate stats based on the fetched page data for now.
        // A separate summary fetch might be better for accuracy across all filtered items.
        await fetchExpensesSummary(filterType, filterStart, filterEnd, searchTerm); // Fetch summary with filters

        renderExpensesTable(); // Render the fetched page

        if (totalItems === 0) {
            const filterActive = filterType || filterStart || filterEnd || searchTerm;
            const messageText = filterActive ? 'لا توجد مصاريف تطابق معايير التصفية.' : `لا توجد مصاريف مسجلة لهذه الحافلة في الشهر المحدد.`;
            showMessage(listMessage, messageText, 'info');
            expensesTableBody.innerHTML = `<tr><td colspan="10" class="loading-message">${messageText}</td></tr>`;
        } else {
             listMessage.style.display = 'none';
        }

    } catch (error) {
        console.error('Error fetching bus expenses:', error);
        showMessage(listMessage, `خطأ في جلب المصروفات: ${error.message}`, 'error', 0);
        expensesTableBody.innerHTML = `<tr><td colspan="10" class="loading-message">خطأ في تحميل البيانات.</td></tr>`;
    }
};

// Fetch summary based on filters (without pagination)
const fetchExpensesSummary = async (filterType = '', filterStart = '', filterEnd = '', searchTerm = '') => {
    if (!_supabase || !selectedBusId || !selectedBudgetMonthId) {
        resetBusStats();
        return;
    }
    console.log("Fetching expense summary with filters:", { filterType, filterStart, filterEnd, searchTerm });
    try {
        let query = _supabase
            .from('bus_expenses')
            .select('expense_type, total_with_tax') // Select only needed columns for summary
            .eq('bus_id', selectedBusId)
            .eq('budget_month_id', selectedBudgetMonthId);

        // Apply Filters (same as fetchExpenses)
        if (filterType) query = query.eq('expense_type', filterType);
        if (filterStart) query = query.gte('expense_date', filterStart);
        if (filterEnd) {
             const endDate = new Date(filterEnd);
             endDate.setDate(endDate.getDate() + 1);
             query = query.lt('expense_date', endDate.toISOString().split('T')[0]);
        }
        if (searchTerm) query = query.ilike('details', `%${searchTerm}%`);

        const { data, error } = await query;

        if (error) throw error;

        calculateAndDisplayBusStats(data || []); // Calculate stats on all matching expenses

    } catch (error) {
        console.error('Error fetching expense summary:', error);
        showMessage(dashboardMessage, `خطأ في حساب ملخص المصروفات: ${error.message}`, 'warning');
        resetBusStats();
    }
};


// --- UI Population ---
const populateBankDropdown = () => {
    // ... (Keep existing populateBankDropdown function) ...
    if (!bankIdSelect) return;
    const firstOption = bankIdSelect.options[0];
    bankIdSelect.innerHTML = '';
    if (firstOption) bankIdSelect.appendChild(firstOption);

    availableBanks.forEach(bank => {
        const option = document.createElement('option');
        option.value = bank.id;
        option.textContent = bank.name;
        bankIdSelect.appendChild(option);
    });
};

// --- Stat Calculation ---
// --- تعديل: دالة إعادة تعيين الإحصائيات ---
const resetBusStats = () => {
    const zeroAmount = '0.00 ريال';
    if (statMaintenance) statMaintenance.textContent = zeroAmount;
    if (statParts) statParts.textContent = zeroAmount;
    if (statInsurance) statInsurance.textContent = zeroAmount;
    if (statOil) statOil.textContent = zeroAmount;
    if (statTires) statTires.textContent = zeroAmount;
    if (statRent) statRent.textContent = zeroAmount;
    if (statFuel) statFuel.textContent = zeroAmount;
    if (statLicenses) statLicenses.textContent = zeroAmount;
    if (statWashing) statWashing.textContent = zeroAmount;
    if (statInspection) statInspection.textContent = zeroAmount;
    if (statOther) statOther.textContent = zeroAmount;
    if (statGrandTotal) statGrandTotal.textContent = zeroAmount;
};
// --- نهاية التعديل ---

// --- تعديل: دالة حساب وعرض الإحصائيات ---
const calculateAndDisplayBusStats = (expenses) => {
    // Reset all stats first
    resetBusStats();

    // Initialize totals for each category
    let totals = {
        'صيانة': 0,
        'قطع غيار': 0,
        'تأمين': 0,
        'تغير زيت': 0,
        'تغير كفرات': 0,
        'ايجار': 0,
        'وقود': 0,
        'تراخيص': 0,
        'غسيل': 0,
        'فحص دوري': 0,
        'أخرى': 0,
        'grandTotal': 0
    };

    expenses.forEach(exp => {
        const amount = parseFloat(exp.total_with_tax || 0);
        const type = exp.expense_type;

        // Add to grand total
        totals.grandTotal += amount;

        // Add to specific category total
        if (totals.hasOwnProperty(type)) {
            totals[type] += amount;
        } else {
            // If type is not explicitly listed, add to 'أخرى'
            totals['أخرى'] += amount;
        }
    });

    // Update DOM elements
    if (statMaintenance) statMaintenance.textContent = `${formatCurrency(totals['صيانة'])} ريال`;
    if (statParts) statParts.textContent = `${formatCurrency(totals['قطع غيار'])} ريال`;
    if (statInsurance) statInsurance.textContent = `${formatCurrency(totals['تأمين'])} ريال`;
    if (statOil) statOil.textContent = `${formatCurrency(totals['تغير زيت'])} ريال`;
    if (statTires) statTires.textContent = `${formatCurrency(totals['تغير كفرات'])} ريال`;
    if (statRent) statRent.textContent = `${formatCurrency(totals['ايجار'])} ريال`;
    if (statFuel) statFuel.textContent = `${formatCurrency(totals['وقود'])} ريال`;
    if (statLicenses) statLicenses.textContent = `${formatCurrency(totals['تراخيص'])} ريال`;
    if (statWashing) statWashing.textContent = `${formatCurrency(totals['غسيل'])} ريال`;
    if (statInspection) statInspection.textContent = `${formatCurrency(totals['فحص دوري'])} ريال`;
    if (statOther) statOther.textContent = `${formatCurrency(totals['أخرى'])} ريال`;
    if (statGrandTotal) statGrandTotal.textContent = `${formatCurrency(totals.grandTotal)} ريال`;
};
// --- نهاية التعديل ---

// --- Table Rendering & Pagination ---
const renderExpensesTable = () => { // Renamed from renderExpensesTablePaginated
    expensesTableBody.innerHTML = '';
    if (!currentBusExpenses || currentBusExpenses.length === 0) {
        // Message handled by fetchExpenses
        if (paginationControls) paginationControls.innerHTML = '';
        return;
    }

    const startIndex = (currentPage - 1) * itemsPerPage;

    currentBusExpenses.forEach((expense, index) => {
        const row = document.createElement('tr');
        const sequenceNumber = startIndex + index + 1;
        const paymentMethodText = 'بنك'; // Assuming always bank

        row.innerHTML = `
            <td>${sequenceNumber}</td>
            <td>${expense.expense_type || 'غير محدد'}</td>
            <td>${formatCurrency(expense.amount)}</td>
            <td>${formatCurrency(expense.tax_amount)}</td>
            <td>${formatCurrency(expense.total_with_tax)}</td>
            <td>${formatDate(expense.expense_date)}</td>
            <td>${paymentMethodText}</td>
            <td>${expense.banks?.name || 'N/A'}</td>
            <td>${expense.details || '-'}</td>
            <td>
                <button class="action-btn edit-btn" data-id="${expense.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                <button class="action-btn delete-btn" data-id="${expense.id}" title="حذف"><i class="fas fa-trash-alt"></i></button>
            </td>
        `;
        expensesTableBody.appendChild(row);
    });
    renderPaginationControls(); // Render controls based on totalItems
};

const renderPaginationControls = () => {
    // ... (Keep existing renderPaginationControls function, it uses totalItems) ...
     if (!paginationControls || totalItems <= itemsPerPage) {
        if (paginationControls) paginationControls.innerHTML = '';
        return;
    }

    paginationControls.innerHTML = '';
    const totalPages = Math.ceil(totalItems / itemsPerPage);

    // Previous Button
    const prevButton = document.createElement('button');
    prevButton.innerHTML = '<i class="fas fa-angle-right"></i>'; // RTL Previous
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', () => goToPage(currentPage - 1));
    paginationControls.appendChild(prevButton);

    // Page Info Span
    const pageInfo = document.createElement('span');
    pageInfo.textContent = `صفحة ${currentPage} من ${totalPages}`;
    pageInfo.className = 'page-info';
    paginationControls.appendChild(pageInfo);

    // Next Button
    const nextButton = document.createElement('button');
    nextButton.innerHTML = '<i class="fas fa-angle-left"></i>'; // RTL Next
    nextButton.disabled = currentPage === totalPages;
    nextButton.addEventListener('click', () => goToPage(currentPage + 1));
    paginationControls.appendChild(nextButton);
};

const goToPage = (page) => {
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    if (page < 1 || page > totalPages) return;
    currentPage = page;
    fetchExpenses(); // Fetch data for the new page with current filters
};

// --- Modal Logic ---
const openExpenseModal = (expenseToEdit = null) => {
    if (!expenseFormSection || !selectedBusId || !selectedBudgetMonthId) {
        showMessage(dashboardMessage, 'خطأ: لا يمكن فتح النموذج. الشهر غير محدد أو الحافلة غير محددة.', 'error');
        return;
    }
    console.log("Opening expense modal for Bus ID:", selectedBusId, "Month ID:", selectedBudgetMonthId);
    resetExpenseForm();

    const busIdentifier = selectedBusInfo?.bus_number || selectedBusInfo?.plate_numbers || 'المحددة';
    expenseFormTitle.textContent = expenseToEdit ? `تعديل مصروف للحافلة: ${busIdentifier}` : `إضافة مصروف للحافلة: ${busIdentifier}`;
    modalBusIdField.value = selectedBusId; // Set hidden bus ID
    modalMonthIdField.value = selectedBudgetMonthId; // Set hidden month ID

    // Update month display in modal
    if (activeMonthDisplay && selectedBudgetMonthInfo) {
        activeMonthDisplay.textContent = `${selectedBudgetMonthInfo.name} ${selectedBudgetMonthInfo.year}`;
    } else if (activeMonthDisplay) {
        activeMonthDisplay.textContent = 'الشهر المحدد';
    }

    if (expenseToEdit) {
        // Populate form for editing
        expenseIdField.value = expenseToEdit.id;
        expenseDateField.value = formatDate(expenseToEdit.expense_date);
        expenseTypeSelect.value = expenseToEdit.expense_type || ''; // Use select element
        amountNumericInput.value = expenseToEdit.amount;
        includeTaxSelect.value = expenseToEdit.include_tax ? 'true' : 'false';
        bankIdSelect.value = expenseToEdit.bank_id || '';
        detailsTextarea.value = expenseToEdit.details || '';
        calculateTax(); // Recalculate tax/total
    } else {
        // Adding new - Reset ID and set default date
        expenseIdField.value = '';
        if (expenseDateField) expenseDateField.value = formatDate(new Date()); // Default to today
        if (includeTaxSelect) includeTaxSelect.value = 'false'; // Default no tax
        calculateTax(); // Calculate initial tax/total (should be 0)
    }

    expenseFormSection.classList.add('show');
    document.body.style.overflow = 'hidden';
    setTimeout(() => expenseDateField.focus(), 100);
};

const closeExpenseModal = () => {
    // ... (Keep existing closeExpenseModal function) ...
    if (!expenseFormSection) return;
    expenseFormSection.classList.remove('show');
    document.body.style.overflow = '';
    resetExpenseForm();
};

const resetExpenseForm = () => {
    // ... (Keep existing resetExpenseForm function, ensure it resets the select) ...
    if (expenseForm) expenseForm.reset();
    if (expenseIdField) expenseIdField.value = '';
    if (modalBusIdField) modalBusIdField.value = '';
    if (modalMonthIdField) modalMonthIdField.value = '';

    // Reset calculated fields and payment method display
    if(amountNumericInput) amountNumericInput.value = '';
    if(taxAmountInput) taxAmountInput.value = '0.00';
    if(totalWithTaxInput) totalWithTaxInput.value = '0.00';
    if(bankIdSelect) bankIdSelect.value = '';
    if(includeTaxSelect) includeTaxSelect.value = 'false';
    if(expenseDateField) expenseDateField.value = '';
    if(expenseTypeSelect) expenseTypeSelect.value = ''; // Reset select
    if(detailsTextarea) detailsTextarea.value = '';

    // Reset messages
    if (expenseFormMessage) expenseFormMessage.style.display = 'none';
    if (expenseFormMessage) expenseFormMessage.className = 'message';
};

// --- Form Calculations ---
const calculateTax = () => {
    // ... (Keep existing calculateTax function) ...
    const amount = parseFloat(amountNumericInput.value) || 0;
    const includeTax = includeTaxSelect.value === 'true';
    let taxAmount = 0;
    let totalWithTax = amount;

    if (includeTax && amount > 0) {
        taxAmount = amount * VAT_RATE;
        totalWithTax = amount + taxAmount;
    }

    if (taxAmountInput) taxAmountInput.value = formatCurrency(taxAmount);
    if (totalWithTaxInput) totalWithTaxInput.value = formatCurrency(totalWithTax);
};

// --- Add Bank Transaction ---
// تم حذف دالة addBankTransactionForBusExpense لأنها تم استبدالها بالتريجرات في قاعدة البيانات

// --- Form Submission ---
const handleExpenseSubmit = async (event) => {
    // ... (Keep existing handleExpenseSubmit function, ensure it reads from expenseTypeSelect) ...
    event.preventDefault();
    if (!selectedBudgetMonthId || !selectedBusId) {
        showMessage(expenseFormMessage, 'خطأ: بيانات غير مكتملة (الحافلة/الشهر).', 'error');
        return;
    }

    const submitBtn = expenseForm.querySelector('.submit-btn');
    submitBtn.disabled = true;
    submitBtn.textContent = 'جاري الحفظ...';
    showMessage(expenseFormMessage, 'جاري حفظ المصروف...', 'info', 0);

    calculateTax(); // Ensure calculations are up-to-date

    const expenseData = {
        bus_id: selectedBusId,
        budget_month_id: selectedBudgetMonthId,
        expense_date: expenseDateField.value,
        expense_type: expenseTypeSelect.value, // Read from select
        amount: parseFloat(amountNumericInput.value) || 0,
        include_tax: includeTaxSelect.value === 'true',
        tax_amount: parseFloat(taxAmountInput.value) || 0,
        payment_method: "Bank", // Assuming always Bank
        bank_id: bankIdSelect.value || null,
        details: detailsTextarea.value.trim() || null,
        // total_with_tax is generated by DB
    };

    let savedExpense = null;
    let bankTransactionWarning = null;
    const expenseId = expenseIdField.value; // Get ID for update check

    try {
        // --- Validation ---
        if (!expenseData.expense_date) throw new Error('تاريخ المصروف مطلوب.');
        if (!expenseData.expense_type) throw new Error('نوع المصروف مطلوب.');
        if (expenseData.amount <= 0) throw new Error('المبلغ الأساسي يجب أن يكون أكبر من صفر.');
        if ((parseFloat(totalWithTaxInput.value) || 0) <= 0) throw new Error('المبلغ الإجمالي (شامل الضريبة) يجب أن يكون أكبر من صفر.');
        if (!expenseData.bank_id) throw new Error('البنك مطلوب.');

        console.log("Attempting to save expense data:", expenseData);

        if (expenseId) {
            // Update
            console.log("Updating expense ID:", expenseId);
            const { data, error } = await _supabase
                .from('bus_expenses')
                .update(expenseData)
                .eq('id', expenseId)
                .select()
                .single();
            if (error) throw error;
            savedExpense = data;
            console.log("Expense updated:", savedExpense);
            bankTransactionWarning = "تم تحديث المصروف. قد تحتاج المعاملة البنكية المرتبطة للمراجعة اليدوية.";

        } else {
            // Insert
            const { data, error } = await _supabase
                .from('bus_expenses')
                .insert([expenseData])
                .select()
                .single();
            if (error) throw error;
            savedExpense = data;
            console.log("Expense inserted:", savedExpense);

            // تم حذف إنشاء المعاملة البنكية يدوياً لأن التريجر في قاعدة البيانات يقوم بهذا تلقائياً
            console.log(`[Expense Success] Bus expense saved with ID: ${savedExpense?.id}. Bank transaction will be created automatically by database trigger.`);
        }

        // --- Success/Warning Message ---
        if (bankTransactionWarning) {
             showMessage(expenseFormMessage, `تم حفظ المصروف، لكن: ${bankTransactionWarning}`, 'warning', 7000);
        } else {
             const successMessage = expenseId ? 'تم تحديث المصروف بنجاح!' : 'تم حفظ المصروف بنجاح!';
             showMessage(expenseFormMessage, successMessage, 'success');
        }

        fetchExpenses(); // Refresh table with current filters
        setTimeout(closeExpenseModal, bankTransactionWarning ? 7000 : 1500);

    } catch (error) {
        console.error('Error saving expense:', error);
        showMessage(expenseFormMessage, `خطأ في حفظ المصروف: ${error.message}`, 'error', 0);
    } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = expenseId ? 'تحديث المصروف' : 'حفظ المصروف';
    }
};

// --- Delete Handling ---
const handleDeleteExpense = async (expenseId) => {
    // ... (Keep existing handleDeleteExpense function) ...
    if (!_supabase) return;

    const confirmation = confirm('هل أنت متأكد من حذف هذا المصروف؟ سيتم أيضاً محاولة حذف المعاملة البنكية المرتبطة.');
    if (!confirmation) return;

    showMessage(listMessage, 'جاري حذف المصروف...', 'info', 0);

    try {
        // 1. Delete from bus_expenses
        const { error: deleteError } = await _supabase
            .from('bus_expenses')
            .delete()
            .eq('id', expenseId);

        if (deleteError) throw new Error(`فشل حذف المصروف: ${deleteError.message}`);

        // 2. Attempt to delete corresponding bank transaction
        const { error: bankTxDeleteError } = await _supabase
            .from('bank_transactions')
            .delete()
            .eq('reference_id', expenseId)
            .eq('reference_table', 'bus_expenses'); // Match reference table

        if (bankTxDeleteError) {
            console.warn("Expense deleted, but failed to delete linked bank transaction:", bankTxDeleteError);
            showMessage(listMessage, `تم حذف المصروف، ولكن فشل حذف المعاملة البنكية المرتبطة: ${bankTxDeleteError.message}. الرجاء المراجعة اليدوية.`, 'warning', 0);
        } else {
            showMessage(listMessage, 'تم حذف المصروف والمعاملة البنكية المرتبطة بنجاح.', 'success');
        }

        // Refresh data after deletion
        currentPage = 1; // Reset to first page after delete
        fetchExpenses();

    } catch (error) {
        console.error('Error deleting expense:', error);
        showMessage(listMessage, `خطأ في عملية الحذف: ${error.message}`, 'error', 0);
    }
};

// --- Event Listeners Setup ---
const setupEventListeners = () => {
    // Back button (Header)
    if (backToDashboardBtn) {
        backToDashboardBtn.addEventListener('click', () => {
            // Navigate back to the main bus expenses dashboard
            window.location.href = 'bus_expenses_dashboard.html'; // Adjust if needed
        });
    }

    // Open Modal Button
    if (openAddExpenseModalBtn) {
        openAddExpenseModalBtn.addEventListener('click', () => openExpenseModal());
    }

    // Modal Close Buttons
    if (closeExpenseFormBtn) closeExpenseFormBtn.addEventListener('click', closeExpenseModal);
    if (expenseFormSection) expenseFormSection.addEventListener('click', (e) => {
        if (e.target === expenseFormSection) closeExpenseModal();
    });

    // Form Submission
    if (expenseForm) {
        expenseForm.addEventListener('submit', handleExpenseSubmit);
    }

    // Form Calculation Listeners
    if (amountNumericInput) amountNumericInput.addEventListener('input', calculateTax);
    if (includeTaxSelect) includeTaxSelect.addEventListener('change', calculateTax);

    // Filter Listeners
    if (filterBtn) filterBtn.addEventListener('click', () => {
        currentPage = 1; // Reset page when applying filters
        fetchExpenses();
    });
    if (resetFilterBtn) resetFilterBtn.addEventListener('click', () => {
        // Reset filter inputs
        if (filterExpenseTypeSelect) filterExpenseTypeSelect.value = '';
        if (filterStartDateInput) filterStartDateInput.value = '';
        if (filterEndDateInput) filterEndDateInput.value = '';
        if (searchInput) searchInput.value = '';
        // Reset page and fetch all
        currentPage = 1;
        fetchExpenses();
    });
    // Optional: Trigger filter on Enter in search input
    if (searchInput) searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            if (filterBtn) filterBtn.click();
        }
    });

    // Table Action Buttons Listener (Delegation)
    if (expensesTableBody) {
        expensesTableBody.addEventListener('click', (event) => {
            const editButton = event.target.closest('.edit-btn');
            const deleteButton = event.target.closest('.delete-btn');

            if (editButton) {
                const expenseId = editButton.dataset.id;
                // Find the full expense object from the *original unfiltered list* if possible,
                // or re-fetch the single expense if needed for editing.
                // For simplicity, let's assume currentBusExpenses holds the data for the visible rows.
                const expenseToEdit = currentBusExpenses.find(exp => exp.id == expenseId);
                if (expenseToEdit) {
                    openExpenseModal(expenseToEdit);
                } else {
                    console.error("Expense not found for editing:", expenseId);
                    showMessage(listMessage, 'خطأ: المصروف المحدد للتعديل غير موجود.', 'error');
                }
            } else if (deleteButton) {
                const expenseId = deleteButton.dataset.id;
                handleDeleteExpense(expenseId);
            }
        });
    }

    // Global Escape Listener
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && expenseFormSection?.classList.contains('show')) {
            closeExpenseModal();
        }
    });
};

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log('Bus Expenses (Single View) page DOM loaded.');

    // Ensure Supabase is ready
    if (!_supabase) {
        showMessage(dashboardMessage, 'خطأ فادح: لم يتم تهيئة الاتصال بقاعدة البيانات.', 'error', 0);
        return;
    }

    // 1. Get Bus ID from URL
    selectedBusId = getBusIdFromUrl();
    if (!selectedBusId) {
        showMessage(dashboardMessage, 'خطأ حرج: لم يتم تحديد معرف الحافلة في عنوان الصفحة.', 'error', 0);
        if (mainHeaderTitle) mainHeaderTitle.textContent = 'خطأ: حافلة غير محددة';
        if (expensesTableBody) expensesTableBody.innerHTML = `<tr><td colspan="10" class="loading-message error">الرجاء العودة واختيار حافلة.</td></tr>`;
        if (openAddExpenseModalBtn) openAddExpenseModalBtn.disabled = true;
        return; // Stop loading if no bus ID
    }

    // 2. Load Selected Month from Storage
    const monthLoaded = loadSelectedMonthFromStorage();
    if (!monthLoaded) {
        // Error message shown by loadSelectedMonthFromStorage
        if (openAddExpenseModalBtn) openAddExpenseModalBtn.disabled = true;
        return; // Stop loading if month is not loaded
    }

    // Show initial loading states
    if (mainHeaderTitle) mainHeaderTitle.textContent = 'جاري تحميل بيانات الحافلة...';
    if (expensesTableBody) expensesTableBody.innerHTML = `<tr><td colspan="10" class="loading-message"><i class="fas fa-spinner fa-spin"></i> جاري تحميل البيانات الأولية...</td></tr>`;
    resetBusStats();
    if (openAddExpenseModalBtn) openAddExpenseModalBtn.disabled = true; // Keep disabled until data loads

    // 3. Fetch Bus Info and Banks in parallel
    try {
        await Promise.all([
            fetchBusInfo(selectedBusId),
            fetchBanks() // For the modal dropdown
        ]);
    } catch (error) {
         // Errors handled within the functions
         // Ensure add button remains disabled if fetchBusInfo failed
         if (!selectedBusInfo && openAddExpenseModalBtn) openAddExpenseModalBtn.disabled = true;
    }


    // 4. Fetch initial expenses (if bus info and month are valid)
    if (selectedBusInfo && selectedBudgetMonthId) {
        if (openAddExpenseModalBtn) openAddExpenseModalBtn.disabled = false; // Enable add button
        await fetchExpenses(); // Initial fetch (no filters)
    } else {
        // Errors likely shown already
        if (openAddExpenseModalBtn) openAddExpenseModalBtn.disabled = true;
    }

    // 5. Setup event listeners
    setupEventListeners();
});