// Supabase Initialization - using variables already defined in config.js
let supabaseUrl, supabaseAnonKey;

// Safely access the global variables
if (typeof SUPABASE_URL !== 'undefined') {
    supabaseUrl = SUPABASE_URL;
} else {
    supabaseUrl = 'YOUR_FALLBACK_SUPABASE_URL'; // Add fallback or handle error
    console.error('SUPABASE_URL not found in global scope.');
}

if (typeof SUPABASE_ANON_KEY !== 'undefined') {
    supabaseAnonKey = SUPABASE_ANON_KEY;
} else {
    supabaseAnonKey = 'YOUR_FALLBACK_SUPABASE_ANON_KEY'; // Add fallback or handle error
    console.error('SUPABASE_ANON_KEY not found in global scope.');
}

const { createClient } = supabase;
const _supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('Supabase Initialized for Institutions:', _supabase);

// --- DOM Elements ---
const totalInstitutionsStat = document.getElementById('total-institutions');
const activeContractsStat = document.getElementById('active-contracts'); // Example
const addInstitutionBtn = document.getElementById('add-institution-btn');
const searchInput = document.getElementById('search-input');
const searchBtn = document.getElementById('search-btn');
const printReportBtn = document.getElementById('print-report-btn');
// const institutionsTableBody = document.getElementById('institutions-tbody'); // Remove or comment out
const institutionsCardsContainer = document.getElementById('institutions-cards-container'); // New Card Container
const institutionsCountBadge = document.getElementById('institutions-count');
const listMessage = document.getElementById('list-message');
const paginationControls = document.getElementById('pagination-controls');
const addInstitutionSection = document.getElementById('add-institution-section');
const institutionForm = document.getElementById('institution-form');
const formTitle = document.getElementById('form-title');
const closeFormBtn = document.getElementById('close-form-btn');
const cancelEditBtn = document.getElementById('cancel-edit-btn');
const formMessage = document.getElementById('form-message');
const institutionIdField = document.getElementById('institution_id');

// --- New Report Modal Elements ---
const reportSelectionModal = document.getElementById('report-selection-modal');
const closeReportModalBtn = document.getElementById('close-report-modal-btn');
const cancelReportBtn = document.getElementById('cancel-report-btn');
const reportInstitutionSelect = document.getElementById('report-institution-select');
const generateReportBtn = document.getElementById('generate-report-btn');
const reportModalMessage = document.getElementById('report-modal-message');
// --- End New Report Modal Elements ---

// --- State ---
let currentInstitutions = [];
let currentPage = 1;
const itemsPerPage = 12; // Adjust items per page for card layout if needed
let totalItems = 0;
let editMode = false;

// --- Helper Functions ---

// Function to display messages (form or list)
const showMessage = (element, message, type = 'info') => {
    if (!element) return; // Guard clause
    element.textContent = message;
    element.className = `message ${type} show`; // Ensure 'show' class is added

    // Auto hide success messages after 5 seconds
    if (type === 'success') {
        setTimeout(() => {
            element.style.display = 'none'; // Hide element directly
            element.classList.remove('show');
        }, 5000);
    } else {
         element.style.display = 'block'; // Ensure non-success messages are visible
    }
};

// *** Add formatCurrency function ***
const formatCurrency = (amount) => {
    const value = parseFloat(amount);
    return isNaN(value) ? '0.00' : value.toFixed(2);
};
// *** End formatCurrency function ***

// --- New Helper Function to format time (copy from institution_details/script.js if needed) ---
const formatTime = (timeString) => {
    if (!timeString) return null;
    try {
        const [hours, minutes] = timeString.split(':');
        const date = new Date();
        date.setHours(parseInt(hours, 10), parseInt(minutes, 10), 0);
        // Format to 12-hour clock with AM/PM
        return date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
    } catch (e) {
        console.error("Error formatting time:", e);
        return timeString; // Return original string on error
    }
};
// --- End New Helper Function ---

// Function to render pagination controls
const renderPaginationControls = () => {
    if (!paginationControls || currentInstitutions.length <= itemsPerPage) {
        if (paginationControls) paginationControls.innerHTML = '';
        return;
    }

    paginationControls.innerHTML = '';
    const totalPages = Math.ceil(currentInstitutions.length / itemsPerPage);

    // Previous Button
    const prevButton = document.createElement('button');
    prevButton.innerHTML = '&laquo; السابق';
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            renderInstitutionCards(currentInstitutions); // Re-render cards for the new page
        }
    });
    paginationControls.appendChild(prevButton);

    // Page Number Info (Optional)
    const pageInfo = document.createElement('span');
    pageInfo.textContent = `صفحة ${currentPage} من ${totalPages}`;
    pageInfo.style.margin = '0 10px';
    paginationControls.appendChild(pageInfo);

    // Next Button
    const nextButton = document.createElement('button');
    nextButton.innerHTML = 'التالي &raquo;';
    nextButton.disabled = currentPage === totalPages;
    nextButton.addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage++;
            renderInstitutionCards(currentInstitutions); // Re-render cards for the new page
        }
    });
    paginationControls.appendChild(nextButton);
};

// Function to navigate to a specific page
const goToPage = (page) => {
    currentPage = page;
    renderInstitutionCards(currentInstitutions); // Re-render cards for the new page
};

// Function to get current page institutions
const getCurrentPageInstitutions = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return currentInstitutions.slice(startIndex, endIndex);
};

// --- Renamed and Modified Function to render institution cards ---
const renderInstitutionCards = (institutions) => {
    if (!institutionsCardsContainer) return;
    institutionsCardsContainer.innerHTML = ''; // Clear previous content
    if (listMessage) listMessage.style.display = 'none';

    if (!institutions || institutions.length === 0) {
        institutionsCardsContainer.innerHTML = `<div class="loading-placeholder">لا توجد مؤسسات لعرضها.</div>`;
        if (institutionsCountBadge) institutionsCountBadge.textContent = '0';
        if (paginationControls) paginationControls.innerHTML = '';
        return;
    }

    // Update total count badge
    if (institutionsCountBadge) institutionsCountBadge.textContent = institutions.length;

    // Get only institutions for the current page
    const pageInstitutions = getCurrentPageInstitutions();

    pageInstitutions.forEach(inst => {
        const card = document.createElement('div');
        card.className = 'institution-card';
        card.innerHTML = `
            <div class="card-content">
                <div class="card-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="card-details">
                    <h3 class="institution-name">${inst.name || 'غير محدد'}</h3>
                    <p class="institution-info">
                        <span title="رقم السجل التجاري"><i class="fas fa-id-card"></i> ${inst.commercial_reg_number || '-'}</span> |
                        <span title="الرقم الضريبي"><i class="fas fa-receipt"></i> ${inst.tax_number || '-'}</span>
                    </p>
                    <!-- Group Counts -->
                    <div class="institution-group-counts">
                         <span title="مجموعات فردية (ذهاب أو عودة فقط)">
                            <i class="fas fa-arrow-right"></i> ${inst.single_trip_group_count ?? 0}
                         </span>
                         <span title="مجموعات كاملة (ذهاب وعودة)">
                            <i class="fas fa-exchange-alt"></i> ${inst.full_group_count ?? 0}
                         </span>
                         <span title="إجمالي المجموعات">
                            <i class="fas fa-users"></i> ${inst.group_count ?? 0}
                         </span>
                    </div>
                    <!-- Financial Summary -->
                    <div class="institution-financials">
                        <span title="إجمالي التكلفة الأساسية">
                            <i class="fas fa-money-bill-wave"></i> ${formatCurrency(inst.total_cost_base)} ريال
                        </span>
                        <span title="إجمالي ضريبة القيمة المضافة">
                            <i class="fas fa-percentage"></i> ${formatCurrency(inst.total_vat)} ريال
                        </span>
                        <span title="الإجمالي النهائي شامل الضريبة">
                            <i class="fas fa-file-invoice-dollar"></i> ${formatCurrency(inst.total_cost_final)} ريال
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-actions">
                <button class="action-btn details-btn" data-id="${inst.id}" title="عرض التفاصيل"><i class="fas fa-eye"></i> تفاصيل</button>
                <button class="action-btn edit-btn" data-id="${inst.id}" title="تعديل"><i class="fas fa-edit"></i> تعديل</button>
                <button class="action-btn delete-btn" data-id="${inst.id}" title="حذف"><i class="fas fa-trash-alt"></i> حذف</button>
            </div>
        `;

        // Add event listeners for edit/delete/details buttons
        const detailsBtn = card.querySelector('.details-btn');
        const editBtn = card.querySelector('.edit-btn');
        const deleteBtn = card.querySelector('.delete-btn');

        if (detailsBtn) {
            detailsBtn.addEventListener('click', () => handleViewDetails(inst.id));
        }
        if (editBtn) {
            editBtn.addEventListener('click', (event) => {
                console.log('Edit button clicked for:', inst.name, inst.id);
                handleEditInstitution(inst);
            });
        }
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => handleDeleteInstitution(inst.id, inst.name));
        }

        institutionsCardsContainer.appendChild(card);
    });

    // Render pagination controls
    renderPaginationControls();
};
// --- End Card Rendering Function ---

// Function to update dashboard stats
const updateDashboardStats = () => {
    try {
        // Use currentInstitutions which holds the fetched data
        const total = currentInstitutions.length;
        if (totalInstitutionsStat) {
            totalInstitutionsStat.textContent = total;
        }
        // Add logic for other stats if needed (e.g., active contracts)
        // if (activeContractsStat) {
        //     const activeCount = currentInstitutions.filter(inst => inst.isActiveContract).length; // Example
        //     activeContractsStat.textContent = activeCount;
        // }
    } catch (error) {
        console.error('Error updating stats:', error); // Log the specific error
        if (totalInstitutionsStat) totalInstitutionsStat.textContent = 'خطأ';
        // if (activeContractsStat) activeContractsStat.textContent = 'خطأ';
    }
};


// Function to fetch institutions from Supabase
const fetchInstitutions = async (searchTerm = '') => {
    // Use the new card container for loading message
    if (!institutionsCardsContainer) return;
    institutionsCardsContainer.innerHTML = `<div class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i> جاري تحميل المؤسسات...</div>`;
    if (paginationControls) paginationControls.innerHTML = ''; // Clear pagination while loading

    try {
        let query = _supabase
            .from('enterprises')
            .select('*'); // Select all columns from enterprises

        if (searchTerm) {
            query = query.or(`name.ilike.%${searchTerm}%,commercial_reg_number.ilike.%${searchTerm}%`);
        }

        query = query.order('name', { ascending: true });

        const { data: institutions, error } = await query;

        if (error) {
            console.error('Supabase Fetch Error:', error);
            institutionsCardsContainer.innerHTML = `<div class="loading-placeholder error">خطأ في تحميل البيانات.</div>`;
            return; // Stop execution on error
        }

        console.log('Fetched institutions:', institutions);

        // --- Fetch Detailed Group Data for Statistics ---
        let groupStatsMap = {};
        if (institutions && institutions.length > 0) {
            const institutionIds = institutions.map(inst => inst.id);
            try {
                // Fetch necessary fields from enterprise_groups - Added more fields for trip type check
                const { data: groupsData, error: groupError } = await _supabase
                    .from('enterprise_groups')
                    .select('enterprise_id, cost, has_vat, vat_amount, total_cost_with_vat, departure_time, return_time, departure_employees_count, return_employees_count, departure_meeting_point, return_meeting_point') // Select more fields
                    .in('enterprise_id', institutionIds);

                if (groupError) {
                    console.error("Error fetching group details for stats:", groupError);
                    showMessage(listMessage, 'تحذير: لم يتم تحميل إحصائيات المجموعات.', 'warning');
                } else {
                    // Process group data to calculate stats for each institution
                    groupStatsMap = institutions.reduce((acc, inst) => {
                        acc[inst.id] = {
                            group_count: 0,
                            single_trip_group_count: 0,
                            full_group_count: 0,
                            total_cost_base: 0,
                            total_vat: 0,
                            total_cost_final: 0,
                        };
                        return acc;
                    }, {});

                    groupsData.forEach(group => {
                        const stats = groupStatsMap[group.enterprise_id];
                        if (stats) {
                            // *** Refined check for departure/return leg existence ***
                            const hasDepartureLeg = !!group.departure_time || !!group.departure_meeting_point || (group.departure_employees_count !== null && group.departure_employees_count > 0);
                            const hasReturnLeg = !!group.return_time || !!group.return_meeting_point || (group.return_employees_count !== null && group.return_employees_count > 0);
                            // *** End Refined check ***

                            let isCounted = false; // Flag to ensure group_count increments only once per valid group

                            if (hasDepartureLeg && hasReturnLeg) {
                                stats.full_group_count++;
                                isCounted = true;
                            } else if (hasDepartureLeg || hasReturnLeg) { // If only one leg exists
                                stats.single_trip_group_count++;
                                isCounted = true;
                            }

                            // *** Only increment total group_count if it's a valid single or full trip ***
                            if (isCounted) {
                                stats.group_count++;
                            }
                            // *** End Conditional Increment ***

                            const baseCost = parseFloat(group.cost) || 0;
                            const vatAmount = parseFloat(group.vat_amount) || 0;
                            const hasVat = group.has_vat;
                            const finalCost = hasVat ? (parseFloat(group.total_cost_with_vat) || baseCost) : baseCost;

                            stats.total_cost_base += baseCost;
                            stats.total_vat += vatAmount;
                            stats.total_cost_final += finalCost;
                        }
                    });
                    console.log("Group stats map (refined check):", groupStatsMap); // Log updated map
                }
            } catch (statsCatchError) {
                 console.error("Error processing group stats:", statsCatchError);
                 showMessage(listMessage, 'تحذير: خطأ في حساب إحصائيات المجموعات.', 'warning');
            }
        }
        // --- End Fetch Group Stats ---

        // Add stats to each institution object
        currentInstitutions = (institutions || []).map(inst => ({
            ...inst,
            ...(groupStatsMap[inst.id] || { // Merge stats, provide defaults if not found
                group_count: 0,
                single_trip_group_count: 0,
                full_group_count: 0,
                total_cost_base: 0,
                total_vat: 0,
                total_cost_final: 0,
            })
        }));

        currentPage = 1; // Reset to first page
        renderInstitutionCards(currentInstitutions); // Call the card rendering function

        if (currentInstitutions.length === 0 && searchTerm) {
            showMessage(listMessage, `لا توجد نتائج للبحث عن "${searchTerm}".`, 'info');
        } else if (currentInstitutions.length === 0) {
             showMessage(listMessage, 'لا يوجد مؤسسات مسجلة حالياً.', 'info');
        }

        // Update dashboard stats after fetching and processing
        updateDashboardStats(); // Now call updateDashboardStats
    } catch (error) {
        console.error('JavaScript Fetch Error:', error);
        showMessage(listMessage, `حدث خطأ غير متوقع: ${error.message}`, 'error');
        institutionsCardsContainer.innerHTML = `<div class="loading-placeholder error">خطأ غير متوقع.</div>`;
        currentInstitutions = [];
        updateDashboardStats();
    }
};

// Function to handle form submission (Add or Update)
const handleFormSubmit = async (event) => {
    event.preventDefault();
    const submitBtn = institutionForm.querySelector('.submit-btn');
    submitBtn.disabled = true;
    submitBtn.textContent = 'جاري الحفظ...';
    showMessage(formMessage, 'جاري حفظ البيانات...', 'info', 0);

    const formData = new FormData(institutionForm);
    const institutionData = {
        name: formData.get('institution_name')?.trim(),
        commercial_reg_number: formData.get('commercial_reg_number')?.trim() || null,
        tax_number: formData.get('tax_number')?.trim() || null,
    };
    const institutionId = formData.get('institution_id'); // Get ID from hidden field

    try {
        // Basic Validation
        if (!institutionData.name) {
            throw new Error('اسم المؤسسة مطلوب.');
        }

        let result;
        let successMessage;

        if (editMode && institutionId) {
            // Update existing institution
            console.log('Updating institution ID:', institutionId, 'with data:', institutionData);
            const { data, error } = await _supabase
                .from('enterprises')
                .update(institutionData)
                .eq('id', institutionId)
                .select()
                .single(); // Use single() if you expect one row back

            if (error) throw error;
            if (!data) throw new Error('فشل تحديث بيانات المؤسسة.');
            result = data;
            successMessage = 'تم تحديث بيانات المؤسسة بنجاح!';
            console.log('Update successful:', result);
        } else {
            // Add new institution
            console.log('Adding new institution with data:', institutionData);
            const { data, error } = await _supabase
                .from('enterprises')
                .insert([institutionData])
                .select()
                .single(); // Use single() to get the inserted row back

            if (error) throw error;
            if (!data) throw new Error('فشل إضافة المؤسسة.');
            result = data;
            successMessage = 'تمت إضافة المؤسسة بنجاح!';
            console.log('Insert successful:', result);
        }

        showMessage(formMessage, successMessage, 'success');
        await fetchInstitutions(searchInput.value.trim()); // Refresh the list
        setTimeout(() => {
            toggleModal(false); // Close modal after a short delay
        }, 1500);

    } catch (error) {
        console.error('Error saving institution:', error);
        showMessage(formMessage, `خطأ في الحفظ: ${error.message}`, 'error', 0); // Show error indefinitely
    } finally {
        submitBtn.disabled = false;
        // Reset button text based on mode (might be redundant if modal closes)
        submitBtn.textContent = editMode ? 'تحديث البيانات' : 'حفظ';
    }
};

// Function to reset the form and UI elements
const resetForm = () => {
    editMode = false;
    if (institutionForm) institutionForm.reset();
    if (institutionIdField) institutionIdField.value = ''; // Clear hidden ID field
    if (formMessage) formMessage.style.display = 'none';
    if (formTitle) formTitle.textContent = 'إضافة مؤسسة جديدة';
    const submitBtn = institutionForm?.querySelector('.submit-btn');
    if (submitBtn) submitBtn.textContent = 'حفظ';
};

// Function to populate form for editing
const handleEditInstitution = (inst) => {
    console.log('handleEditInstitution called with:', inst);
    if (!inst || !institutionForm) {
        console.error("Institution data or form not found for editing.");
        return;
    }

    // Reset form first (toggleModal calls resetGroupForm)
    toggleModal(true); // Show the modal, which also resets the form
    editMode = true; // Set edit mode *after* resetting

    // Update modal title and button text
    if (formTitle) formTitle.textContent = 'تعديل بيانات المؤسسة';
    const submitBtn = institutionForm.querySelector('.submit-btn');
    if (submitBtn) submitBtn.textContent = 'تحديث البيانات';

    // Populate form fields - Ensure IDs match the HTML form
    const nameInput = document.getElementById('institution_name');
    const crInput = document.getElementById('commercial_reg_number');
    const taxInput = document.getElementById('tax_number');
    const idInput = document.getElementById('institution_id'); // Hidden field for ID

    if (idInput) idInput.value = inst.id || '';
    if (nameInput) nameInput.value = inst.name || '';
    if (crInput) crInput.value = inst.commercial_reg_number || '';
    if (taxInput) taxInput.value = inst.tax_number || '';

    console.log('Form populated for editing:', {
        id: idInput?.value,
        name: nameInput?.value,
        cr: crInput?.value,
        tax: taxInput?.value
    });

    // Focus on the first editable field
    setTimeout(() => {
        nameInput?.focus();
    }, 100); // Small delay to ensure modal is visible
};

// Function to handle institution deletion
const handleDeleteInstitution = async (institutionId, institutionName) => {
    const confirmDelete = confirm(`هل أنت متأكد من حذف مؤسسة "${institutionName || 'هذه المؤسسة'}"؟ لا يمكن التراجع عن هذا الإجراء.`);

    if (confirmDelete) {
        try {
            const { error } = await _supabase
                .from('enterprises') // Updated table name
                .delete()
                .eq('id', institutionId); // Use 'id' (int4) as the PK

            if (error) {
                console.error('Supabase Delete Error:', error);
                 // Check for foreign key constraint violation (e.g., if contracts are linked)
                let userMessage = `خطأ في الحذف: ${error.message}`;
                if (error.code === '23503') {
                    userMessage = 'خطأ: لا يمكن حذف المؤسسة لوجود عقود أو بيانات أخرى مرتبطة بها.';
                }
                showMessage(listMessage, userMessage, 'error');
            } else {
                console.log('Institution deleted:', institutionId);
                showMessage(listMessage, `تم حذف المؤسسة بنجاح.`, 'success');
                fetchInstitutions(); // Refresh the list
            }
        } catch (error) {
            console.error('JavaScript Delete Error:', error);
            showMessage(listMessage, `حدث خطأ غير متوقع أثناء الحذف: ${error.message}`, 'error');
        }
    }
};

// Function to handle search
const handleSearch = () => {
    if (!searchInput) return;
    const searchTerm = searchInput.value.trim();
    fetchInstitutions(searchTerm);
};

// Function to handle printing report
const handlePrintReport = () => {
    // Instead of generating the simple table, open the selection modal
    openReportSelectionModal();
};

// --- Modal Toggling ---
const toggleModal = (show) => {
    console.log(`toggleModal called with show = ${show}`); // Debug log
    if (!addInstitutionSection) {
        console.error("Modal element (#add-institution-section) not found!"); // Debug log
        return;
    }
    console.log("Modal element found:", addInstitutionSection); // Debug log

    if (show) {
        console.log("Attempting to show modal..."); // Debug log
        // Reset form *before* potentially populating for edit
        resetForm();
        addInstitutionSection.classList.add('active');
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
        console.log("Modal should be active now. Classes:", addInstitutionSection.classList); // Debug log
        // Focus is handled in the event listener or handleEditInstitution
    } else {
        console.log("Attempting to hide modal..."); // Debug log
        addInstitutionSection.classList.remove('active');
        document.body.style.overflow = ''; // Restore scrolling
        resetForm(); // Also reset when closing explicitly
        console.log("Modal should be hidden now. Classes:", addInstitutionSection.classList); // Debug log
    }
};

// Function to handle viewing institution details
const handleViewDetails = (institutionId) => {
    if (!institutionId) return;
    // Navigate to the details page, passing the ID as a URL parameter
    window.location.href = `../institution_details/details.html?id=${institutionId}`;
};

// --- Report Generation Logic ---

// Function to open the report selection modal
const openReportSelectionModal = () => {
    if (!reportSelectionModal) return;
    populateReportInstitutionSelect(); // Populate dropdown with current institutions
    if (reportModalMessage) reportModalMessage.style.display = 'none';
    reportSelectionModal.classList.add('active'); // Use 'active' class like the other modal
    document.body.style.overflow = 'hidden';
};

// Function to close the report selection modal
const closeReportSelectionModal = () => {
    if (!reportSelectionModal) return;
    reportSelectionModal.classList.remove('active');
    document.body.style.overflow = '';
};

// Function to populate the institution select dropdown in the report modal
const populateReportInstitutionSelect = () => {
    if (!reportInstitutionSelect) return;
    reportInstitutionSelect.innerHTML = '<option value="" disabled selected>-- اختر مؤسسة --</option>'; // Reset

    if (currentInstitutions.length === 0) {
        reportInstitutionSelect.innerHTML = '<option value="" disabled>لا توجد مؤسسات لعرضها</option>';
        return;
    }

    // Sort institutions alphabetically for the dropdown
    const sortedInstitutions = [...currentInstitutions].sort((a, b) => (a.name || '').localeCompare(b.name || ''));

    sortedInstitutions.forEach(inst => {
        const option = document.createElement('option');
        option.value = inst.id;
        option.textContent = inst.name || 'مؤسسة بدون اسم';
        reportInstitutionSelect.appendChild(option);
    });
};

// Function to generate the report HTML
const generateReportHTML = (institution, groups) => {
    const reportDate = new Date().toLocaleDateString('ar-SA');
    let groupsHTML = '';

    if (!groups || groups.length === 0) {
        groupsHTML = '<p style="text-align: center; color: #888;">لا توجد مجموعات لهذه المؤسسة.</p>';
    } else {
        groups.forEach((group, index) => {
            const hasDeparture = group.departure_time || group.departure_meeting_point || (group.departure_employees_count !== null && group.departure_employees_count > 0);
            const hasReturn = group.return_time || group.return_meeting_point || (group.return_employees_count !== null && group.return_employees_count > 0);
            const totalCost = group.has_vat ? (group.total_cost_with_vat ?? 0) : (group.cost ?? 0);

            groupsHTML += `
                <div class="group-section">
                    <h4>${index + 1}. مجموعة: ${group.name || 'بدون اسم'}</h4>
                    <table>
                        <thead>
                            <tr>
                                <th colspan="2">تفاصيل الرحلة</th>
                                <th>العدد</th>
                                <th>وقت التجمع</th>
                                <th>وقت الانطلاق/العودة</th>
                                <th>نقطة التجمع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${hasDeparture ? `
                            <tr>
                                <td rowspan="1"><strong>الذهاب</strong></td>
                                <td></td>
                                <td>${group.departure_employees_count ?? '-'}</td>
                                <td>${formatTime(group.departure_meeting_time) || '-'}</td>
                                <td>${formatTime(group.departure_time) || '-'}</td>
                                <td>${group.departure_meeting_point ? `<a href="${group.departure_meeting_point}" target="_blank">رابط</a>` : '-'}</td>
                            </tr>
                            ` : `
                            <tr>
                                <td rowspan="1"><strong>الذهاب</strong></td>
                                <td colspan="5" style="text-align:center; color:#888;">-- لا توجد بيانات --</td>
                            </tr>
                            `}
                            ${hasReturn ? `
                            <tr>
                                <td rowspan="1"><strong>العودة</strong></td>
                                <td></td>
                                <td>${group.return_employees_count ?? '-'}</td>
                                <td>${formatTime(group.return_meeting_time) || '-'}</td>
                                <td>${formatTime(group.return_time) || '-'}</td>
                                <td>${group.return_meeting_point ? `<a href="${group.return_meeting_point}" target="_blank">رابط</a>` : '-'}</td>
                            </tr>
                            ` : `
                            <tr>
                                <td rowspan="1"><strong>العودة</strong></td>
                                <td colspan="5" style="text-align:center; color:#888;">-- لا توجد بيانات --</td>
                            </tr>
                            `}
                        </tbody>
                    </table>
                    <table class="cost-table">
                         <tr>
                            <td><strong>التكلفة الأساسية:</strong> ${formatCurrency(group.cost)} ريال</td>
                            <td><strong>الضريبة (${group.has_vat ? (group.vat_percentage ?? 0) + '%' : '0%'}):</strong> ${formatCurrency(group.vat_amount)} ريال</td>
                            <td><strong>الإجمالي النهائي:</strong> ${formatCurrency(totalCost)} ريال</td>
                         </tr>
                         ${group.notes ? `<tr><td colspan="3"><strong>ملاحظات:</strong> ${group.notes}</td></tr>` : ''}
                    </table>
                </div>
            `;
        });
    }

    return `
        <html dir="rtl">
        <head>
            <title>تقرير مجموعات مؤسسة: ${institution.name}</title>
            <style>
                body { font-family: 'Tajawal', sans-serif; padding: 20px; direction: rtl; background-color: #f8f9fa; }
                .report-container { max-width: 900px; margin: 20px auto; background-color: #fff; padding: 30px; border: 1px solid #dee2e6; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                h1, h2, h3 { text-align: center; color: #333; }
                h1 { font-size: 1.8em; margin-bottom: 10px; color: #2980b9; }
                h2 { font-size: 1.5em; margin-bottom: 20px; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
                h3 { font-size: 1.3em; margin-bottom: 15px; color: #2c3e50; }
                .report-meta { display: flex; justify-content: space-between; margin-bottom: 25px; font-size: 0.9em; color: #555; padding-bottom: 15px; border-bottom: 1px dashed #ccc; }
                .institution-details { margin-bottom: 25px; background-color: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #eee; }
                .institution-details p { margin: 5px 0; }
                .group-section { margin-bottom: 30px; padding-bottom: 20px; border-bottom: 1px solid #eee; }
                .group-section:last-child { border-bottom: none; }
                .group-section h4 { font-size: 1.2em; color: #3498db; margin-bottom: 15px; background-color: #eaf5fd; padding: 8px 12px; border-radius: 4px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 15px; font-size: 0.9em; }
                th, td { padding: 8px 10px; text-align: right; border: 1px solid #ddd; }
                th { background-color: #f2f2f2; font-weight: bold; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                .cost-table { margin-top: 10px; background-color: #fdfdfd; }
                .cost-table td { font-weight: 500; }
                .cost-table strong { color: #333; }
                a { color: #3498db; text-decoration: none; }
                a:hover { text-decoration: underline; }
                @media print {
                    body { background-color: #fff; padding: 5px; margin: 0; -webkit-print-color-adjust: exact; print-color-adjust: exact; }
                    .report-container { margin: 0; padding: 15px; border: none; box-shadow: none; max-width: 100%; }
                    .no-print { display: none; }
                    h1 { font-size: 1.6em; } h2 { font-size: 1.4em; } h3 { font-size: 1.2em; }
                    table { font-size: 0.85em; }
                    th, td { padding: 6px 8px; }
                }
            </style>
        </head>
        <body>
            <div class="report-container">
                <h1>تقرير المجموعات</h1>
                <div class="report-meta">
                    <span>تاريخ التقرير: ${reportDate}</span>
                    <span>المؤسسة: ${institution.name}</span>
                </div>

                <div class="institution-details">
                    <h3>بيانات المؤسسة</h3>
                    <p><strong>الاسم:</strong> ${institution.name || '-'}</p>
                    <p><strong>السجل التجاري:</strong> ${institution.commercial_reg_number || '-'}</p>
                    <p><strong>الرقم الضريبي:</strong> ${institution.tax_number || '-'}</p>
                </div>

                <h2>تفاصيل المجموعات (${groups.length})</h2>
                ${groupsHTML}

                <div class="no-print" style="margin-top: 30px; text-align: center;">
                    <button onclick="window.print()" style="padding: 10px 20px; font-size: 1rem; cursor: pointer;">طباعة</button>
                    <button onclick="window.close()" style="padding: 10px 20px; font-size: 1rem; cursor: pointer; margin-right: 10px;">إغلاق</button>
                </div>
            </div>
        </body>
        </html>
    `;
};

// Function to handle the "Generate Report" button click inside the modal
const handleGenerateReport = async () => {
    if (!reportInstitutionSelect || !reportModalMessage) return;
    const selectedInstitutionId = reportInstitutionSelect.value;

    if (!selectedInstitutionId) {
        showMessage(reportModalMessage, 'الرجاء اختيار مؤسسة أولاً.', 'warning');
        return;
    }

    showMessage(reportModalMessage, 'جاري إنشاء التقرير...', 'info', 0);
    generateReportBtn.disabled = true;

    try {
        // --- Debugging Logs ---
        console.log("Attempting to generate report for ID:", selectedInstitutionId, "(Type:", typeof selectedInstitutionId, ")");
        console.log("Current Institutions available:", currentInstitutions.length);
        if (currentInstitutions.length > 0) {
            console.log("First institution ID in array:", currentInstitutions[0].id, "(Type:", typeof currentInstitutions[0].id, ")");
        }
        // --- End Debugging Logs ---

        // Find the selected institution details from the current list
        // Using loose equality (==) to potentially handle string/number mismatch.
        // If IDs are consistently strings (like UUIDs), === is safer.
        const selectedInstitution = currentInstitutions.find(inst => inst.id == selectedInstitutionId);

        if (!selectedInstitution) {
            // Log the IDs for comparison if not found
            console.error("Institution not found. Comparing selected ID with available IDs:");
            currentInstitutions.forEach((inst, index) => {
                console.log(`Index ${index}: ID = ${inst.id} (Type: ${typeof inst.id})`);
            });
            throw new Error('لم يتم العثور على بيانات المؤسسة المختارة.');
        }

        console.log("Found institution data:", selectedInstitution); // Log found data

        // Fetch all groups for the selected institution
        const { data: groups, error: groupsError } = await _supabase
            .from('enterprise_groups')
            .select('*') // Select all columns for the report
            .eq('enterprise_id', selectedInstitutionId) // Use the ID from the dropdown value
            .order('name', { ascending: true });

        if (groupsError) {
            throw groupsError;
        }

        // Generate HTML
        const reportHTML = generateReportHTML(selectedInstitution, groups || []);

        // Open in new window
        const reportWindow = window.open('', '_blank', 'height=700,width=900');
        if (reportWindow) {
            reportWindow.document.write(reportHTML);
            reportWindow.document.close(); // Important for rendering
            showMessage(reportModalMessage, 'تم إنشاء التقرير بنجاح!', 'success');
            setTimeout(closeReportSelectionModal, 1500);
        } else {
            throw new Error('فشل فتح نافذة التقرير. قد يكون المتصفح يمنع النوافذ المنبثقة.');
        }

    } catch (error) {
        console.error('Error generating report:', error); // Log the full error object
        showMessage(reportModalMessage, `خطأ في إنشاء التقرير: ${error.message}`, 'error', 0);
    } finally {
        generateReportBtn.disabled = false;
    }
};

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOM loaded, initializing institutions application...');

    // Setup event listeners
    setupEventListeners();

    // Fetch initial institutions data
    fetchInstitutions();
});

// --- Event Listeners ---
const setupEventListeners = () => {
    if (institutionForm) {
        institutionForm.addEventListener('submit', handleFormSubmit);
    }

    if (addInstitutionBtn) {
        addInstitutionBtn.addEventListener('click', () => {
            console.log('Add institution button clicked');
            editMode = false;
            toggleModal(true);
            setTimeout(() => {
                document.getElementById('institution_name')?.focus();
            }, 100);
        });
    }

    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', () => {
            toggleModal(false);
        });
    }

    if (closeFormBtn) {
        closeFormBtn.addEventListener('click', () => {
            toggleModal(false);
        });
    }

    // Close modal on background click
    if (addInstitutionSection) {
        addInstitutionSection.addEventListener('click', (event) => {
            if (event.target === addInstitutionSection) {
                toggleModal(false);
            }
        });
    }

    if (searchBtn) {
        searchBtn.addEventListener('click', handleSearch);
    }

    if (searchInput) {
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
    }

    if (printReportBtn) {
        printReportBtn.addEventListener('click', handlePrintReport);
    }

    // Escape key listener for modal
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && addInstitutionSection && addInstitutionSection.classList.contains('show')) {
            toggleModal(false);
        }
    });

    // --- New Report Modal Listeners ---
    if (closeReportModalBtn) closeReportModalBtn.addEventListener('click', closeReportSelectionModal);
    if (cancelReportBtn) cancelReportBtn.addEventListener('click', closeReportSelectionModal);
    if (generateReportBtn) generateReportBtn.addEventListener('click', handleGenerateReport);
    if (reportSelectionModal) reportSelectionModal.addEventListener('click', (e) => {
        if (e.target === reportSelectionModal) closeReportSelectionModal();
    });
    // --- End New Report Modal Listeners ---
};

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log("DOM loaded, initializing institutions application...");
    setupEventListeners(); // Setup listeners first
    await fetchInstitutions(); // Fetch initial data
    // Initial stats update is called within fetchInstitutions
});
