console.log("Bus Report script loaded.");

// --- Supabase Initialization ---
let _supabase;
try {
    if (typeof supabase !== 'undefined' && supabase.createClient && typeof SUPABASE_URL !== 'undefined' && typeof SUPABASE_ANON_KEY !== 'undefined') {
        _supabase = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('Supabase Initialized for Bus Report');
    } else {
        throw new Error('Supabase client or config variables not available.');
    }
} catch (error) {
    console.error('Error initializing Supabase client:', error);
    alert('خطأ فادح: لا يمكن الاتصال بقاعدة البيانات.');
    // Display error in the report content area
    const reportContent = document.getElementById('report-content');
    if (reportContent) reportContent.innerHTML = '<p class="loading-placeholder error">خطأ في تهيئة قاعدة البيانات.</p>';
}

// --- DOM Elements ---
const reportContent = document.getElementById('report-content');
const printReportBtn = document.getElementById('print-report-btn');
const expensesByTypeChartCtx = document.getElementById('expenses-by-type-chart')?.getContext('2d');
const expensesByBusChartCtx = document.getElementById('expenses-by-bus-chart')?.getContext('2d');
const chartMessage = document.getElementById('chart-message');
const analysisQuestionArea = document.getElementById('analysis-question-area');
const systemQuestionText = document.getElementById('system-question-text');
const analysisAnswerButtons = document.getElementById('analysis-answer-buttons');
const answerYesBtn = document.getElementById('answer-yes-btn');
const answerNoBtn = document.getElementById('answer-no-btn');
const analysisFeedbackArea = document.getElementById('analysis-feedback-area');
const feedbackCorrectnessSpan = document.getElementById('feedback-correctness');
const feedbackReasonSpan = document.getElementById('feedback-reason');
const nextQuestionBtn = document.getElementById('next-question-btn');
const analysisMessage = document.getElementById('analysis-message'); // Keep for general messages

// --- State Variables for Analysis ---
let reportRawExpenses = [];
let reportExpensesByType = {};
let reportExpensesByBus = {};
let reportGrandTotal = 0;
let reportBusDetailsMap = {};
let currentAnalysisQuestion = {
    question: '',
    correctAnswer: null, // 'نعم' or 'لا'
    reason: ''
};

// --- Helper Functions ---

// --- إضافة: تعريف دالة showMessage ---
const showMessage = (element, message, type = 'info', duration = 4000) => {
    if (!element) return;
    element.textContent = message;
    element.className = `message ${type} show`; // Ensure 'show' class is added
    element.style.display = 'block'; // Make sure it's visible

    // Clear previous timeouts if any
    if (element.timeoutId) {
        clearTimeout(element.timeoutId);
    }

    if (duration > 0) {
        element.timeoutId = setTimeout(() => {
            // Only hide if the message hasn't changed
            if (element.textContent === message) {
                 element.classList.remove('show');
                 // Use a small delay before setting display to none for fade-out effect if CSS transition is added
                 setTimeout(() => { element.style.display = 'none'; }, 300); // Adjust timing based on CSS transition
            }
            element.timeoutId = null; // Clear the stored timeout ID
        }, duration);
    } else {
         element.timeoutId = null; // No timeout needed for permanent messages (like errors)
    }
};
// --- نهاية الإضافة ---

const formatCurrency = (amount) => {
    const value = parseFloat(amount);
    return isNaN(value) ? '0.00' : value.toFixed(2);
};

const formatDate = (dateString) => {
    if (!dateString) return '';
    try {
        const date = new Date(dateString);
        const offset = date.getTimezoneOffset();
        const adjustedDate = new Date(date.getTime() - (offset * 60 * 1000));
        return adjustedDate.toISOString().split('T')[0];
    } catch (e) { return dateString; }
};

const getMonthName = (monthNumber) => {
    const names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"];
    const index = parseInt(monthNumber, 10) - 1;
    return names[index] || `شهر ${monthNumber}`;
};

// --- Chart Generation ---
let expensesByTypeChart = null;
let expensesByBusChart = null;

const generateCharts = (expensesData, busDetailsMap) => {
    if (!expensesByTypeChartCtx || !expensesByBusChartCtx) {
        console.warn('Chart contexts not found.');
        if (chartMessage) {
            chartMessage.textContent = 'خطأ: لا يمكن عرض الرسوم البيانية.';
            chartMessage.style.display = 'block';
            chartMessage.className = 'message error';
        }
        return;
    }

    // --- تعديل خيارات المخططات للتحكم في الحجم ---
    const commonChartOptions = {
        responsive: true,
        maintainAspectRatio: false, // Allow CSS to control aspect ratio/height
        plugins: {
            legend: {
                position: 'top',
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        let label = context.label || '';
                        if (label) {
                            label += ': ';
                        }
                        let value = context.parsed;
                        // Adjust for pie vs bar chart data structure
                        if (context.chart.config.type === 'pie') {
                             value = context.parsed;
                        } else if (context.chart.config.type === 'bar') {
                             value = context.parsed.x; // Assuming horizontal bar chart
                        }
                        if (value !== null && value !== undefined) {
                            label += formatCurrency(value) + ' ريال';
                        }
                        return label;
                    }
                }
            }
        }
    };
    // --- نهاية التعديل ---

    // 1. Expenses by Type Chart (Pie Chart)
    const expensesByType = {};
    expensesData.forEach(exp => {
        const type = exp.expense_type || 'غير محدد';
        const amount = parseFloat(exp.total_with_tax || 0);
        if (!expensesByType[type]) {
            expensesByType[type] = 0;
        }
        expensesByType[type] += amount;
    });

    const typeLabels = Object.keys(expensesByType);
    const typeData = Object.values(expensesByType);

    // Destroy previous chart instance if exists
    if (expensesByTypeChart) {
        expensesByTypeChart.destroy();
    }

    if (typeLabels.length > 0) {
        expensesByTypeChart = new Chart(expensesByTypeChartCtx, {
            type: 'pie',
            data: {
                labels: typeLabels,
                datasets: [{
                    label: 'المصاريف حسب النوع',
                    data: typeData,
                    backgroundColor: [ // Add more colors if needed
                        '#3498db', '#e74c3c', '#2ecc71', '#f1c40f', '#9b59b6',
                        '#34495e', '#1abc9c', '#e67e22', '#bdc3c7', '#7f8c8d',
                        '#2980b9', '#c0392b'
                    ],
                    hoverOffset: 4
                }]
            },
            options: { // Use common options
                ...commonChartOptions,
                 plugins: { // Override or add specific plugins if needed
                    ...commonChartOptions.plugins,
                    title: { display: false } // Example: No title within chart area
                 }
            }
        });
    } else {
         if (chartMessage) chartMessage.textContent = 'لا توجد بيانات لعرضها في مخطط أنواع المصاريف.';
         if (chartMessage) chartMessage.style.display = 'block';
    }


    // 2. Expenses by Bus Chart (Bar Chart)
    const expensesByBus = {};
    expensesData.forEach(exp => {
        const busId = exp.bus_id;
        const amount = parseFloat(exp.total_with_tax || 0);
        if (!expensesByBus[busId]) {
            expensesByBus[busId] = 0;
        }
        expensesByBus[busId] += amount;
    });

    const busLabels = Object.keys(expensesByBus).map(id => busDetailsMap[id]?.label || `ID: ${id.substring(0,6)}`);
    const busData = Object.values(expensesByBus);

    // Destroy previous chart instance if exists
    if (expensesByBusChart) {
        expensesByBusChart.destroy();
    }

    if (busLabels.length > 0) {
        expensesByBusChart = new Chart(expensesByBusChartCtx, {
            type: 'bar',
            data: {
                labels: busLabels,
                datasets: [{
                    label: 'إجمالي المصاريف (ريال)',
                    data: busData,
                    backgroundColor: '#3498db',
                    borderColor: '#2980b9',
                    borderWidth: 1
                }]
            },
            options: { // Use common options and add bar-specific scales
                ...commonChartOptions,
                indexAxis: 'y', // Keep horizontal bars
                scales: {
                    x: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'المبلغ (ريال)'
                        }
                    },
                    y: {
                         ticks: {
                             autoSkip: false // Ensure all labels are shown
                         }
                    }
                },
                 plugins: { // Override or add specific plugins if needed
                    ...commonChartOptions.plugins,
                    legend: { display: false }, // No legend for single dataset bar
                    title: { display: false } // Example: No title within chart area
                 }
            }
        });
    } else {
         if (chartMessage && !chartMessage.textContent.includes('أنواع المصاريف')) { // Avoid overwriting previous message
             chartMessage.textContent = 'لا توجد بيانات لعرضها في مخطط مصاريف الحافلات.';
             if (chartMessage) chartMessage.style.display = 'block';
         }
    }
     if (busLabels.length === 0 && typeLabels.length === 0 && chartMessage) {
         chartMessage.textContent = 'لا توجد بيانات كافية لعرض الرسوم البيانية.';
         chartMessage.style.display = 'block';
     } else if (chartMessage && chartMessage.textContent === '') {
         chartMessage.style.display = 'none'; // Hide if charts were generated
     }

};

// --- Analysis Question Generation ---
const generateAnalysisQuestion = () => {
    console.log("Generating analysis question...");
    // Reset UI
    if (analysisAnswerButtons) analysisAnswerButtons.style.display = 'none';
    if (analysisFeedbackArea) analysisFeedbackArea.style.display = 'none';
    if (systemQuestionText) systemQuestionText.textContent = 'جاري إنشاء السؤال...';
    if (analysisMessage) analysisMessage.style.display = 'none';

    currentAnalysisQuestion = { question: '', correctAnswer: null, reason: '' }; // Reset current question

    const questionTypes = [];
    const hasTypeData = Object.keys(reportExpensesByType).length > 0;
    const hasBusData = Object.keys(reportExpensesByBus).length > 0;
    const hasRawData = reportRawExpenses.length > 0;

    if (hasTypeData) questionTypes.push('highestType', 'typeThreshold');
    if (hasBusData) questionTypes.push('highestBus', 'busThreshold');
    if (hasRawData) questionTypes.push('singleExpenseThreshold');
    if (hasTypeData && hasBusData) questionTypes.push('typeVsBus'); // Example complex question

    if (questionTypes.length === 0) {
        if (systemQuestionText) systemQuestionText.textContent = 'لا توجد بيانات كافية لإنشاء سؤال.';
        showMessage(analysisMessage, 'لا توجد بيانات كافية للتحليل.', 'info');
        return;
    }

    const randomType = questionTypes[Math.floor(Math.random() * questionTypes.length)];
    let question = '';
    let correctAnswer = null;
    let reason = '';
    let threshold = 0; // For threshold questions

    try {
        switch (randomType) {
            case 'highestType': {
                let highestType = '';
                let highestAmount = -1;
                for (const type in reportExpensesByType) {
                    if (reportExpensesByType[type] > highestAmount) {
                        highestAmount = reportExpensesByType[type];
                        highestType = type;
                    }
                }
                // Ask if a *different* type is the highest
                const types = Object.keys(reportExpensesByType);
                let randomOtherType = highestType;
                if (types.length > 1) {
                    while (randomOtherType === highestType) {
                        randomOtherType = types[Math.floor(Math.random() * types.length)];
                    }
                    question = `هل يعتبر نوع المصروف '${randomOtherType}' هو الأعلى تكلفة في هذا التقرير؟`;
                    correctAnswer = 'لا';
                    reason = `نوع المصروف الأعلى تكلفة هو '${highestType}' بمبلغ ${formatCurrency(highestAmount)} ريال.`;
                } else { // Only one type exists
                    question = `هل يعتبر نوع المصروف '${highestType}' هو الأعلى تكلفة في هذا التقرير؟`;
                    correctAnswer = 'نعم';
                    reason = `نعم، '${highestType}' هو النوع الوحيد المسجل وبالتالي هو الأعلى تكلفة بمبلغ ${formatCurrency(highestAmount)} ريال.`;
                }
                break;
            }
            case 'typeThreshold': {
                const types = Object.keys(reportExpensesByType);
                const randomType = types[Math.floor(Math.random() * types.length)];
                const actualAmount = reportExpensesByType[randomType];
                // Set threshold slightly above or below the actual amount
                threshold = Math.random() > 0.5
                    ? Math.ceil(actualAmount * (1 + Math.random() * 0.2)) // 0-20% higher
                    : Math.floor(actualAmount * (1 - Math.random() * 0.2)); // 0-20% lower
                threshold = Math.max(10, threshold); // Ensure threshold is at least 10

                question = `هل تجاوزت مصاريف '${randomType}' مبلغ ${formatCurrency(threshold)} ريال؟`;
                if (actualAmount > threshold) {
                    correctAnswer = 'نعم';
                    reason = `نعم، إجمالي مصاريف '${randomType}' هو ${formatCurrency(actualAmount)} ريال، وهو أعلى من ${formatCurrency(threshold)} ريال.`;
                } else {
                    correctAnswer = 'لا';
                    reason = `لا، إجمالي مصاريف '${randomType}' هو ${formatCurrency(actualAmount)} ريال، وهو ليس أعلى من ${formatCurrency(threshold)} ريال.`;
                }
                break;
            }
             case 'highestBus': {
                let highestBusId = '';
                let highestBusLabel = '';
                let highestAmount = -1;
                for (const busId in reportExpensesByBus) {
                    if (reportExpensesByBus[busId].total > highestAmount) {
                        highestAmount = reportExpensesByBus[busId].total;
                        highestBusId = busId;
                        highestBusLabel = reportExpensesByBus[busId].label;
                    }
                }
                 const buses = Object.keys(reportExpensesByBus);
                 let randomOtherBusLabel = highestBusLabel;
                 if (buses.length > 1) {
                     while(randomOtherBusLabel === highestBusLabel) {
                         const randomId = buses[Math.floor(Math.random() * buses.length)];
                         randomOtherBusLabel = reportExpensesByBus[randomId].label;
                     }
                     question = `هل الحافلة '${randomOtherBusLabel}' هي الأعلى مصاريفاً؟`;
                     correctAnswer = 'لا';
                     reason = `الحافلة الأعلى مصاريفاً هي '${highestBusLabel}' بمبلغ ${formatCurrency(highestAmount)} ريال.`;
                 } else {
                     question = `هل الحافلة '${highestBusLabel}' هي الأعلى مصاريفاً؟`;
                     correctAnswer = 'نعم';
                     reason = `نعم، الحافلة '${highestBusLabel}' هي الوحيدة المسجلة وبالتالي هي الأعلى مصاريفاً بمبلغ ${formatCurrency(highestAmount)} ريال.`;
                 }
                break;
            }
            case 'busThreshold': {
                const busIds = Object.keys(reportExpensesByBus);
                const randomBusId = busIds[Math.floor(Math.random() * busIds.length)];
                const busData = reportExpensesByBus[randomBusId];
                const actualAmount = busData.total;
                threshold = Math.random() > 0.5
                    ? Math.ceil(actualAmount * (1 + Math.random() * 0.3)) // 0-30% higher
                    : Math.floor(actualAmount * (1 - Math.random() * 0.3)); // 0-30% lower
                threshold = Math.max(50, threshold);

                question = `هل تجاوزت مصاريف الحافلة '${busData.label}' مبلغ ${formatCurrency(threshold)} ريال؟`;
                 if (actualAmount > threshold) {
                    correctAnswer = 'نعم';
                    reason = `نعم، إجمالي مصاريف الحافلة '${busData.label}' هو ${formatCurrency(actualAmount)} ريال، وهو أعلى من ${formatCurrency(threshold)} ريال.`;
                } else {
                    correctAnswer = 'لا';
                    reason = `لا، إجمالي مصاريف الحافلة '${busData.label}' هو ${formatCurrency(actualAmount)} ريال، وهو ليس أعلى من ${formatCurrency(threshold)} ريال.`;
                }
                break;
            }
            case 'singleExpenseThreshold': {
                let maxSingleExpense = 0;
                reportRawExpenses.forEach(exp => {
                    const amount = parseFloat(exp.total_with_tax || 0);
                    if (amount > maxSingleExpense) maxSingleExpense = amount;
                });
                threshold = Math.random() > 0.5
                    ? Math.ceil(maxSingleExpense * (1 + Math.random() * 0.1)) // 0-10% higher
                    : Math.floor(maxSingleExpense * (1 - Math.random() * 0.1)); // 0-10% lower
                 threshold = Math.max(100, threshold); // Min threshold

                question = `هل يوجد أي مصروف فردي يتجاوز مبلغ ${formatCurrency(threshold)} ريال؟`;
                if (maxSingleExpense > threshold) {
                    correctAnswer = 'نعم';
                    reason = `نعم، يوجد مصروف واحد على الأقل يتجاوز ${formatCurrency(threshold)} ريال (أعلى مصروف فردي هو ${formatCurrency(maxSingleExpense)} ريال).`;
                } else {
                    correctAnswer = 'لا';
                    reason = `لا، أعلى مصروف فردي هو ${formatCurrency(maxSingleExpense)} ريال، وهو لا يتجاوز ${formatCurrency(threshold)} ريال.`;
                }
                break;
            }
             case 'typeVsBus': // Example: Is the highest expense type mainly from the highest expense bus?
                {
                    let highestType = ''; let highestTypeAmount = -1;
                    for (const type in reportExpensesByType) { if (reportExpensesByType[type] > highestTypeAmount) { highestTypeAmount = reportExpensesByType[type]; highestType = type; } }

                    let highestBusId = ''; let highestBusAmount = -1; let highestBusLabel = '';
                    for (const busId in reportExpensesByBus) { if (reportExpensesByBus[busId].total > highestBusAmount) { highestBusAmount = reportExpensesByBus[busId].total; highestBusId = busId; highestBusLabel = reportExpensesByBus[busId].label; } }

                    const highestBusData = reportExpensesByBus[highestBusId];
                    const amountOfTypeFromHighestBus = highestBusData?.types[highestType] || 0;

                    // Ask if more than 50% of the highest expense type comes from the highest expense bus
                    const percentage = highestTypeAmount > 0 ? (amountOfTypeFromHighestBus / highestTypeAmount) * 100 : 0;

                    question = `هل أكثر من نصف مصاريف '${highestType}' (النوع الأعلى) تأتي من الحافلة '${highestBusLabel}' (الحافلة الأعلى مصاريفاً)؟`;
                    if (percentage > 50) {
                        correctAnswer = 'نعم';
                        reason = `نعم، حوالي ${percentage.toFixed(0)}% من مصاريف '${highestType}' تأتي من الحافلة '${highestBusLabel}'.`;
                    } else {
                        correctAnswer = 'لا';
                        reason = `لا، فقط حوالي ${percentage.toFixed(0)}% من مصاريف '${highestType}' تأتي من الحافلة '${highestBusLabel}'.`;
                    }
                }
                break;

            default:
                question = 'خطأ في إنشاء السؤال.';
                reason = 'نوع السؤال غير معروف.';
        }

        // Store and display the question
        currentAnalysisQuestion.question = question;
        currentAnalysisQuestion.correctAnswer = correctAnswer;
        currentAnalysisQuestion.reason = reason;

        if (systemQuestionText) systemQuestionText.textContent = question;
        if (analysisAnswerButtons) analysisAnswerButtons.style.display = 'flex'; // Show buttons

        console.log("Generated Question:", currentAnalysisQuestion);

    } catch (error) {
        console.error("Error generating analysis question:", error);
        if (systemQuestionText) systemQuestionText.textContent = 'حدث خطأ أثناء إنشاء السؤال.';
        showMessage(analysisMessage, `خطأ: ${error.message}`, 'error');
    }
};

// --- Analysis Answer Checking ---
const checkUserAnswer = (userAnswer) => { // userAnswer is 'نعم' or 'لا'
    if (!currentAnalysisQuestion.correctAnswer) return; // No question generated or error

    const isCorrect = userAnswer === currentAnalysisQuestion.correctAnswer;

    if (feedbackCorrectnessSpan) {
        feedbackCorrectnessSpan.textContent = isCorrect ? 'صحيح!' : 'غير صحيح.';
        feedbackCorrectnessSpan.className = isCorrect ? 'correct' : 'incorrect';
    }
    if (feedbackReasonSpan) {
        feedbackReasonSpan.textContent = currentAnalysisQuestion.reason;
    }

    if (analysisFeedbackArea) analysisFeedbackArea.style.display = 'block';
    if (analysisAnswerButtons) analysisAnswerButtons.style.display = 'none'; // Hide answer buttons
};

// --- Report Generation ---
const generateReportHTML = (expensesData, monthInfo, busDetailsMap) => {
    let html = '';
    let grandTotal = 0;
    const expensesByTypeSummary = {};
    const alerts = [];

    // 1. Report Metadata
    const selectedBusLabels = Object.values(busDetailsMap).map(b => b.label).join('، ');
    html += `
        <div class="report-meta">
            <p><strong>فترة التقرير:</strong> ${monthInfo.name} ${monthInfo.year}</p>
            <p><strong>الحافلات المشمولة:</strong> ${selectedBusLabels || 'لا يوجد'}</p>
            <p><strong>تاريخ إنشاء التقرير:</strong> ${formatDate(new Date())}</p>
        </div>
    `;

    // 2. Group expenses by bus
    const expensesByBus = {};
    expensesData.forEach(exp => {
        const busId = exp.bus_id;
        if (!expensesByBus[busId]) {
            expensesByBus[busId] = {
                label: busDetailsMap[busId]?.label || `ID: ${busId.substring(0,6)}`,
                expenses: [],
                total: 0,
                types: {}
            };
        }
        const totalAmount = parseFloat(exp.total_with_tax || 0);
        expensesByBus[busId].expenses.push(exp);
        expensesByBus[busId].total += totalAmount;
        grandTotal += totalAmount;

        // Group by type within bus
        const type = exp.expense_type || 'غير محدد';
        if (!expensesByBus[busId].types[type]) {
            expensesByBus[busId].types[type] = 0;
        }
        expensesByBus[busId].types[type] += totalAmount;

        // Group by type overall
        if (!expensesByTypeSummary[type]) {
            expensesByTypeSummary[type] = 0;
        }
        expensesByTypeSummary[type] += totalAmount;

        // Simple Alert Example: High individual expense
        if (totalAmount > 5000) {
            alerts.push(`يوجد مصروف مرتفع (${formatCurrency(totalAmount)} ريال) للحافلة ${expensesByBus[busId].label} بتاريخ ${formatDate(exp.expense_date)} (${type}).`);
        }
    });

    // 3. Overall Summary Section
    html += `
        <div class="report-summary">
            <h3><i class="fas fa-calculator"></i> ملخص المصروفات الإجمالي</h3>
            <div class="summary-grid">
    `;
    for (const type in expensesByTypeSummary) {
        html += `
            <div class="summary-item">
                <span class="label">${type}:</span>
                <span class="value">${formatCurrency(expensesByTypeSummary[type])} ريال</span>
            </div>
        `;
    }
    html += `
            <div class="summary-item total">
                <span class="label">الإجمالي العام:</span>
                <span class="value">${formatCurrency(grandTotal)} ريال</span>
            </div>
        </div></div>
    `;

    // 4. Detailed Section per Bus
    for (const busId in expensesByBus) {
        const busData = expensesByBus[busId];
        html += `
            <section class="bus-section">
                <h4><i class="fas fa-bus"></i> تفاصيل مصاريف الحافلة: ${busData.label}</h4>
                <div class="table-responsive">
                    <table class="details-table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>نوع المصروف</th>
                                <th>الوصف</th>
                                <th>المبلغ الأساسي</th>
                                <th>الضريبة</th>
                                <th>الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
        `;
        // Sort expenses by date for clarity
        busData.expenses.sort((a, b) => new Date(a.expense_date) - new Date(b.expense_date));

        busData.expenses.forEach(exp => {
            html += `
                <tr>
                    <td>${formatDate(exp.expense_date)}</td>
                    <td>${exp.expense_type || 'غير محدد'}</td>
                    <td>${exp.details || '-'}</td>
                    <td>${formatCurrency(exp.amount)}</td>
                    <td>${formatCurrency(exp.tax_amount)}</td>
                    <td>${formatCurrency(exp.total_with_tax)}</td>
                </tr>
            `;
        });
        html += `
                            <tr class="total-row">
                                <td colspan="5">إجمالي مصاريف الحافلة ${busData.label}</td>
                                <td>${formatCurrency(busData.total)} ريال</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>
        `;
         // Simple Alert Example: High total for bus
         if (busData.total > 15000) {
             alerts.push(`إجمالي المصاريف مرتفع للحافلة ${busData.label} (${formatCurrency(busData.total)} ريال).`);
         }
    }

    // 5. Alerts Section
    if (alerts.length > 0) {
        html += `
            <section class="report-alerts">
                <h4><i class="fas fa-exclamation-triangle"></i> تنبيهات</h4>
                <ul>
        `;
        alerts.forEach(alertMsg => {
            html += `<li><i class="fas fa-exclamation-circle"></i> ${alertMsg}</li>`;
        });
        html += `</ul></section>`;
    }

    // --- تخزين البيانات للتحليل ---
    reportRawExpenses = expensesData;
    reportExpensesByType = expensesByTypeSummary; // Assuming expensesByTypeSummary is calculated here
    reportExpensesByBus = expensesByBus;         // Assuming expensesByBus is calculated here
    reportGrandTotal = grandTotal;               // Assuming grandTotal is calculated here
    reportBusDetailsMap = busDetailsMap;
    // --- نهاية التخزين ---

    return html;
};


// --- Main Function ---
const loadReportData = async () => {
    if (!_supabase || !reportContent) return;

    try {
        // 1. Get parameters from sessionStorage
        const selectedBusIdsString = sessionStorage.getItem('reportBusIds');
        const selectedMonthId = sessionStorage.getItem('reportMonthId');

        if (!selectedBusIdsString || !selectedMonthId) {
            throw new Error('بيانات التقرير المطلوبة (معرفات الحافلات أو الشهر) غير موجودة.');
        }

        const selectedBusIds = JSON.parse(selectedBusIdsString);
        if (!Array.isArray(selectedBusIds) || selectedBusIds.length === 0) {
            throw new Error('لم يتم تحديد حافلات للتقرير.');
        }

        console.log('Report Parameters:', { selectedBusIds, selectedMonthId });

        // 2. Fetch required data in parallel
        // --- تعديل الاستعلام لجلب year_number ---
        const [monthRes, busesRes, expensesRes] = await Promise.all([
            _supabase.from('budget_months').select('month_number, budget_years(year_number)').eq('id', selectedMonthId).single(), // Fetch year_number
            _supabase.from('buses').select('id, bus_number, plate_numbers').in('id', selectedBusIds),
            _supabase.from('bus_expenses').select('*').eq('budget_month_id', selectedMonthId).in('bus_id', selectedBusIds)
        ]);
        // --- نهاية التعديل ---

        // Check for errors
        if (monthRes.error) throw new Error(`خطأ في جلب بيانات الشهر: ${monthRes.error.message}`);
        if (busesRes.error) throw new Error(`خطأ في جلب بيانات الحافلات: ${busesRes.error.message}`);
        if (expensesRes.error) throw new Error(`خطأ في جلب بيانات المصاريف: ${expensesRes.error.message}`);

        const monthData = monthRes.data;
        const busData = busesRes.data;
        const expensesData = expensesRes.data;

        // --- تعديل التحقق والوصول إلى السنة ---
        if (!monthData || !monthData.budget_years) throw new Error('لم يتم العثور على بيانات الشهر أو السنة المحددة.');

        // Prepare data for report generation
        const monthInfo = {
            id: selectedMonthId,
            name: getMonthName(monthData.month_number),
            year: monthData.budget_years.year_number // Access year_number
        };
        // --- نهاية التعديل ---

        const busDetailsMap = {};
        busData.forEach(bus => {
            busDetailsMap[bus.id] = {
                label: bus.bus_number ? `رقم ${bus.bus_number}` : (bus.plate_numbers || `ID: ${bus.id.substring(0, 6)}`)
            };
        });

        // Process expensesData to calculate summaries
        const expensesByBus = {};
        const expensesByTypeSummary = {};
        let grandTotal = 0;

        (expensesData || []).forEach(exp => { // Ensure expensesData is an array
            const busId = exp.bus_id;
            if (!expensesByBus[busId]) {
                expensesByBus[busId] = {
                    label: busDetailsMap[busId]?.label || `ID: ${busId.substring(0,6)}`,
                    expenses: [],
                    total: 0,
                    types: {}
                };
            }
            const totalAmount = parseFloat(exp.total_with_tax || 0);
            if (!isNaN(totalAmount)) { // Only process valid amounts
                expensesByBus[busId].expenses.push(exp);
                expensesByBus[busId].total += totalAmount;
                grandTotal += totalAmount;

                // Group by type within bus
                const type = exp.expense_type || 'غير محدد';
                if (!expensesByBus[busId].types[type]) {
                    expensesByBus[busId].types[type] = 0;
                }
                expensesByBus[busId].types[type] += totalAmount;

                // Group by type overall
                if (!expensesByTypeSummary[type]) {
                    expensesByTypeSummary[type] = 0;
                }
                expensesByTypeSummary[type] += totalAmount;
            } else {
                console.warn(`Invalid amount found for expense ID ${exp.id}, skipping calculation.`);
            }
        });

        // --- Store processed data globally/module-scoped ---
        reportRawExpenses = expensesData || [];
        reportExpensesByType = expensesByTypeSummary || {};
        reportExpensesByBus = expensesByBus || {};
        reportGrandTotal = grandTotal || 0;
        reportBusDetailsMap = busDetailsMap || {};
        // --- Log stored data ---
        console.log("--- Data Stored After Load ---");
        console.log("Stored Data:", {
            reportRawExpenses: reportRawExpenses.length,
            reportExpensesByType: Object.keys(reportExpensesByType).length,
            reportExpensesByBus: Object.keys(reportExpensesByBus).length,
            reportGrandTotal,
            reportBusDetailsMap: Object.keys(reportBusDetailsMap).length
        });
        console.log("Sample Type Data:", Object.entries(reportExpensesByType).slice(0, 5)); // Log first 5 types
        console.log("Sample Bus Data:", Object.entries(reportExpensesByBus).slice(0, 3)); // Log first 3 buses
        // --- End Log ---


        // 3. Generate Report HTML
        // Pass the calculated summaries to generateReportHTML if needed, or it can use globals
        const reportHTML = generateReportHTML(reportRawExpenses, monthInfo, reportBusDetailsMap);
        reportContent.innerHTML = reportHTML;

        // 4. Generate Charts
        generateCharts(reportRawExpenses, reportBusDetailsMap);

        // --- إضافة: استدعاء دالة إنشاء السؤال التحليلي ---
        generateAnalysisQuestion();
        // --- نهاية الإضافة ---

    } catch (error) {
        console.error('Error loading report data:', error);
        reportContent.innerHTML = `<p class="loading-placeholder error">فشل تحميل التقرير: ${error.message}</p>`;
         if (chartMessage) {
             chartMessage.textContent = 'لا يمكن عرض الرسوم البيانية بسبب خطأ في تحميل البيانات.';
             chartMessage.style.display = 'block';
             chartMessage.className = 'message error';
         }
         // Clear stored analysis data on error
         reportRawExpenses = [];
         reportExpensesByType = {};
         reportExpensesByBus = {};
         reportGrandTotal = 0;
         reportBusDetailsMap = {};
         // Display error in analysis section as well
         if (systemQuestionText) systemQuestionText.textContent = 'فشل تحميل البيانات اللازمة للتحليل.';
         if (analysisMessage) showMessage(analysisMessage, 'لا يمكن إجراء التحليل بسبب خطأ في تحميل البيانات.', 'error');
    }
};

// --- Event Listeners ---
document.addEventListener('DOMContentLoaded', () => {
    loadReportData();

    if (printReportBtn) {
        printReportBtn.addEventListener('click', () => {
            window.print();
        });
    }

    // --- تعديل: مستمعات أحداث لأزرار الإجابة والسؤال التالي ---
    if (answerYesBtn) {
        answerYesBtn.addEventListener('click', () => checkUserAnswer('نعم'));
    }
    if (answerNoBtn) {
        answerNoBtn.addEventListener('click', () => checkUserAnswer('لا'));
    }
    if (nextQuestionBtn) {
        nextQuestionBtn.addEventListener('click', generateAnalysisQuestion);
    }
    // --- نهاية التعديل ---

    // Remove old analysis button listener
    // if (analyzeQuestionBtn && analysisQuestionInput) { ... }
});
