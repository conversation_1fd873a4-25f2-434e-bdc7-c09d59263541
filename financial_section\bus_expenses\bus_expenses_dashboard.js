// --- Auth Check ---
checkAuth('../../login.html');

// --- Supabase Initialization ---
let _supabase;
try {
    if (typeof supabase !== 'undefined' && supabase.createClient && typeof SUPABASE_URL !== 'undefined' && typeof SUPABASE_ANON_KEY !== 'undefined') {
        _supabase = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('Supabase client initialized for bus expenses dashboard.');
    } else {
        throw new Error('Supabase client or config variables not available.');
    }
} catch (error) {
    console.error('Error initializing Supabase client:', error);
    alert('خطأ فادح: لا يمكن الاتصال بقاعدة البيانات.');
}

// --- DOM Elements ---
// عناصر المستخدم والرسائل
const navbarUsername = document.getElementById('navbar-username');
const logoutBtn = document.getElementById('logout-btn');
const backToFinanceBtn = document.getElementById('back-to-finance');
const dashboardMessage = document.getElementById('dashboard-message');

// عناصر إحصائيات المصاريف
// --- تعديل: تحديث معرفات عناصر الإجمالي ---
const totalBaseAmountElement = document.getElementById('total-base-amount');
const totalTaxAmountElement = document.getElementById('total-tax-amount');
const grandTotalAmountElement = document.getElementById('grand-total-amount');
// --- نهاية التعديل ---
const periodDisplay = document.getElementById('period-display');

// عنصر قائمة الباصات
const busesList = document.getElementById('buses-list');

// --- إعادة إضافة: عنصر عرض الشهر والسنة ---
const currentMonthYearDisplay = document.getElementById('current-month-year');

// --- State ---
let allBuses = [];
let expensesByType = {};
// --- تعديل: تغيير اسم المتغير وتحديد أنه للمبلغ الأساسي ---
let totalBaseExpensesAmount = 0;
// --- إضافة: متغيرات لحفظ إجمالي الضريبة والإجمالي الكلي ---
let totalTaxAmount = 0;
let grandTotalAmount = 0;
// --- نهاية الإضافة ---
let selectedBudgetMonthId = null;

// --- Helper Functions ---
const showMessage = (message, type = 'info', duration = 3000) => {
    if (!dashboardMessage) return;
    
    dashboardMessage.textContent = message;
    dashboardMessage.className = `message ${type}`;
    dashboardMessage.style.display = 'block';
    
    if (duration > 0) {
        setTimeout(() => {
            if (dashboardMessage.textContent === message) {
                dashboardMessage.style.display = 'none';
            }
        }, duration);
    }
};

const formatCurrency = (amount) => {
    const value = parseFloat(amount);
    return isNaN(value) ? '0.00 ريال' : value.toFixed(2) + ' ريال';
};

// --- إضافة: دالة جلب اسم الشهر ---
// Function to get month name from number (Arabic)
const getMonthName = (monthNumber) => {
    const names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"];
    const index = parseInt(monthNumber, 10) - 1;
    return names[index] || `شهر ${monthNumber}`;
};
// --- نهاية الإضافة ---

// --- تعديل: دالة عرض الشهر والسنة من sessionStorage ---
const displayActiveMonthFromStorage = () => {
    if (!currentMonthYearDisplay) return false; // Return false if display element is missing

    try {
        console.log('[bus_expenses_dashboard] Attempting to read selected month/year from sessionStorage...');
        const monthId = sessionStorage.getItem('selectedBudgetMonthId'); // قراءة المعرف
        const monthNumber = sessionStorage.getItem('selectedBudgetMonthNumber');
        const year = sessionStorage.getItem('selectedBudgetYearNumber');
        console.log(`[bus_expenses_dashboard] Read from sessionStorage: ID=${monthId}, MonthNumber=${monthNumber}, Year=${year}`);

        if (monthId && monthNumber && year) {
            selectedBudgetMonthId = monthId; // تخزين المعرف في متغير الحالة
            const monthName = getMonthName(monthNumber);
            currentMonthYearDisplay.textContent = `${monthName} ${year}`;
            console.log(`[bus_expenses_dashboard] Displayed month/year from sessionStorage: ${monthName} ${year}. Month ID: ${selectedBudgetMonthId}`);
            return true; // تم التحميل بنجاح
        } else {
            selectedBudgetMonthId = null; // مسح المعرف إذا لم يتم العثور عليه
            currentMonthYearDisplay.textContent = 'الشهر غير محدد';
            currentMonthYearDisplay.style.color = 'orange';
            console.warn('[bus_expenses_dashboard] Selected month ID, number, or year not found in sessionStorage. Please select a month first.');
            showMessage('لم يتم تحديد الشهر. يرجى العودة واختيار شهر أولاً.', 'warning', 0); // Show persistent warning
            return false; // فشل التحميل
        }
    } catch (e) {
        selectedBudgetMonthId = null; // مسح المعرف عند الخطأ
        console.error('[bus_expenses_dashboard] Error reading selected month/year from sessionStorage:', e);
        currentMonthYearDisplay.textContent = 'خطأ في القراءة';
        currentMonthYearDisplay.style.color = 'red';
        showMessage('خطأ في قراءة بيانات الشهر المحدد.', 'error', 0);
        return false; // فشل التحميل
    }
};

// --- Fetch Data ---

// --- إزالة: دالة جلب الشهر النشط ---
/*
const fetchActiveBudgetMonth = async () => {
    // ... محتوى الدالة المحذوفة ...
};
*/

// دالة جلب الباصات
const fetchBuses = async () => {
    if (!_supabase || !busesList) return;
    
    try {
        busesList.innerHTML = '<div class="loading-message">جاري تحميل بيانات الباصات...</div>';
        
        // جلب الباصات النشطة فقط
        const { data, error } = await _supabase
            .from('buses')
            .select('id, bus_number, bus_type, model, status')
            .eq('status', 'نشط')  // فلترة الباصات النشطة فقط
            .order('bus_number', { ascending: true }); // ترتيب تصاعدي حسب رقم الباص
        
        if (error) throw error;
        
        allBuses = data || [];
        renderBuses();
        
    } catch (error) {
        console.error('Error fetching buses:', error);
        showMessage(`خطأ في جلب بيانات الباصات: ${error.message}`, 'error');
        busesList.innerHTML = '<div class="loading-message error">فشل في تحميل بيانات الباصات.</div>';
    }
};

// دالة جلب إحصائيات المصاريف حسب النوع
const fetchExpenseSummaries = async () => {
    // --- تعديل: التحقق من وجود معرف الشهر المحدد ---
    if (!_supabase || !selectedBudgetMonthId) {
        console.warn("Skipping fetchExpenseSummaries: Supabase client not ready or selectedBudgetMonthId is missing.");
        // مسح البيانات القديمة وعرض رسالة
        expensesByType = {};
        // --- تعديل: مسح الإجماليات ---
        totalBaseExpensesAmount = 0;
        totalTaxAmount = 0;
        grandTotalAmount = 0;
        // --- نهاية التعديل ---
        updateExpenseBoxes(); // تحديث الواجهة لعرض الأصفار
        if (!selectedBudgetMonthId) {
             showMessage('لا يمكن تحميل الإحصائيات. لم يتم تحديد الشهر.', 'warning', 0);
        }
        return;
    }
    // --- نهاية التعديل ---

    console.log(`Fetching expense summaries for Month ID: ${selectedBudgetMonthId}`); // Log start

    try {
        // --- تعديل: جلب حقول الضريبة والإجمالي ---
        let query = _supabase
            .from('bus_expenses')
            .select('expense_type, amount, tax_amount, total_with_tax') // جلب الحقول المطلوبة
            .eq('budget_month_id', selectedBudgetMonthId); // فلترة حسب الشهر المحدد
        // --- نهاية التعديل ---

        const { data, error } = await query;

        if (error) throw error;

        // حساب مجموع المصاريف لكل نوع والإجماليات
        expensesByType = {};
        // --- تعديل: إعادة تعيين الإجماليات ---
        totalBaseExpensesAmount = 0; // Reset base amount
        totalTaxAmount = 0;
        grandTotalAmount = 0;
        // --- نهاية التعديل ---

        if (data && data.length > 0) {
            data.forEach(expense => {
                let expenseType = expense.expense_type;
                const baseAmount = parseFloat(expense.amount) || 0;
                // --- تعديل: حساب إجمالي الضريبة والإجمالي الكلي ---
                const taxAmount = parseFloat(expense.tax_amount) || 0;
                // استخدام total_with_tax إذا كان موجودًا ودقيقًا، أو حسابه
                const totalWithTax = parseFloat(expense.total_with_tax) || (baseAmount + taxAmount);

                totalBaseExpensesAmount += baseAmount; // Sum base amounts
                totalTaxAmount += taxAmount;       // Sum tax amounts
                grandTotalAmount += totalWithTax;  // Sum total amounts (base + tax)
                // --- نهاية التعديل ---

                // --- تعديل اختياري: تجميع "قطع غيار" مع "صيانة" ---
                /*
                if (expenseType === 'قطع غيار') {
                    expenseType = 'صيانة'; // Assign to 'صيانة' category
                }
                */
                // --- نهاية التعديل الاختياري ---

                if (!expensesByType[expenseType]) {
                    expensesByType[expenseType] = 0;
                }
                // تجميع حسب النوع بناءً على المبلغ الأساسي
                expensesByType[expenseType] += baseAmount;

            });
             console.log(`Expense summary fetched for month ${selectedBudgetMonthId}:`, { expensesByType, totalBaseExpensesAmount, totalTaxAmount, grandTotalAmount });
             // --- تعديل: استخدام dashboardMessage بدلاً من listMessage ---
             if (dashboardMessage && dashboardMessage.textContent.includes('جاري تحميل')) { // Clear loading message if still present
                 showMessage('تم تحميل الإحصائيات بنجاح.', 'success', 3000);
             }
             // --- نهاية التعديل ---
        } else {
             console.log(`No expenses found for month ${selectedBudgetMonthId}.`);
             showMessage(`لا توجد مصاريف مسجلة للشهر المحدد (${currentMonthYearDisplay.textContent}).`, 'info');
        }

        // تحديث واجهة المستخدم بالإحصائيات
        updateExpenseBoxes();

    } catch (error) {
        console.error('Error fetching expense summaries:', error);
        showMessage(`خطأ في جلب إحصائيات المصاريف: ${error.message}`, 'error');
        // مسح البيانات عند الخطأ
        expensesByType = {};
        // --- تعديل: مسح الإجماليات عند الخطأ ---
        totalBaseExpensesAmount = 0;
        totalTaxAmount = 0;
        grandTotalAmount = 0;
        // --- نهاية التعديل ---
        updateExpenseBoxes();
    }
};

// --- Render Functions ---
// دالة عرض بطاقات الباصات
const renderBuses = () => {
    if (!busesList) return;
    
    if (allBuses.length === 0) {
        busesList.innerHTML = '<div class="loading-message">لا توجد باصات نشطة.</div>';
        return;
    }
    
    busesList.innerHTML = '';
    
    // إنشاء بطاقة لكل باص
    allBuses.forEach(bus => {
        const busItem = document.createElement('div');
        busItem.className = 'bus-item';
        busItem.dataset.busId = bus.id;
        
        // تحديد الأيقونة المناسبة حسب نوع الباص
        let iconClass = 'fa-bus';
        
        // تخصيص الأيقونة بناءً على نوع الباص (يمكن تعديلها حسب الأنواع الفعلية)
        if (bus.bus_type === 'صغير') {
            iconClass = 'fa-shuttle-van';
        } else if (bus.bus_type === 'متوسط') {
            iconClass = 'fa-bus-alt';
        } else if (bus.bus_type === 'VIP') {
            iconClass = 'fa-bus'; // يمكن استخدام أيقونة مخصصة للباصات الفاخرة
        }
        
        busItem.innerHTML = `
            <div class="bus-type">${bus.bus_type}</div>
            <div class="bus-icon"><i class="fas ${iconClass}"></i></div>
            <div class="bus-number">${bus.bus_number}</div>
        `;
        
        // استخدام معالج النقر لفتح صفحة تفاصيل الباص مع إرسال معرف الباص كمعلمة في العنوان
        busItem.addEventListener('click', () => {
            console.log(`تم النقر على الباص رقم ${bus.bus_number} بمعرف ${bus.id}`);
            window.location.href = `bus_expenses.html?bus_id=${bus.id}`;
        });
        
        busesList.appendChild(busItem);
    });
};

// دالة تحديث مربعات المصاريف
const updateExpenseBoxes = () => {
    // --- تعديل: تحديث عناصر الإجمالي الجديدة ---
    if (totalBaseAmountElement) {
        totalBaseAmountElement.innerHTML = `<span>الأساسي:</span> ${formatCurrency(totalBaseExpensesAmount)}`;
    }
    if (totalTaxAmountElement) {
        totalTaxAmountElement.innerHTML = `<span>الضريبة:</span> ${formatCurrency(totalTaxAmount)}`;
    }
    if (grandTotalAmountElement) {
        grandTotalAmountElement.innerHTML = `<span>الكلي:</span> ${formatCurrency(grandTotalAmount)}`;
    }
    // --- نهاية التعديل ---

    // --- تعديل: تحديث نص الفترة الزمنية ليعكس الشهر المحدد ---
    if (periodDisplay) {
        // النص الآن يعكس الشهر المحدد من currentMonthYearDisplay
        periodDisplay.textContent = `لـ ${currentMonthYearDisplay.textContent || 'الشهر المحدد'}`;
        /*
        // --- إزالة الكود القديم ---
        periodDisplay.textContent = 'للشهر الحالي';
        */
    }
    // --- نهاية التعديل ---

    // تحديث مربعات أنواع المصاريف
    const expenseBoxes = document.querySelectorAll('.expense-box');
    expenseBoxes.forEach(box => {
        const expenseType = box.dataset.type;
        const amountElement = box.querySelector('.expense-amount');
        
        if (amountElement) {
            // عرض المبلغ الأساسي لكل نوع
            const amount = expensesByType[expenseType] || 0;
            amountElement.textContent = formatCurrency(amount);
            
            // إضافة تأثير بصري للمبالغ الكبيرة (اختياري)
            if (amount > 0) {
                box.classList.add('has-expenses');
                // إضافة فئات للمبالغ المختلفة لأغراض التصميم
                if (amount > 10000) box.classList.add('high-expense');
                else if (amount > 5000) box.classList.add('medium-expense');
                else box.classList.add('low-expense');
            } else {
                box.classList.remove('has-expenses', 'high-expense', 'medium-expense', 'low-expense');
            }
        }
    });
};

// --- Event Handlers ---
// --- إزالة: دالة تغيير الفترة الزمنية ---
/*
const changePeriod = (periodType) => {
    activePeriod = periodType;

    // تحديث الزر النشط
    periodButtons.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.period === periodType);
    });

    // إعادة جلب البيانات للفترة الجديدة
    fetchExpenseSummaries();
};
*/

// --- Setup Event Listeners ---
const setupEventListeners = () => {
    // --- إزالة: مستمعات أحداث لأزرار تغيير الفترة الزمنية ---
    /*
    periodButtons.forEach(btn => {
        btn.addEventListener('click', () => changePeriod(btn.dataset.period));
    });
    */

    // زر العودة للوحة المالية الرئيسية
    if (backToFinanceBtn) {
        backToFinanceBtn.addEventListener('click', () => {
            window.location.href = '../financial_dashboard.html';
        });
    }
    
    // زر تسجيل الخروج
    if (logoutBtn) {
        logoutBtn.addEventListener('click', async () => {
            try {
                await _supabase.auth.signOut();
                window.location.href = '../../login.html';
            } catch (error) {
                console.error('Error logging out:', error);
                alert('حدث خطأ أثناء تسجيل الخروج');
            }
        });
    }

    // Report Modal Listeners
    if (openReportModalBtn) {
        openReportModalBtn.addEventListener('click', openReportModal);
    }
    if (closeReportModalBtn) {
        closeReportModalBtn.addEventListener('click', closeReportModal);
    }
    if (reportModal) {
        reportModal.addEventListener('click', (e) => {
            if (e.target === reportModal) {
                closeReportModal();
            }
        });
    }
    // تغيير مستمع الحدث للزر الجديد
    if (launchReportBtn) {
        launchReportBtn.addEventListener('click', launchReportPage); // Call the new function
    } else {
        console.warn('Launch report button not found.');
    }

    // Global Escape Listener (Update to include report modal)
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            if (reportModal?.classList.contains('show')) {
                closeReportModal();
            }
            // Add similar checks for other modals if they exist
        }
    });
};

// --- Initialization ---
document.addEventListener('DOMContentLoaded', async () => {
    // التحقق من جاهزية Supabase
    if (!_supabase) {
        showMessage('خطأ فادح: لم يتم تهيئة الاتصال بقاعدة البيانات.', 'error', 0);
        return;
    }
    
    // عرض اسم المستخدم وزر تسجيل الخروج
    const { data: { user } } = await _supabase.auth.getUser();
    if (user && navbarUsername) {
        navbarUsername.textContent = user.email;
        if (logoutBtn) logoutBtn.style.display = 'flex';
    }
    
    // --- تعديل: قراءة الشهر أولاً ---
    const monthLoaded = displayActiveMonthFromStorage(); // قراءة الشهر وتخزين المعرف

    // إعداد مستمعات الأحداث
    setupEventListeners();

    // جلب بيانات الباصات
    await fetchBuses();

    // --- تعديل: جلب الإحصائيات فقط إذا تم تحميل الشهر بنجاح ---
    if (monthLoaded) {
        await fetchExpenseSummaries(); // جلب الإحصائيات للشهر المحدد
    } else {
        // رسالة الخطأ تم عرضها بواسطة displayActiveMonthFromStorage
        console.error("Cannot fetch summaries because selected month is not loaded.");
        // تأكد من أن الواجهة تعكس عدم وجود بيانات
        expensesByType = {};
        totalBaseExpensesAmount = 0;
        totalTaxAmount = 0;
        grandTotalAmount = 0;
        updateExpenseBoxes();
    }
    // --- نهاية التعديل ---

    // Initialize Choices.js after other initial setup
    initializeChoices();

    // Populate bus select initially if buses are already fetched
    // (Ensure fetchBuses is called before this point or call populate again after fetch)
    if (allBuses.length > 0) {
        // populateReportBusSelect(); // Choices is initialized, but might need data first
    }

    // Setup event listeners (ensure this is called after elements are defined)
    setupEventListeners();

    // ... rest of initialization ...
     // Fetch buses (ensure this happens before populating report select if needed on load)
    await fetchBuses();
    // Now populate the select as buses are available
    populateReportBusSelect();

    // ... rest of DOMContentLoaded ...
});

// --- DOM Elements ---
const openReportModalBtn = document.getElementById('open-report-modal-btn');
const reportModal = document.getElementById('report-modal');
const closeReportModalBtn = document.getElementById('close-report-modal-btn');
const reportBusSelect = document.getElementById('report-bus-select');
const reportPeriodDisplay = document.getElementById('report-period-display');
const generateReportBtn = document.getElementById('generate-report-btn');
const reportModalMessage = document.getElementById('report-modal-message');
const launchReportBtn = document.getElementById('launch-report-btn'); // Changed ID

// --- State ---
let choicesBusSelect = null; // لتخزين كائن Choices.js

// --- تهيئة Choices.js ---
const initializeChoices = () => {
    if (reportBusSelect) {
        choicesBusSelect = new Choices(reportBusSelect, {
            removeItemButton: true,
            placeholder: true,
            placeholderValue: 'اختر حافلة واحدة أو أكثر...',
            searchPlaceholderValue: 'ابحث عن حافلة...',
            itemSelectText: 'اضغط للاختيار',
            noResultsText: 'لا توجد نتائج',
            noChoicesText: 'لا توجد خيارات أخرى',
        });
        console.log('Choices.js initialized for bus selection.');
    } else {
        console.warn('Report bus select element not found for Choices.js initialization.');
    }
};

// --- ملء قائمة اختيار الحافلات في نافذة التقرير ---
const populateReportBusSelect = () => {
    if (!choicesBusSelect) {
        console.warn('Choices.js instance not available for populating buses.');
        return;
    }
    // مسح الخيارات الحالية
    choicesBusSelect.clearStore();

    if (allBuses.length > 0) {
        const choices = allBuses.map(bus => ({
            value: bus.id,
            label: bus.bus_number ? `رقم ${bus.bus_number}` : (bus.plate_numbers || `ID: ${bus.id.substring(0, 6)}`),
            selected: false, // Not selected by default
            disabled: false,
        }));
        choicesBusSelect.setChoices(choices, 'value', 'label', false);
        console.log('Report bus select populated.');
    } else {
        console.warn('No buses available to populate report select.');
        // يمكن عرض رسالة داخل Choices.js باستخدام placeholder أو noChoicesText
    }
};

// --- فتح/إغلاق نافذة التقرير ---
const openReportModal = () => {
    if (!reportModal) return;
    // التأكد من أن الشهر محدد
    if (!selectedBudgetMonthId) {
        showMessage('الرجاء تحديد الشهر أولاً قبل إنشاء التقرير.', 'warning');
        return;
    }
    // ملء قائمة الحافلات عند الفتح
    populateReportBusSelect();
    // عرض فترة التقرير (الشهر الحالي)
    if (reportPeriodDisplay && currentMonthYearDisplay) {
        reportPeriodDisplay.textContent = currentMonthYearDisplay.textContent;
    }
    // مسح المخرجات والتنبيهات السابقة
    if (reportModalMessage) reportModalMessage.style.display = 'none';

    reportModal.classList.add('show');
    document.body.style.overflow = 'hidden';
};

const closeReportModal = () => {
    if (!reportModal) return;
    reportModal.classList.remove('show');
    document.body.style.overflow = '';
    // مسح اختيار Choices.js عند الإغلاق (اختياري)
    if (choicesBusSelect) {
        choicesBusSelect.removeActiveItems();
    }
};

// --- تعديل: دالة فتح التقرير في صفحة جديدة ---
const launchReportPage = () => {
    if (!choicesBusSelect || !selectedBudgetMonthId) {
        showMessage(reportModalMessage, 'خطأ: بيانات غير مكتملة (الاختيار/الشهر).', 'error');
        return;
    }

    const selectedBusIds = choicesBusSelect.getValue(true); // الحصول على قيم ID المختارة

    if (!selectedBusIds || selectedBusIds.length === 0) {
        showMessage(reportModalMessage, 'الرجاء اختيار حافلة واحدة على الأقل.', 'warning');
        return;
    }

    // تخزين البيانات اللازمة في sessionStorage
    try {
        sessionStorage.setItem('reportBusIds', JSON.stringify(selectedBusIds));
        sessionStorage.setItem('reportMonthId', selectedBudgetMonthId);
        console.log('Report parameters stored in sessionStorage:', { selectedBusIds, selectedBudgetMonthId });

        // فتح صفحة التقرير في تبويب جديد
        window.open('bus_report.html', '_blank');

        // إغلاق النافذة المنبثقة بعد الفتح
        closeReportModal();

    } catch (error) {
        console.error('Error storing report parameters or opening report page:', error);
        showMessage(reportModalMessage, 'حدث خطأ عند محاولة فتح صفحة التقرير.', 'error');
    }
};

// --- إنشاء التقرير ---
const generateReport = async () => {
    if (!_supabase || !choicesBusSelect || !selectedBudgetMonthId) {
        showMessage(reportModalMessage, 'خطأ: بيانات غير مكتملة (Supabase/الاختيار/الشهر).', 'error');
        return;
    }

    const selectedBusIds = choicesBusSelect.getValue(true); // الحصول على قيم ID المختارة

    if (!selectedBusIds || selectedBusIds.length === 0) {
        showMessage(reportModalMessage, 'الرجاء اختيار حافلة واحدة على الأقل.', 'warning');
        return;
    }

    // عرض رسالة التحميل
    showMessage(reportModalMessage, 'جاري جلب البيانات وإنشاء التقرير...', 'info', 0);
    generateReportBtn.disabled = true;
    generateReportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإنشاء...';

    try {
        // جلب المصاريف للحافلات المحددة والشهر المحدد
        const { data: expenses, error } = await _supabase
            .from('bus_expenses')
            .select('bus_id, expense_type, amount, total_with_tax, expense_date, details, buses(bus_number, plate_numbers)') // جلب بيانات الحافلة المرتبطة
            .eq('budget_month_id', selectedBudgetMonthId)
            .in('bus_id', selectedBusIds)
            .order('bus_id') // ترتيب حسب الحافلة لتسهيل المعالجة
            .order('expense_date');

        if (error) throw error;

        // معالجة البيانات وإنشاء HTML للتقرير والتنبيهات
        let reportHTML = '';
        let alertsHTML = '';
        let grandTotal = 0;
        const expensesByBus = {};
        const expenseTypesSummary = {}; // ملخص إجمالي لكل نوع مصروف

        // تجميع المصاريف حسب الحافلة
        expenses.forEach(exp => {
            const busId = exp.bus_id;
            if (!expensesByBus[busId]) {
                const busInfo = exp.buses;
                const busLabel = busInfo?.bus_number ? `رقم ${busInfo.bus_number}` : (busInfo?.plate_numbers || `ID: ${busId.substring(0, 6)}`);
                expensesByBus[busId] = {
                    label: busLabel,
                    expenses: [],
                    total: 0,
                    types: {}
                };
            }
            const totalAmount = parseFloat(exp.total_with_tax || 0);
            expensesByBus[busId].expenses.push(exp);
            expensesByBus[busId].total += totalAmount;
            grandTotal += totalAmount;

            // تجميع حسب النوع داخل الحافلة
            const type = exp.expense_type || 'غير محدد';
            if (!expensesByBus[busId].types[type]) {
                expensesByBus[busId].types[type] = 0;
            }
            expensesByBus[busId].types[type] += totalAmount;

            // تجميع إجمالي حسب النوع
            if (!expenseTypesSummary[type]) {
                expenseTypesSummary[type] = 0;
            }
            expenseTypesSummary[type] += totalAmount;
        });

        // إنشاء HTML لكل حافلة
        for (const busId in expensesByBus) {
            const busData = expensesByBus[busId];
            reportHTML += `<h5><i class="fas fa-bus"></i> الحافلة: ${busData.label}</h5>`;
            reportHTML += '<ul>';
            for (const type in busData.types) {
                reportHTML += `<li>${type}: <strong>${formatCurrency(busData.types[type])} ريال</strong></li>`;
            }
            reportHTML += `<li class="total-line">إجمالي الحافلة: <strong>${formatCurrency(busData.total)} ريال</strong></li>`;
            reportHTML += '</ul>';

            // إضافة تنبيهات بسيطة (مثال)
            if (busData.total > 15000) { // مثال: إذا تجاوز إجمالي الحافلة 15000
                alertsHTML += `<div class="alert-item"><i class="fas fa-exclamation-circle"></i> المصاريف مرتفعة للحافلة ${busData.label} (${formatCurrency(busData.total)} ريال).</div>`;
            }
        }

        // إضافة الملخص الإجمالي
        reportHTML += `<hr><h4>الملخص الإجمالي</h4><ul>`;
        for (const type in expenseTypesSummary) {
             reportHTML += `<li>${type}: <strong>${formatCurrency(expenseTypesSummary[type])} ريال</strong></li>`;
        }
        reportHTML += `<li class="total-line">الإجمالي العام لجميع الحافلات المختارة: <strong>${formatCurrency(grandTotal)} ريال</strong></li>`;
        reportHTML += `</ul>`;


        // عرض التقرير والتنبيهات
        showMessage(reportModalMessage, 'تم إنشاء التقرير بنجاح.', 'success');

    } catch (error) {
        console.error('Error generating report:', error);
        showMessage(reportModalMessage, `خطأ في إنشاء التقرير: ${error.message}`, 'error', 0);
    } finally {
        generateReportBtn.disabled = false;
        generateReportBtn.innerHTML = '<i class="fas fa-cogs"></i> إنشاء التقرير';
    }
};
