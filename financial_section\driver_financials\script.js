// --- Auth Check ---
// checkAuth('../../login.html'); // تأكد من تفعيل هذا السطر بعد التشخيص

// --- Supabase Initialization ---
let _supabase;
try {
    if (typeof supabase !== 'undefined' && supabase.createClient && typeof SUPABASE_URL !== 'undefined' && typeof SUPABASE_ANON_KEY !== 'undefined') {
        _supabase = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('Supabase client initialized for driver financials.'); // <-- تسجيل التهيئة
    } else {
        console.error('Supabase client or config variables not found.');
        throw new Error('Supabase client or configuration not available.');
    }
} catch (error) {
    console.error('Error initializing Supabase client:', error);
    // Consider showing a user-facing error message here
}

// --- DOM Elements ---
const driverNameHeader = document.getElementById('driver-name-header'); // Assuming this exists
const currentMonthYearEl = document.getElementById('current-month-year'); // <-- Define this element
const pageMessage = document.getElementById('page-message'); // Assuming this exists for general messages
// --- Summary Card Elements (تأكد من وجود هذه المعرفات في HTML) ---
const summaryBaseSalaryEl = document.getElementById('summary-base-salary'); // <-- Define Base Salary Element
const summaryPreviousBalanceEl = document.getElementById('summary-previous-balance');
const summaryCommissionsEl = document.getElementById('summary-commissions'); // <-- عنصر ملخص العمولات
const summaryDeductionsEl = document.getElementById('summary-deductions');
const summaryTotalDueEl = document.getElementById('summary-total-due');
const summaryPaidEl = document.getElementById('summary-paid');
const summaryRemainingEl = document.getElementById('summary-remaining');
// --- End Summary Card Elements ---
let transactionsTableBody = document.getElementById('transactions-tbody');
let transactionsMessage = document.getElementById('transactions-message');
let commissionListEl = document.getElementById('commission-list');
let deductionListEl = document.getElementById('deduction-list');
const saveCurrentBalanceBtn = document.getElementById('save-current-balance-btn'); // <-- Add button element

// --- تسجيل التحقق من عناصر DOM ---
console.log("Checking DOM elements after definition:", {
    driverNameHeader: !!driverNameHeader, // <-- Add check
    currentMonthYearEl: !!currentMonthYearEl, // <-- Add check
    pageMessage: !!pageMessage, // <-- Add check
    // --- Add Summary Card Checks ---
    summaryBaseSalaryEl: !!summaryBaseSalaryEl, // <-- Check Base Salary Element
    summaryPreviousBalanceEl: !!summaryPreviousBalanceEl,
    summaryCommissionsEl: !!summaryCommissionsEl, // <-- تحقق من عنصر العمولات
    summaryDeductionsEl: !!summaryDeductionsEl,
    summaryTotalDueEl: !!summaryTotalDueEl,
    summaryPaidEl: !!summaryPaidEl,
    summaryRemainingEl: !!summaryRemainingEl,
    // --- End Summary Card Checks ---
    transactionsTableBody: !!transactionsTableBody,
    transactionsMessage: !!transactionsMessage,
    commissionListEl: !!commissionListEl,
    deductionListEl: !!deductionListEl,
    saveCurrentBalanceBtn: !!saveCurrentBalanceBtn, // <-- Add check
});
// --- نهاية تسجيل التحقق ---

// --- DOM Elements (Add Modal related elements) ---
const addCommissionBtn = document.getElementById('add-commission-btn'); // <-- تأكد من وجود هذا الزر في HTML
const addDeductionBtn = document.getElementById('add-deduction-btn');   // <-- تأكد من وجود هذا الزر في HTML

const commissionModal = document.getElementById('commission-modal');     // <-- تأكد من وجود هذه النافذة في HTML
const commissionForm = document.getElementById('commission-form');       // <-- تأكد من وجود هذا النموذج في HTML
const commissionMessage = document.getElementById('commission-message'); // <-- عنصر لعرض الرسائل داخل نافذة العمولة

const deductionModal = document.getElementById('deduction-modal');       // <-- تأكد من وجود هذه النافذة في HTML
const deductionForm = document.getElementById('deduction-form');         // <-- تأكد من وجود هذا النموذج في HTML
const deductionMessage = document.getElementById('deduction-message');   // <-- عنصر لعرض الرسائل داخل نافذة الخصم
const deductionModalTitle = document.getElementById('deduction-modal-title'); // Title element
const typeSelectionView = document.getElementById('type-selection-view');
const deductionExpenseTypeInput = document.getElementById('deduction-expense-type'); // Hidden input for type

// Form Fields (Ensure these IDs match your HTML)
const deductionDateInput = document.getElementById('deduction-date');
const specificTypeGroup = document.getElementById('specific-type-group');
const specificTypeLabel = document.getElementById('specific-type-label');
const specificTypeSelect = document.getElementById('deduction-specific-type');
const deductionAmountInput = document.getElementById('deduction-amount');
const vatSection = document.getElementById('vat-section');
const includeVatCheckbox = document.getElementById('include-vat-checkbox');
const vatDetailsDiv = document.getElementById('vat-details');
const vatAmountDisplay = document.getElementById('vat-amount-display');
const totalWithVatDisplay = document.getElementById('total-with-vat-display');
const paymentMethodSection = document.getElementById('payment-method-section');
const bankDetailsSection = document.getElementById('bank-details-section');
const bankSelect = document.getElementById('deduction-bank-select');
const bankPaidAmountGroup = document.getElementById('bank-paid-amount-group');
const bankPaidAmountInput = document.getElementById('deduction-bank-paid-amount');
const deductionNotesInput = document.getElementById('deduction-notes');
const backToTypeSelectBtn = deductionForm?.querySelector('.back-to-type-select'); // Back button inside form

// --- Payment Modal Elements ---
const addPaymentBtn = document.getElementById('add-payment-btn');
const paymentModal = document.getElementById('payment-modal');
const paymentForm = document.getElementById('payment-form');
const paymentMessage = document.getElementById('payment-message');
const paymentBankDetailsDiv = document.getElementById('payment-bank-details');
const paymentBankSelect = document.getElementById('payment-bank-id'); // Specific select for payment modal

// --- Edit Opening Balance Modal Elements ---
const editOpeningBalanceBtn = document.getElementById('edit-opening-balance-btn');
const editOpeningBalanceModal = document.getElementById('edit-opening-balance-modal');
const editOpeningBalanceForm = document.getElementById('edit-opening-balance-form');
const openingBalanceAmountInput = document.getElementById('opening-balance-amount');
// Note: The save button inside the modal has id="save-opening-balance-btn" in HTML
const editOpeningBalanceMessage = document.getElementById('edit-opening-balance-message');
const closeEditOpeningBalanceModalBtn = document.getElementById('close-edit-opening-balance-modal-btn');

const editPreviousBalanceBtn = document.getElementById('edit-previous-balance-btn');
const editPreviousBalanceModal = document.getElementById('edit-previous-balance-modal');
const closeEditPreviousBalanceModalBtn = document.getElementById('close-edit-previous-balance-modal');
const editPreviousBalanceForm = document.getElementById('edit-previous-balance-form');
const previousBalanceCurrentInput = document.getElementById('previous-balance-current');
const previousBalanceNewInput = document.getElementById('previous-balance-new');
const editPreviousBalanceMessage = document.getElementById('edit-previous-balance-message');
const cancelEditPreviousBalanceBtn = document.getElementById('cancel-edit-previous-balance');

// --- State ---
let currentDriverId = null;
let currentDriverName = null;
let currentDriverBaseSalary = 0; // <-- State for base salary (already exists)
let selectedMonthId = null;
let selectedMonthNumber = null;
let selectedYearNumber = null;
let bankList = [];
const VAT_RATE = 0.15;
let currentTransactions = [];
// Global variable to store summary data, including previous balance
let pageSummaryData = {
    commissions: 0,
    deductions: 0,
    paid: 0,
    previousBalance: 0,
    baseSalary: 0 // Will be populated from currentDriverBaseSalary
};

// --- Helper Functions ---
function showMessage(element, message, type = 'error') {
    if (!element) return;
    element.textContent = message;
    element.className = `message ${type} show`;
    element.style.display = 'block';
}

function formatCurrency(amount) {
    const value = parseFloat(amount);
    return isNaN(value) ? '0.00 ريال' : `${value.toFixed(2)} ريال`;
}

function translateExpenseType(type, isDeduction) {
    // ... (منطق ترجمة الأنواع كما كان موجوداً سابقاً) ...
    switch (type) {
        case 'advance': return 'سلفة';
        case 'fine': return 'مخالفة';
        case 'accident': return 'حادث';
        case 'commission': return 'عمولة';
        case 'payment': return 'دفعة راتب';
        // أضف أنواع أخرى حسب الحاجة
        default: return type || 'غير معروف';
    }
}
// --- نهاية الدوال المساعدة ---

function getMonthName(monthNumber) {
    const names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"];
    const index = parseInt(monthNumber, 10) - 1;
    return names[index] || `شهر ${monthNumber}`;
}

/**
 * فتح نافذة منبثقة محددة.
 * @param {HTMLElement} modalElement - عنصر النافذة المنبثقة.
 */
function openModal(modalElement) {
    if (modalElement) {
        // Reset deduction modal to type selection view
        if (modalElement.id === 'deduction-modal') {
            if (typeSelectionView) typeSelectionView.style.display = 'block';
            if (deductionForm) deductionForm.style.display = 'none';
            if (deductionModalTitle) deductionModalTitle.innerHTML = '<i class="fas fa-minus-circle"></i> إضافة خصم / سلفة'; // Reset title
            clearForm(deductionForm); // Clear form fields
            resetVatAndPayment(); // Reset VAT and payment sections
        } else if (modalElement.id === 'commission-modal') {
             clearForm(commissionForm);
        }
        // Add similar resets for other modals if needed

        modalElement.style.display = 'flex'; // Use flex for centering
    }
}

/**
 * إغلاق نافذة منبثقة محددة.
 * @param {HTMLElement} modalElement - عنصر النافذة المنبثقة.
 */
function closeModal(modalElement) {
    if (modalElement) {
        modalElement.style.display = 'none';
        const messageEl = modalElement.querySelector('.modal-message');
        if (messageEl) {
            messageEl.style.display = 'none';
            messageEl.textContent = '';
        }
        // Reset specific modal states if needed on close
        if (modalElement.id === 'deduction-modal') {
             resetVatAndPayment();
        }
    }
}

/**
 * مسح حقول نموذج محدد.
 * @param {HTMLFormElement} formElement - عنصر النموذج.
 */
function clearForm(formElement) {
    if (formElement) {
        formElement.reset();
        // Reset specific elements not handled by reset()
        if (includeVatCheckbox) includeVatCheckbox.checked = false;
        if (vatDetailsDiv) vatDetailsDiv.style.display = 'none';
        if (bankDetailsSection) bankDetailsSection.style.display = 'none';
        if (bankPaidAmountGroup) bankPaidAmountGroup.style.display = 'none';
        // Reset radio button to default (deduct)
        const deductRadio = formElement.querySelector('input[name="payment_method"][value="deduct"]');
        if (deductRadio) deductRadio.checked = true;
    }
}

/**
 * إعادة تعيين أقسام الضريبة والدفع في نموذج الخصم.
 */
function resetVatAndPayment() {
    if (includeVatCheckbox) includeVatCheckbox.checked = false;
    if (vatSection) vatSection.style.display = 'none';
    if (vatDetailsDiv) vatDetailsDiv.style.display = 'none';
    if (paymentMethodSection) paymentMethodSection.style.display = 'none';
    if (bankDetailsSection) bankDetailsSection.style.display = 'none';
    if (bankPaidAmountGroup) bankPaidAmountGroup.style.display = 'none';
    const deductRadio = deductionForm?.querySelector('input[name="payment_method"][value="deduct"]');
    if (deductRadio) deductRadio.checked = true;
}

/**
 * حساب ضريبة القيمة المضافة والمبلغ الإجمالي.
 */
function calculateVAT() {
    if (!deductionAmountInput || !includeVatCheckbox || !vatAmountDisplay || !totalWithVatDisplay || !vatDetailsDiv) return;

    const amount = parseFloat(deductionAmountInput.value) || 0;
    let vatAmount = 0;
    let totalWithVat = amount;

    if (includeVatCheckbox.checked && amount > 0) {
        vatAmount = amount * VAT_RATE;
        totalWithVat = amount + vatAmount;
        vatDetailsDiv.style.display = 'block';
    } else {
        vatDetailsDiv.style.display = 'none';
    }

    vatAmountDisplay.textContent = formatCurrency(vatAmount);
    totalWithVatDisplay.textContent = formatCurrency(totalWithVat);
}

/**
 * ملء قائمة البنوك المنسدلة (لكل النوافذ التي تحتاجها).
 * @param {Array} banks - مصفوفة بيانات البنوك.
 */
function populateBankSelect(banks) {
    // Populate deduction bank select
    const deductionBankSelect = document.getElementById('deduction-bank-select'); // Use the specific ID
    if (deductionBankSelect) {
        const defaultOption = deductionBankSelect.options[0];
        deductionBankSelect.innerHTML = '';
        if (defaultOption) deductionBankSelect.appendChild(defaultOption);
        if (banks && banks.length > 0) {
            banks.forEach(bank => {
                const option = document.createElement('option');
                option.value = bank.id;
                option.textContent = bank.name;
                deductionBankSelect.appendChild(option);
            });
        }
    } else {
        console.warn("Deduction bank select element not found.");
    }

    // Populate payment bank select
    if (paymentBankSelect) { // Use the specific variable for payment modal
        const defaultOption = paymentBankSelect.options[0];
        paymentBankSelect.innerHTML = '';
        if (defaultOption) paymentBankSelect.appendChild(defaultOption);
        if (banks && banks.length > 0) {
            banks.forEach(bank => {
                const option = document.createElement('option');
                option.value = bank.id;
                option.textContent = bank.name;
                paymentBankSelect.appendChild(option);
            });
        }
    } else {
        console.warn("Payment bank select element not found.");
    }
}

/**
 * جلب تفاصيل السائق (الاسم والراتب الأساسي الافتراضي) بناءً على ID.
 * @param {string} driverId - معرف السائق.
 */
async function fetchDriverDetails(driverId) {
    console.log(`Fetching details for driver: ${driverId}`);
    if (!_supabase || !driverId) {
        console.error('Supabase client not available or driverId missing for fetchDriverDetails.');
        if (driverNameHeader) driverNameHeader.textContent = 'خطأ في التحميل';
        if (summaryBaseSalaryEl) summaryBaseSalaryEl.textContent = 'خطأ'; // <-- Update salary element on error
        return;
    }
    if (!driverNameHeader) {
        console.warn('driverNameHeader element not found.');
    }
    if (!summaryBaseSalaryEl) { // <-- Check for salary element
        console.warn('summaryBaseSalaryEl element not found.'); // <-- Add warning if missing
    }

    try {
        // Fetch driver name and default settings in one go using a join
        const { data, error } = await _supabase
            .from('drivers')
            .select(`
                name,
                driver_default_settings ( default_base_salary )
            `)
            .eq('id', driverId)
            .single(); // Expecting one driver

        // --- Diagnostic Log ---
        console.log('Raw data received from fetchDriverDetails query:', JSON.stringify(data, null, 2));
        // --- End Diagnostic Log ---

        if (error) throw error;

        if (data) {
            currentDriverName = data.name;

            // --- Diagnostic Log for Settings ---
            console.log('Accessing driver_default_settings:', data.driver_default_settings);
            // --- End Diagnostic Log ---

            // --- Modified Salary Extraction Logic ---
            let salaryValue = 0;
            const settings = data.driver_default_settings; // Get the settings data

            if (settings) {
                // Check if settings is an array (one-to-many or possibly one-to-one returned as array)
                if (Array.isArray(settings) && settings.length > 0) {
                    salaryValue = parseFloat(settings[0].default_base_salary) || 0;
                    console.log('Extracted salary from array:', settings[0].default_base_salary);
                }
                // Check if settings is an object (likely one-to-one)
                else if (typeof settings === 'object' && !Array.isArray(settings) && settings.default_base_salary !== undefined) {
                    salaryValue = parseFloat(settings.default_base_salary) || 0;
                    console.log('Extracted salary from object:', settings.default_base_salary);
                } else {
                     console.log('driver_default_settings is present but not in expected format or empty.');
                }
            } else {
                console.log('driver_default_settings is null or undefined.');
            }

            currentDriverBaseSalary = salaryValue;
            // --- End Modified Salary Extraction Logic ---


            // --- Diagnostic Log for Salary Value ---
            console.log('Final currentDriverBaseSalary:', currentDriverBaseSalary);
            // --- End Diagnostic Log ---


            if (driverNameHeader) {
                driverNameHeader.textContent = currentDriverName || 'اسم غير معروف';
            }
            if (summaryBaseSalaryEl) { // <-- Update salary element
                summaryBaseSalaryEl.textContent = formatCurrency(currentDriverBaseSalary); // <-- Ensure update happens
            }
            console.log(`Driver name fetched: ${currentDriverName}, Base Salary: ${currentDriverBaseSalary}`);
        } else {
            if (driverNameHeader) driverNameHeader.textContent = 'السائق غير موجود';
            if (summaryBaseSalaryEl) summaryBaseSalaryEl.textContent = '---'; // <-- Update salary element
            console.warn(`Driver with ID ${driverId} not found.`);
        }
    } catch (error) {
        console.error('Error fetching driver details:', error);
        if (driverNameHeader) driverNameHeader.textContent = 'خطأ في تحميل الاسم';
        if (summaryBaseSalaryEl) summaryBaseSalaryEl.textContent = 'خطأ'; // <-- Update salary element on error
        if (typeof showMessage === 'function' && pageMessage) {
            showMessage(pageMessage, `خطأ في جلب تفاصيل السائق: ${error.message}`, 'error');
        }
    }
}

/**
 * جلب قائمة البنوك وملء القائمة المنسدلة.
 */
async function fetchBanks() {
    console.log("Fetching banks...");
    if (!_supabase) {
        console.error('Supabase client not available for fetchBanks.');
        return;
    }
    try {
        const { data, error } = await _supabase
            .from('banks')
            .select('id, name') // Changed bank_name to name
            .order('name'); // Changed bank_name to name

        if (error) throw error;

        bankList = data || [];
        console.log(`Fetched ${bankList.length} banks.`);
        populateBankSelect(bankList); // <-- ملء القائمة المنسدلة
    } catch (error) {
        console.error('Error fetching banks:', error);
        // Consider showing a message
        if (typeof showMessage === 'function' && pageMessage) {
            showMessage(pageMessage, `خطأ في جلب قائمة البنوك: ${error.message}`, 'error');
        }
    }
}

/**
 * جلب ملخص بيانات الشهر للسائق (الرصيد السابق).
 * حاليًا، يجلب الرصيد السابق من إعدادات السائق الافتراضية.
 * @param {string} driverId - معرف السائق.
 * @param {string} monthId - معرف شهر الميزانية (غير مستخدم حاليًا لجلب الرصيد السابق).
 * @returns {Promise<object>} - Object with { previousBalance: number }.
 */
async function fetchMonthlySummary(driverId, monthId) {
    console.log(`Fetching previous balance for driver: ${driverId}`); // Updated log message
    if (!_supabase || !driverId) {
        console.error('Supabase client or driverId missing for fetchMonthlySummary.');
        // Return zero balance on error
        return { commissions: 0, deductions: 0, paid: 0, previousBalance: 0 };
    }

    try {
        // Fetch the current_balance from driver_default_settings
        const { data: settingData, error: settingError } = await _supabase
            .from('driver_default_settings')
            .select('current_balance')
            .eq('driver_id', driverId)
            .single(); // Expecting one setting record per driver

        if (settingError) {
            // Handle case where driver might not have a settings record yet
            if (settingError.code === 'PGRST116') { // PGRST116 = No rows found
                console.warn(`No default settings found for driver ${driverId}. Assuming previous balance is 0.`);
                return { commissions: 0, deductions: 0, paid: 0, previousBalance: 0 };
            } else {
                // Throw other errors
                throw settingError;
            }
        }

        const previousBalance = parseFloat(settingData?.current_balance) || 0;
        console.log(`Previous balance fetched for driver ${driverId}: ${previousBalance}`);

        // Return only the previous balance for now. Other summaries are calculated from transactions.
        return { commissions: 0, deductions: 0, paid: 0, previousBalance: previousBalance };

    } catch (error) {
        console.error('Error fetching previous balance:', error);
        if (typeof showMessage === 'function' && pageMessage) {
            showMessage(pageMessage, `خطأ في جلب الرصيد السابق: ${error.message}`, 'error');
        }
        // Return zero balance on error
        return { commissions: 0, deductions: 0, paid: 0, previousBalance: 0 };
    }
}

/**
 * جلب وعرض الحركات المالية للسائق في شهر محدد.
 * @param {string} driverId - معرف السائق.
 * @param {string} monthId - معرف شهر الميزانية.
 */
async function fetchAndDisplayTransactions(driverId, monthId) {
    console.log("بدء fetchAndDisplayTransactions للسائق:", driverId, "الشهر:", monthId);

    // التحقق من وجود العناصر اللازمة
    if (!commissionListEl || !deductionListEl || !transactionsTableBody) {
        console.error("خطأ: عنصر قائمة العمولات أو الخصومات أو جدول الحركات غير موجود.");
        return;
    }

    // التحقق من المدخلات و Supabase
    if (!_supabase || !driverId || !monthId) {
        showMessage(transactionsMessage, 'لا يمكن تحميل الحركات (بيانات ناقصة أو خطأ في الاتصال).', 'error');
        commissionListEl.innerHTML = '<p class="list-placeholder error">لا يمكن تحميل العمولات.</p>';
        deductionListEl.innerHTML = '<p class="list-placeholder error">لا يمكن تحميل المصروفات الأخرى.</p>';
        transactionsTableBody.innerHTML = `<tr><td colspan="7" class="no-data-placeholder">لا يمكن تحميل الحركات.</td></tr>`; // افترض وجود 7 أعمدة
        return;
    }

    // إظهار حالة التحميل
    commissionListEl.innerHTML = '<p class="list-placeholder"><i class="fas fa-spinner fa-spin"></i> جاري تحميل العمولات...</p>';
    deductionListEl.innerHTML = '<p class="list-placeholder"><i class="fas fa-spinner fa-spin"></i> جاري تحميل المصروفات الأخرى...</p>';
    transactionsTableBody.innerHTML = `<tr><td colspan="7" class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i> جاري تحميل الحركات...</td></tr>`;
    showMessage(transactionsMessage, '', 'info'); // إخفاء الرسائل السابقة

    try {
        console.log("جاري جلب بيانات driver_expenses من Supabase...");
        const { data, error } = await _supabase
            .from('driver_expenses')
            .select(`
                id,
                expense_date,
                expense_type,
                total_amount,
                is_deduction,
                notes,
                bank_paid_amount,
                vat_amount,
                fine_or_accident_type,
                commission_type,
                created_at
            `)
            .eq('driver_id', driverId)
            .eq('budget_month_id', monthId)
            .order('expense_date', { ascending: false }) // الأحدث أولاً
            .order('created_at', { ascending: false }); // ترتيب ثانوي حسب وقت الإنشاء

        if (error) {
            console.error("خطأ أثناء جلب البيانات:", error);
            throw error; // إلقاء الخطأ للمعالجة في catch
        }

        console.log("تم جلب البيانات:", data ? data.length : 0, "سجلات.");
        currentTransactions = data || []; // تخزين البيانات المسترجعة

        // --- فلترة البيانات بناءً على expense_type ---
        const commissions = currentTransactions.filter(tx => tx.expense_type === 'commission');
        const otherExpenses = currentTransactions.filter(tx => tx.expense_type !== 'commission');
        // --- نهاية الفلترة ---

        console.log("عدد العمولات:", commissions.length);
        console.log("عدد المصروفات الأخرى:", otherExpenses.length);

        // استدعاء دوال العرض لكل قائمة وجدول
        renderCommissionsList(commissions); // عرض قائمة العمولات (العمود الأيمن)
        renderOtherExpensesList(otherExpenses); // عرض قائمة المصروفات الأخرى (العمود الأيسر)
        renderTransactionsTable(currentTransactions); // عرض الجدول الكامل

        // --- تحديث بطاقات الملخص بعد عرض البيانات ---
        // updateSummaryCards(currentTransactions); // <-- استدعاء تحديث الملخص هنا

    } catch (error) {
        console.error('فشل جلب وعرض الحركات:', error);
        showMessage(transactionsMessage, `خطأ في تحميل الحركات: ${error.message}`, 'error');
        commissionListEl.innerHTML = '<p class="list-placeholder error">خطأ في تحميل العمولات.</p>';
        deductionListEl.innerHTML = '<p class="list-placeholder error">خطأ في تحميل المصروفات الأخرى.</p>';
        transactionsTableBody.innerHTML = `<tr><td colspan="7" class="no-data-placeholder error">خطأ في تحميل الحركات.</td></tr>`;
    }
}

/**
 * عرض قائمة العمولات في العمود المخصص.
 * @param {Array} commissions - مصفوفة بيانات العمولات.
 */
function renderCommissionsList(commissions) {
    if (!commissionListEl) return;
    console.log("جاري عرض قائمة العمولات:", commissions.length, "عناصر.");
    commissionListEl.innerHTML = ''; // مسح المحتوى الحالي

    if (commissions.length === 0) {
        commissionListEl.innerHTML = '<p class="list-placeholder">لا توجد عمولات مسجلة لهذا الشهر.</p>';
        return;
    }

    commissions.forEach(tx => {
        const item = document.createElement('div');
        item.className = 'transaction-item commission-item'; // استخدام كلاس مناسب للتنسيق
        item.dataset.txId = tx.id;

        const description = tx.commission_type || tx.notes || 'عمولة';
        const amount = parseFloat(tx.total_amount) || 0;
        const formattedDate = tx.expense_date ? new Date(tx.expense_date).toLocaleDateString('ar-SA') : '---';

        // العمولات تعتبر إضافة موجبة
        const amountClass = 'positive';
        const amountPrefix = '+ ';

        item.innerHTML = `
            <div class="item-details">
                <span class="item-date">${formattedDate}</span>
                <span class="item-description">${description}</span>
            </div>
            <div class="item-amount ${amountClass}">
                ${amountPrefix}${formatCurrency(amount)}
            </div>
        `;
        commissionListEl.appendChild(item);
    });
    console.log("انتهاء عرض قائمة العمولات.");
}

/**
 * عرض قائمة المصروفات الأخرى (خصومات، سلف، دفعات...) في العمود المخصص.
 * @param {Array} otherExpenses - مصفوفة بيانات المصروفات الأخرى.
 */
function renderOtherExpensesList(otherExpenses) {
    // استخدام العنصر المخصص للخصومات لعرض المصروفات الأخرى
    const targetListEl = deductionListEl;
    if (!targetListEl) return;

    console.log("جاري عرض قائمة المصروفات الأخرى:", otherExpenses.length, "عناصر.");
    targetListEl.innerHTML = ''; // مسح المحتوى الحالي

    if (otherExpenses.length === 0) {
        targetListEl.innerHTML = '<p class="list-placeholder">لا توجد مصروفات أخرى (خصومات، سلف، دفعات...) مسجلة لهذا الشهر.</p>';
        return;
    }

    otherExpenses.forEach(tx => {
        const item = document.createElement('div');
        item.className = 'transaction-item other-expense-item'; // استخدام كلاس مناسب للتنسيق
        item.dataset.txId = tx.id;

        // --- تعديل بناء الوصف ---
        let descriptionParts = [];
        const typeText = translateExpenseType(tx.expense_type, tx.is_deduction);
        const specificType = tx.fine_or_accident_type || ''; // جلب نوع المخالفة/الحادث
        const notes = tx.notes || '';

        descriptionParts.push(typeText); // إضافة النوع الأساسي (سلفة، مخالفة..)

        // إضافة نوع المخالفة/الحادث إذا كان موجوداً
        if (specificType) {
            descriptionParts.push(`(${specificType})`);
        }

        // إضافة الملاحظات إذا كانت موجودة (مع اختصارها إذا لزم الأمر)
        if (notes) {
            const truncatedNotes = notes.length > 40 ? notes.substring(0, 40) + '...' : notes;
            descriptionParts.push(`- ${truncatedNotes}`);
        }

        const description = descriptionParts.join(' '); // دمج الأجزاء بمسافة
        // --- نهاية تعديل بناء الوصف ---


        const amount = parseFloat(tx.total_amount) || 0;
        const formattedDate = tx.expense_date ? new Date(tx.expense_date).toLocaleDateString('ar-SA') : '---';

        // تحديد إشارة المبلغ (سالب للخصومات والمصروفات، موجب للدفعات إذا أردت)
        let amountClass = 'negative'; // الافتراضي سالب للمصروفات/الخصومات
        let amountPrefix = '- ';
        if (tx.expense_type === 'payment' && !tx.is_deduction) {
            // الدفعات للسائق تقلل المستحق، نعرضها كقيمة سالبة هنا
             amountClass = 'negative'; // أو 'positive' إذا أردت إظهارها كتدفق نقدي موجب للسائق
             amountPrefix = '- '; // أو '+ '
        } else if (!tx.is_deduction && tx.expense_type !== 'payment') {
            // مصروفات غير مخصومة (مثل مخالفة مدفوعة بالكامل من البنك)
             amountClass = 'negative'; // نعاملها كسالب لتبسيط العرض
             amountPrefix = '- ';
        }

        item.innerHTML = `
            <div class="item-details">
                <span class="item-date">${formattedDate}</span>
                <span class="item-description">${description}</span>
            </div>
            <div class="item-amount ${amountClass}">
                ${amountPrefix}${formatCurrency(amount)}
            </div>
        `;
        targetListEl.appendChild(item);
    });
    console.log("انتهاء عرض قائمة المصروفات الأخرى.");
}

/**
 * عرض جدول الحركات المالية الكامل.
 * @param {Array} transactions - مصفوفة بجميع الحركات للشهر.
 */
function renderTransactionsTable(transactions) {
    if (!transactionsTableBody) return;
    console.log("جاري عرض جدول الحركات:", transactions.length, "عناصر.");
    transactionsTableBody.innerHTML = ''; // مسح المحتوى الحالي

    if (transactions.length === 0) {
        transactionsTableBody.innerHTML = `<tr><td colspan="8" class="no-data-placeholder">لا توجد حركات مالية لهذا الشهر.</td></tr>`; // Updated colspan
        return;
    }

    transactions.forEach(tx => {
        const row = document.createElement('tr');
        row.dataset.txId = tx.id;

        const displayType = translateExpenseType(tx.expense_type, tx.is_deduction);
        const description = tx.notes || tx.fine_or_accident_type || tx.commission_type || '---';
        const totalAmount = parseFloat(tx.total_amount) || 0;
        const bankPaid = parseFloat(tx.bank_paid_amount) || 0;
        const vat = parseFloat(tx.vat_amount) || 0;

        let salaryImpactAmount = 0;
        let salaryImpactClass = 'amount-neutral';
        let salaryImpactText = '---'; // Default to ---

        // تحديد تأثير الحركة على الراتب
        if (tx.expense_type === 'commission' && !tx.is_deduction) {
            salaryImpactAmount = totalAmount;
            salaryImpactClass = 'amount-addition'; // إضافة للراتب
            salaryImpactText = `+ ${formatCurrency(salaryImpactAmount).replace(' ريال', '')}`;
        } else if (tx.expense_type === 'payment' && !tx.is_deduction) {
            // Payments reduce the amount due but aren't direct salary deductions/additions in the same way
            // We show them in the 'Bank Paid' column if applicable, or just as a transaction.
            // Let's keep salary impact neutral for payments.
            salaryImpactClass = 'amount-neutral';
            salaryImpactText = '---';
        } else if (tx.is_deduction) {
            // This covers advances, fines/accidents marked as deductions, or mixed payments
            salaryImpactAmount = totalAmount; // total_amount here represents the portion deducted from salary
            salaryImpactClass = 'amount-deduction'; // خصم مباشر من الراتب
            salaryImpactText = `- ${formatCurrency(salaryImpactAmount).replace(' ريال', '')}`;
        }

        // تحديد المبلغ الإجمالي للعرض (يمثل التكلفة الكلية للحركة)
        let displayTotalAmount = totalAmount;
        if (tx.is_deduction && bankPaid > 0) { // خصم دفع مختلط
            displayTotalAmount = totalAmount + bankPaid; // عرض التكلفة الأصلية (المخصوم + المدفوع بنكياً)
        } else if (!tx.is_deduction && bankPaid > 0 && tx.expense_type !== 'payment') { // مصروف مدفوع بالكامل بنكياً (مثل مخالفة)
             displayTotalAmount = bankPaid; // عرض المبلغ المدفوع بنكياً لأنه يمثل التكلفة
        } else if (tx.expense_type === 'payment') {
            displayTotalAmount = totalAmount; // For payments, total_amount is the payment amount
        }
        // For commissions and simple deductions, totalAmount is already correct

        row.innerHTML = `
            <td>${tx.expense_date || '---'}</td>
            <td>${displayType}</td>
            <td>${description}</td>
            <td>${formatCurrency(displayTotalAmount)}</td>
            <td>${vat > 0 ? formatCurrency(vat) : '---'}</td>
            <td>${bankPaid > 0 ? formatCurrency(bankPaid) : '---'}</td>
            <td class="${salaryImpactClass}">${salaryImpactText}</td>
            <td>
                <button class="edit-btn" data-tx-id="${tx.id}" title="تعديل الحركة">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="delete-btn" data-tx-id="${tx.id}" title="حذف الحركة">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </td>
        `;
        transactionsTableBody.appendChild(row);
    });
     console.log("انتهاء عرض جدول الحركات.");
}

// --- يجب استدعاء fetchAndDisplayTransactions عند تحميل الصفحة أو تغيير الشهر ---
// مثال داخل دالة تحميل الصفحة الرئيسية (مثل loadPageData)
// async function loadPageData() {
//     // ... (جلب driverId و monthId) ...
//     if (currentDriverId && selectedMonthId) {
//         await fetchAndDisplayTransactions(currentDriverId, selectedMonthId);
//     }
//     // ... (بقية منطق تحميل الصفحة) ...
// }

// --- تأكد من استدعاء loadPageData عند تحميل الصفحة ---
// document.addEventListener('DOMContentLoaded', loadPageData);

async function loadPageData() {
    console.log("Starting loadPageData..."); // <-- تسجيل بداية التحميل

    // Get driver ID from URL first
    const urlParams = new URLSearchParams(window.location.search);
    let driverIdFromUrl = urlParams.get('driver_id');

    if (driverIdFromUrl) {
        currentDriverId = driverIdFromUrl;
    } else {
        // Fallback to sessionStorage if not found in URL
        currentDriverId = sessionStorage.getItem('selectedDriverId');
        console.log("Driver ID not found in URL, attempting to retrieve from sessionStorage:", currentDriverId);
    }

    // Get selected month/year context from sessionStorage
    selectedMonthId = sessionStorage.getItem('selectedBudgetMonthId');
    selectedMonthNumber = sessionStorage.getItem('selectedBudgetMonthNumber');
    selectedYearNumber = sessionStorage.getItem('selectedBudgetYearNumber');

    // --- تسجيل IDs المسترجعة ---
    console.log("Retrieved IDs:", {
        currentDriverId: currentDriverId,
        selectedMonthId: selectedMonthId,
        selectedMonthNumber: selectedMonthNumber,
        selectedYearNumber: selectedYearNumber
    });
    // --- نهاية تسجيل IDs ---

    if (currentMonthYearEl && selectedMonthNumber && selectedYearNumber) {
        currentMonthYearEl.textContent = `${getMonthName(selectedMonthNumber)} ${selectedYearNumber}`;
    } else if (currentMonthYearEl) {
         currentMonthYearEl.textContent = 'الشهر غير محدد';
    }

    if (!currentDriverId) {
        console.error('Driver ID not found in URL.');
        showMessage(pageMessage, 'خطأ: لم يتم تحديد السائق في الرابط.', 'error');
        if (driverNameHeader) driverNameHeader.textContent = 'خطأ';
        return; // إيقاف التحميل
    }

    if (!selectedMonthId) {
        console.warn('Month ID not found in sessionStorage. Monthly summary and transactions might be unavailable.');
        // يمكنك عرض رسالة للمستخدم هنا أو مسح القوائم
        if (commissionListEl) commissionListEl.innerHTML = '<p class="list-placeholder">الرجاء تحديد شهر لعرض العمولات.</p>';
        if (deductionListEl) deductionListEl.innerHTML = '<p class="list-placeholder">الرجاء تحديد شهر لعرض المصروفات الأخرى.</p>';
        if (transactionsTableBody) transactionsTableBody.innerHTML = `<tr><td colspan="7" class="no-data-placeholder">الرجاء تحديد شهر لعرض الحركات.</td></tr>`;
        // لا نوقف التحميل بالكامل، قد نحتاج لجلب بيانات السائق الأساسية
    }

    // Show loading states
    // ... (إظهار حالات التحميل للعناصر المختلفة) ...

    // Fetch base details and banks first
    await Promise.all([
        fetchDriverDetails(currentDriverId),
        fetchBanks()
    ]);

    // Fetch monthly summary and transactions ONLY if month is selected
    let monthlyData = { commissions: 0, deductions: 0, paid: 0, previousBalance: 0 };
    if (selectedMonthId) {
        console.log("Month ID found. Fetching monthly data and transactions..."); // <-- تسجيل قبل الجلب
        // --- تسجيل IDs قبل الاستدعاء مباشرة ---
        console.log(`Calling fetchAndDisplayTransactions with driverId: ${currentDriverId}, monthId: ${selectedMonthId}`);
        // --- نهاية التسجيل ---
        monthlyData = await fetchMonthlySummary(currentDriverId, selectedMonthId);
        await fetchAndDisplayTransactions(currentDriverId, selectedMonthId); // استدعاء الدالة الرئيسية

        // --- تحديث بطاقات الملخص بعد جلب كل شيء ---
        updateSummaryCards(currentTransactions, monthlyData.previousBalance, currentDriverBaseSalary); // <-- استدعاء التحديث هنا

    } else {
        // Fetch only previous balance if month isn't selected
        // ... (منطق جلب الرصيد السابق فقط) ...
        // --- مسح/إعادة تعيين بطاقات الملخص إذا لم يتم تحديد شهر ---
        updateSummaryCards([], 0, currentDriverBaseSalary); // استدعاء بقيم صفرية
    }

    // Update Summary Cards
    // ... (تحديث بطاقات الملخص) ...
    console.log("Finished loadPageData."); // <-- تسجيل نهاية التحميل
}

// --- Event Listeners ---
function setupEventListeners() {
    console.log("Setting up event listeners..."); // Placeholder
    // Add actual event listeners here later
    // e.g., for modal buttons, back buttons, etc.

    // Example: Back button listener
    const backBtn = document.getElementById('back-to-drivers-list-btn');
    if (backBtn) {
        backBtn.addEventListener('click', () => {
            // Navigate back to the drivers management page
            window.location.href = '../drivers_management/drivers_management.html';
        });
    }

    // --- Add Commission Button Listener ---
    if (addCommissionBtn) {
        addCommissionBtn.addEventListener('click', () => {
            console.log("Add Commission button clicked");
            if (commissionModal) {
                resetCommissionModalForAdd(); // Reset modal for adding new commission
                openModal(commissionModal);
            } else {
                console.error("Commission modal not found!");
                alert("خطأ: نافذة إضافة العمولة غير موجودة.");
            }
        });
    } else {
        console.warn("Add Commission button not found.");
    }

    // --- Add Deduction/Advance Button Listener ---
    if (addDeductionBtn) {
        addDeductionBtn.addEventListener('click', () => {
            console.log("Add Deduction/Advance button clicked");
            if (deductionModal) {
                resetDeductionModalForAdd(); // Reset modal for adding new deduction
                openModal(deductionModal); // Will show type selection view
            } else {
                console.error("Deduction modal not found!");
                alert("خطأ: نافذة إضافة الخصم/السلفة غير موجودة.");
            }
        });
    } else {
        console.warn("Add Deduction/Advance button not found.");
    }

    // --- Add Payment Button Listener ---
    if (addPaymentBtn) {
        addPaymentBtn.addEventListener('click', () => {
            console.log("Add Payment button clicked");
            if (paymentModal) {
                resetPaymentModalForAdd(); // Reset modal for adding new payment
                if (paymentBankDetailsDiv) paymentBankDetailsDiv.style.display = 'none'; // Hide bank details initially
                const cashRadio = paymentForm?.querySelector('input[name="payment_method"][value="cash"]');
                if (cashRadio) cashRadio.checked = true; // Default to cash
                openModal(paymentModal);
            } else {
                console.error("Payment modal not found!");
                alert("خطأ: نافذة إضافة الدفعة غير موجودة.");
            }
        });
    } else {
        console.warn("Add Payment button not found.");
    }

    // --- Modal Close Button Listeners ---
    // ابحث عن جميع أزرار الإغلاق داخل النوافذ وأضف مستمعات
    document.querySelectorAll('.modal .close-btn').forEach(btn => {
        btn.addEventListener('click', (event) => {
            const modal = event.target.closest('.modal');
            if (modal) {
                closeModal(modal);
            }
        });
    });

    // --- Close Modal on Background Click ---
    window.addEventListener('click', (event) => {
        if (event.target.classList.contains('modal')) {
            closeModal(event.target);
        }
    });

    // --- Deduction Modal: Type Selection Button Listeners ---
    document.querySelectorAll('.type-select-btn').forEach(button => {
        button.addEventListener('click', (event) => {
            const formType = event.currentTarget.dataset.form; // 'advance', 'fine', 'accident'
            if (!formType || !typeSelectionView || !deductionForm || !deductionExpenseTypeInput || !deductionModalTitle) return;

            console.log(`Selected deduction type: ${formType}`);
            deductionExpenseTypeInput.value = formType; // Store the type

            // Hide type selection, show form
            typeSelectionView.style.display = 'none';
            deductionForm.style.display = 'block';

            // Configure form based on type
            resetVatAndPayment(); // Reset sections first
            if (formType === 'advance') {
                deductionModalTitle.innerHTML = '<i class="fas fa-hand-holding-usd"></i> تسجيل سلفة';
                if (specificTypeGroup) specificTypeGroup.style.display = 'none';
                if (vatSection) vatSection.style.display = 'none';
                if (paymentMethodSection) paymentMethodSection.style.display = 'none';
                if (bankDetailsSection) bankDetailsSection.style.display = 'none';
            } else if (formType === 'fine' || formType === 'accident') {
                const typeLabel = formType === 'fine' ? 'المخالفة' : 'الحادث';
                deductionModalTitle.innerHTML = `<i class="${formType === 'fine' ? 'fas fa-receipt' : 'fas fa-car-crash'}"></i> تسجيل ${typeLabel}`;
                if (specificTypeGroup) specificTypeGroup.style.display = 'block';
                if (specificTypeLabel) specificTypeLabel.textContent = typeLabel;
                // TODO: Populate specificTypeSelect based on formType if needed
                if (vatSection) vatSection.style.display = 'block'; // Show VAT option
                if (paymentMethodSection) paymentMethodSection.style.display = 'block'; // Show payment method
                // Bank details shown based on radio selection later
            }
        });
    });

    // --- Deduction Modal: Back Button Listener ---
    if (backToTypeSelectBtn) {
        backToTypeSelectBtn.addEventListener('click', () => {
            if (typeSelectionView) typeSelectionView.style.display = 'block';
            if (deductionForm) deductionForm.style.display = 'none';
            if (deductionModalTitle) deductionModalTitle.innerHTML = '<i class="fas fa-minus-circle"></i> إضافة خصم / سلفة'; // Reset title
            clearForm(deductionForm);
            resetVatAndPayment();
            if (deductionMessage) deductionMessage.style.display = 'none'; // Hide messages
        });
    }

    // --- Deduction Modal: VAT Calculation Listeners ---
    if (deductionAmountInput) {
        deductionAmountInput.addEventListener('input', calculateVAT);
    }
    if (includeVatCheckbox) {
        includeVatCheckbox.addEventListener('change', calculateVAT);
    }

    // --- Deduction Modal: Payment Method Listener ---
    deductionForm?.querySelectorAll('input[name="payment_method"]').forEach(radio => {
        radio.addEventListener('change', (event) => {
            const paymentMethod = event.target.value;
            if (paymentMethod === 'bank' || paymentMethod === 'mixed') {
                if (bankDetailsSection) bankDetailsSection.style.display = 'block';
                if (bankPaidAmountGroup) {
                    bankPaidAmountGroup.style.display = paymentMethod === 'mixed' ? 'block' : 'none';
                }
            } else { // 'deduct'
                if (bankDetailsSection) bankDetailsSection.style.display = 'none';
            }
        });
    });

    // Payment method listener removed since only bank payment is available now

    // --- Sidebar Toggle Listener ---
    const sidebarToggleBtn = document.getElementById('sidebar-toggle-btn');
    if (sidebarToggleBtn) {
        sidebarToggleBtn.addEventListener('click', () => {
            if (typeof toggleSidebar === 'function') {
                toggleSidebar();
            }
        });
    }

    // --- Form Submission Listeners ---
    if (commissionForm) {
        commissionForm.addEventListener('submit', handleCommissionSubmit);
    }
    if (deductionForm) {
        deductionForm.addEventListener('submit', handleDeductionSubmit); // Use the updated handler
    }
    if (paymentForm) { // <-- Add listener for payment form
        paymentForm.addEventListener('submit', handlePaymentSubmit);
    }

    // --- Save Current Balance Button Listener ---
    if (saveCurrentBalanceBtn) {
        saveCurrentBalanceBtn.addEventListener('click', saveCurrentBalanceSnapshot); // <-- Add listener
    } else {
        console.warn("Save Current Balance button not found.");
    }

    // --- Add listener for edit and delete buttons (delegated to table body) ---
    if (transactionsTableBody) {
        transactionsTableBody.addEventListener('click', (event) => {
            if (event.target.closest('.delete-btn')) {
                const button = event.target.closest('.delete-btn');
                const transactionId = button.dataset.txId;
                if (transactionId) {
                    handleDeleteTransaction(transactionId);
                }
            } else if (event.target.closest('.edit-btn')) {
                const button = event.target.closest('.edit-btn');
                const transactionId = button.dataset.txId;
                if (transactionId) {
                    handleEditTransaction(transactionId);
                }
            }
        });
    } else {
        console.warn("Transactions table body not found for edit/delete listener setup.");
    }

    // ... (Add other listeners as needed) ...
}

// --- Form Submission Handlers ---

/**
 * معالجة إرسال نموذج إضافة العمولة.
 * @param {Event} event - كائن الحدث.
 */
async function handleCommissionSubmit(event) {
    event.preventDefault(); // منع الإرسال الافتراضي
    console.log("Handling commission form submission...");

    if (!commissionForm || !_supabase || !currentDriverId || !selectedMonthId) {
        showMessage(commissionMessage, 'خطأ: لا يمكن إرسال النموذج.', 'error');
        return;
    }

    const submitButton = commissionForm.querySelector('button[type="submit"]');
    const originalButtonText = submitButton.innerHTML;
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    showMessage(commissionMessage, '', 'info'); // Clear previous messages

    // --- جمع البيانات من النموذج (عدّل المعرفات حسب الحاجة) ---
    const amount = commissionForm.querySelector('#commission-amount')?.value;
    const date = commissionForm.querySelector('#commission-date')?.value;
    const commissionType = commissionForm.querySelector('#commission-type')?.value; // نوع العمولة
    const notes = commissionForm.querySelector('#commission-notes')?.value;
    const editingId = commissionForm.dataset.editingId; // Check if we're editing
    // --- نهاية جمع البيانات ---

    // --- التحقق من الصحة ---
    if (!amount || parseFloat(amount) <= 0 || !date) {
        showMessage(commissionMessage, 'الرجاء إدخال مبلغ صحيح وتاريخ للعمولة.', 'error');
        submitButton.disabled = false;
        submitButton.innerHTML = originalButtonText;
        return;
    }
    // --- نهاية التحقق ---

    const commissionData = {
        driver_id: currentDriverId,
        budget_month_id: selectedMonthId,
        expense_type: 'commission',
        total_amount: parseFloat(amount),
        expense_date: date,
        commission_type: commissionType || null, // استخدم القيمة أو null
        notes: notes || null,
        is_deduction: false, // العمولات ليست خصومات
        bank_paid_amount: 0, // العمولات لا تدفع من البنك مباشرة هنا
        vat_amount: 0
    };

    console.log("Commission data to save:", commissionData);

    try {
        let result;
        if (editingId) {
            // تحديث عمولة موجودة
            const { data: updatedCommission, error } = await _supabase
                .from('driver_expenses')
                .update(commissionData)
                .eq('id', editingId)
                .select('id')
                .single();

            if (error) throw error;
            result = updatedCommission;
            console.log(`Commission updated successfully with ID: ${editingId}.`);
            showMessage(commissionMessage, 'تم تحديث العمولة بنجاح!', 'success');
        } else {
            // إضافة عمولة جديدة
            const { data: insertedCommission, error } = await _supabase
                .from('driver_expenses')
                .insert(commissionData)
                .select('id')
                .single();

            if (error) throw error;
            result = insertedCommission;
            console.log(`Commission added successfully with ID: ${result.id}.`);
            showMessage(commissionMessage, 'تمت إضافة العمولة بنجاح!', 'success');
        }

        // --- تحديث البيانات والملخص بعد النجاح ---
        await fetchAndDisplayTransactions(currentDriverId, selectedMonthId); // إعادة جلب وعرض الحركات
        const previousBalance = (await fetchMonthlySummary(currentDriverId, selectedMonthId)).previousBalance || 0;
        updateSummaryCards(currentTransactions, previousBalance, currentDriverBaseSalary); // <-- تحديث بطاقات الملخص

        // إغلاق النافذة بعد فترة قصيرة وتحديث البيانات
        setTimeout(() => {
            closeModal(commissionModal);
            // Clear editing state
            delete commissionForm.dataset.editingId;
            // Reset modal title and button text
            const modalTitle = commissionModal.querySelector('h2');
            if (modalTitle) modalTitle.innerHTML = '<i class="fas fa-plus"></i> إضافة عمولة';
            const submitBtn = commissionForm.querySelector('button[type="submit"]');
            if (submitBtn) submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ العمولة';
        }, 1500);

    } catch (error) {
        console.error('Error saving commission:', error);
        showMessage(commissionMessage, `خطأ في حفظ العمولة: ${error.message}`, 'error');
    } finally {
        submitButton.disabled = false;
        submitButton.innerHTML = originalButtonText;
    }
}

/**
 * معالجة إرسال نموذج إضافة الخصم/السلفة/المخالفة/الحادث.
 * @param {Event} event - كائن الحدث.
 */
async function handleDeductionSubmit(event) {
    event.preventDefault();
    console.log("Handling deduction/advance form submission...");

    if (!deductionForm || !_supabase || !currentDriverId || !selectedMonthId || !deductionExpenseTypeInput) {
        showMessage(deductionMessage, 'خطأ: لا يمكن إرسال النموذج (بيانات أساسية مفقودة).', 'error');
        return;
    }

    const expenseType = deductionExpenseTypeInput.value; // 'advance', 'fine', 'accident'
    if (!expenseType) {
        showMessage(deductionMessage, 'خطأ: نوع الحركة غير محدد.', 'error');
        return; // Should not happen if UI logic is correct
    }

    const submitButton = deductionForm.querySelector('button[type="submit"]');
    const originalButtonText = submitButton.innerHTML;
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    showMessage(deductionMessage, '', 'info');

    // --- جمع البيانات الأساسية ---
    const date = deductionDateInput?.value;
    const amountStr = deductionAmountInput?.value;
    const notes = deductionNotesInput?.value;
    const editingId = deductionForm.dataset.editingId; // Check if we're editing
    // --- نهاية جمع البيانات الأساسية ---

    // --- التحقق الأساسي ---
    if (!date || !amountStr || parseFloat(amountStr) <= 0) {
        showMessage(deductionMessage, 'الرجاء إدخال تاريخ ومبلغ صحيح للحركة.', 'error');
        submitButton.disabled = false;
        submitButton.innerHTML = originalButtonText;
        return;
    }
    const baseAmount = parseFloat(amountStr);
    // --- نهاية التحقق الأساسي ---

    // --- إعداد بيانات الإدخال ---
    let expenseData = {
        driver_id: currentDriverId,
        budget_month_id: selectedMonthId,
        expense_type: expenseType,
        expense_date: date,
        notes: notes || null,
        // القيم التالية سيتم تحديدها بناءً على النوع وطريقة الدفع
        total_amount: 0, // المبلغ الذي سيخصم من الراتب
        is_deduction: false,
        vat_amount: 0,
        bank_paid_amount: 0,
        bank_id: null,
        fine_or_accident_type: null,
        commission_type: null // Not applicable here
    };

    // --- منطق خاص بكل نوع ---
    if (expenseType === 'advance') {
        expenseData.total_amount = baseAmount;
        expenseData.is_deduction = true; // السلف تخصم دائمًا
    } else if (expenseType === 'fine' || expenseType === 'accident') {
        expenseData.fine_or_accident_type = specificTypeSelect?.value || null;

        // حساب الضريبة والإجمالي
        let vatAmount = 0;
        let totalWithVat = baseAmount;
        if (includeVatCheckbox?.checked && baseAmount > 0) {
            vatAmount = baseAmount * VAT_RATE;
            totalWithVat = baseAmount + vatAmount;
        }
        expenseData.vat_amount = vatAmount;

        // تحديد طريقة الدفع
        const paymentMethod = deductionForm.querySelector('input[name="payment_method"]:checked')?.value;

        if (paymentMethod === 'deduct') {
            expenseData.total_amount = totalWithVat; // المبلغ الكامل يخصم
            expenseData.is_deduction = true;
        } else if (paymentMethod === 'bank') {
            expenseData.total_amount = 0; // لا شيء يخصم من الراتب
            expenseData.is_deduction = false;
            expenseData.bank_paid_amount = totalWithVat; // المبلغ الكامل يدفع من البنك
            expenseData.bank_id = bankSelect?.value || null;
            if (!expenseData.bank_id) {
                 showMessage(deductionMessage, 'الرجاء اختيار البنك للدفع البنكي.', 'error');
                 submitButton.disabled = false;
                 submitButton.innerHTML = originalButtonText;
                 return;
            }
        } else if (paymentMethod === 'mixed') {
            const bankPaidStr = bankPaidAmountInput?.value;
            if (!bankPaidStr || parseFloat(bankPaidStr) <= 0) {
                 showMessage(deductionMessage, 'الرجاء إدخال مبلغ صحيح للدفع من البنك.', 'error');
                 submitButton.disabled = false;
                 submitButton.innerHTML = originalButtonText;
                 return;
            }
            const bankPaid = parseFloat(bankPaidStr);
            if (bankPaid >= totalWithVat) {
                 showMessage(deductionMessage, 'المبلغ المدفوع من البنك يجب أن يكون أقل من الإجمالي شامل الضريبة للدفع المختلط.', 'error');
                 submitButton.disabled = false;
                 submitButton.innerHTML = originalButtonText;
                 return;
            }

            expenseData.total_amount = totalWithVat - bankPaid; // المبلغ المتبقي يخصم
            expenseData.is_deduction = true;
            expenseData.bank_paid_amount = bankPaid;
            expenseData.bank_id = bankSelect?.value || null;
             if (!expenseData.bank_id) {
                 showMessage(deductionMessage, 'الرجاء اختيار البنك للدفع المختلط.', 'error');
                 submitButton.disabled = false;
                 submitButton.innerHTML = originalButtonText;
                 return;
            }
        } else {
             showMessage(deductionMessage, 'الرجاء اختيار طريقة الدفع/الخصم.', 'error');
             submitButton.disabled = false;
             submitButton.innerHTML = originalButtonText;
             return;
        }
    }
    // --- نهاية منطق النوع ---

    console.log("Data to save:", expenseData);

    // --- الإرسال إلى Supabase ---
    try {
        let result;
        if (editingId) {
            // تحديث معاملة موجودة
            const { data: updatedExpense, error } = await _supabase
                .from('driver_expenses')
                .update(expenseData)
                .eq('id', editingId)
                .select('id')
                .single();

            if (error) throw error;
            result = updatedExpense;
            console.log(`Deduction/Advance/Expense updated successfully with ID: ${editingId}.`);
            showMessage(deductionMessage, 'تم تحديث الحركة بنجاح!', 'success');
        } else {
            // إضافة معاملة جديدة
            const { data: insertedExpense, error } = await _supabase
                .from('driver_expenses')
                .insert(expenseData)
                .select('id')
                .single();

            if (error) throw error;
            result = insertedExpense;
            console.log(`Deduction/Advance/Expense added successfully with ID: ${result.id}.`);
            showMessage(deductionMessage, 'تمت إضافة الحركة بنجاح!', 'success');
        }

        // ملاحظة: المعاملات البنكية يتم إنشاؤها تلقائياً بواسطة database trigger

        // تحديث البيانات والملخص
        await fetchAndDisplayTransactions(currentDriverId, selectedMonthId);
        const previousBalance = (await fetchMonthlySummary(currentDriverId, selectedMonthId)).previousBalance || 0;
        updateSummaryCards(currentTransactions, previousBalance, currentDriverBaseSalary);

        // إغلاق النافذة
        setTimeout(() => {
            closeModal(deductionModal);
            // Clear editing state
            delete deductionForm.dataset.editingId;
            // Reset modal title and button text
            const modalTitle = deductionModal.querySelector('h2');
            if (modalTitle) modalTitle.innerHTML = '<i class="fas fa-plus"></i> إضافة خصم/سلفة/مخالفة/حادث';
            const submitBtn = deductionForm.querySelector('button[type="submit"]');
            if (submitBtn) submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ الحركة';
        }, 1500);

    } catch (error) {
        console.error('Error saving deduction/advance/expense:', error);
        showMessage(deductionMessage, `خطأ في حفظ الحركة: ${error.message}`, 'error');
    } finally {
        submitButton.disabled = false;
        submitButton.innerHTML = originalButtonText;
    }
}

/**
 * معالجة إرسال نموذج إضافة دفعة للسائق.
 * @param {Event} event - كائن الحدث.
 */
async function handlePaymentSubmit(event) {
    event.preventDefault();
    console.log("Handling payment form submission...");

    if (!paymentForm || !_supabase || !currentDriverId || !selectedMonthId) {
        showMessage(paymentMessage, 'خطأ: لا يمكن إرسال النموذج (بيانات أساسية مفقودة).', 'error');
        return;
    }

    const submitButton = paymentForm.querySelector('button[type="submit"]');
    const originalButtonText = submitButton.innerHTML;
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    showMessage(paymentMessage, '', 'info'); // Clear previous messages

    // --- جمع البيانات من النموذج ---
    const amountStr = paymentForm.querySelector('#payment-amount')?.value;
    const date = paymentForm.querySelector('#payment-date')?.value;
    const bankId = paymentForm.querySelector('#payment-bank-id')?.value;
    const notes = paymentForm.querySelector('#payment-notes')?.value;
    const editingId = paymentForm.dataset.editingId; // Check if we're editing
    // --- نهاية جمع البيانات ---

    // --- التحقق من الصحة ---
    if (!amountStr || parseFloat(amountStr) <= 0 || !date || !bankId) {
        showMessage(paymentMessage, 'الرجاء إدخال مبلغ صحيح وتاريخ واختيار البنك.', 'error');
        submitButton.disabled = false;
        submitButton.innerHTML = originalButtonText;
        return;
    }
    // --- نهاية التحقق ---

    const paymentData = {
        driver_id: currentDriverId,
        budget_month_id: selectedMonthId,
        expense_type: 'payment', // النوع 'payment'
        total_amount: parseFloat(amountStr), // مبلغ الدفعة
        expense_date: date,
        is_deduction: false, // الدفعات ليست خصومات مباشرة
        notes: notes || null,
        bank_id: bankId, // معرف البنك (مطلوب دائماً الآن)
        bank_paid_amount: parseFloat(amountStr), // المبلغ يُرسل دائماً إلى bank_paid_amount
        vat_amount: 0, // لا ضريبة على الدفعات
        fine_or_accident_type: null,
        commission_type: null
    };

    console.log("Payment data to save:", paymentData);

    try {
        let result;
        if (editingId) {
            // تحديث معاملة موجودة
            const { data: updatedPayment, error } = await _supabase
                .from('driver_expenses')
                .update(paymentData)
                .eq('id', editingId)
                .select('id')
                .single();

            if (error) throw error;
            result = updatedPayment;
            console.log(`Payment updated successfully with ID: ${editingId}.`);
            showMessage(paymentMessage, 'تم تحديث الدفعة بنجاح!', 'success');
        } else {
            // إضافة معاملة جديدة
            const { data: insertedPayment, error } = await _supabase
                .from('driver_expenses')
                .insert(paymentData)
                .select('id')
                .single();

            if (error) throw error;
            result = insertedPayment;
            console.log(`Payment added successfully with ID: ${result.id}.`);
            showMessage(paymentMessage, 'تمت إضافة الدفعة بنجاح!', 'success');
        }

        // ملاحظة: المعاملات البنكية يتم إنشاؤها تلقائياً بواسطة database trigger

        // تحديث البيانات والملخص
        await fetchAndDisplayTransactions(currentDriverId, selectedMonthId);
        const previousBalance = (await fetchMonthlySummary(currentDriverId, selectedMonthId)).previousBalance || 0;
        updateSummaryCards(currentTransactions, previousBalance, currentDriverBaseSalary);

        // إغلاق النافذة بعد فترة قصيرة
        setTimeout(() => {
            closeModal(paymentModal);
            // Clear editing state
            delete paymentForm.dataset.editingId;
            // Reset modal title and button text
            const modalTitle = paymentModal.querySelector('h2');
            if (modalTitle) modalTitle.innerHTML = '<i class="fas fa-plus"></i> إضافة دفعة راتب';
            const submitBtn = paymentForm.querySelector('button[type="submit"]');
            if (submitBtn) submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ الدفعة';
        }, 1500);

    } catch (error) {
        console.error('Error saving payment:', error);
        showMessage(paymentMessage, `خطأ في حفظ الدفعة: ${error.message}`, 'error');
    } finally {
        submitButton.disabled = false;
        submitButton.innerHTML = originalButtonText;
    }
}

// --- Summary Card Update Function ---
/**
 * تحديث بطاقات الملخص بناءً على الحركات الحالية والرصيد السابق والراتب الأساسي.
 * @param {Array} transactions - مصفوفة الحركات الحالية للشهر.
 * @param {number} previousBalance - الرصيد المتبقي من الشهر السابق.
 * @param {number} baseSalary - الراتب الأساسي الافتراضي للسائق.
 */
function updateSummaryCards(transactions, previousBalance = 0, baseSalary = 0) { // <-- baseSalary parameter already exists
    console.log("Updating summary cards...");
    // --- Diagnostic Log: Log transactions being processed ---
    console.log("Transactions received for summary:", JSON.stringify(transactions, null, 2));
    // --- End Diagnostic Log ---

    let totalCommissions = 0;
    let totalDeductions = 0;
    let totalPaid = 0;

    transactions.forEach(tx => {
        const amount = parseFloat(tx.total_amount) || 0;
        // --- Diagnostic Log: Log each transaction details ---
        console.log(`Processing TX ID ${tx.id}: Type=${tx.expense_type}, IsDeduction=${tx.is_deduction}, Amount=${amount}`);
        // --- End Diagnostic Log ---

        if (tx.expense_type === 'commission' && !tx.is_deduction) {
            totalCommissions += amount;
        } else if (tx.is_deduction) {
            // Include only deductions (advances, fines, accidents marked as deduction)
            totalDeductions += amount;
             // --- Diagnostic Log: Log when adding to deductions ---
            console.log(` -> Added ${amount} to totalDeductions.`);
             // --- End Diagnostic Log ---
        } else if (tx.expense_type === 'payment' && !tx.is_deduction) {
            totalPaid += amount;
        }
        // Note: Expenses paid by bank (is_deduction=false, type!=commission/payment) are not directly included in salary calculation here
    });

    // --- Diagnostic Log: Log final calculated totals ---
    console.log("Final Calculated Totals:", { totalCommissions, totalDeductions, totalPaid });
    // --- End Diagnostic Log ---

    // Calculate Total Due: Previous Balance + Base Salary + Commissions - Deductions
    const totalDue = previousBalance + baseSalary + totalCommissions - totalDeductions;
    const remainingAmount = totalDue - totalPaid;

    // تحديث عناصر DOM (تأكد من وجودها)
    if (summaryPreviousBalanceEl) summaryPreviousBalanceEl.textContent = formatCurrency(previousBalance);
    if (summaryBaseSalaryEl) summaryBaseSalaryEl.textContent = formatCurrency(baseSalary); // <-- Update base salary display
    if (summaryCommissionsEl) summaryCommissionsEl.textContent = formatCurrency(totalCommissions);
    if (summaryDeductionsEl) summaryDeductionsEl.textContent = formatCurrency(totalDeductions); // <-- This is the element in question
    if (summaryTotalDueEl) summaryTotalDueEl.textContent = formatCurrency(totalDue);
    if (summaryPaidEl) summaryPaidEl.textContent = formatCurrency(totalPaid);
    // Assuming summaryRemainingEl is the final remaining amount for the month
    if (summaryRemainingEl) summaryRemainingEl.textContent = formatCurrency(remainingAmount);
    // If summaryRemainingMonthEl exists and means the same thing:
    const summaryRemainingMonthEl = document.getElementById('summary-remaining-month');
    if (summaryRemainingMonthEl) summaryRemainingMonthEl.textContent = formatCurrency(remainingAmount);


    console.log("Summary cards updated:", { baseSalary, previousBalance, totalCommissions, totalDeductions, totalPaid, totalDue, remainingAmount }); // Added previousBalance to log
}

/**
 * Calculates the financial summary for the current month, saves it to driver_monthly_closings,
 * and updates the driver's current_balance in driver_default_settings.
 * This effectively "closes" the month for the driver.
 */
async function saveCurrentBalanceSnapshot() {
    console.log("Attempting to save current balance snapshot and close month...");
    if (!currentDriverId || !selectedMonthId) {
        showMessage(pageMessage, 'خطأ: لا يمكن حفظ الرصيد وإغلاق الشهر. السائق أو الشهر غير محدد.', 'error');
        return;
    }
    if (!_supabase) {
         showMessage(pageMessage, 'خطأ: لا يمكن الاتصال بقاعدة البيانات.', 'error');
         return;
    }

    // Disable button during operation
    if (saveCurrentBalanceBtn) {
        saveCurrentBalanceBtn.disabled = true;
        saveCurrentBalanceBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ والإغلاق...';
    }
    showMessage(pageMessage, '', 'info'); // Clear previous messages

    try {
        // 1. Get Previous Balance (starting point for the current month)
        //    fetchMonthlySummary now correctly gets it from driver_default_settings.current_balance
        const { previousBalance } = await fetchMonthlySummary(currentDriverId, selectedMonthId);

        // 2. Get base salary (from currentDriverBaseSalary state)
        // 3. Get current transactions (from currentTransactions state)

        // 4. Recalculate totals for the current month
        let totalCommissions = 0;
        let totalDeductions = 0;
        let totalPaid = 0;

        currentTransactions.forEach(tx => {
            const amount = parseFloat(tx.total_amount) || 0;
            if (tx.expense_type === 'commission' && !tx.is_deduction) {
                totalCommissions += amount;
            } else if (tx.is_deduction) {
                totalDeductions += amount;
            } else if (tx.expense_type === 'payment' && !tx.is_deduction) {
                totalPaid += amount;
            }
        });

        // 5. Calculate financial figures for closing
        const calculatedNetDue = previousBalance + currentDriverBaseSalary + totalCommissions - totalDeductions;
        const finalRemainingBalance = calculatedNetDue - totalPaid;

        console.log(`Calculated financial figures for closing:
            Previous Balance: ${previousBalance}, Base Salary: ${currentDriverBaseSalary},
            Commissions: ${totalCommissions}, Deductions: ${totalDeductions}, Paid: ${totalPaid},
            Net Due: ${calculatedNetDue}, Final Remaining: ${finalRemainingBalance}`);

        // 6. Get current user ID (optional, for closed_by_user_id)
        let userId = null;
        try {
            const { data: { user } } = await _supabase.auth.getUser();
            if (user) {
                userId = user.id;
            }
        } catch (authError) {
            console.warn("Could not get current user ID for closing record:", authError.message);
        }

        // 7. Prepare data for driver_monthly_closings table
        const closingData = {
            driver_id: currentDriverId,
            budget_month_id: selectedMonthId,
            // closing_date is default CURRENT_TIMESTAMP
            previous_balance_at_closing: previousBalance,
            base_salary_at_closing: currentDriverBaseSalary,
            total_commissions_at_closing: totalCommissions,
            total_deductions_at_closing: totalDeductions,
            total_payments_at_closing: totalPaid,
            calculated_net_due_at_closing: calculatedNetDue,
            final_remaining_balance: finalRemainingBalance,
            closed_by_user_id: userId,
            notes: `إغلاق تلقائي لشهر ${getMonthName(selectedMonthNumber)} ${selectedYearNumber}`
            // created_at is default CURRENT_TIMESTAMP
        };

        // 8. Insert into driver_monthly_closings
        console.log("Inserting into driver_monthly_closings:", closingData);
        const { data: closingRecord, error: closingError } = await _supabase
            .from('driver_monthly_closings')
            .insert(closingData)
            .select() // Optionally select to confirm
            .single(); // Expect one record

        if (closingError) {
            if (closingError.code === '23505') { // Unique violation
                console.error('Error inserting into driver_monthly_closings: Month already closed.', closingError);
                // This specific error message will be shown by checkAndDisplayMonthClosingStatus usually.
                // If somehow this function is called directly on an already closed month, this error is a fallback.
                throw new Error(`هذا الشهر (${getMonthName(selectedMonthNumber)} ${selectedYearNumber}) مغلق بالفعل لهذا السائق.`);
            }
            console.error('Error inserting into driver_monthly_closings:', closingError);
            throw new Error(`فشل تسجيل إغلاق الشهر في سجل الإغلاقات: ${closingError.message}`);
        }
        console.log("Successfully inserted closing record:", closingRecord);

        // 9. Update driver_default_settings with the new current_balance
        // The new current_balance is the final_remaining_balance of the month just closed.
        console.log(`Updating driver_default_settings.current_balance to: ${finalRemainingBalance} for driver ${currentDriverId}`);
        const { error: updateSettingsError } = await _supabase
            .from('driver_default_settings')
            .update({ current_balance: finalRemainingBalance })
            .eq('driver_id', currentDriverId);

        if (updateSettingsError) {
            console.error('Error updating driver_default_settings.current_balance:', updateSettingsError);
            // Potentially try to revert the closing record or log inconsistency
            throw new Error(`فشل تحديث الرصيد الحالي للسائق بعد إغلاق الشهر: ${updateSettingsError.message}. تم إغلاق الشهر ولكن الرصيد الافتراضي قد لا يكون محدثًا.`);
        }

        console.log(`Successfully saved current balance snapshot (${finalRemainingBalance}) and closed month ${selectedMonthId} for driver ${currentDriverId}`);
        const successMessage = `تم إغلاق حساب الشهر ${getMonthName(selectedMonthNumber)} ${selectedYearNumber} لهذا السائق بنجاح. الرصيد النهائي المحفوظ هو ${formatCurrency(finalRemainingBalance)}. سيتم الآن إعادتك إلى قائمة السائقين.`;
        showMessage(pageMessage, successMessage, 'success');

        // Redirect after a delay
        setTimeout(() => {
            window.location.href = '../drivers_management/drivers_management.html';
        }, 4000);

    } catch (error) {
        console.error('Error saving current balance snapshot and closing month:', error);
        showMessage(pageMessage, `فشل حفظ الرصيد وإغلاق الشهر: ${error.message}`, 'error');
        // Re-enable button only on error if redirect doesn't happen
        if (saveCurrentBalanceBtn) {
            saveCurrentBalanceBtn.disabled = false;
            saveCurrentBalanceBtn.innerHTML = '<i class="fas fa-save"></i> حفظ الرصيد وإغلاق الشهر';
            // It's important that checkAndDisplayMonthClosingStatus runs to set the correct button state
            // if the page doesn't redirect. Consider calling it here or ensuring loadPageData is robust.
            // For now, the redirect handles the next state.
        }
    }
    // Button remains disabled on success because the page will redirect.
}

/**
 * إنشاء سجل معاملة بنكية تلقائي لمصروف سائق تم دفعه (جزئيًا أو كليًا) من البنك.
 * @param {string|number} expenseId - معرف سجل المصروف في driver_expenses (bigserial/integer).
 * @param {string} bankId - معرف البنك المستخدم للدفع (uuid).
 * @param {number} paidAmount - المبلغ المدفوع من البنك.
 * @param {string} expenseDate - تاريخ المصروف.
 * @param {string} expenseType - نوع المصروف ('fine' أو 'accident' أو 'payment' أو غيره). // <-- تم تحديث الوصف ليشمل 'payment'
 * @param {string} driverName - اسم السائق.
 * @param {string} monthId - معرف شهر الميزانية. (لم يعد يُستخدم للإدراج المباشر)
 * @param {string|null} specificType - النوع المحدد للمخالفة أو الحادث (e.g., 'سرعة').
 */
async function createBankTransactionForExpense(expenseId, bankId, paidAmount, expenseDate, expenseType, driverName, monthId, specificType) { // Added specificType parameter
    // ملاحظة: هذه الدالة لم تعد مستخدمة - المعاملات البنكية يتم إنشاؤها تلقائياً بواسطة database trigger
    console.warn('createBankTransactionForExpense is deprecated - bank transactions are now created automatically by database trigger');
    return;

    // --- Updated: Ensure transactionAmount is positive ---
    const transactionAmount = Math.abs(paidAmount); // المبلغ موجب لتلبية قيد التحقق
    // --- End Update ---
    const translatedType = translateExpenseType(expenseType, false); // ترجمة النوع الأساسي (مخالفة، حادث, دفعة راتب)

    // --- Updated Description Format ---
    let description = `${driverName || 'غير معروف'} - ${translatedType}`;
    if (specificType) {
        description += ` (${specificType})`; // Add specific type in parentheses if available
    }
    // Add reference ID for clarity, but keep it separate from the main description part
    description += ` (مصروف #${expenseId})`;
    // --- End Updated Description Format ---


    // --- Updated bankTransactionData ---
    // Using reference_code (text) and reference_table (text) instead of reference_id (uuid)
    // because driver_expenses.id is bigserial (integer), not uuid.
    // Changed transaction_type to 'withdrawal' and transaction_source_type to driverName.
    const bankTransactionData = {
        transaction_date: expenseDate,
        bank_id: bankId, // Should be UUID
        amount: transactionAmount, // Use the positive amount
        transaction_type: 'withdrawal', // Changed from 'expense' to 'withdrawal'
        description: description, // Use the new description format
        transaction_source_type: driverName || 'driver_expense', // Use driverName as source, fallback if null
        reference_table: 'driver_expenses', // Name of the source table
        reference_code: String(expenseId), // The ID from the source table (as text)
        // reference_id: null, // Do not use reference_id as it expects a UUID
        // is_reconciled: false, // Column likely doesn't exist or shouldn't be set here
        // budget_month_id: monthId, // Column likely doesn't exist or shouldn't be set here
    };
    // --- End Updated bankTransactionData ---

    console.log("Bank transaction data to insert:", bankTransactionData);

    try {
        const { data, error } = await _supabase
            .from('bank_transactions')
            .insert(bankTransactionData)
            .select() // Optionally select the inserted data to confirm
            .single(); // Assuming only one record is inserted

        if (error) {
            // --- Enhanced Error Logging ---
            console.error(`Error creating bank transaction for expense ${expenseId}:`);
            console.error(`  Status: ${error.code || 'N/A'}`);
            console.error(`  Message: ${error.message}`);
            console.error(`  Details: ${error.details}`);
            console.error(`  Hint: ${error.hint}`);
            console.error(`  Data Sent:`, bankTransactionData);
            // Show a user-friendly message about the failure
            showMessage(pageMessage, `فشل تسجيل الحركة البنكية للمصروف ${expenseId} تلقائيًا. يرجى مراجعة سجل الأخطاء أو الدعم الفني.`, 'error');
        } else {
            console.log(`Successfully created bank transaction for expense ${expenseId}.`, data);
            // Optionally show a success message for the bank transaction part
            // showMessage(pageMessage, `تم تسجيل الحركة البنكية للمصروف ${expenseId} بنجاح.`, 'success');
        }
    } catch (error) {
        console.error(`Unexpected error in createBankTransactionForExpense for expense ${expenseId}:`, error);
         showMessage(pageMessage, `حدث خطأ غير متوقع أثناء تسجيل الحركة البنكية للمصروف ${expenseId}.`, 'error');
    }
}

// --- Edit Transaction Logic ---

/**
 * Handle editing a transaction by opening the appropriate modal with pre-filled data
 * @param {string} transactionId - ID of the transaction to edit
 */
async function handleEditTransaction(transactionId) {
    console.log("Editing transaction:", transactionId);

    // Find the transaction in currentTransactions
    const transaction = currentTransactions.find(tx => tx.id == transactionId);
    if (!transaction) {
        console.error("Transaction not found for editing:", transactionId);
        showMessage(pageMessage, 'خطأ: المعاملة المحددة للتعديل غير موجودة.', 'error');
        return;
    }

    // Open the appropriate modal based on expense_type
    switch (transaction.expense_type) {
        case 'commission':
            openCommissionModalForEdit(transaction);
            break;
        case 'payment':
            openPaymentModalForEdit(transaction);
            break;
        default:
            // For deductions, advances, fines, accidents
            openDeductionModalForEdit(transaction);
            break;
    }
}

/**
 * Open commission modal for editing
 */
function openCommissionModalForEdit(transaction) {
    if (!commissionModal || !commissionForm) return;

    // Set editing state
    commissionForm.dataset.editingId = transaction.id;

    // Update modal title and button text
    const modalTitle = commissionModal.querySelector('h2');
    if (modalTitle) modalTitle.innerHTML = '<i class="fas fa-edit"></i> تعديل العمولة';
    const submitBtn = commissionForm.querySelector('button[type="submit"]');
    if (submitBtn) submitBtn.innerHTML = '<i class="fas fa-save"></i> تحديث العمولة';

    // Fill form fields
    const amountField = commissionForm.querySelector('#commission-amount');
    const dateField = commissionForm.querySelector('#commission-date');
    const typeField = commissionForm.querySelector('#commission-type');
    const notesField = commissionForm.querySelector('#commission-notes');

    if (amountField) amountField.value = transaction.total_amount || '';
    if (dateField) dateField.value = transaction.expense_date || '';
    if (typeField) typeField.value = transaction.commission_type || '';
    if (notesField) notesField.value = transaction.notes || '';

    openModal(commissionModal);
}

/**
 * Open payment modal for editing
 */
function openPaymentModalForEdit(transaction) {
    if (!paymentModal || !paymentForm) return;

    // Set editing state
    paymentForm.dataset.editingId = transaction.id;

    // Update modal title and button text
    const modalTitle = paymentModal.querySelector('h2');
    if (modalTitle) modalTitle.innerHTML = '<i class="fas fa-edit"></i> تعديل دفعة الراتب';
    const submitBtn = paymentForm.querySelector('button[type="submit"]');
    if (submitBtn) submitBtn.innerHTML = '<i class="fas fa-save"></i> تحديث الدفعة';

    // Fill form fields
    const amountField = paymentForm.querySelector('#payment-amount');
    const dateField = paymentForm.querySelector('#payment-date');
    const notesField = paymentForm.querySelector('#payment-notes');

    if (amountField) amountField.value = transaction.total_amount || '';
    if (dateField) dateField.value = transaction.expense_date || '';
    if (notesField) notesField.value = transaction.notes || '';

    // Set bank selection (bank payment is now the only option)
    const bankSelect = paymentForm.querySelector('#payment-bank-id');
    if (bankSelect) bankSelect.value = transaction.bank_id || '';

    openModal(paymentModal);
}

/**
 * Open deduction modal for editing
 */
function openDeductionModalForEdit(transaction) {
    if (!deductionModal || !deductionForm) return;

    // Set editing state
    deductionForm.dataset.editingId = transaction.id;

    // Update modal title and button text
    const modalTitle = deductionModal.querySelector('h2');
    if (modalTitle) modalTitle.innerHTML = '<i class="fas fa-edit"></i> تعديل الحركة';
    const submitBtn = deductionForm.querySelector('button[type="submit"]');
    if (submitBtn) submitBtn.innerHTML = '<i class="fas fa-save"></i> تحديث الحركة';

    // Skip type selection and go directly to form
    if (typeSelectionView) typeSelectionView.style.display = 'none';
    if (deductionForm) deductionForm.style.display = 'block';

    // Fill form fields
    const amountField = deductionForm.querySelector('#deduction-amount');
    const dateField = deductionForm.querySelector('#deduction-date');
    const notesField = deductionForm.querySelector('#deduction-notes');

    if (amountField) amountField.value = transaction.total_amount || '';
    if (dateField) dateField.value = transaction.expense_date || '';
    if (notesField) notesField.value = transaction.notes || '';

    // Set expense type in hidden field or form data
    if (deductionForm.dataset) {
        deductionForm.dataset.expenseType = transaction.expense_type;
    }

    // Show/hide sections based on expense type
    const vatSection = deductionForm.querySelector('#vat-section');
    const paymentMethodSection = deductionForm.querySelector('#payment-method-section');

    if (transaction.expense_type === 'fine' || transaction.expense_type === 'accident') {
        if (vatSection) vatSection.style.display = 'block';
        if (paymentMethodSection) paymentMethodSection.style.display = 'block';
    } else {
        // For advances, hide VAT and payment method sections
        if (vatSection) vatSection.style.display = 'none';
        if (paymentMethodSection) paymentMethodSection.style.display = 'none';
    }

    // Handle specific fields based on expense type
    if (transaction.expense_type === 'fine' || transaction.expense_type === 'accident') {
        const specificTypeField = deductionForm.querySelector('#deduction-specific-type');
        if (specificTypeField) specificTypeField.value = transaction.fine_or_accident_type || '';

        // Show specific type group
        const specificTypeGroup = deductionForm.querySelector('#specific-type-group');
        if (specificTypeGroup) specificTypeGroup.style.display = 'block';
    }

    // Handle VAT if present
    if (transaction.vat_amount && transaction.vat_amount > 0) {
        const vatCheckbox = deductionForm.querySelector('#include-vat-checkbox');
        if (vatCheckbox) vatCheckbox.checked = true;

        // Show VAT section and details
        const vatSection = deductionForm.querySelector('#vat-section');
        if (vatSection) vatSection.style.display = 'block';

        const vatDetails = deductionForm.querySelector('#vat-details');
        if (vatDetails) vatDetails.style.display = 'block';

        // Update VAT display
        const vatAmountDisplay = deductionForm.querySelector('#vat-amount-display');
        const totalWithVatDisplay = deductionForm.querySelector('#total-with-vat-display');
        if (vatAmountDisplay) vatAmountDisplay.textContent = `${transaction.vat_amount} ريال`;
        if (totalWithVatDisplay) totalWithVatDisplay.textContent = `${transaction.total_amount} ريال`;
    }

    // Handle bank payment if present
    if (transaction.bank_id && transaction.bank_paid_amount > 0) {
        // Set payment method to bank or mixed
        const bankRadio = deductionForm.querySelector('input[name="payment_method"][value="bank"]');
        const mixedRadio = deductionForm.querySelector('input[name="payment_method"][value="mixed"]');

        if (transaction.bank_paid_amount === transaction.total_amount && bankRadio) {
            bankRadio.checked = true;
        } else if (mixedRadio) {
            mixedRadio.checked = true;
        }

        const bankSelect = deductionForm.querySelector('#deduction-bank-select');
        const bankAmountField = deductionForm.querySelector('#deduction-bank-paid-amount');

        if (bankSelect) bankSelect.value = transaction.bank_id;
        if (bankAmountField) bankAmountField.value = transaction.bank_paid_amount;

        // Show bank details section
        const bankDetailsSection = deductionForm.querySelector('#bank-details-section');
        if (bankDetailsSection) bankDetailsSection.style.display = 'block';

        // Show bank amount field for mixed payments
        if (transaction.bank_paid_amount < transaction.total_amount) {
            const bankAmountGroup = deductionForm.querySelector('#bank-paid-amount-group');
            if (bankAmountGroup) bankAmountGroup.style.display = 'block';
        }
    }

    openModal(deductionModal);
}

// --- Delete Transaction Logic ---

/**
 * Handles the deletion of a driver expense transaction.
 * Also attempts to delete the associated bank transaction if applicable.
 * @param {string} transactionId - The ID of the driver_expense record to delete.
 */
async function handleDeleteTransaction(transactionId) {
    console.log(`Attempting to delete transaction ID: ${transactionId}`);
    if (!_supabase || !transactionId) {
        showMessage(pageMessage, 'خطأ: لا يمكن بدء عملية الحذف (بيانات ناقصة).', 'error');
        return;
    }

    // 1. Fetch transaction details to check for bank payment
    let transactionToDelete;
    try {
        const { data, error } = await _supabase
            .from('driver_expenses')
            .select('id, bank_paid_amount, bank_id, expense_type')
            .eq('id', transactionId)
            .single();
        if (error) throw error;
        if (!data) throw new Error('لم يتم العثور على الحركة.');
        transactionToDelete = data;
    } catch (error) {
        console.error('Error fetching transaction details before delete:', error);
        showMessage(pageMessage, `خطأ في جلب تفاصيل الحركة للحذف: ${error.message}`, 'error');
        return;
    }

    // 2. Confirmation Dialog
    const confirmationMessage = `هل أنت متأكد من رغبتك في حذف هذه الحركة؟ هذا الإجراء لا يمكن التراجع عنه وقد يؤثر على الحسابات.`;
    if (!confirm(confirmationMessage)) {
        console.log('Transaction deletion cancelled by user.');
        return;
    }

    // Show loading state on page message
    showMessage(pageMessage, 'جاري حذف الحركة...', 'info');

    try {
        // 3. Delete associated bank transaction (if applicable)
        // We use reference_table and reference_code to find the bank transaction
        if (transactionToDelete.bank_paid_amount > 0 && transactionToDelete.bank_id) {
            console.log(`Attempting to delete associated bank transaction for driver_expense ID: ${transactionId}`);
            const { error: bankDeleteError } = await _supabase
                .from('bank_transactions')
                .delete()
                .eq('reference_table', 'driver_expenses')
                .eq('reference_code', String(transactionId)); // reference_code is text

            if (bankDeleteError) {
                // Log the error but proceed to delete the main expense record
                console.error(`Error deleting associated bank transaction: ${bankDeleteError.message}. Proceeding to delete main expense.`);
                // Optionally inform the user about the partial failure
                showMessage(pageMessage, `تحذير: فشل حذف الحركة البنكية المرتبطة. ${bankDeleteError.message}`, 'warning');
            } else {
                console.log(`Successfully deleted associated bank transaction.`);
            }
        }

        // 4. Delete the main driver_expense transaction
        console.log(`Attempting to delete driver_expense record ID: ${transactionId}`);
        const { error: expenseDeleteError } = await _supabase
            .from('driver_expenses')
            .delete()
            .eq('id', transactionId);

        if (expenseDeleteError) {
            throw expenseDeleteError; // Throw error to be caught below
        }

        // 5. Success
        console.log(`Successfully deleted driver_expense record ID: ${transactionId}`);
        showMessage(pageMessage, 'تم حذف الحركة بنجاح. جاري تحديث البيانات...', 'success');

        // 6. Reload page data
        await loadPageData(); // Reload all data to reflect the deletion

    } catch (error) {
        console.error('Error during transaction deletion process:', error);
        showMessage(pageMessage, `فشل حذف الحركة: ${error.message}`, 'error');
    } finally {
        // Clear loading message if it wasn't replaced by success/error after reload
        // (loadPageData might clear it anyway)
    }
}

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', () => {
    // --- تسجيل بداية DOMContentLoaded ---
    console.log("DOMContentLoaded event fired.");
    // --- نهاية التسجيل ---

    if (!_supabase) {
        console.error("Supabase client not available on DOMContentLoaded.");
        // Show critical error message to user
        const bodyMessage = document.createElement('div');
        bodyMessage.textContent = 'خطأ حرج: فشل تهيئة الاتصال بقاعدة البيانات. لا يمكن تحميل الصفحة.';
        bodyMessage.style.color = 'red';
        bodyMessage.style.padding = '20px';
        bodyMessage.style.textAlign = 'center';
        document.body.prepend(bodyMessage);
        return; // Stop execution
    }

    // Initialize DOM element references AGAIN inside DOMContentLoaded
    // to ensure they are available
    transactionsTableBody = document.getElementById('transactions-tbody');
    transactionsMessage = document.getElementById('transactions-message');
    commissionListEl = document.getElementById('commission-list');
    deductionListEl = document.getElementById('deduction-list');
    // ... (إعادة تعريف بقية العناصر إذا لزم الأمر) ...

    // --- تسجيل التحقق من العناصر داخل DOMContentLoaded ---
    console.log("Checking DOM elements inside DOMContentLoaded:", {
        transactionsTableBody: !!transactionsTableBody,
        commissionListEl: !!commissionListEl,
        deductionListEl: !!deductionListEl
    });
    // --- نهاية تسجيل التحقق ---

    // --- تسجيل التحقق من عناصر النوافذ المنبثقة ---
    console.log("Checking Modal DOM elements inside DOMContentLoaded:", {
        addCommissionBtn: !!addCommissionBtn,
        addDeductionBtn: !!addDeductionBtn,
        commissionModal: !!commissionModal,
        commissionForm: !!commissionForm,
        deductionModal: !!deductionModal,
        deductionForm: !!deductionForm
    });
    // --- نهاية تسجيل التحقق ---

    // --- تسجيل التحقق من عناصر الملخص ---
    console.log("Checking Summary Card DOM elements inside DOMContentLoaded:", {
        summaryPreviousBalanceEl: !!summaryPreviousBalanceEl,
        summaryCommissionsEl: !!summaryCommissionsEl,
        summaryDeductionsEl: !!summaryDeductionsEl,
        summaryTotalDueEl: !!summaryTotalDueEl,
        summaryPaidEl: !!summaryPaidEl,
        summaryRemainingEl: !!summaryRemainingEl,
    });
    // --- نهاية تسجيل التحقق ---

    // --- تسجيل التحقق من عناصر نافذة الخصم ---
    console.log("Checking Deduction Modal DOM elements inside DOMContentLoaded:", {
        deductionModal: !!deductionModal,
        deductionForm: !!deductionForm,
        typeSelectionView: !!typeSelectionView,
        // Add checks for other crucial elements if needed
        deductionAmountInput: !!deductionAmountInput,
        includeVatCheckbox: !!includeVatCheckbox,
        bankSelect: !!bankSelect
    });
    // --- نهاية تسجيل التحقق ---

    loadPageData();
    setupEventListeners();
});

// --- Helper functions to clear editing state when opening modals for adding new records ---

/**
 * Clear editing state and reset modal for adding new commission
 */
function resetCommissionModalForAdd() {
    if (commissionForm) {
        delete commissionForm.dataset.editingId;
        commissionForm.reset();
        const modalTitle = commissionModal?.querySelector('h2');
        if (modalTitle) modalTitle.innerHTML = '<i class="fas fa-plus"></i> إضافة عمولة';
        const submitBtn = commissionForm.querySelector('button[type="submit"]');
        if (submitBtn) submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ العمولة';
    }
}

/**
 * Clear editing state and reset modal for adding new payment
 */
function resetPaymentModalForAdd() {
    if (paymentForm) {
        delete paymentForm.dataset.editingId;
        paymentForm.reset();
        const modalTitle = paymentModal?.querySelector('h2');
        if (modalTitle) modalTitle.innerHTML = '<i class="fas fa-plus"></i> إضافة دفعة راتب';
        const submitBtn = paymentForm.querySelector('button[type="submit"]');
        if (submitBtn) submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ الدفعة';
    }
}

/**
 * Clear editing state and reset modal for adding new deduction
 */
function resetDeductionModalForAdd() {
    if (deductionForm) {
        delete deductionForm.dataset.editingId;
        deductionForm.reset();
        const modalTitle = deductionModal?.querySelector('h2');
        if (modalTitle) modalTitle.innerHTML = '<i class="fas fa-plus"></i> إضافة خصم/سلفة/مخالفة/حادث';
        const submitBtn = deductionForm.querySelector('button[type="submit"]');
        if (submitBtn) submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ الحركة';
    }
}

// --- Edit Previous Balance Modal Logic ---
if (editPreviousBalanceBtn) {
    editPreviousBalanceBtn.addEventListener('click', async () => {
        if (!currentDriverId) return;
        // Fetch current previous balance from Supabase
        if (previousBalanceCurrentInput) previousBalanceCurrentInput.value = '...';
        if (previousBalanceNewInput) previousBalanceNewInput.value = '';
        if (editPreviousBalanceMessage) editPreviousBalanceMessage.style.display = 'none';
        if (editPreviousBalanceModal) editPreviousBalanceModal.style.display = 'flex';
        try {
            const { data, error } = await _supabase
                .from('driver_default_settings')
                .select('current_balance')
                .eq('driver_id', currentDriverId)
                .single();
            if (error) throw error;
            previousBalanceCurrentInput.value = data?.current_balance ?? '0.00';
        } catch (err) {
            previousBalanceCurrentInput.value = 'خطأ';
        }
    });
}
if (closeEditPreviousBalanceModalBtn) {
    closeEditPreviousBalanceModalBtn.addEventListener('click', () => {
        if (editPreviousBalanceModal) editPreviousBalanceModal.style.display = 'none';
    });
}
if (cancelEditPreviousBalanceBtn) {
    cancelEditPreviousBalanceBtn.addEventListener('click', () => {
        if (editPreviousBalanceModal) editPreviousBalanceModal.style.display = 'none';
    });
}
if (editPreviousBalanceForm) {
    editPreviousBalanceForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        if (!currentDriverId) return;
        const newBalance = parseFloat(previousBalanceNewInput.value);
        if (isNaN(newBalance)) {
            editPreviousBalanceMessage.textContent = 'يرجى إدخال رصيد صحيح.';
            editPreviousBalanceMessage.className = 'message error show';
            editPreviousBalanceMessage.style.display = 'block';
            return;
        }
        editPreviousBalanceMessage.textContent = 'جاري الحفظ...';
        editPreviousBalanceMessage.className = 'message info show';
        editPreviousBalanceMessage.style.display = 'block';
        try {
            const { error } = await _supabase
                .from('driver_default_settings')
                .update({ current_balance: newBalance })
                .eq('driver_id', currentDriverId);
            if (error) throw error;
            editPreviousBalanceMessage.textContent = 'تم تحديث الرصيد بنجاح!';
            editPreviousBalanceMessage.className = 'message success show';
            // Update summary card
            if (summaryPreviousBalanceEl) summaryPreviousBalanceEl.textContent = `${newBalance.toFixed(2)} ريال`;
            setTimeout(() => {
                if (editPreviousBalanceModal) editPreviousBalanceModal.style.display = 'none';
            }, 1200);
        } catch (err) {
            editPreviousBalanceMessage.textContent = 'حدث خطأ أثناء التحديث.';
            editPreviousBalanceMessage.className = 'message error show';
        }
    });
}
