/* Inherit base styles from shared_styles.css */

/* Dashboard Layout */
.dashboard-layout {
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    overflow-x: hidden;
}

/* Top Navigation Bar */
.top-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-toggle-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.sidebar-toggle-btn:hover {
    background-color: rgba(255,255,255,0.1);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 600;
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.navbar-user {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logout-btn {
    background: rgba(255,255,255,0.1);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: rgba(255,255,255,0.2);
}

/* Main Content */
.main-content {
    margin-top: 70px;
    padding: 0;
    min-height: calc(100vh - 70px);
    transition: margin-right 0.3s ease;
}

.content-wrapper {
    padding: 30px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Dashboard Header */
.dashboard-header {
    background: white;
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--card-shadow);
    border-top: 4px solid var(--primary-color);
}

.header-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.dashboard-header h1 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.dashboard-header h1 i {
    color: var(--primary-color);
}

.header-description {
    color: var(--text-muted);
    font-size: 1rem;
    margin: 0;
    opacity: 0.9;
}

/* Statistics Section */
.stats-section {
    margin-bottom: 40px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    border-top: 4px solid transparent;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.stat-card:nth-child(1) {
    border-top-color: var(--primary-color);
}

.stat-card:nth-child(2) {
    border-top-color: var(--success-color);
}

.stat-card:nth-child(3) {
    border-top-color: var(--info-color);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: var(--primary-color);
}

.stat-card:nth-child(2) .stat-icon {
    background: var(--success-color);
}

.stat-card:nth-child(3) .stat-icon {
    background: var(--info-color);
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin: 0 0 5px 0;
}

.stat-info p {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin: 0;
}

/* Cards */
.card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    margin-bottom: 30px;
}

.card-header {
    background: var(--card-bg);
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h2 {
    margin: 0;
    color: var(--secondary-color);
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header h2 i {
    color: var(--primary-color);
}

.card-body {
    padding: 25px;
}

/* Filters Section */
.filters-section {
    margin-bottom: 30px;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    color: var(--text-color);
    font-weight: 500;
    font-size: 0.9rem;
}

.search-input-container {
    position: relative;
}

.search-input-container input {
    padding-right: 45px;
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
}

.search-input-container input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.search-input-container i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

select {
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: white;
    color: var(--text-color);
    font-family: inherit;
    font-size: 0.95rem;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); /* Responsive grid */
    gap: 25px;
    padding: 10px 0;
}

.driver-card {
    background-color: #fff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px 20px;
    text-align: center;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 180px; /* Ensure cards have a minimum height */
}

.driver-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
    border-color: var(--accent-color);
}

.driver-card i {
    font-size: 3.5rem; /* Larger icon */
    color: var(--primary-color);
    margin-bottom: 15px;
    display: block;
}

.driver-card .driver-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--secondary-color);
    display: block;
    margin-bottom: 5px;
}

.driver-card .driver-salary {
    font-size: 0.9rem;
    color: var(--text-muted);
    display: block;
}

.loading-placeholder {
    grid-column: 1 / -1; /* Span full width */
    text-align: center;
    padding: 40px 20px;
    font-size: 1.2rem;
    color: var(--text-muted);
}

.loading-placeholder i {
    margin-left: 10px;
    font-size: 1.5rem;
}

/* Table Responsive */
.table-responsive {
    overflow-x: auto;
    border-radius: var(--border-radius);
}

/* Modern Table */
.modern-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.modern-table thead {
    background: var(--card-bg);
}

.modern-table th {
    padding: 15px;
    text-align: right;
    font-weight: 600;
    color: var(--secondary-color);
    border-bottom: 2px solid var(--border-color);
    font-size: 0.9rem;
}

.modern-table td {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.modern-table tbody tr {
    transition: background-color 0.2s ease;
}

.modern-table tbody tr:hover {
    background-color: var(--card-bg);
}

.loading-message {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 40px 20px;
}

.loading-message i {
    margin-left: 10px;
}

/* Table Inputs and Selects */
.modern-table select,
.modern-table input[type="number"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.modern-table select:focus,
.modern-table input[type="number"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.modern-table input[readonly] {
    background-color: var(--card-bg);
    color: var(--text-muted);
    cursor: not-allowed;
}

/* Buttons */
.btn {
    padding: 10px 16px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
}

.btn-secondary {
    background: var(--text-muted);
    color: white;
}

.btn-secondary:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.8rem;
}

.btn-edit {
    background: var(--warning-color);
    color: white;
}

.btn-edit:hover {
    background: var(--warning-color);
    transform: scale(1.1);
}

.btn-save {
    background: var(--success-color);
    color: white;
}

.btn-save:hover {
    background: var(--success-color);
    transform: scale(1.1);
}

.btn-save:disabled {
    background: var(--text-muted);
    cursor: not-allowed;
    transform: none;
}

.btn-delete {
    background: var(--danger-color);
    color: white;
}

.btn-delete:hover {
    background: var(--danger-color);
    transform: scale(1.1);
}

.btn-add-defaults {
    background: var(--primary-color);
    color: white;
}

.btn-add-defaults:hover {
    background: var(--primary-color);
    transform: scale(1.1);
}

/* Balance Display */
.balance-display {
    padding: 8px 12px;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-align: center;
}

.balance-positive {
    background-color: #fee;
    color: #d32f2f;
    border: 1px solid #ffcdd2;
}

.balance-negative {
    background-color: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

.balance-zero {
    background-color: var(--card-bg);
    color: var(--text-muted);
    border: 1px solid var(--border-color);
}

.balance-display.clickable:hover {
    opacity: 0.8;
    transform: scale(1.02);
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.pagination-info {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.pagination {
    display: flex;
    gap: 5px;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    background: white;
    color: var(--text-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagination button:hover:not(:disabled) {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination button.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination span {
    padding: 8px 12px;
    color: var(--text-muted);
}

/* Message area styles (if not fully covered by shared_styles.css) */
.message {
    padding: 12px 15px;
    margin: 15px 0;
    border-radius: var(--border-radius);
    border: 1px solid transparent;
    display: none; /* Hidden by default */
}
.message.show { display: block; }
.message.info { background-color: #eaf5fd; border-color: var(--info-color); color: #2980b9; }
.message.success { background-color: #eafaf1; border-color: var(--success-color); color: #27ae60; }
.message.warning { background-color: #fef5e7; border-color: var(--warning-color); color: #d35400; }
.message.error { background-color: #fdedec; border-color: var(--danger-color); color: #c0392b; }

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 600px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background: var(--card-bg);
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    color: var(--secondary-color);
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-header h2 i {
    color: var(--primary-color);
}

.close-modal-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-modal-btn:hover {
    background: var(--danger-color);
    color: white;
}

.modal-body {
    padding: 25px;
}

/* Form Styles */
.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    color: var(--text-color);
    font-weight: 500;
    font-size: 0.9rem;
}

.form-group input,
.form-group select {
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.form-help {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: 5px;
}

.driver-info {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    border: 2px solid var(--border-color);
}

.driver-info i {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.driver-info span {
    font-weight: 600;
    color: var(--secondary-color);
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

/* Balance Info Modal */
.balance-info-modal .modal-content {
    max-width: 800px;
}

.balance-breakdown-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.balance-breakdown-table th,
.balance-breakdown-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.balance-breakdown-table th {
    background: var(--card-bg);
    font-weight: 600;
    color: var(--secondary-color);
}

.balance-breakdown-table tbody tr:hover {
    background: var(--card-bg);
}

/* Badge */
.badge {
    background: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.badge-success {
    background: var(--success-color);
    color: white;
}

.badge-warning {
    background: var(--warning-color);
    color: white;
}

.badge-danger {
    background: var(--danger-color);
    color: white;
}

/* Clickable elements */
.clickable {
    cursor: pointer;
    transition: all 0.2s ease;
}

.clickable:hover {
    opacity: 0.8;
    transform: scale(1.02);
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Footer button style (if needed) */
.main-footer {
    display: flex;
    justify-content: space-between; /* Adjust alignment */
    align-items: center;
    padding: 0 20px; /* Add padding */
}
.main-footer p {
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .content-wrapper {
        padding: 20px 15px;
    }

    .navbar-brand span {
        display: none;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .filters-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .modal-body {
        padding: 20px;
    }

    .modal-actions {
        flex-direction: column;
        gap: 10px;
    }

    .modal-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .pagination-container {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-actions {
        flex-direction: column;
        gap: 10px;
    }

    .card-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .modern-table {
        font-size: 0.8rem;
    }

    .modern-table th,
    .modern-table td {
        padding: 10px 8px;
    }
}

@media (max-width: 480px) {
    .dashboard-header {
        padding: 20px;
    }

    .dashboard-header h1 {
        font-size: 1.5rem;
    }

    .stat-card {
        padding: 20px;
    }

    .stat-info h3 {
        font-size: 1.5rem;
    }

    .btn {
        padding: 8px 12px;
        font-size: 0.8rem;
    }

    .btn-icon {
        width: 28px;
        height: 28px;
        font-size: 0.7rem;
    }
}
