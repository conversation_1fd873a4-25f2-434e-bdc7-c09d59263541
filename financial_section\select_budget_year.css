.selection-main {
    padding-top: 20px;
}

.selection-card .card-header h2 i {
    margin-left: 10px;
}

.cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)); /* Responsive grid */
    gap: 25px;
    padding: 10px 0;
}

.year-card {
    background-color: #fff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px 20px;
    text-align: center;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.year-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
    border-color: var(--accent-color);
}

.year-card i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    display: block;
}

.year-card span {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--secondary-color);
    display: block;
}

.loading-placeholder {
    grid-column: 1 / -1; /* Span full width */
    text-align: center;
    padding: 40px 20px;
    font-size: 1.2rem;
    color: var(--text-muted);
}

.loading-placeholder i {
    margin-left: 10px;
    font-size: 1.5rem;
}
