// --- Auth Check ---
// First thing: Check if user is authenticated. Adjust path to login page as needed.
checkAuth('../login.html'); // Assumes login.html is one level up

// --- Supabase Initialization ---
let _supabase;
try {
    if (typeof supabase !== 'undefined' && supabase.createClient) {
        // Use Supabase client from config.js
        _supabase = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('Supabase client initialized using config.');
    } else {
        console.error('Supabase client not found. Make sure config.js is loaded correctly.');
        // Handle the error appropriately, maybe show a message to the user
    }
} catch (error) {
    console.error('Error initializing Supabase client:', error);
}


// --- DOM Elements ---
const yearCardsContainer = document.getElementById('year-cards-container');
const messageArea = document.getElementById('message-area');

// --- Functions ---
const showMessage = (message, type = 'info') => {
    if (!messageArea) return;
    messageArea.textContent = message;
    messageArea.className = `message ${type} show`;
    messageArea.style.display = 'block';
};

const renderYearCards = (years) => {
    if (!yearCardsContainer) return;
    yearCardsContainer.innerHTML = ''; // Clear loading/previous cards

    if (!years || years.length === 0) {
        showMessage('لم يتم العثور على سنوات مالية متاحة.', 'warning');
        yearCardsContainer.innerHTML = '<p class="loading-placeholder">لا توجد سنوات لإظهارها.</p>';
        return;
    }

    years.forEach(year => {
        const card = document.createElement('div');
        card.className = 'year-card';
        card.dataset.yearId = year.id;
        card.dataset.yearNumber = year.year_number;
        card.innerHTML = `
            <i class="fas fa-folder"></i>
            <span>${year.year_number}</span>
        `;
        card.addEventListener('click', handleYearSelection);
        yearCardsContainer.appendChild(card);
    });
};

const handleYearSelection = (event) => {
    const card = event.currentTarget;
    const yearId = card.dataset.yearId;
    const yearNumber = card.dataset.yearNumber;

    if (yearId && yearNumber) {
        console.log(`Year selected: ID=${yearId}, Number=${yearNumber}`);
        // Store selected year info in sessionStorage
        sessionStorage.setItem('selectedBudgetYearId', yearId);
        sessionStorage.setItem('selectedBudgetYearNumber', yearNumber);

        // Redirect to month selection page
        // Ensure the path is correct relative to select_budget_year.html
        window.location.href = 'select_budget_month.html';
    } else {
        console.error('Missing year data on card:', card);
        showMessage('حدث خطأ عند اختيار السنة.', 'error');
    }
};

const fetchYears = async () => {
    if (!_supabase) {
        showMessage('خطأ في الاتصال بقاعدة البيانات.', 'error');
        if (yearCardsContainer) yearCardsContainer.innerHTML = '<p class="loading-placeholder">خطأ في الاتصال.</p>';
        return;
    }

    try {
        const { data, error } = await _supabase
            .from('budget_years')
            .select('id, year_number')
            .order('year_number', { ascending: false }); // Show newest years first

        if (error) {
            console.error('Error fetching years:', error);
            throw error;
        }

        renderYearCards(data);

    } catch (error) {
        showMessage(`خطأ في جلب السنوات: ${error.message}`, 'error');
        if (yearCardsContainer) yearCardsContainer.innerHTML = '<p class="loading-placeholder">فشل تحميل السنوات.</p>';
    }
};

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', () => {
    console.log('Select Budget Year page loaded.');
    // setupLogoutButton(); // Already called within checkAuth if successful
    fetchYears();

    // Optional: Add functionality to a potential "Home" button in the footer if needed
    // const homeBtn = document.querySelector('.home-btn');
    // if (homeBtn) {
    //     homeBtn.addEventListener('click', () => {
    //         // Decide where home should go, maybe back to login or this page itself
    //         window.location.href = '../login.html';
    //     });
    // }
});
