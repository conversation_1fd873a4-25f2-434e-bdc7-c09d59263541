<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الاشتراكات الافتراضية للطلاب</title>
    <!-- Shared Styles -->
    <link rel="stylesheet" href="../../shared_styles.css">
    <!-- Page Specific Styles -->
    <link rel="stylesheet" href="student_defaults.css">
    <!-- Sidebar Component Styles -->
    <link rel="stylesheet" href="../../shared_components/sidebar.css">
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Config -->
    <script src="../../config.js"></script>
    <!-- Auth Script -->
    <script src="../../auth.js"></script>
    <!-- Sidebar Component Script -->
    <script src="../../shared_components/sidebar.js"></script>
    <!-- Page Script -->
    <script defer src="student_defaults.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body class="dashboard-layout" data-load-sidebar="true">
    <!-- Top Navigation Bar -->
    <nav class="top-navbar">
        <div class="navbar-left">
            <button id="sidebar-toggle" class="sidebar-toggle-btn">
                <i class="fas fa-bars"></i>
            </button>
            <div class="navbar-brand">
                <i class="fas fa-user-cog"></i>
                <span>إدارة الاشتراكات الافتراضية</span>
            </div>
        </div>
        <div class="navbar-right">
            <div class="header-actions">
                <button id="hidden-students-btn" class="btn btn-warning hidden-students-btn" style="display: none;">
                    <i class="fas fa-eye-slash"></i>
                    <span id="hidden-students-text">طلاب مخفيين</span>
                    <span id="hidden-students-count" class="badge-count">0</span>
                </button>
                <button id="refresh-btn" class="btn btn-secondary">
                    <i class="fas fa-sync-alt"></i>
                    تحديث البيانات
                </button>
            </div>
            <div class="navbar-user">
                <span id="navbar-username"></span>
                <button id="logout-btn" class="logout-btn" style="display: none;">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Sidebar Container (will be populated by sidebar.js) -->
    <div id="sidebar-container"></div>

    <!-- Main Content Area -->
    <main class="main-content">
        <div class="content-wrapper">
            <!-- Dashboard Header -->
            <header class="dashboard-header">
                <div class="header-content">
                    <h1>
                        <i class="fas fa-user-cog"></i>
                        إدارة الاشتراكات الافتراضية للطلاب
                    </h1>
                    <p class="header-description">تحديد نوع الخدمة والمبلغ الأساسي ورصيد الافتتاح لكل طالب</p>
                </div>
                <div id="dashboard-message" class="message" style="display: none;"></div>
            </header>

            <!-- Statistics Cards -->
                <section class="stats-section">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="total-students">0</h3>
                                <p>إجمالي الطلاب</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="configured-students">0</h3>
                                <p>طلاب مُعدين</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="total-balance">0</h3>
                                <p>إجمالي الأرصدة</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Search and Filters -->
                <section class="filters-section">
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-search"></i> البحث والتصفية</h2>
                        </div>
                        <div class="card-body">
                            <div class="filters-grid">
                                <div class="form-group">
                                    <label for="search-input">البحث عن طالب</label>
                                    <div class="search-input-container">
                                        <input type="text" id="search-input" placeholder="ابحث بالاسم أو رقم الهاتف...">
                                        <i class="fas fa-search"></i>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="service-filter">تصفية حسب نوع الخدمة</label>
                                    <select id="service-filter">
                                        <option value="">جميع الخدمات</option>
                                        <option value="ذهاب وعودة">ذهاب وعودة</option>
                                        <option value="ذهاب فقط">ذهاب فقط</option>
                                        <option value="عودة فقط">عودة فقط</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="balance-filter">تصفية حسب الرصيد</label>
                                    <select id="balance-filter">
                                        <option value="">جميع الأرصدة</option>
                                        <option value="positive">رصيد موجب</option>
                                        <option value="negative">رصيد سالب</option>
                                        <option value="zero">رصيد صفر</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Students Table -->
                <section class="table-section">
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-table"></i> <span id="table-title">الطلاب الجدد</span></h2>
                            <div class="header-actions">
                                <span class="badge" id="students-count">0 طالب</span>
                                <button id="toggle-view-btn" class="btn btn-outline">
                                    <i class="fas fa-users"></i>
                                    <span id="toggle-btn-text">عرض الطلاب المسجلين</span>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Message Area -->
                            <div id="list-message" class="message" style="display: none;"></div>

                            <!-- Table Container -->
                            <div class="table-responsive">
                                <table id="defaults-table" class="modern-table">
                                    <thead>
                                        <tr>
                                            <th>اسم الطالب</th>
                                            <th>نوع الخدمة</th>
                                            <th>المبلغ الشهري</th>
                                            <th>رصيد الافتتاح</th>
                                            <th>الرصيد الحالي</th>
                                            <th>المجموعة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="defaults-tbody">
                                        <tr>
                                            <td colspan="7" class="loading-message">
                                                <i class="fas fa-spinner fa-spin"></i>
                                                جاري تحميل بيانات الطلاب...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <div class="pagination-container">
                                <div class="pagination-info">
                                    <span id="pagination-info">عرض 0 من 0 طالب</span>
                                </div>
                                <div class="pagination" id="pagination-controls">
                                    <!-- Pagination buttons will be added by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
        </div>
    </main>

    <!-- Assign Student to Group Modal -->
    <div id="assign-group-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-users"></i> تعيين طالب إلى مجموعة</h2>
                <button class="close-modal-btn" id="close-assign-modal-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="assign-modal-message-area" class="message" style="display: none;"></div>
                <form id="assign-group-form">
                    <input type="hidden" id="assign-student-id">
                    <div class="form-group">
                        <label>الطالب المحدد:</label>
                        <div class="student-info">
                            <i class="fas fa-user"></i>
                            <span id="assign-student-name"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="assign-group-select">اختر المجموعة:</label>
                        <select id="assign-group-select" name="groupId" required>
                            <option value="">-- اختر مجموعة --</option>
                        </select>
                    </div>
                    <div class="modal-actions">
                        <button type="submit" class="btn btn-primary" id="save-assignment-btn">
                            <i class="fas fa-check"></i>
                            تعيين الطالب
                        </button>
                        <button type="button" class="btn btn-secondary" id="cancel-assignment-btn">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Student Modal -->
    <div id="edit-student-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-edit"></i> تعديل إعدادات الطالب</h2>
                <button class="close-modal-btn" id="close-edit-modal-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="edit-modal-message-area" class="message" style="display: none;"></div>
                <form id="edit-student-form">
                    <input type="hidden" id="edit-student-id">
                    <div class="form-row">
                        <div class="form-group">
                            <label>اسم الطالب:</label>
                            <div class="student-info">
                                <i class="fas fa-user"></i>
                                <span id="edit-student-name"></span>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-service-type">نوع الخدمة:</label>
                            <select id="edit-service-type" required>
                                <option value="">-- اختر نوع الخدمة --</option>
                                <option value="ذهاب وعودة">ذهاب وعودة</option>
                                <option value="ذهاب فقط">ذهاب فقط</option>
                                <option value="عودة فقط">عودة فقط</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="edit-default-amount">المبلغ الافتراضي (ريال):</label>
                            <input type="number" id="edit-default-amount" step="0.01" min="0" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-opening-balance">رصيد الافتتاح (ريال):</label>
                            <input type="number" id="edit-opening-balance" step="0.01">
                            <small class="form-help">رصيد الافتتاح للطالب (اختياري)</small>
                        </div>
                        <div class="form-group">
                            <label for="edit-current-balance">الرصيد الحالي (ريال):</label>
                            <input type="number" id="edit-current-balance" step="0.01" readonly>
                            <small class="form-help">للعرض فقط - يتم حسابه تلقائياً</small>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ التغييرات
                        </button>
                        <button type="button" class="btn btn-secondary" id="cancel-edit-btn">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Students Modal -->
    <div id="new-students-modal" class="modal" style="display: none;">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3><i class="fas fa-users"></i> الطلاب المسجلين</h3>
                <button id="close-new-students-modal-btn" class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <div id="new-students-message-area" class="message-area" style="display: none;"></div>

                <!-- Search and Filter -->
                <div class="modal-filters">
                    <div class="search-container">
                        <input type="text" id="modal-search-input" placeholder="البحث عن طالب..." class="search-input">
                        <i class="fas fa-search search-icon"></i>
                    </div>
                    <select id="modal-service-filter" class="filter-select">
                        <option value="">جميع أنواع الخدمة</option>
                        <option value="ذهاب وعودة">ذهاب وعودة</option>
                        <option value="ذهاب فقط">ذهاب فقط</option>
                        <option value="عودة فقط">عودة فقط</option>
                        <option value="خدمة خاصة">خدمة خاصة</option>
                    </select>
                </div>

                <!-- Bulk Actions -->
                <div class="bulk-actions">
                    <div class="bulk-selection">
                        <label class="checkbox-container">
                            <input type="checkbox" id="select-all-students">
                            <span class="checkmark"></span>
                            تحديد الكل
                        </label>
                        <span id="selected-students-count-display" class="selected-count">لم يتم تحديد أي طالب</span>
                    </div>
                    <button id="bulk-edit-students-btn" class="btn btn-primary" disabled>
                        <i class="fas fa-edit"></i>
                        تعديل المحددين (<span id="bulk-edit-count">0</span>)
                    </button>
                </div>

                <!-- Students List -->
                <div class="students-list">
                    <div id="students-container">
                        <!-- Students will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden Students Modal -->
    <div id="hidden-students-modal" class="modal" style="display: none;">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3><i class="fas fa-eye-slash"></i> الطلاب المخفيين (لديهم رصيد)</h3>
                <button id="close-hidden-students-modal-btn" class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <div id="hidden-students-message-area" class="message-area" style="display: none;"></div>

                <!-- Search -->
                <div class="modal-filters">
                    <div class="search-container">
                        <input type="text" id="hidden-search-input" placeholder="البحث عن طالب مخفي..." class="search-input">
                        <i class="fas fa-search search-icon"></i>
                    </div>
                </div>

                <!-- Hidden Students List -->
                <div class="hidden-students-list">
                    <div id="hidden-students-container">
                        <!-- Hidden students will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Balance Info Modal -->
    <div id="balance-info-modal" class="balance-info-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تفاصيل الرصيد - <span id="balance-info-student-name">...</span></h2>
                <button type="button" class="close-modal-btn" id="close-balance-info-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div id="balance-info-message" class="message" style="display: none;"></div>
                <table class="balance-breakdown-table">
                    <thead>
                        <tr>
                            <th>البيان</th>
                            <th>المبلغ</th>
                            <th>الملاحظات</th>
                        </tr>
                    </thead>
                    <tbody id="balance-breakdown-tbody">
                        <!-- Balance breakdown will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

</body>
</html>
