<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الحافلات</title>

    <!-- الترتيب مهم جداً حسب الدليل -->
    <!-- Shared Styles -->
    <link rel="stylesheet" href="../shared_styles.css">
    <!-- Page Specific Styles -->
    <link rel="stylesheet" href="style.css">
    <!-- Sidebar Component Styles -->
    <link rel="stylesheet" href="../shared_components/sidebar.css">

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="../config.js"></script>
    <script src="../auth.js"></script>
    <!-- Sidebar Component Script -->
    <script src="../shared_components/sidebar.js"></script>
    <!-- <PERSON> Script -->
    <script defer src="script.js"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body class="dashboard-layout" data-load-sidebar="true">
    <!-- Top Navigation Bar -->
    <nav class="top-navbar">
        <div class="navbar-left">
            <button id="sidebar-toggle" class="sidebar-toggle-btn">
                <i class="fas fa-bars"></i>
            </button>
            <div class="navbar-brand">
                <i class="fas fa-bus"></i>
                <span>نظام إدارة الحافلات</span>
            </div>
        </div>
        <div class="navbar-right">
            <div class="navbar-user">
                <span id="navbar-username"></span>
                <button id="logout-btn" class="logout-btn" style="display: none;">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Sidebar Container (will be populated by sidebar.js) -->
    <div id="sidebar-container"></div>

    <!-- Main Content Area -->
    <main class="main-content">
        <div class="content-wrapper">
            <!-- Dashboard Header -->
            <header class="dashboard-header">
                <div class="header-content">
                    <h1>
                        <i class="fas fa-bus"></i>
                        نظام إدارة الحافلات
                    </h1>
                    <p>إدارة بيانات وأسطول الحافلات</p>
                </div>
            </header>

            <!-- Statistics Cards -->
            <section class="stats-section">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-bus-alt"></i></div>
                    <div class="stat-info">
                        <h3 id="total-buses">0</h3>
                        <p>إجمالي الحافلات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                    <div class="stat-info">
                        <h3 id="active-buses">0</h3>
                        <p>حافلات نشطة</p>
                    </div>
                </div>
                <div class="stat-card"> <!-- Example: Add another relevant stat -->
                    <div class="stat-icon"><i class="fas fa-tools"></i></div>
                    <div class="stat-info">
                        <h3 id="needs-maintenance">0</h3> <!-- Example Placeholder -->
                        <p>تحتاج صيانة (مثال)</p>
                    </div>
                </div>
            </section>

            <!-- Controls Section -->
            <section class="controls-section">
                <div class="control-card">
                    <div class="card-header">
                        <h2>أدوات التحكم</h2>
                    </div>
                    <div class="card-body">
                        <div class="controls-grid">
                            <!-- Add Bus Button -->
                            <button id="add-bus-btn" class="control-btn add-btn">
                                <i class="fas fa-plus-circle"></i> إضافة حافلة
                            </button>
                            <div class="search-container">
                                <input type="text" id="search-input" placeholder="ابحث برقم الحافلة أو اللوحة...">
                                <button id="search-btn" class="search-btn"><i class="fas fa-search"></i></button>
                            </div>
                            <button id="print-report-btn" class="control-btn print-btn">
                                <i class="fas fa-print"></i> طباعة تقرير
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Buses Table Section -->
            <section class="table-section">
                <div class="table-card">
                    <div class="card-header">
                        <h2>قائمة الحافلات</h2>
                        <span class="badge" id="buses-count">0</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="buses-table">
                                <thead>
                                    <tr>
                                        <th>رقم الحافلة</th>
                                        <th>نوع الحافلة</th>
                                        <th>الموديل</th>
                                        <th>رقم اللوحة</th>
                                        <th>الحالة</th>
                                        <th>ملاحظات</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="buses-tbody">
                                    <!-- Adjusted colspan to 7 -->
                                    <tr><td colspan="7" class="loading-message">جاري تحميل البيانات...</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <div id="list-message" class="message"></div>
                        <div class="pagination" id="pagination-controls">
                            <!-- Pagination will be added by JavaScript -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Bus Form Section (Modal) -->
            <section id="add-bus-section" class="form-section">
                <div class="form-card">
                    <div class="card-header">
                        <h2 id="form-title">إضافة حافلة جديدة</h2>
                        <button id="close-form-btn" class="close-btn"><i class="fas fa-times"></i></button>
                    </div>
                    <div class="card-body">
                        <form id="bus-form">
                            <input type="hidden" id="bus_id" name="bus_id"> <!-- Use 'id' or 'uuid' based on your PK -->

                            <!-- Bus Information Section -->
                            <fieldset class="form-fieldset">
                                <legend>بيانات الحافلة</legend>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="bus_number">رقم الحافلة <span class="required">*</span></label>
                                        <input type="text" id="bus_number" name="bus_number" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="bus_type">نوع الحافلة</label>
                                        <!-- Changed to select dropdown -->
                                        <select id="bus_type" name="bus_type">
                                            <option value="" selected>اختر النوع</option>
                                            <option value="كوستر">كوستر</option>
                                            <option value="هايس">هايس</option>
                                            <option value="H1">H1</option>
                                            <!-- Add other types if needed -->
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="model">الموديل (سنة الصنع)</label>
                                        <input type="number" id="model" name="model" placeholder="مثال: 2015">
                                    </div>
                                     <div class="form-group">
                                        <label for="status">الحالة <span class="required">*</span></label>
                                        <select id="status" name="status" required>
                                            <option value="نشط" selected>نشط</option>
                                            <option value="صيانة">صيانة</option>
                                            <option value="متوقف">متوقف</option>
                                            <!-- Add other statuses if needed -->
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="plate_letters">حروف اللوحة <span class="required">*</span></label>
                                        <!-- Updated attributes for plate_letters -->
                                        <input type="text" id="plate_letters" name="plate_letters" minlength="3" maxlength="5" placeholder="3 إلى 5 أحرف" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="plate_numbers">أرقام اللوحة <span class="required">*</span></label>
                                        <input type="text" id="plate_numbers" name="plate_numbers" maxlength="4" pattern="\d{1,4}" placeholder="مثال: 1234" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                     <div class="form-group">
                                        <label for="notes">ملاحظات</label>
                                        <textarea id="notes" name="notes" rows="3"></textarea>
                                    </div>
                                </div>
                            </fieldset>

                            <div class="form-actions">
                                <button type="submit" class="submit-btn">حفظ</button>
                                <button type="button" id="cancel-edit-btn" class="cancel-btn">إلغاء</button>
                            </div>
                        </form>
                        <div id="form-message" class="message"></div>
                    </div>
                </div>
            </section>
        </div>
    </main>
</body>
</html>
