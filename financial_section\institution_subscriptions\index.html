<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة اشتراكات المنشآت</title> <!-- Renamed -->
    <!-- Shared Styles -->
    <link rel="stylesheet" href="../../shared_styles.css"> <!-- Adjusted path -->
    <!-- Page Specific Styles -->
    <link rel="stylesheet" href="style.css">
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Config -->
    <script src="../../config.js"></script> <!-- Adjusted path -->
    <!-- Auth Script -->
    <script src="../../auth.js"></script> <!-- Adjusted path -->
    <!-- Page Scripts -->
    <script defer src="institution_subscriptions_linker.js"></script> <!-- ربط الملف الجديد أولاً -->
    <script defer src="script.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts (Example: Tajawal) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navbar -->
    <nav class="main-navbar">
        <div class="navbar-brand">
            <i class="fas fa-building-columns"></i> <!-- Changed icon -->
            إدارة اشتراكات المنشآت <!-- Renamed -->
        </div>
        <div class="navbar-user">
            <span id="navbar-username"></span>
            <button id="logout-btn" class="logout-btn" style="display: none;">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </button>
        </div>
    </nav>

    <!-- Main Layout Container -->
    <div class="page-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <h3 class="sidebar-title"><i class="fas fa-building"></i> المنشآت</h3>
            <div class="sidebar-search">
                <input type="text" id="enterprise-search-input" placeholder="بحث عن منشأة...">
            </div>
            <ul id="enterprise-list" class="sidebar-list">
                <!-- Enterprise list items will be loaded here by JS -->
                <li class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i> تحميل...</li>
            </ul>
        </aside>

        <!-- Main Content Area -->
        <div class="main-content-area">
            <div class="container">
                <main class="data-management-main">
                    <!-- Header -->
                    <header class="page-header">
                        <h1>
                            <i class="fas fa-clipboard-list"></i> اشتراكات المنشآت للشهر: <!-- Renamed -->
                            <span id="current-month-year">...</span>
                        </h1>
                        <div id="message-area" class="message" style="display: none;"></div>
                    </header>

                    <!-- Controls and Filters -->
                    <section class="controls-section card">
                        <!-- Removed Enterprise Filter Dropdown -->
                        <div class="filter-group">
                            <label for="status-filter">فلترة حسب الحالة:</label>
                            <select id="status-filter">
                                <option value="">الكل</option>
                                <option value="paid">مدفوع</option>
                                <option value="not_paid">غير مدفوع</option>
                                <option value="partially_paid">مدفوع جزئياً</option>
                            </select>
                        </div>
                        <div class="action-group">
                            <button id="add-subscription-btn" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة اشتراك
                            </button>
                            <button id="refresh-data-btn" class="btn btn-secondary">
                                <i class="fas fa-sync-alt"></i> تحديث
                            </button>
                        </div>
                    </section>

                    <!-- Month Closing Controls Section (NEW) -->
                    <section id="month-closing-section" class="card" style="display: none; margin-top: 15px; margin-bottom:15px; padding: 15px;">
                        <h3 id="month-closing-title">إدارة إقفال الشهر للمؤسسة: <span id="selected-enterprise-for-closing"></span></h3>
                        <p>الشهر الحالي: <strong id="current-month-for-closing"></strong></p>
                        <p>الحالة: <strong id="month-closing-status">جاري التحقق...</strong></p>
                        <div id="month-closing-message" class="message" style="display: none; margin-top: 10px;"></div>
                        <div class="action-group" style="margin-top: 10px;">
                            <button id="close-month-btn" class="btn btn-danger" style="display: none;">
                                <i class="fas fa-lock"></i> إقفال الشهر
                            </button>
                            <button id="reopen-month-btn" class="btn btn-success" style="display: none;">
                                <i class="fas fa-unlock"></i> فتح إقفال الشهر
                            </button>
                        </div>
                    </section>

                    <!-- Enterprise Groups Section (NEW) -->
                    <section id="enterprise-groups-section" class="enterprise-groups-section card" style="display: none;">
                        <h3 id="enterprise-groups-title">مجموعات المنشأة</h3>
                        <div id="enterprise-groups-list" class="groups-list-container">
                            <!-- Groups will be loaded here by JS -->
                            <div class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i> جاري تحميل المجموعات...</div>
                        </div>
                    </section>

                    <!-- Data Table Section -->
                    <section class="data-table-section card">
                        <h3 class="table-title">مجموعات المنشآت ودفعاتها</h3> <!-- Updated title -->
                        <div class="table-container">
                            <table id="subscriptions-table" class="data-table">
                                <thead>
                                    <tr>
                                        <th>المنشأة</th>
                                        <th>المجموعة</th>
                                        <th>الرصيد السابق</th>
                                        <th>تكلفة المجموعة</th>
                                        <th>إجمالي المستحق</th>
                                        <th>المدفوع</th>
                                        <th>المتبقي</th>
                                        <th>حالة الدفع</th>
                                        <th>تاريخ الاشتراك</th>
                                        <th>تاريخ آخر دفعة</th>
                                        <th>حالة الإقفال</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="subscriptions-table-body">
                                    <!-- Data rows will be loaded here by JS -->
                                    <tr>
                                        <td colspan="12" class="loading-placeholder">
                                            <i class="fas fa-spinner fa-spin"></i> جاري تحميل البيانات...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- Pagination Controls -->
                        <div class="pagination-controls">
                            <button id="prev-page-btn" disabled><i class="fas fa-chevron-right"></i> السابق</button>
                            <span id="page-info">صفحة 1 من 1</span>
                            <button id="next-page-btn" disabled>التالي <i class="fas fa-chevron-left"></i></button>
                        </div>
                    </section>
                </main>
            </div>
        </div> <!-- End Main Content Area -->
    </div> <!-- End Page Layout -->

    <!-- Add/Edit Subscription Modal -->
    <div id="subscription-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal-btn" id="close-subscription-modal">&times;</span>
            <h2 id="modal-title">إضافة اشتراك منشأة</h2> <!-- Renamed -->
            <form id="subscription-form">
                <input type="hidden" id="subscription-id">
                <div class="form-group">
                    <label for="modal-enterprise">المنشأة:</label> <!-- Renamed -->
                    <select id="modal-enterprise" required> <!-- Renamed ID -->
                        <option value="" disabled selected>اختر المنشأة...</option> <!-- Renamed -->
                        <!-- Options loaded by JS -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="modal-service-type">نوع الاشتراك:</label>
                    <input type="text" id="modal-service-type" required placeholder="مثال: اشتراك شهري">
                </div>
                <div class="form-group">
                    <label for="modal-total-amount">المبلغ الإجمالي:</label>
                    <input type="number" id="modal-total-amount" step="0.01" required placeholder="0.00">
                </div>
                <div class="form-group">
                    <label for="modal-paid-amount">المبلغ المدفوع:</label>
                    <input type="number" id="modal-paid-amount" step="0.01" placeholder="0.00">
                    <small id="remaining-amount-display" class="form-text text-muted"></small>
                </div>
                <div class="form-group">
                    <label for="modal-subscription-date">تاريخ الاشتراك:</label>
                    <input type="date" id="modal-subscription-date" required>
                </div>
                 <div class="form-group">
                    <label for="modal-payment-date">تاريخ آخر دفعة:</label>
                    <input type="date" id="modal-payment-date">
                </div>
                <div class="form-group">
                    <label for="modal-notes">ملاحظات:</label>
                    <textarea id="modal-notes" rows="3"></textarea>
                </div>
                <div id="modal-message" class="message" style="display: none;"></div>
                <div class="modal-actions">
                    <button type="submit" id="save-subscription-btn" class="modal-btn primary">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                    <button type="button" id="cancel-subscription-btn" class="modal-btn secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Group Details Modal (NEW) -->
    <div id="group-details-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal-btn" id="close-group-details-modal">&times;</span>
            <h2 id="group-details-modal-title">تفاصيل تكلفة المجموعة</h2>
            <div id="group-details-modal-body">
                <!-- Details will be loaded here by JS -->
                <div class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i> جاري تحميل التفاصيل...</div>
            </div>
             <div class="modal-actions">
                <button id="cancel-group-details-btn" class="btn btn-secondary">إغلاق</button>
                <!-- Add payment button later if needed -->
            </div>
        </div>
    </div>
    <!-- End Group Details Modal -->

    <!-- Payment Modal for Group (Institution) -->
    <div id="group-payment-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal-btn" id="close-group-payment-modal">&times;</span>
            <h2 id="group-payment-modal-title"><i class="fas fa-credit-card"></i> تسجيل دفعة لمجموعة: <span id="group-payment-group-name">...</span></h2>
            <form id="group-payment-form">
                <div class="modal-body">
                    <!-- Subscription summary fields -->
                    <div class="form-group">
                        <label for="group-payment-total-amount">الإجمالي المستحق:</label>
                        <input type="text" id="group-payment-total-amount" readonly>
                    </div>
                    <div class="form-group">
                        <label for="group-payment-current-paid">المبلغ المدفوع سابقاً:</label>
                        <input type="text" id="group-payment-current-paid" readonly>
                    </div>
                    <div class="form-group">
                        <label for="group-payment-remaining-amount">المتبقي:</label>
                        <input type="text" id="group-payment-remaining-amount" readonly>
                    </div>
                    <div class="form-group">
                        <label for="group-payment-status">حالة الدفع:</label>
                        <select id="group-payment-status" disabled>
                            <option value="not_paid">غير مدفوع</option>
                            <option value="partially_paid">مدفوع جزئياً</option>
                            <option value="paid">مدفوع</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="group-payment-subscription-date">تاريخ الاشتراك:</label>
                        <input type="date" id="group-payment-subscription-date" readonly>
                    </div>
                    <div id="group-payment-message" class="message" style="display: none;"></div>
                    <input type="hidden" id="group-payment-group-id">
                    <div class="form-group">
                        <label for="group-payment-amount">المبلغ المدفوع <span class="required">*</span></label>
                        <input type="number" id="group-payment-amount" name="amount" min="0" step="0.01" required placeholder="أدخل المبلغ المدفوع">
                    </div>
                    <div class="form-group">
                        <label for="group-payment-date">تاريخ الدفع <span class="required">*</span></label>
                        <input type="date" id="group-payment-date" name="payment_date" required>
                    </div>
                    <div class="form-group"> <!-- حقل اختيار البنك الجديد -->
                        <label for="group-payment-bank">البنك <span class="required">*</span></label>
                        <select id="group-payment-bank" name="payment_bank_id" required>
                            <option value="" disabled selected>اختر البنك...</option>
                            <!-- سيتم تعبئة الخيارات بواسطة JavaScript -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="group-payment-notes">ملاحظات:</label>
                        <textarea id="group-payment-notes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" id="cancel-group-payment-btn" class="control-btn secondary-btn">إلغاء</button>
                    <button type="submit" id="confirm-group-payment-btn" class="control-btn primary-btn">
                        <i class="fas fa-check"></i> تسجيل الدفعة
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- End Payment Modal -->

    <!-- Footer -->
    <footer class="main-footer">
         <a href="../financial_dashboard.html" class="home-btn"> <!-- Adjusted path -->
             <i class="fas fa-tachometer-alt"></i> العودة للوحة المعلومات
         </a>
         <p>&copy; 2024 نظام الإدارة المالية</p>
    </footer>
</body>
</html>
