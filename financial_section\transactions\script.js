// Supabase Initialization
let supabaseUrl, supabaseAnonKey;
if (typeof SUPABASE_URL !== 'undefined') supabaseUrl = SUPABASE_URL;
else console.error('SUPABASE_URL not found.');
if (typeof SUPABASE_ANON_KEY !== 'undefined') supabaseAnonKey = SUPABASE_ANON_KEY;
else console.error('SUPABASE_ANON_KEY not found.');

const { createClient } = supabase;
const _supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('Supabase Initialized for Transactions:', _supabase);

// --- DOM Elements ---
const transactionForm = document.getElementById('transaction-form');
const formMessage = document.getElementById('form-message');
const addTransactionBtn = document.getElementById('add-transaction-btn');
const addTransactionSection = document.getElementById('add-transaction-section');
const cancelEditBtn = document.getElementById('cancel-edit-btn');
const closeFormBtn = document.getElementById('close-form-btn');
const transactionsTableBody = document.getElementById('transactions-tbody');
const listMessage = document.getElementById('list-message');
const transactionIdField = document.getElementById('transaction_id');
const formTitle = document.getElementById('form-title');
const transactionsCountBadge = document.getElementById('transactions-count');
const paginationControls = document.getElementById('pagination-controls');

// Filter elements
const filterBankSelect = document.getElementById('filter-bank');
const filterTypeSelect = document.getElementById('filter-type');
const filterSourceSelect = document.getElementById('filter-source');
const filterStartDateInput = document.getElementById('filter-start-date');
const filterEndDateInput = document.getElementById('filter-end-date');
const filterBtn = document.getElementById('filter-btn');
const resetFilterBtn = document.getElementById('reset-filter-btn');

// Form elements
const formBankSelect = document.getElementById('bank_id');
const formTransactionDate = document.getElementById('transaction_date');
const formTransactionType = document.getElementById('transaction_type');
const formAmount = document.getElementById('amount');
const formDescription = document.getElementById('description');
const formReferenceCode = document.getElementById('reference_code');
const formSourceType = document.getElementById('transaction_source_type'); // Hidden

// --- State ---
let currentTransactions = [];
let editMode = false;
let availableBanks = []; // To store fetched banks for dropdowns

// Pagination variables
let currentPage = 1;
const itemsPerPage = 15;
let totalItems = 0;

// --- Functions ---

// Function to display messages
const showMessage = (element, message, type = 'info', duration = 5000) => {
    if (!element) return;
    element.textContent = message;
    element.className = `message ${type} show`;
    if (type === 'success' || duration > 0) {
        setTimeout(() => { element.style.display = 'none'; element.classList.remove('show'); }, duration);
    } else {
        element.style.display = 'block';
    }
};

// Function to format currency
const formatCurrency = (amount) => {
    if (amount === null || amount === undefined) return '0.00';
    return parseFloat(amount).toFixed(2);
};

// Function to format date (YYYY-MM-DD)
const formatDate = (dateString) => {
    if (!dateString) return '';
    try {
        const date = new Date(dateString);
        // Adjust for timezone offset to prevent date shifting
        const offset = date.getTimezoneOffset();
        const adjustedDate = new Date(date.getTime() - (offset*60*1000));
        return adjustedDate.toISOString().split('T')[0];
    } catch (e) {
        console.error("Error formatting date:", dateString, e);
        return dateString;
    }
};

// Function to translate transaction type
const translateType = (type) => {
    switch (type) {
        case 'deposit': return 'إيداع';
        case 'withdrawal': return 'سحب';
        default: return type || '';
    }
};

// Function to translate transaction source
const translateSource = (source) => {
    switch (source) {
        case 'manual': return 'يدوي';
        case 'subscription': return 'اشتراك طالب';
        case 'driver_expense': return 'مصروف سائق';
        case 'driver_salary': return 'راتب سائق';
        case 'bus_expense': return 'مصروف حافلة';
        // Add other sources as needed
        default: return source || 'غير محدد';
    }
};

// Function to render pagination controls
const renderPaginationControls = () => {
    if (!paginationControls || totalItems <= itemsPerPage) {
        if (paginationControls) paginationControls.innerHTML = '';
        return;
    }
    paginationControls.innerHTML = '';
    const totalPages = Math.ceil(totalItems / itemsPerPage);

    // Previous button
    const prevButton = document.createElement('button');
    prevButton.innerHTML = '<i class="fas fa-angle-right"></i>';
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', () => goToPage(currentPage - 1));
    paginationControls.appendChild(prevButton);

    // Page buttons logic (simplified)
    const maxPageButtons = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
    let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);
    if (endPage - startPage + 1 < maxPageButtons) {
        startPage = Math.max(1, endPage - maxPageButtons + 1);
    }

    if (startPage > 1) {
        const firstPageBtn = document.createElement('button');
        firstPageBtn.textContent = '1';
        firstPageBtn.addEventListener('click', () => goToPage(1));
        paginationControls.appendChild(firstPageBtn);
        if (startPage > 2) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'pagination-ellipsis';
            paginationControls.appendChild(ellipsis);
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.textContent = i.toString();
        pageBtn.className = i === currentPage ? 'active' : '';
        pageBtn.addEventListener('click', () => goToPage(i));
        paginationControls.appendChild(pageBtn);
    }

     if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
             const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'pagination-ellipsis';
            paginationControls.appendChild(ellipsis);
        }
        const lastPageBtn = document.createElement('button');
        lastPageBtn.textContent = totalPages.toString();
        lastPageBtn.addEventListener('click', () => goToPage(totalPages));
        paginationControls.appendChild(lastPageBtn);
    }

    // Next button
    const nextButton = document.createElement('button');
    nextButton.innerHTML = '<i class="fas fa-angle-left"></i>';
    nextButton.disabled = currentPage === totalPages;
    nextButton.addEventListener('click', () => goToPage(currentPage + 1));
    paginationControls.appendChild(nextButton);
};

// Function to navigate to a specific page
const goToPage = (page) => {
    currentPage = page;
    fetchTransactions(); // Re-fetch data for the new page
};


// Function to render the transactions table
const renderTransactionsTable = (transactions) => {
    if (!transactionsTableBody) return;
    transactionsTableBody.innerHTML = '';
    if (listMessage) listMessage.style.display = 'none';

    if (!transactions || transactions.length === 0) {
        transactionsTableBody.innerHTML = `<tr><td colspan="7" class="loading-message">لا توجد معاملات تطابق البحث أو الفلترة.</td></tr>`;
        return;
    }

    transactions.forEach(tx => {
        const row = document.createElement('tr');
        const typeClass = tx.transaction_type === 'deposit' ? 'transaction-deposit' : 'transaction-withdrawal';
        // Find bank name - using 'name' column as specified
        const bankName = availableBanks.find(b => b.id === tx.bank_id)?.name || 'غير محدد'; // Updated column name to 'name'
        const isManual = tx.transaction_source_type === 'manual';

        row.innerHTML = `
            <td>${formatDate(tx.transaction_date)}</td>
            <td>${bankName}</td>
            <td><span class="${typeClass}">${translateType(tx.transaction_type)}</span></td>
            <td>${formatCurrency(tx.amount)}</td>
            <td>${translateSource(tx.transaction_source_type)}</td>
            <td>${tx.description || tx.reference_code || ''}</td>
            <td>
                <button class="action-btn edit-btn" data-id="${tx.id}" title="تعديل" ${!isManual ? 'disabled' : ''}>
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" data-id="${tx.id}" title="حذف" ${!isManual ? 'disabled' : ''}>
                    <i class="fas fa-trash-alt"></i>
                </button>
            </td>
        `;

        // Add event listeners only for manual transactions
        if (isManual) {
            const editBtn = row.querySelector('.edit-btn');
            const deleteBtn = row.querySelector('.delete-btn');
            if (editBtn) {
                editBtn.addEventListener('click', () => handleEditTransaction(tx));
            }
            if (deleteBtn) {
                deleteBtn.addEventListener('click', () => handleDeleteTransaction(tx.id));
            }
        }

        transactionsTableBody.appendChild(row);
    });
};

// Function to fetch transactions from Supabase with filtering and pagination
const fetchTransactions = async () => {
    if (!transactionsTableBody) return;
    transactionsTableBody.innerHTML = `<tr><td colspan="7" class="loading-message">جاري تحميل المعاملات...</td></tr>`;
    if (paginationControls) paginationControls.innerHTML = ''; // Clear pagination

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage - 1;

    try {
        let query = _supabase
            .from('bank_transactions')
            .select('*', { count: 'exact' }); // Get total count for pagination

        // Apply filters
        const bankFilter = filterBankSelect?.value;
        const typeFilter = filterTypeSelect?.value;
        const sourceFilter = filterSourceSelect?.value;
        const startDateFilter = filterStartDateInput?.value;
        const endDateFilter = filterEndDateInput?.value;

        if (bankFilter) query = query.eq('bank_id', bankFilter);
        if (typeFilter) query = query.eq('transaction_type', typeFilter);
        if (sourceFilter) query = query.eq('transaction_source_type', sourceFilter);
        if (startDateFilter) query = query.gte('transaction_date', startDateFilter);
        if (endDateFilter) query = query.lte('transaction_date', endDateFilter);

        // Apply ordering and pagination
        query = query.order('transaction_date', { ascending: false }) // Show newest first
                     .order('created_at', { ascending: false }) // Secondary sort
                     .range(startIndex, endIndex);

        const { data, error, count } = await query;

        if (error) {
            console.error('Supabase Fetch Transactions Error:', error);
            showMessage(listMessage, `خطأ في جلب المعاملات: ${error.message}`, 'error', 0);
            transactionsTableBody.innerHTML = `<tr><td colspan="7" class="loading-message">خطأ في تحميل المعاملات.</td></tr>`;
            totalItems = 0;
        } else {
            console.log('Fetched transactions:', data);
            currentTransactions = data || [];
            totalItems = count || 0;
            if (transactionsCountBadge) transactionsCountBadge.textContent = totalItems;
            renderTransactionsTable(currentTransactions);
            renderPaginationControls(); // Render pagination based on total count
            if (totalItems === 0) {
                 showMessage(listMessage, 'لا توجد معاملات تطابق الفلاتر المحددة.', 'info');
            }
        }
    } catch (error) {
        console.error('JavaScript Fetch Transactions Error:', error);
        showMessage(listMessage, `حدث خطأ غير متوقع: ${error.message}`, 'error', 0);
        transactionsTableBody.innerHTML = `<tr><td colspan="7" class="loading-message">خطأ غير متوقع.</td></tr>`;
        totalItems = 0;
    }
};

// Function to fetch available banks for dropdowns
const fetchBanks = async () => {
    try {
        const { data, error } = await _supabase
            .from('banks') // Assuming table name is 'banks'
            .select('id, name') // Updated column name to 'name'
            .order('name', { ascending: true }); // Order by 'name'

        if (error) {
            console.error('Error fetching banks:', error);
            // Handle error display if needed
        } else {
            availableBanks = data || [];
            // Populate filter dropdown
            if (filterBankSelect) {
                filterBankSelect.innerHTML = '<option value="">الكل</option>'; // Reset
                availableBanks.forEach(bank => {
                    const option = document.createElement('option');
                    option.value = bank.id;
                    option.textContent = bank.name; // Use 'name'
                    filterBankSelect.appendChild(option);
                });
            }
            // Populate form dropdown
            if (formBankSelect) {
                formBankSelect.innerHTML = '<option value="">اختر البنك...</option>'; // Reset
                availableBanks.forEach(bank => {
                    const option = document.createElement('option');
                    option.value = bank.id;
                    option.textContent = bank.name; // Use 'name'
                    formBankSelect.appendChild(option);
                });
            }
        }
    } catch (error) {
        console.error('JS Error fetching banks:', error);
    }
};


// Function to handle form submission (Add/Update Manual Transaction)
const handleFormSubmit = async (event) => {
    event.preventDefault();
    if (!transactionForm || !formMessage) return;

    showMessage(formMessage, 'جاري حفظ المعاملة...', 'info');
    const submitBtn = transactionForm.querySelector('.submit-btn');
    if (submitBtn) submitBtn.disabled = true;

    const formData = new FormData(transactionForm);
    const transactionData = {};
    formData.forEach((value, key) => {
        transactionData[key] = value.trim() === '' ? null : value.trim();
    });

    const transactionId = transactionData.transaction_id;

    // Basic validation
    if (!transactionData.transaction_date || !transactionData.bank_id || !transactionData.transaction_type || !transactionData.amount) {
        showMessage(formMessage, 'الرجاء ملء جميع الحقول المطلوبة (*).', 'error');
        if (submitBtn) submitBtn.disabled = false;
        return;
    }

    // Prepare data for Supabase
    const dataToUpsert = {
        transaction_date: transactionData.transaction_date,
        bank_id: transactionData.bank_id,
        transaction_type: transactionData.transaction_type,
        amount: parseFloat(transactionData.amount),
        description: transactionData.description,
        reference_code: transactionData.reference_code,
        transaction_source_type: 'manual', // Always manual for this form
        // reference_id will be null for manual transactions
    };

    try {
        let result;
        if (editMode && transactionId) {
            // Update existing manual transaction
            result = await _supabase
                .from('bank_transactions')
                .update(dataToUpsert)
                .eq('id', transactionId)
                .eq('transaction_source_type', 'manual') // Ensure we only update manual ones
                .select();
        } else {
            // Insert new manual transaction
            result = await _supabase
                .from('bank_transactions')
                .insert([dataToUpsert])
                .select();
        }

        const { data, error } = result;

        if (error) {
            console.error('Supabase Save Transaction Error:', error);
            showMessage(formMessage, `خطأ في حفظ المعاملة: ${error.message}`, 'error');
        } else if (data && data.length > 0) {
            console.log('Supabase Save Transaction Success:', data);
            showMessage(formMessage, `تم ${editMode ? 'تحديث' : 'إضافة'} المعاملة بنجاح!`, 'success');
            setTimeout(() => {
                toggleTransactionModal(false);
            }, 1500);
            fetchTransactions(); // Refresh list
        } else if (editMode) {
             showMessage(formMessage, 'لم يتم العثور على المعاملة للتحديث أو أنها ليست معاملة يدوية.', 'warning');
        }
         else {
             showMessage(formMessage, 'حدث خطأ غير متوقع أثناء الحفظ.', 'error');
         }
    } catch (error) {
        console.error('JavaScript Save Transaction Error:', error);
        showMessage(formMessage, `حدث خطأ غير متوقع: ${error.message}`, 'error');
    } finally {
        if (submitBtn) submitBtn.disabled = false;
    }
};

// Function to reset the transaction form
const resetForm = () => {
    editMode = false;
    if (transactionForm) transactionForm.reset();
    if (transactionIdField) transactionIdField.value = '';
    if (formMessage) formMessage.style.display = 'none';
    if (formTitle) formTitle.textContent = 'إضافة معاملة يدوية';
    const submitBtn = transactionForm?.querySelector('.submit-btn');
    if (submitBtn) submitBtn.textContent = 'حفظ المعاملة';
    if (formSourceType) formSourceType.value = 'manual'; // Ensure source is manual
    // Set default date to today
    if (formTransactionDate) formTransactionDate.value = formatDate(new Date());
};

// Function to populate form for editing a manual transaction
const handleEditTransaction = (tx) => {
    if (!tx || tx.transaction_source_type !== 'manual' || !transactionForm) return;
    toggleTransactionModal(true);
    editMode = true;

    if (formTitle) formTitle.textContent = 'تعديل معاملة يدوية';
    const submitBtn = transactionForm.querySelector('.submit-btn');
    if (submitBtn) submitBtn.textContent = 'تحديث المعاملة';

    // Populate form fields
    if (transactionIdField) transactionIdField.value = tx.id || '';
    if (formTransactionDate) formTransactionDate.value = formatDate(tx.transaction_date);
    if (formBankSelect) formBankSelect.value = tx.bank_id || '';
    if (formTransactionType) formTransactionType.value = tx.transaction_type || '';
    if (formAmount) formAmount.value = tx.amount || '';
    if (formDescription) formDescription.value = tx.description || '';
    if (formReferenceCode) formReferenceCode.value = tx.reference_code || '';
    if (formSourceType) formSourceType.value = 'manual'; // Ensure source is manual

    setTimeout(() => formTransactionDate?.focus(), 100);
};

// Function to handle deleting a manual transaction
const handleDeleteTransaction = async (transactionId) => {
    const confirmDelete = confirm(`هل أنت متأكد من حذف هذه المعاملة اليدوية؟ لا يمكن التراجع عن هذا الإجراء.`);

    if (confirmDelete) {
        try {
            const { error } = await _supabase
                .from('bank_transactions')
                .delete()
                .eq('id', transactionId)
                .eq('transaction_source_type', 'manual'); // Ensure we only delete manual ones

            if (error) {
                console.error('Supabase Delete Transaction Error:', error);
                showMessage(listMessage, `خطأ في حذف المعاملة: ${error.message}`, 'error');
            } else {
                console.log('Manual transaction deleted:', transactionId);
                showMessage(listMessage, `تم حذف المعاملة اليدوية بنجاح.`, 'success');
                fetchTransactions(); // Refresh the list
            }
        } catch (error) {
            console.error('JavaScript Delete Transaction Error:', error);
            showMessage(listMessage, `حدث خطأ غير متوقع أثناء الحذف: ${error.message}`, 'error');
        }
    }
};

// Function to handle applying filters
const handleApplyFilters = () => {
    currentPage = 1; // Reset to first page when applying filters
    fetchTransactions();
};

// Function to handle resetting filters
const handleResetFilters = () => {
    if (filterBankSelect) filterBankSelect.value = '';
    if (filterTypeSelect) filterTypeSelect.value = '';
    if (filterSourceSelect) filterSourceSelect.value = '';
    if (filterStartDateInput) filterStartDateInput.value = '';
    if (filterEndDateInput) filterEndDateInput.value = '';
    currentPage = 1; // Reset to first page
    fetchTransactions();
};


// Function to toggle the transaction modal
function toggleTransactionModal(show = true) {
    if (!addTransactionSection) return;
    if (show) {
        resetForm();
        addTransactionSection.classList.add('show');
        setTimeout(() => formTransactionDate?.focus(), 100);
        document.body.style.overflow = 'hidden';
    } else {
        addTransactionSection.classList.remove('show');
        document.body.style.overflow = '';
    }
}

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOM loaded, initializing transactions page...');

    // Fetch banks for dropdowns
    await fetchBanks();

    // Fetch initial transactions data (page 1, no filters)
    await fetchTransactions();

    // Setup event listeners
    setupEventListeners();
});


// --- Event Listeners ---
const setupEventListeners = () => {
    if (transactionForm) {
        transactionForm.addEventListener('submit', handleFormSubmit);
    }
    if (addTransactionBtn) {
        addTransactionBtn.addEventListener('click', () => toggleTransactionModal(true));
    }
    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', () => toggleTransactionModal(false));
    }
    if (closeFormBtn) {
        closeFormBtn.addEventListener('click', () => toggleTransactionModal(false));
    }
    if (addTransactionSection) {
        addTransactionSection.addEventListener('click', (event) => {
            if (event.target === addTransactionSection) toggleTransactionModal(false);
        });
    }
    if (filterBtn) {
        filterBtn.addEventListener('click', handleApplyFilters);
    }
    if (resetFilterBtn) {
        resetFilterBtn.addEventListener('click', handleResetFilters);
    }
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && addTransactionSection && addTransactionSection.classList.contains('show')) {
            toggleTransactionModal(false);
        }
    });
};
