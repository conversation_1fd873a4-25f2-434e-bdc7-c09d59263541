/* Inherit base styles */
@import url('../../shared_styles.css'); /* Adjusted path */

/* Page Layout with Sidebar */
.page-layout {
    display: flex;
    min-height: calc(100vh - var(--navbar-height) - var(--footer-height)); /* Full height minus nav/footer */
}

.sidebar {
    width: 280px; /* Sidebar width */
    background-color: #f8f9fa; /* Light background */
    border-left: 1px solid var(--border-color); /* Border on the left for RTL */
    padding: 20px 15px;
    overflow-y: auto; /* Enable scrolling if list is long */
    flex-shrink: 0; /* Prevent sidebar from shrinking */
    display: flex;
    flex-direction: column;
}

.sidebar-title {
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.sidebar-search {
    margin-bottom: 15px;
}

.sidebar-search input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.9rem;
}

.sidebar-list {
    list-style: none;
    padding: 0;
    margin: 0;
    flex-grow: 1; /* Allow list to take remaining space */
    overflow-y: auto; /* Scroll within the list if needed */
}

.sidebar-list li {
    padding: 10px 15px;
    margin-bottom: 5px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease, color 0.2s ease;
    font-size: 0.95rem;
    color: var(--secondary-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.sidebar-list li:hover {
    background-color: #e9ecef;
}

.sidebar-list li.active {
    background-color: var(--primary-color);
    color: #fff;
    font-weight: 500;
}

.sidebar-list li.active i {
    color: #fff; /* Ensure icon color matches text */
}

.sidebar-list li i {
    color: var(--primary-color);
    font-size: 0.9em;
    width: 16px; /* Fixed width for alignment */
    text-align: center;
}

.sidebar-list .loading-placeholder {
    text-align: center;
    color: var(--text-muted);
    padding: 20px;
    cursor: default;
}
.sidebar-list .loading-placeholder:hover {
    background-color: transparent;
}


.main-content-area {
    flex-grow: 1; /* Take remaining width */
    overflow-y: auto; /* Allow content to scroll independently */
    padding: 0; /* Remove padding if container inside handles it */
}

/* Adjust container padding if needed */
.main-content-area .container {
     padding-top: 0; /* Remove top padding if header is inside */
     max-width: none; /* Allow container to fill the area */
     padding-right: 20px; /* Add padding to separate from sidebar */
     padding-left: 20px;
}


/* Page specific styles */
.data-management-main {
    padding-top: 0; /* Removed top padding */
}

.page-header {
    text-align: center;
    margin-bottom: 25px;
}

.page-header h1 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

#current-month-year {
    color: var(--accent-color);
    font-weight: bold;
}

.controls-section {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    padding: 15px 20px;
    margin-bottom: 25px;
    align-items: flex-end; /* Align items to bottom */
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    min-width: 180px;
}

.action-group {
    margin-right: auto; /* Push action buttons to the right */
    display: flex;
    gap: 10px;
}

.data-table-section {
    padding: 0; /* Remove padding to allow table to fill */
    overflow: hidden; /* Contain table borders */
}

.table-container {
    max-height: 60vh; /* Limit table height and enable scrolling */
    overflow-y: auto;
    border-bottom: 1px solid var(--border-color);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 10px 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
    white-space: nowrap; /* Prevent wrapping initially */
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 1;
}

.data-table tbody tr:hover {
    background-color: #f1f5f9;
}

.data-table td:last-child { /* Actions column */
    white-space: nowrap;
}

.data-table .action-btn {
    margin-left: 8px;
    padding: 4px 8px;
    font-size: 0.8rem;
}

.loading-placeholder td,
.no-data-placeholder td {
    text-align: center;
    padding: 30px;
    color: var(--text-muted);
    font-size: 1.1rem;
}

.pagination-controls {
    padding: 15px;
    text-align: center;
}

.pagination-controls button {
    margin: 0 10px;
}

.pagination-controls span {
    color: var(--text-muted);
}

/* Status Badges */
.status-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    color: #fff;
    white-space: nowrap;
}
.status-paid { background-color: var(--success-color); }
.status-not_paid { background-color: var(--danger-color); }
.status-partially_paid { background-color: var(--warning-color); color: #333; }

/* Modal Styles (Inherited from shared, can add specifics) */
#subscription-modal .modal-content {
    max-width: 550px; /* Slightly wider modal */
}

#subscription-form {
    text-align: right;
}

#subscription-form .form-group {
    margin-bottom: 15px;
}

#subscription-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

#subscription-form input[type="text"],
#subscription-form input[type="number"],
#subscription-form input[type="date"],
#subscription-form select,
#subscription-form textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
}

#subscription-form textarea {
    resize: vertical;
}

/* Adjust controls section - remove margin-right: auto if filters are fewer */
.controls-section .action-group {
    margin-right: 0; /* Reset margin */
    margin-left: auto; /* Push actions to the left in RTL */
}

/* Enterprise Groups Section */
.enterprise-groups-section {
    margin-bottom: 30px;
    padding: 25px;
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

.enterprise-groups-section h3 {
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.groups-list-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 24px;
    margin-bottom: 0;
}

.group-card {
    background: #fff;
    border: 1.5px solid var(--border-color);
    border-radius: 14px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    padding: 22px 20px 18px 20px;
    font-size: 1rem;
    display: flex;
    flex-direction: column;
    min-height: 260px;
    transition: box-shadow 0.2s, transform 0.2s;
    position: relative;
    cursor: pointer; /* Make it clear the card is clickable */
}

.group-card:hover {
    box-shadow: 0 6px 24px rgba(52, 152, 219, 0.10);
    transform: translateY(-4px) scale(1.02);
    border-color: var(--primary-color);
}

.group-card h4 {
    margin-top: 0;
    margin-bottom: 18px;
    font-size: 1.15rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 700;
    letter-spacing: 0.5px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 10px;
}

.group-card h4 i {
    color: var(--accent-color);
    font-size: 1.3em;
}

.group-card p {
    margin-bottom: 10px;
    color: var(--text-dark);
    line-height: 1.7;
    font-size: 0.97rem;
}

.group-card p strong {
    color: var(--secondary-color);
    margin-left: 5px;
    font-weight: 600;
    display: inline-block; /* Ensure label and value/button align well */
    min-width: 120px; /* Adjust as needed for alignment */
}

/* Style for the location link button */
.location-link-btn {
    display: inline-block;
    padding: 4px 10px;
    background-color: var(--accent-color);
    color: #fff;
    border-radius: 4px;
    text-decoration: none;
    font-size: 0.85rem;
    transition: background-color 0.2s ease;
    margin-right: 5px; /* Space before the time */
}

.location-link-btn:hover {
    background-color: #0056b3; /* Darker shade of accent */
    color: #fff;
}

.location-link-btn i {
    margin-left: 5px;
}

.group-card hr {
    border: none;
    border-top: 1px dashed var(--border-color);
    margin: 12px 0;
}

.group-card .cost-details {
    margin-top: 12px;
    padding: 13px 10px 10px 10px;
    border-top: 1px solid #eee;
    background: #f8f9fa;
    border-radius: 0 0 10px 10px;
    font-weight: 500;
    font-size: 0.97rem;
}

.group-card .cost-details span {
    display: block;
    margin-bottom: 7px;
    font-size: 0.97rem;
}
.group-card .cost-details span:last-child {
    margin-bottom: 0;
}
.group-card .cost-details span strong {
    color: var(--primary-color);
    width: 140px;
    display: inline-block;
}

.group-card .notes {
    margin-top: 14px;
    font-style: italic;
    color: var(--text-muted);
    background: #f6f6f6;
    border-radius: 6px;
    padding: 8px 10px;
    font-size: 0.93rem;
}

/* Loading and empty states */
.enterprise-groups-section .loading-placeholder,
.enterprise-groups-section .no-data-placeholder {
    grid-column: 1 / -1;
    text-align: center;
    padding: 32px 20px;
    font-size: 1.1rem;
    color: var(--text-muted);
    background: #f8f9fa;
    border-radius: var(--border-radius);
}

/* Table Title */
.data-table-section h3.table-title {
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 15px;
}

/* Group Details Modal Styles */
#group-details-modal .modal-content {
    max-width: 500px; /* Adjust width as needed */
}

#group-details-modal-body {
    padding: 15px 0;
    margin-bottom: 20px;
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
}

#group-details-modal-body p {
    margin-bottom: 12px;
    font-size: 1rem;
    color: var(--text-dark);
    line-height: 1.6;
}

#group-details-modal-body p strong {
    display: inline-block;
    min-width: 150px; /* Label width */
    color: var(--secondary-color);
    margin-left: 10px;
}

#group-details-modal-body .loading-placeholder {
    text-align: center;
    padding: 20px;
    color: var(--text-muted);
}

/* Center all modals (including group details modal) */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex; /* Flex to center content */
}

.modal-content {
    background-color: #fff;
    margin: auto;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    width: 90%;
    max-width: 500px;
    text-align: center;
    position: relative;
    animation: fadeInModal 0.3s ease-out;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

@keyframes fadeInModal {
    from { opacity: 0; transform: translateY(-20px);}
    to { opacity: 1; transform: translateY(0);}
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .sidebar {
        width: 240px;
    }
}

@media (max-width: 768px) {
    .page-layout {
        flex-direction: column; /* Stack sidebar and content */
    }
    .sidebar {
        width: 100%; /* Full width */
        height: auto; /* Adjust height */
        max-height: 40vh; /* Limit height */
        border-left: none;
        border-bottom: 1px solid var(--border-color);
    }
    .main-content-area .container {
        padding-right: 15px; /* Adjust padding */
        padding-left: 15px;
    }
}

/* أنماط عرض المبلغ المتبقي */
#remaining-amount-display {
    font-size: 0.85rem;
    font-weight: 500;
    margin-top: 5px;
    padding: 5px 8px;
    border-radius: 4px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

#remaining-amount-display:not(:empty) {
    display: block;
}

#remaining-amount-display:empty {
    display: none;
}

/* تأثير بصري عند تحديث المبلغ الإجمالي */
#modal-total-amount.auto-updated {
    background-color: #e8f5e8 !important;
    border-color: #28a745 !important;
    transition: all 0.3s ease;
}

/* أنماط المجموعات المقفلة */
.closed-group {
    background-color: #f8f9fa !important;
    opacity: 0.7;
}

.closed-group td {
    color: #6c757d;
}

/* أنماط حالة الإقفال */
.status-badge.closed {
    background-color: #dc3545;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.open {
    background-color: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* أزرار إدارة المجموعات */
.action-btn.close-btn {
    background-color: #dc3545;
    color: white;
}

.action-btn.close-btn:hover {
    background-color: #c82333;
}

.action-btn.reopen-btn {
    background-color: #28a745;
    color: white;
}

.action-btn.reopen-btn:hover {
    background-color: #218838;
}

.action-btn.view-btn {
    background-color: #17a2b8;
    color: white;
}

.action-btn.view-btn:hover {
    background-color: #138496;
}
