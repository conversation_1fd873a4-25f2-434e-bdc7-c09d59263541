-- إصلا<PERSON> العلاقة بين جدول السجلات وجدول المعاملات البنكية
-- المشكلة: العلاقة معكوسة، يجب أن تكون bank_transactions تشير إلى financial_transactions_log

-- الخطوة 1: إزالة التريجرات الحالية المتضاربة
DROP TRIGGER IF EXISTS auto_cleanup_reversed_trigger ON public.financial_transactions_log;
DROP TRIGGER IF EXISTS financial_log_sync_trigger ON public.financial_transactions_log;
DROP TRIGGER IF EXISTS sync_financial_log_to_bank_transactions_trigger ON public.financial_transactions_log;
DROP TRIGGER IF EXISTS student_payments_trigger ON public.student_payments;

-- الخطوة 2: تنظيف البيانات المكررة في bank_transactions
WITH duplicates AS (
    SELECT id, 
           ROW_NUMBER() OVER (
               PARTITION BY reference_table, reference_id 
               ORDER BY created_at DESC, id DESC
           ) as rn
    FROM public.bank_transactions 
    WHERE reference_table = 'student_payments' 
      AND reference_id IS NOT NULL
)
DELETE FROM public.bank_transactions 
WHERE id IN (
    SELECT id FROM duplicates WHERE rn > 1
);

-- الخطوة 3: إنشاء دالة محسنة لمعالجة دفعات الطلاب
CREATE OR REPLACE FUNCTION public.handle_student_payment_changes()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_description text;
    v_log_id uuid;
    v_old_log_id uuid;
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- إنشاء سجل جديد في financial_transactions_log
        v_description := 'Student payment: ' || NEW.student_id::text;
        
        INSERT INTO public.financial_transactions_log (
            transaction_date, amount, transaction_type, description,
            source_table, source_record_id, bank_id, budget_month_id, is_reversal
        )
        VALUES (
            NEW.payment_date, NEW.amount, 'deposit', v_description,
            'student_payments', NEW.id::text, NEW.bank_id, NEW.budget_month_id, false
        )
        RETURNING id INTO v_log_id;
        
        -- إنشاء معاملة بنكية مرتبطة بالسجل
        INSERT INTO public.bank_transactions (
            bank_id, amount, transaction_type, transaction_date, description,
            transaction_source, reference_table, reference_id,
            budget_month_id, financial_transaction_log_id
        )
        VALUES (
            NEW.bank_id, NEW.amount, 'deposit', NEW.payment_date, v_description,
            'student_payments-' || NEW.id::text, 'student_payments', NEW.id,
            NEW.budget_month_id, v_log_id
        );
        
        RAISE NOTICE 'Created payment log % and bank transaction for payment %', v_log_id, NEW.id;
        
    ELSIF TG_OP = 'UPDATE' THEN
        -- التحقق من وجود تغييرات مهمة
        IF (OLD.amount IS DISTINCT FROM NEW.amount OR 
            OLD.bank_id IS DISTINCT FROM NEW.bank_id OR 
            OLD.payment_date IS DISTINCT FROM NEW.payment_date) THEN
            
            -- العثور على السجل القديم في financial_transactions_log
            SELECT id INTO v_old_log_id
            FROM public.financial_transactions_log
            WHERE source_table = 'student_payments' 
              AND source_record_id = OLD.id::text
              AND is_reversal = false
            ORDER BY created_at DESC
            LIMIT 1;
            
            -- إنشاء سجل عكسي للمعاملة القديمة
            IF v_old_log_id IS NOT NULL THEN
                v_description := 'Reversal for student payment: ' || OLD.student_id::text;
                
                INSERT INTO public.financial_transactions_log (
                    transaction_date, amount, transaction_type, description,
                    source_table, source_record_id, bank_id, budget_month_id, 
                    is_reversal, original_log_entry_id
                )
                VALUES (
                    OLD.payment_date, OLD.amount, 'withdrawal', v_description,
                    'student_payments', OLD.id::text, OLD.bank_id, OLD.budget_month_id, 
                    true, v_old_log_id
                );
                
                -- إلغاء ربط المعاملة البنكية القديمة (جعلها غير مربوطة)
                UPDATE public.bank_transactions 
                SET financial_transaction_log_id = NULL,
                    description = description || ' (تم التعديل)'
                WHERE financial_transaction_log_id = v_old_log_id;
                
                RAISE NOTICE 'Created reversal entry and unlinked bank transaction for old payment %', OLD.id;
            END IF;
            
            -- إنشاء سجل جديد للمعاملة المحدثة
            v_description := 'Student payment (updated): ' || NEW.student_id::text;
            
            INSERT INTO public.financial_transactions_log (
                transaction_date, amount, transaction_type, description,
                source_table, source_record_id, bank_id, budget_month_id, is_reversal
            )
            VALUES (
                NEW.payment_date, NEW.amount, 'deposit', v_description,
                'student_payments', NEW.id::text, NEW.bank_id, NEW.budget_month_id, false
            )
            RETURNING id INTO v_log_id;
            
            -- إنشاء معاملة بنكية جديدة مرتبطة بالسجل الجديد
            INSERT INTO public.bank_transactions (
                bank_id, amount, transaction_type, transaction_date, description,
                transaction_source, reference_table, reference_id,
                budget_month_id, financial_transaction_log_id
            )
            VALUES (
                NEW.bank_id, NEW.amount, 'deposit', NEW.payment_date, v_description,
                'student_payments-' || NEW.id::text, 'student_payments', NEW.id,
                NEW.budget_month_id, v_log_id
            );
            
            RAISE NOTICE 'Created new payment log % and bank transaction for updated payment %', v_log_id, NEW.id;
        END IF;
        
    ELSIF TG_OP = 'DELETE' THEN
        -- العثور على السجل في financial_transactions_log
        SELECT id INTO v_old_log_id
        FROM public.financial_transactions_log
        WHERE source_table = 'student_payments' 
          AND source_record_id = OLD.id::text
          AND is_reversal = false
        ORDER BY created_at DESC
        LIMIT 1;
        
        -- إنشاء سجل عكسي للحذف
        IF v_old_log_id IS NOT NULL THEN
            v_description := 'Deletion reversal for student payment: ' || OLD.student_id::text;
            
            INSERT INTO public.financial_transactions_log (
                transaction_date, amount, transaction_type, description,
                source_table, source_record_id, bank_id, budget_month_id, 
                is_reversal, original_log_entry_id
            )
            VALUES (
                OLD.payment_date, OLD.amount, 'withdrawal', v_description,
                'student_payments', OLD.id::text, OLD.bank_id, OLD.budget_month_id, 
                true, v_old_log_id
            );
            
            -- إلغاء ربط المعاملة البنكية (جعلها غير مربوطة)
            UPDATE public.bank_transactions 
            SET financial_transaction_log_id = NULL,
                description = description || ' (تم الحذف)'
            WHERE financial_transaction_log_id = v_old_log_id;
            
            RAISE NOTICE 'Created deletion reversal and unlinked bank transaction for deleted payment %', OLD.id;
        END IF;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$;

-- الخطوة 4: إنشاء التريجر الجديد
CREATE TRIGGER student_payments_enhanced_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.student_payments
FOR EACH ROW EXECUTE FUNCTION public.handle_student_payment_changes();

-- الخطوة 5: دالة لتنظيف المعاملات البنكية غير المربوطة
CREATE OR REPLACE FUNCTION public.cleanup_unlinked_bank_transactions()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    -- حذف المعاملات البنكية غير المربوطة والقديمة (أكثر من يوم)
    DELETE FROM public.bank_transactions 
    WHERE reference_table = 'student_payments'
      AND financial_transaction_log_id IS NULL
      AND created_at < NOW() - INTERVAL '1 day';
    
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
    
    RAISE NOTICE 'Deleted % unlinked old bank transactions', v_deleted_count;
    
    RETURN v_deleted_count;
END;
$$;

-- الخطوة 6: دالة للتحقق من صحة الربط
CREATE OR REPLACE FUNCTION public.verify_correct_linking()
RETURNS TABLE(
    payment_id UUID,
    payment_amount NUMERIC,
    financial_logs_count INTEGER,
    active_bank_transactions INTEGER,
    unlinked_bank_transactions INTEGER,
    status TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sp.id as payment_id,
        sp.amount as payment_amount,
        (SELECT COUNT(*)::INTEGER 
         FROM public.financial_transactions_log ftl 
         WHERE ftl.source_table = 'student_payments' 
           AND ftl.source_record_id = sp.id::text) as financial_logs_count,
        (SELECT COUNT(*)::INTEGER 
         FROM public.bank_transactions bt 
         WHERE bt.reference_table = 'student_payments' 
           AND bt.reference_id = sp.id
           AND bt.financial_transaction_log_id IS NOT NULL) as active_bank_transactions,
        (SELECT COUNT(*)::INTEGER 
         FROM public.bank_transactions bt 
         WHERE bt.reference_table = 'student_payments' 
           AND bt.reference_id = sp.id
           AND bt.financial_transaction_log_id IS NULL) as unlinked_bank_transactions,
        CASE 
            WHEN (SELECT COUNT(*) 
                  FROM public.bank_transactions bt 
                  WHERE bt.reference_table = 'student_payments' 
                    AND bt.reference_id = sp.id
                    AND bt.financial_transaction_log_id IS NOT NULL) = 1 THEN 'صحيح'
            WHEN (SELECT COUNT(*) 
                  FROM public.bank_transactions bt 
                  WHERE bt.reference_table = 'student_payments' 
                    AND bt.reference_id = sp.id
                    AND bt.financial_transaction_log_id IS NOT NULL) = 0 THEN 'لا توجد معاملة مربوطة'
            ELSE 'معاملات متعددة'
        END as status
    FROM public.student_payments sp
    ORDER BY sp.created_at DESC;
END;
$$;

-- الخطوة 7: تحديث المعاملات البنكية الموجودة لربطها بالسجلات الصحيحة
UPDATE public.bank_transactions bt
SET financial_transaction_log_id = (
    SELECT ftl.id 
    FROM public.financial_transactions_log ftl
    WHERE ftl.source_table = 'student_payments'
      AND ftl.source_record_id = bt.reference_id::text
      AND ftl.is_reversal = false
    ORDER BY ftl.created_at DESC
    LIMIT 1
)
WHERE bt.reference_table = 'student_payments'
  AND bt.reference_id IS NOT NULL
  AND bt.financial_transaction_log_id IS NULL;

-- الخطوة 8: حذف المعاملات البنكية المكررة (الاحتفاظ بواحدة فقط لكل دفعة)
WITH ranked_transactions AS (
    SELECT id,
           ROW_NUMBER() OVER (
               PARTITION BY reference_id 
               ORDER BY 
                   CASE WHEN financial_transaction_log_id IS NOT NULL THEN 0 ELSE 1 END,
                   created_at DESC
           ) as rn
    FROM public.bank_transactions
    WHERE reference_table = 'student_payments'
      AND reference_id IS NOT NULL
)
DELETE FROM public.bank_transactions
WHERE id IN (
    SELECT id FROM ranked_transactions WHERE rn > 1
);

-- الخطوة 9: عرض النتائج
SELECT 'تم إصلاح العلاقة بين الجداول بنجاح!' as status;

-- عرض إحصائيات
SELECT 
    'دفعات الطلاب' as table_name,
    COUNT(*) as total_count
FROM public.student_payments

UNION ALL

SELECT 
    'سجلات المعاملات المالية' as table_name,
    COUNT(*) as total_count
FROM public.financial_transactions_log
WHERE source_table = 'student_payments'

UNION ALL

SELECT 
    'المعاملات البنكية المربوطة' as table_name,
    COUNT(*) as total_count
FROM public.bank_transactions
WHERE reference_table = 'student_payments'
  AND financial_transaction_log_id IS NOT NULL

UNION ALL

SELECT 
    'المعاملات البنكية غير المربوطة' as table_name,
    COUNT(*) as total_count
FROM public.bank_transactions
WHERE reference_table = 'student_payments'
  AND financial_transaction_log_id IS NULL;

-- تعليمات الاستخدام
SELECT 'للتحقق من صحة الربط: SELECT * FROM public.verify_correct_linking();' as instruction
UNION ALL
SELECT 'لتنظيف المعاملات غير المربوطة: SELECT public.cleanup_unlinked_bank_transactions();' as instruction;
