// --- Auth Check ---
checkAuth('../login.html'); // Assumes login.html is one level up

// --- Supabase Initialization ---
let _supabase;
try {
    if (typeof supabase !== 'undefined' && supabase.createClient) {
        _supabase = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('Supabase client initialized using config.');
    } else {
        console.error('Supabase client not found.');
    }
} catch (error) {
    console.error('Error initializing Supabase client:', error);
}

// --- DOM Elements ---
const monthCardsContainer = document.getElementById('month-cards-container');
const messageArea = document.getElementById('message-area');
const selectedYearDisplay = document.getElementById('selected-year-display');
const backToYearBtn = document.getElementById('back-to-year-btn');

// --- State ---
let selectedYearId = null;
let selectedYearNumber = null;

// --- Functions ---
const showMessage = (message, type = 'info') => {
    if (!messageArea) return;
    messageArea.textContent = message;
    messageArea.className = `message ${type} show`;
    messageArea.style.display = 'block';
};

// Function to get month name from number (Arabic)
const getMonthName = (monthNumber) => {
    const names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"];
    const index = parseInt(monthNumber, 10) - 1;
    return names[index] || `شهر ${monthNumber}`;
};

const renderMonthCards = (months) => {
    if (!monthCardsContainer) return;
    monthCardsContainer.innerHTML = ''; // Clear loading/previous cards

    if (!months || months.length === 0) {
        showMessage(`لا توجد شهور متاحة للسنة ${selectedYearNumber}.`, 'warning');
        monthCardsContainer.innerHTML = '<p class="loading-placeholder">لا توجد شهور لإظهارها.</p>';
        return;
    }

    // Sort months by month_number before rendering
    months.sort((a, b) => a.month_number - b.month_number);

    months.forEach(month => {
        const card = document.createElement('div');
        card.className = 'month-card';
        if (month.is_closed) {
            card.classList.add('closed');
            card.title = 'هذا الشهر مغلق';
        } else {
            card.dataset.monthId = month.id;
            card.dataset.monthNumber = month.month_number;
            card.addEventListener('click', handleMonthSelection);
        }

        card.innerHTML = `
            <span class="month-number">${month.month_number}</span>
            <span class="month-name">${getMonthName(month.month_number)}</span>
        `;
        monthCardsContainer.appendChild(card);
    });
};

const handleMonthSelection = (event) => {
    const card = event.currentTarget;
    const monthId = card.dataset.monthId;
    const monthNumber = card.dataset.monthNumber;

    if (monthId && monthNumber && selectedYearNumber) {
        console.log(`Month selected: ID=${monthId}, Number=${monthNumber}, Year=${selectedYearNumber}`);
        // Store selected month info in sessionStorage
        sessionStorage.setItem('selectedBudgetMonthId', monthId);
        sessionStorage.setItem('selectedBudgetMonthNumber', monthNumber);
        // Keep year number as well for context
        sessionStorage.setItem('selectedBudgetYearNumber', selectedYearNumber);

        // --- Added Logging ---
        console.log(`Stored in sessionStorage: monthId=${monthId}, monthNumber=${monthNumber}, yearNumber=${selectedYearNumber}`);
        // --- End Added Logging ---

        // Redirect to the main financial dashboard page
        // Ensure the path is correct relative to select_budget_month.html
        window.location.href = 'financial_dashboard.html';
    } else {
        console.error('Missing month/year data on card:', card);
        showMessage('حدث خطأ عند اختيار الشهر.', 'error');
    }
};

const fetchMonths = async () => {
    if (!_supabase) {
        showMessage('خطأ في الاتصال بقاعدة البيانات.', 'error');
        if (monthCardsContainer) monthCardsContainer.innerHTML = '<p class="loading-placeholder">خطأ في الاتصال.</p>';
        return;
    }
    if (!selectedYearId) {
         showMessage('لم يتم تحديد السنة المالية. جاري إعادة التوجيه...', 'error');
         setTimeout(() => { window.location.href = 'select_budget_year.html'; }, 1500);
         return;
    }

    if (monthCardsContainer) monthCardsContainer.innerHTML = '<p class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i> جاري تحميل الشهور...</p>';

    try {
        // 1. Fetch existing months for the selected year
        const { data: existingMonths, error: fetchError } = await _supabase
            .from('budget_months')
            .select('id, month_number, is_closed')
            .eq('budget_year_id', selectedYearId)
            .order('month_number', { ascending: true });

        if (fetchError) {
            console.error('Error fetching existing months:', fetchError);
            throw new Error(`خطأ في جلب الشهور الحالية: ${fetchError.message}`);
        }

        let allMonthsData = existingMonths || [];

        // 2. Check if all 12 months exist
        if (allMonthsData.length < 12) {
            showMessage('بعض الشهور غير موجودة، جاري إنشاؤها...', 'info');
            console.log(`Found ${allMonthsData.length} months for year ${selectedYearId}. Attempting to create missing ones.`);

            const existingMonthNumbers = new Set(allMonthsData.map(m => m.month_number));
            const monthsToCreate = [];

            for (let monthNumber = 1; monthNumber <= 12; monthNumber++) {
                if (!existingMonthNumbers.has(monthNumber)) {
                    monthsToCreate.push({
                        budget_year_id: selectedYearId,
                        month_number: monthNumber,
                        month_name: getMonthName(monthNumber), // *** Added month_name ***
                        is_closed: false // Default to not closed
                        // !!! IMPORTANT: Add any other required (NOT NULL) fields from your
                        // budget_months table schema here with appropriate default values.
                        // Example: notes: '', start_date: null, etc.
                    });
                }
            }

            // 3. Create missing months if any
            if (monthsToCreate.length > 0) {
                console.log('Creating months:', monthsToCreate);
                const { error: insertError } = await _supabase
                    .from('budget_months')
                    .insert(monthsToCreate); // Send the array with month_name included

                if (insertError) {
                    // Enhanced error logging
                    console.error('Error creating missing months:', insertError);
                    console.error('Supabase error details:', JSON.stringify(insertError, null, 2)); // Log detailed error
                    // Decide how to handle partial creation or failure.
                    // For now, show a warning and proceed to render what we have/re-fetch.
                    // Provide more specific error message if possible
                    const detailedMessage = insertError.message || 'فشل في إرسال البيانات.';
                    showMessage(`خطأ أثناء إنشاء بعض الشهور: ${detailedMessage}. قد تظهر القائمة غير مكتملة.`, 'warning', 0); // Show warning indefinitely
                } else {
                    console.log('Successfully created missing months.');
                    showMessage('تم إنشاء الشهور المفقودة بنجاح.', 'success');
                    // 4. Re-fetch all months after creation to get IDs and ensure consistency
                    const { data: refreshedMonths, error: refreshError } = await _supabase
                        .from('budget_months')
                        .select('id, month_number, is_closed')
                        .eq('budget_year_id', selectedYearId)
                        .order('month_number', { ascending: true });

                    if (refreshError) {
                        console.error('Error re-fetching months after creation:', refreshError);
                        // Render with potentially incomplete data from the first fetch
                        showMessage('خطأ في تحديث قائمة الشهور بعد الإنشاء.', 'warning');
                    } else {
                        allMonthsData = refreshedMonths || []; // Update the data to render
                    }
                }
            }
        } else {
            console.log('All 12 months already exist for this year.');
        }

        // 5. Render the cards with the complete data
        renderMonthCards(allMonthsData);
        // Clear any persistent messages if rendering is successful
        if (allMonthsData.length > 0 && messageArea.textContent.includes('جاري إنشاؤها')) {
             messageArea.style.display = 'none';
        }


    } catch (error) {
        // Catch errors from initial fetch or re-fetch
        showMessage(`خطأ فادح في جلب أو إنشاء الشهور: ${error.message}`, 'error', 0);
        if (monthCardsContainer) monthCardsContainer.innerHTML = '<p class="loading-placeholder error">فشل تحميل الشهور.</p>';
    }
};

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', () => {
    console.log('Select Budget Month page loaded.');

    // Get selected year from sessionStorage
    selectedYearId = sessionStorage.getItem('selectedBudgetYearId');
    selectedYearNumber = sessionStorage.getItem('selectedBudgetYearNumber');

    if (selectedYearNumber && selectedYearDisplay) {
        selectedYearDisplay.textContent = selectedYearNumber;
    }

    if (!selectedYearId) {
        console.error('Selected Budget Year ID not found in sessionStorage.');
        showMessage('لم يتم تحديد السنة المالية. الرجاء العودة واختيار سنة أولاً.', 'error');
        // Disable interaction or redirect
        if (monthCardsContainer) monthCardsContainer.innerHTML = '<p class="loading-placeholder">الرجاء العودة واختيار سنة.</p>';
        // Optionally redirect after a delay
        // setTimeout(() => { window.location.href = 'select_budget_year.html'; }, 3000);
    } else {
        fetchMonths();
    }

    // Back button functionality
    if (backToYearBtn) {
        backToYearBtn.addEventListener('click', () => {
            // Clear selected month if going back? Optional.
            // sessionStorage.removeItem('selectedBudgetMonthId');
            // sessionStorage.removeItem('selectedBudgetMonthNumber');
            window.location.href = 'select_budget_year.html';
        });
    }
});
