@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* ===== CSS الأساسي للقائمة الجانبية حسب الدليل ===== */

/* Dashboard Layout */
.dashboard-layout {
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
    font-family: 'Tajawal', sans-serif;
    overflow-x: hidden;
}

/* Top Navigation Bar */
.top-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-toggle-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.sidebar-toggle-btn:hover {
    background-color: rgba(255,255,255,0.1);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 600;
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.navbar-user {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logout-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: rgba(255,255,255,0.1);
}

/* Main Content */
.main-content {
    margin-top: 70px;
    padding: 0;
    min-height: calc(100vh - 70px);
    transition: margin-right 0.3s ease;
}

.content-wrapper {
    padding: 30px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Dashboard Header */
.dashboard-header {
    margin-bottom: 30px;
}

.dashboard-header .header-content h1 {
    font-size: 2.2rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.dashboard-header .header-content p {
    font-size: 1.1rem;
    color: #7f8c8d;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar-brand span {
        display: none;
    }

    .content-wrapper {
        padding: 20px 15px;
    }
}

/* ===== نهاية CSS القائمة الجانبية ===== */

/* ===== CSS للشاشات المنبثقة الجديدة ===== */

/* Management Modals */
.add-form-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
}

.add-form-section h3 {
    color: var(--primary-dark);
    margin-bottom: 15px;
    font-size: 1.1rem;
    font-weight: 600;
}

.list-section {
    margin-top: 20px;
}

.list-section h3 {
    color: var(--secondary-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 5px;
}

/* Management Tables */
#schools-table,
#neighborhoods-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

#schools-table th,
#schools-table td,
#neighborhoods-table th,
#neighborhoods-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

#schools-table th,
#neighborhoods-table th {
    background-color: rgba(52, 152, 219, 0.05);
    color: var(--secondary-color);
    font-weight: 500;
}

#schools-table tbody tr:hover,
#neighborhoods-table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

/* Management Action Buttons */
.manage-action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px 8px;
    margin: 0 3px;
    font-size: 0.9rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.manage-action-btn.edit-btn {
    color: var(--warning-color);
}

.manage-action-btn.edit-btn:hover {
    background-color: rgba(243, 156, 18, 0.1);
}

.manage-action-btn.delete-btn {
    color: var(--danger-color);
}

.manage-action-btn.delete-btn:hover {
    background-color: rgba(231, 76, 60, 0.1);
}

/* Modal Styles for Management Screens */
#schools-modal,
#neighborhoods-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    padding: 20px;
    box-sizing: border-box;
}

#schools-modal.show,
#neighborhoods-modal.show {
    display: flex;
}

#schools-modal .form-card,
#neighborhoods-modal .form-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    width: 100%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

#schools-modal .card-header,
#neighborhoods-modal .card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 20px;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#schools-modal .card-header h2,
#neighborhoods-modal .card-header h2 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

#schools-modal .card-body,
#neighborhoods-modal .card-body {
    padding: 20px;
}

/* Cancel buttons for edit mode */
#cancel-school-edit,
#cancel-neighborhood-edit {
    display: none;
    background-color: var(--text-muted);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    margin-left: 10px;
    transition: background-color 0.3s ease;
}

#cancel-school-edit:hover,
#cancel-neighborhood-edit:hover {
    background-color: #6c757d;
}

/* ===== CSS الفلاتر وعرض الأخوان ===== */

/* Filters Section */
.filters-section {
    margin-bottom: 30px;
}

.filters-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.filters-card .card-header {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filters-card .card-header h2 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.clear-filters-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
}

.clear-filters-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    padding: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: 500;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Siblings Display Styles */
.sibling-group {
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    margin-bottom: 10px;
    padding: 5px;
    transition: all 0.3s ease;
}

.sibling-group.has-siblings {
    border-color: #e74c3c;
    background-color: rgba(231, 76, 60, 0.05);
}

.sibling-group.has-siblings .student-row {
    background-color: rgba(231, 76, 60, 0.1);
}

.sibling-indicator {
    display: inline-block;
    background: #e74c3c;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    margin-left: 5px;
    font-weight: 500;
}

.sibling-count {
    background: #3498db;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
    margin-right: 5px;
}

/* Student Row Highlighting */
.student-row.sibling-highlight {
    background-color: rgba(231, 76, 60, 0.1);
    border-left: 4px solid #e74c3c;
}

.student-row.sibling-highlight:hover {
    background-color: rgba(231, 76, 60, 0.15);
}

/* Filter Results Info */
.filter-results-info {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: var(--text-muted);
    border-left: 4px solid var(--primary-color);
}

.filter-results-info .highlight {
    color: var(--primary-color);
    font-weight: 600;
}

/* Responsive Design for Filters */
@media (max-width: 768px) {
    .filters-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px;
    }

    .filters-card .card-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}

/* ===== CSS شاشة خيارات الكروت ===== */

/* Cards Options Modal */
#cards-options-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    padding: 20px;
    box-sizing: border-box;
}

#cards-options-modal.show {
    display: flex;
}

#cards-options-modal .form-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    width: 100%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

#cards-options-modal .card-header {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
    padding: 20px;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#cards-options-modal .card-header h2 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

/* Cards Options Grid */
.cards-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    padding: 30px;
}

/* Card Option */
.card-option {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: var(--border-radius);
    padding: 25px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.card-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    transition: all 0.3s ease;
}

/* Schools Option */
.schools-option {
    border-color: #3498db;
}

.schools-option::before {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.schools-option:hover {
    border-color: #2980b9;
    background: rgba(52, 152, 219, 0.05);
}

.schools-option .option-icon {
    color: #3498db;
}

/* Neighborhoods Option */
.neighborhoods-option {
    border-color: #2ecc71;
}

.neighborhoods-option::before {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
}

.neighborhoods-option:hover {
    border-color: #27ae60;
    background: rgba(46, 204, 113, 0.05);
}

.neighborhoods-option .option-icon {
    color: #2ecc71;
}

/* Siblings Option */
.siblings-option {
    border-color: #e74c3c;
}

.siblings-option::before {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.siblings-option:hover {
    border-color: #c0392b;
    background: rgba(231, 76, 60, 0.05);
}

.siblings-option .option-icon {
    color: #e74c3c;
}

/* Table Option */
.table-option {
    border-color: #f39c12;
}

.table-option::before {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.table-option:hover {
    border-color: #e67e22;
    background: rgba(243, 156, 18, 0.05);
}

.table-option .option-icon {
    color: #f39c12;
}

/* Option Icon */
.option-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.card-option:hover .option-icon {
    transform: scale(1.1);
}

/* Option Text */
.card-option h3 {
    margin: 0 0 10px 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--secondary-color);
}

.card-option p {
    margin: 0 0 20px 0;
    font-size: 0.9rem;
    color: var(--text-muted);
    line-height: 1.4;
}

/* Option Stats */
.option-stats {
    background: #f8f9fa;
    border-radius: var(--border-radius);
    padding: 10px;
    margin-top: 15px;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 2px;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Cards Button Style */
.control-btn.cards-btn {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
}

.control-btn.cards-btn:hover {
    background: linear-gradient(135deg, #8e44ad 0%, #7d3c98 100%);
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .cards-options-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 20px;
    }

    .card-option {
        padding: 20px 15px;
    }

    .option-icon {
        font-size: 2.5rem;
    }

    #cards-options-modal .form-card {
        margin: 10px;
        max-width: none;
    }
}

/* ===== CSS عرض الكروت ===== */

/* Cards Container */
.cards-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.cards-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cards-header h2 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.cards-header .badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Cards Grid */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    padding: 20px;
}

/* Individual Card */
.group-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.group-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

/* Card Header */
.group-card-header {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.group-card-header.school-card {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.group-card-header.neighborhood-card {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
}

.group-card-header.stage-card {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.group-card-header.siblings-card {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.group-card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.group-card-subtitle {
    font-size: 0.85rem;
    font-weight: 400;
    margin: 0;
    opacity: 0.9;
    color: rgba(255, 255, 255, 0.95);
}

.group-card-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Card Body */
.group-card-body {
    padding: 15px 20px;
}

.group-info {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.group-info .info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.group-info .info-item:last-child {
    margin-bottom: 0;
}

.group-info .info-label {
    font-weight: 500;
    color: var(--secondary-color);
}

/* Students List in Card */
.card-students-list {
    max-height: 300px;
    overflow-y: auto;
}

.card-student-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease;
}

.card-student-item:last-child {
    border-bottom: none;
}

.card-student-item:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

.student-info {
    flex: 1;
}

.student-name {
    font-weight: 500;
    color: var(--secondary-color);
    margin-bottom: 3px;
}

.student-details {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.student-actions {
    display: flex;
    gap: 5px;
}

.mini-action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    font-size: 0.8rem;
    transition: background-color 0.3s ease;
}

.mini-action-btn.edit {
    color: var(--warning-color);
}

.mini-action-btn.edit:hover {
    background-color: rgba(243, 156, 18, 0.1);
}

.mini-action-btn.delete {
    color: var(--danger-color);
}

.mini-action-btn.delete:hover {
    background-color: rgba(231, 76, 60, 0.1);
}

/* Card Footer */
.group-card-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid var(--border-color);
}

.add-student-to-group-btn {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.add-student-to-group-btn:hover {
    background: var(--primary-dark);
}

/* Siblings Card Special Styling */
.siblings-card .group-info {
    background: rgba(231, 76, 60, 0.1);
    border-left: 4px solid #e74c3c;
}

.siblings-card .card-student-item {
    background: rgba(231, 76, 60, 0.05);
}

/* Empty State */
.empty-cards-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-muted);
}

.empty-cards-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: var(--border-color);
}

.empty-cards-state h3 {
    margin-bottom: 10px;
    color: var(--secondary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .cards-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px;
    }

    .group-card-header {
        padding: 12px 15px;
    }

    .group-card-body {
        padding: 12px 15px;
    }

    .group-card-footer {
        padding: 12px 15px;
    }
}

/* ===== نهاية CSS عرض الكروت ===== */

/* ===== نهاية CSS الفلاتر وعرض الأخوان ===== */

/* ===== نهاية CSS الشاشات المنبثقة ===== */

:root {
    /* Main Colors */
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;

    /* Backgrounds */
    --body-bg: #f5f7fa;
    --card-bg: #ffffff;

    /* Borders */
    --border-color: #e1e8ed;
    --border-radius: 8px;

    /* Typography */
    --text-dark: #34495e;
    --text-muted: #7f8c8d;
    --text-light: #ffffff;

    /* Shadow */
    --card-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    --hover-shadow: 0 5px 25px rgba(0, 0, 0, 0.12);
    --button-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--body-bg);
    color: var(--text-dark);
    line-height: 1.6;
}

/* Dashboard Container */
.dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.dashboard-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-light);
    padding: 20px;
    text-align: center;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
}

.header-content h1 {
    font-size: 1.8rem;
    margin-bottom: 5px;
    font-weight: 700;
}

.header-content p {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 300;
}

.header-content h1 i {
    margin-left: 10px;
}

/* Main Content */
.dashboard-main {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    width: 100%;
}

/* Stats Cards */
.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

/* Clickable Cards */
.clickable-card {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.clickable-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--hover-shadow);
    border: 2px solid var(--primary-color);
}

.clickable-card::after {
    content: "انقر للإدارة";
    position: absolute;
    bottom: 5px;
    right: 10px;
    font-size: 0.7rem;
    color: var(--text-muted);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.clickable-card:hover::after {
    opacity: 1;
}

.stat-icon {
    font-size: 2rem;
    margin-left: 15px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-card:nth-child(1) .stat-icon {
    color: var(--primary-color);
    background-color: rgba(52, 152, 219, 0.1);
}

.stat-card:nth-child(2) .stat-icon {
    color: var(--success-color);
    background-color: rgba(46, 204, 113, 0.1);
}

.stat-card:nth-child(3) .stat-icon {
    color: var(--warning-color);
    background-color: rgba(243, 156, 18, 0.1);
}

.stat-info h3 {
    font-size: 1.8rem;
    margin-bottom: 5px;
    font-weight: 700;
}

.stat-info p {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Controls Section */
.controls-section {
    margin-bottom: 20px;
}

.control-card, .table-card, .form-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa; /* Light header background */
}

.card-header h2 {
    font-size: 1.2rem;
    font-weight: 500;
    margin: 0;
}

.card-body {
    padding: 20px 30px; /* Adjust padding */
    max-height: 75vh; /* Limit height and allow scrolling */
    overflow-y: auto;
}

.controls-grid {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 15px;
    align-items: center;
}

.control-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var(--text-light);
    transition: all 0.3s ease;
    box-shadow: var(--button-shadow);
}

.control-btn i {
    font-size: 1rem;
}

.add-btn {
    background-color: var(--success-color);
}

.add-btn:hover {
    background-color: #27ae60;
}

.print-btn {
    background-color: var(--primary-color);
}

.print-btn:hover {
    background-color: var(--primary-dark);
}

.search-container {
    display: flex;
    max-width: 100%;
    position: relative;
}

#search-input {
    width: 100%;
    padding: 10px 45px 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

#search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.25);
}

.search-btn {
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 1rem;
    padding: 5px;
    cursor: pointer;
}

/* Table */
.table-section {
    margin-bottom: 20px;
}

.badge {
    background-color: var(--primary-color);
    color: var(--text-light);
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.table-responsive {
    overflow-x: auto;
}

#students-table {
    width: 100%;
    border-collapse: collapse;
}

#students-table th,
#students-table td {
    padding: 12px 15px;
    text-align: right;
}

#students-table th {
    background-color: rgba(52, 152, 219, 0.05);
    color: var(--secondary-color);
    font-weight: 500;
    border-bottom: 1px solid var(--border-color);
}

#students-table tbody tr {
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.3s ease;
}

#students-table tbody tr:last-child {
    border-bottom: none;
}

#students-table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

#students-table .loading-message {
    text-align: center;
    color: var(--text-muted);
    padding: 20px;
}

.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px 8px;
    margin: 0 3px;
    font-size: 1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.action-btn.edit-btn {
    color: var(--warning-color);
}

.action-btn.edit-btn:hover {
    background-color: rgba(243, 156, 18, 0.1);
}

.action-btn.delete-btn {
    color: var(--danger-color);
}

.action-btn.delete-btn:hover {
    background-color: rgba(231, 76, 60, 0.1);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    gap: 5px;
}

.pagination button {
    background-color: var(--light-color);
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination button:hover:not(.disabled) {
    background-color: var(--primary-color);
    color: var(--text-light);
}

.pagination-ellipsis {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Enhanced Form Section - Modal Styling */
.form-section {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px); /* Add blur effect to background */
    -webkit-backdrop-filter: blur(4px); /* For Safari */
    justify-content: center;
    align-items: center;
    padding: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.form-section.show {
    display: flex;
    opacity: 1;
}

.form-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    width: 100%;
    max-width: 700px;
    margin: auto;
    position: relative;
    transform: translateY(30px);
    transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    overflow: hidden;
}

.form-section.show .form-card {
    transform: translateY(0);
}

.card-header {
    padding: 15px 25px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-btn:hover {
    color: var(--danger-color);
    background-color: rgba(231, 76, 60, 0.1);
}

.card-body {
    padding: 20px 30px; /* Adjust padding */
    max-height: 75vh; /* Limit height and allow scrolling */
    overflow-y: auto;
}

/* Ensure this .form-row style is used inside the modal */
.form-row {
    display: grid;
    /* Adjust grid columns based on content, allow flexibility */
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 15px;
}

/* Style for fieldsets */
.form-fieldset {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px 20px 20px 20px; /* Add padding */
    margin-bottom: 25px; /* Space between fieldsets */
    position: relative;
}

.form-fieldset legend {
    font-weight: 600;
    color: var(--primary-dark);
    padding: 0 10px; /* Padding around legend text */
    margin-right: 10px; /* Position legend correctly in RTL */
    font-size: 1.1rem;
}

/* Style for required fields */
.required {
    color: var(--danger-color);
    margin-right: 3px;
}

/* Checkbox styling */
.form-group-checkbox {
    display: flex;
    align-items: center; /* Align checkbox and label vertically */
    padding-top: 25px; /* Align with input fields */
}

.form-group-checkbox label {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 0; /* Remove default margin */
}

.form-group-checkbox input[type="checkbox"] {
    width: auto; /* Override default width */
    margin-left: 8px; /* Space between checkbox and text */
    accent-color: var(--primary-color); /* Style checkbox color */
    height: 18px;
    width: 18px;
}

/* File input styling */
.form-group input[type="file"] {
    padding: 8px; /* Adjust padding */
    border: 1px dashed var(--border-color); /* Dashed border */
    background-color: #f9f9f9;
}

.form-group input[type="file"]::file-selector-button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    background-color: var(--primary-color);
    color: var(--text-light);
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-left: 10px; /* Space in RTL */
}

.form-group input[type="file"]::file-selector-button:hover {
    background-color: var(--primary-dark);
}

/* Small text hint */
.form-group small {
    display: block;
    margin-top: 5px;
    font-size: 0.85rem;
    color: var(--text-muted);
}

/* Responsive adjustments for modal */
@media (max-width: 768px) {
    /* ... existing responsive styles ... */
    .form-row {
        grid-template-columns: 1fr; /* Stack form elements on smaller screens */
        gap: 15px; /* Adjusted gap for stacked */
    }
    .form-group-checkbox {
        padding-top: 0; /* Adjust alignment when stacked */
        margin-top: 10px;
    }
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 0.95rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.25);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.submit-btn,
.cancel-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.submit-btn {
    background-color: var(--success-color);
    color: var(--text-light);
}

.submit-btn:hover {
    background-color: #27ae60;
}

.cancel-btn {
    background-color: var(--light-color);
    color: var(--text-dark);
}

.cancel-btn:hover {
    background-color: #bdc3c7;
}

/* Message Styles */
.message {
    margin-top: 15px;
    padding: 12px;
    border-radius: var(--border-radius);
    text-align: center;
    font-weight: 500;
    display: none;
}

.message.show {
    display: block;
}

.message.info {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.message.success {
    background-color: rgba(46, 204, 113, 0.1);
    color: var (--success-color);
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.message.error {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

/* Footer */
.dashboard-footer {
    background-color: var(--card-bg);
    text-align: center;
    padding: 15px;
    color: var(--text-muted);
    border-top: 1px solid var(--border-color);
    margin-top: auto;
}

/* Responsive */
@media (max-width: 992px) {
    .dashboard-main {
        padding: 15px;
    }
}

@media (max-width: 768px) {
    .stats-section {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .controls-grid {
        grid-template-columns: 1fr;
    }

    .search-container {
        order: -1;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

@media (max-width: 576px) {
    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .badge {
        margin-top: 5px;
        align-self: flex-start;
    }

    .form-actions {
        flex-direction: column;
    }

    .submit-btn,
    .cancel-btn {
        width: 100%;
    }

    .header-content h1 {
        font-size: 1.5rem;
    }
}
