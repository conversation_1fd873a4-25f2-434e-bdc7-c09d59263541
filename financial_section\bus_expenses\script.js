console.log("Bus Expenses script loaded.");

// --- Auth Check ---
checkAuth('../../auth.js'); // Adjusted path

// Supabase Initialization (Assuming config.js is loaded)
let supabaseUrl, supabaseAnonKey;
if (typeof SUPABASE_URL !== 'undefined') supabaseUrl = SUPABASE_URL;
else console.error('SUPABASE_URL not found.');
if (typeof SUPABASE_ANON_KEY !== 'undefined') supabaseAnonKey = SUPABASE_ANON_KEY;
else console.error('SUPABASE_ANON_KEY not found.');

const { createClient } = supabase;
const _supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('Supabase Initialized for Bus Expenses:', _supabase);

// --- DOM Elements ---
// Removed old inline form elements
// const addExpenseForm = document.getElementById('add-expense-form');
// const expenseDateInput = document.getElementById('expense_date'); // Now part of modal
// const busIdSelect = document.getElementById('bus_id'); // Now part of modal or sidebar
// const expenseTypeSelect = document.getElementById('expense_type'); // Now part of modal
// const amountInput = document.getElementById('amount'); // Now part of modal
// const descriptionInput = document.getElementById('description'); // Now part of modal
// const addExpenseMessage = document.getElementById('add-expense-message'); // Now part of modal

// Common Elements
const pageMonthYearTitle = document.getElementById('page-month-year-title');
const dashboardMessage = document.getElementById('dashboard-message'); // Standard message area
const expensesTableBody = document.getElementById('expenses-tbody');
const expensesCountBadge = document.getElementById('expenses-count');
const listMessage = document.getElementById('list-message');
const paginationControls = document.getElementById('pagination-controls'); // Added

// Sidebar
const busesSidebarList = document.getElementById('buses-sidebar-list'); // Added
const backToFinanceBtnSidebar = document.getElementById('back-to-finance-btn-sidebar'); // Added

// Header
const mainHeaderTitle = document.getElementById('main-header-title'); // Added
const mainHeaderSubtitle = document.getElementById('main-header-subtitle'); // Added
const tableHeaderTitle = document.getElementById('table-header-title'); // Added

// Dashboard Cards (Stats)
const totalMaintenanceCard = document.getElementById('total-maintenance-card'); // Added
const totalFuelCard = document.getElementById('total-fuel-card'); // Added
const totalOtherCard = document.getElementById('total-other-card'); // Added
const totalExpensesAmountCard = document.getElementById('total-expenses-amount-card'); // Added

// Controls
const openAddExpenseModalBtn = document.getElementById('open-add-expense-modal-btn'); // Added
const searchInput = document.getElementById('search-input'); // Added
const filterBtn = document.getElementById('filter-btn'); // Added
const resetFilterBtn = document.getElementById('reset-filter-btn'); // Added

// Expense Form (Modal)
const expenseFormSection = document.getElementById('expense-form-section'); // Added
const expenseForm = document.getElementById('expense-form'); // Added
const expenseFormTitle = document.getElementById('expense-form-title'); // Added
const closeExpenseFormBtn = document.getElementById('close-expense-form-btn'); // Added
const expenseFormMessage = document.getElementById('expense-form-message'); // Added
const expenseIdField = document.getElementById('expense_id'); // Added
const modalBusIdField = document.getElementById('modal_bus_id'); // Added
const modalMonthIdField = document.getElementById('modal_month_id'); // Added

// Modal Steps
const modalStepMonth = document.getElementById('modal-step-month'); // Added
const modalStepDetails = document.getElementById('modal-step-details'); // Added

// Step 1: Month Selection
const modalYearSelect = document.getElementById('modal_year'); // Added
const modalMonthSelect = document.getElementById('modal_month'); // Added
const modalNextToDetailsBtn = document.getElementById('modal-next-to-details-btn'); // Added

// Step 2: Details Form
const modalSelectedMonthLabel = document.getElementById('modal-selected-month-label'); // Added
const modalBackToMonthBtn = document.getElementById('modal-back-to-month-btn'); // Added
const expenseDateField = document.getElementById('expense_date'); // Added (Modal version)
const expenseTypeInput = document.getElementById('expense_type'); // Added (Modal version)
const amountNumericInput = document.getElementById('amount_numeric'); // Added (Modal version)
const includeTaxSelect = document.getElementById('include_tax'); // Added (Modal version)
const taxAmountInput = document.getElementById('tax_amount');     // Added (Modal version)
const totalWithTaxInput = document.getElementById('total_with_tax'); // Added (Modal version)
const bankIdSelect = document.getElementById('bank_id'); // Added (Modal version)
const detailsTextarea = document.getElementById('details'); // Added (Modal version)

// --- State ---
let selectedMonthId = null;
let selectedMonthNumber = null;
let selectedYearNumber = null;
let availableBuses = [];
let availableBanks = [];
let availableYears = []; // For modal
let availableMonths = []; // For modal (active months for selected year)
let currentBusExpenses = []; // All expenses for the selected bus
let selectedBusId = null; // Bus ID from sidebar
let selectedBusIdentifier = null; // e.g., Plate number or Bus number
let currentPage = 1;
const itemsPerPage = 15;
let totalItems = 0;
const VAT_RATE = 0.15; // Assuming same VAT rate

// --- Helper Functions ---

// Function to display messages (assuming it's globally available or defined here)
const showMessage = (element, message, type = 'info', duration = 5000) => {
    if (!element) return;
    element.textContent = message;
    element.className = `message ${type} show`;
    element.style.display = 'block';
    if (type === 'success' || (duration > 0 && type !== 'error' && type !== 'warning')) {
        setTimeout(() => {
            if (element.textContent === message) {
                 element.style.display = 'none';
                 element.classList.remove('show');
            }
        }, duration);
    }
};

// Function to format currency
const formatCurrency = (amount) => {
    if (amount === null || amount === undefined || isNaN(amount)) return '0.00';
    return parseFloat(amount).toFixed(2);
};

// Function to format date (YYYY-MM-DD)
const formatDate = (dateString) => {
    if (!dateString) return '';
    try {
        const date = new Date(dateString);
        const offset = date.getTimezoneOffset();
        const adjustedDate = new Date(date.getTime() - (offset*60*1000));
        return adjustedDate.toISOString().split('T')[0];
    } catch (e) { return dateString; }
};

// Function to get month name from number (Arabic)
const getMonthName = (monthNumber) => {
    const names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"];
    const index = parseInt(monthNumber, 10) - 1;
    return names[index] || `شهر ${monthNumber}`;
};

// --- Data Fetching ---

// Fetch available buses
const fetchBuses = async () => {
    if (!_supabase) return;
    try {
        const { data, error } = await _supabase
            .from('buses') // Assuming table name is 'buses'
            .select('id, bus_number, plate_numbers') // *** CORRECTED: plate_numbers ***
            .order('bus_number', { ascending: true });
        if (error) throw error;
        availableBuses = data || [];
        // *** CORRECTED: Call populateBusSidebar here ***
        populateBusSidebar();
        // *** REMOVED: populateBusDropdown() - This dropdown is not in the new HTML structure ***
    } catch (error) {
        console.error('Error fetching buses:', error);
        // *** CORRECTED: Use dashboardMessage for general errors ***
        showMessage(dashboardMessage, `خطأ في جلب قائمة الحافلات: ${error.message}`, 'error');
    }
};

// Fetch available banks
const fetchBanks = async () => {
    if (!_supabase) return;
    try {
        const { data, error } = await _supabase
            .from('banks')
            .select('id, name')
            .order('name', { ascending: true });
        if (error) throw error;
        availableBanks = data || [];
        populateBankDropdown();
    } catch (error) {
        console.error('Error fetching banks:', error);
        // *** CORRECTED: Use dashboardMessage for general errors ***
        showMessage(dashboardMessage, `خطأ في جلب قائمة البنوك: ${error.message}`, 'error');
    }
};

// Fetch active years (Similar to driver_expenses.js)
const fetchActiveYears = async () => {
    console.log("Fetching active years...");
    try {
        const { data, error } = await _supabase
            .from('budget_years')
            .select('id, year_number')
            .eq('is_active', true)
            .order('year_number', { ascending: false });
        if (error) throw error;
        availableYears = data || [];
        console.log("Active years fetched:", availableYears);
        populateYearDropdown();
    } catch (error) {
        console.error('Error fetching active years:', error);
        if (modalYearSelect) {
            modalYearSelect.innerHTML = '<option value="">خطأ!</option>';
            modalYearSelect.disabled = true;
        }
    }
};

// Fetch active months for a year (Similar to driver_expenses.js)
const fetchActiveMonthsForYear = async (yearId) => {
    if (!yearId) {
        modalMonthSelect.innerHTML = '<option value="">اختر سنة أولاً...</option>';
        modalMonthSelect.disabled = true;
        modalNextToDetailsBtn.disabled = true;
        return;
    }
    console.log(`Fetching active months for year ID: ${yearId}`);
    modalMonthSelect.innerHTML = '<option value="">جاري التحميل...</option>';
    modalMonthSelect.disabled = true;
    modalNextToDetailsBtn.disabled = true;
    try {
        const { data, error } = await _supabase
            .from('budget_months')
            .select('id, month_number')
            .eq('budget_year_id', yearId)
            .eq('is_closed', false)
            .order('month_number', { ascending: true });
        if (error) throw error;
        availableMonths = data || [];
        console.log(`Active months for year ID ${yearId}:`, availableMonths);
        populateMonthDropdown();
    } catch (error) {
        console.error(`Error fetching active months for year ID ${yearId}:`, error);
        modalMonthSelect.innerHTML = '<option value="">خطأ!</option>';
    }
};

// Fetch expenses for the selected bus
const fetchBusExpenses = async (searchTerm = '') => {
    if (!selectedBusId) return;

    // Update loading message to use bus_number
    expensesTableBody.innerHTML = `<tr><td colspan="10" class="loading-message">جاري تحميل مصاريف الحافلة ${selectedBusIdentifier}...</td></tr>`;
    if (paginationControls) paginationControls.innerHTML = '';
    if (listMessage) listMessage.style.display = 'none';
    resetBusStats(); // Reset counts before fetching

    try {
        let query = _supabase
            .from('bus_expenses')
            .select('*, banks(name)', { count: 'exact' }) // Fetch bank name too
            .eq('bus_id', selectedBusId)
            .eq('budget_month_id', selectedMonthId); // Filter by selected month ID

        // Apply search term if provided
        if (searchTerm) {
             query = query.or(`expense_type.ilike.%${searchTerm}%,details.ilike.%${searchTerm}%`);
        }

        // Ordering
        query = query.order('expense_date', { ascending: false }).order('created_at', { ascending: false });

        // Fetch all data for stats, then paginate rendering
        const { data, error, count } = await query;

        if (error) throw error;

        currentBusExpenses = data || [];
        totalItems = count || 0;

        if (expensesCountBadge) expensesCountBadge.textContent = totalItems;

        calculateAndDisplayBusStats(currentBusExpenses); // Calculate stats on all expenses
        renderExpensesTablePaginated(); // Render the first page

        if (totalItems === 0) {
            // Update no expenses message
            showMessage(listMessage, `لا توجد مصاريف مسجلة للحافلة ${selectedBusIdentifier}.`, 'info');
            expensesTableBody.innerHTML = `<tr><td colspan="10" class="loading-message">لا توجد مصاريف مسجلة.</td></tr>`;
        } else {
             listMessage.style.display = 'none'; // Hide message if there are items
        }

    } catch (error) {
        console.error('Error fetching bus expenses:', error);
        showMessage(listMessage, `خطأ في جلب المصروفات: ${error.message}`, 'error', 0);
        expensesTableBody.innerHTML = `<tr><td colspan="10" class="loading-message">خطأ في تحميل البيانات.</td></tr>`;
    }
};

// --- UI Population ---
const populateBusSidebar = () => {
    if (!busesSidebarList) return;
    busesSidebarList.innerHTML = ''; // Clear previous content

    if (availableBuses.length === 0) {
        busesSidebarList.innerHTML = '<li class="info-message">لا يوجد حافلات.</li>';
        return;
    }

    availableBuses.forEach(bus => {
        const li = document.createElement('li');
        const link = document.createElement('a');
        link.href = '#';
        // *** CORRECTED: Use plate_numbers ***
        const busDisplayText = `حافلة ${bus.bus_number || 'N/A'} (${bus.plate_numbers || 'N/A'})`;
        link.textContent = busDisplayText;
        link.dataset.busId = bus.id;
        // *** CORRECTED: Use plate_numbers for identifier if needed, or just bus_number ***
        link.dataset.busIdentifier = bus.bus_number || 'N/A'; // Using bus_number as main identifier
        if (selectedBusId === bus.id.toString()) link.classList.add('active');
        link.addEventListener('click', handleSidebarBusClick);
        li.appendChild(link);
        busesSidebarList.appendChild(li);
    });
};

const populateBankDropdown = () => {
    if (!bankIdSelect) return;
    const firstOption = bankIdSelect.options[0];
    bankIdSelect.innerHTML = '';
    if (firstOption) bankIdSelect.appendChild(firstOption);

    availableBanks.forEach(bank => {
        const option = document.createElement('option');
        option.value = bank.id;
        option.textContent = bank.name;
        bankIdSelect.appendChild(option);
    });
};

const populateYearDropdown = () => {
    if (!modalYearSelect) return;
    const firstOption = modalYearSelect.options[0];
    modalYearSelect.innerHTML = '';
    if (firstOption) modalYearSelect.appendChild(firstOption);

    if (availableYears.length > 0) {
        availableYears.forEach(yearData => {
            const option = document.createElement('option');
            option.value = yearData.id;
            option.textContent = yearData.year_number;
            option.dataset.yearNumber = yearData.year_number;
            modalYearSelect.appendChild(option);
        });
        modalYearSelect.disabled = false;
    } else {
        if (firstOption) firstOption.textContent = 'لا سنوات نشطة';
        modalYearSelect.disabled = true;
    }
};

const populateMonthDropdown = () => {
    if (!modalMonthSelect) return;
    const firstOption = modalMonthSelect.options[0];
    modalMonthSelect.innerHTML = '';
    if (firstOption) {
        firstOption.textContent = 'اختر الشهر...';
        modalMonthSelect.appendChild(firstOption);
    }

    if (availableMonths.length > 0) {
        availableMonths.forEach(monthData => {
            const option = document.createElement('option');
            option.value = monthData.id;
            option.textContent = `شهر ${monthData.month_number}`;
            option.dataset.monthNumber = monthData.month_number;
            modalMonthSelect.appendChild(option);
        });
        modalMonthSelect.disabled = false;
    } else {
        modalMonthSelect.innerHTML = '<option value="">لا شهور نشطة</option>';
        modalMonthSelect.disabled = true;
        modalNextToDetailsBtn.disabled = true;
    }
};

// --- Event Handlers ---
const handleSidebarBusClick = (event) => {
    event.preventDefault();
    const clickedLink = event.target.closest('a');
    if (!clickedLink) return;

    const busId = clickedLink.dataset.busId;
    const busIdentifier = clickedLink.dataset.busIdentifier; // Get bus_number identifier

    if (busId === selectedBusId) return;

    selectedBusId = busId;
    selectedBusIdentifier = busIdentifier; // Store bus_number identifier

    // Update active state in sidebar
    busesSidebarList.querySelectorAll('a').forEach(link => link.classList.remove('active'));
    clickedLink.classList.add('active');

    // Update header using bus_number
    if (mainHeaderTitle) mainHeaderTitle.innerHTML = `<i class="fas fa-bus"></i> مصاريف الحافلة: ${busIdentifier}`;
    if (mainHeaderSubtitle) mainHeaderSubtitle.textContent = `عرض وإدارة مصاريف الحافلة رقم ${busIdentifier}.`;
    if (tableHeaderTitle) tableHeaderTitle.textContent = `قائمة مصاريف الحافلة ${busIdentifier}`;

    // Enable Add button
    if (openAddExpenseModalBtn) openAddExpenseModalBtn.disabled = false;

    // Reset pagination and fetch data
    currentPage = 1;
    fetchBusExpenses();
};

// --- Stat Calculation ---
const resetBusStats = () => {
    if (totalMaintenanceCard) totalMaintenanceCard.textContent = '0 ريال';
    if (totalFuelCard) totalFuelCard.textContent = '0 ريال';
    if (totalOtherCard) totalOtherCard.textContent = '0 ريال';
    if (totalExpensesAmountCard) totalExpensesAmountCard.textContent = '0 ريال';
};

const calculateAndDisplayBusStats = (expenses) => {
    let maintenanceTotal = 0;
    let fuelTotal = 0;
    let otherTotal = 0;
    let grandTotal = 0;

    expenses.forEach(exp => {
        const amount = parseFloat(exp.total_with_tax || 0);
        grandTotal += amount;
        // Simple categorization based on type text (adjust keywords as needed)
        if (exp.expense_type?.toLowerCase().includes('صيانة')) {
            maintenanceTotal += amount;
        } else if (exp.expense_type?.toLowerCase().includes('وقود') || exp.expense_type?.toLowerCase().includes('بنزين') || exp.expense_type?.toLowerCase().includes('ديزل')) {
            fuelTotal += amount;
        } else {
            otherTotal += amount;
        }
    });

    if (totalMaintenanceCard) totalMaintenanceCard.textContent = `${formatCurrency(maintenanceTotal)} ريال`;
    if (totalFuelCard) totalFuelCard.textContent = `${formatCurrency(fuelTotal)} ريال`;
    if (totalOtherCard) totalOtherCard.textContent = `${formatCurrency(otherTotal)} ريال`;
    if (totalExpensesAmountCard) totalExpensesAmountCard.textContent = `${formatCurrency(grandTotal)} ريال`;
};

// --- Table Rendering (Paginated) ---
const renderExpensesTablePaginated = () => {
    expensesTableBody.innerHTML = '';
    if (!currentBusExpenses || currentBusExpenses.length === 0) return;

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedItems = currentBusExpenses.slice(startIndex, endIndex);

    paginatedItems.forEach((expense, index) => {
        const row = document.createElement('tr');
        const sequenceNumber = startIndex + index + 1;

        // Simplified payment method text as it's always bank
        const paymentMethodText = 'بنك';

        row.innerHTML = `
            <td>${sequenceNumber}</td>
            <td>${expense.expense_type || 'غير محدد'}</td>
            <td>${formatCurrency(expense.amount)}</td>
            <td>${formatCurrency(expense.tax_amount)}</td>
            <td>${formatCurrency(expense.total_with_tax)}</td>
            <td>${formatDate(expense.expense_date)}</td>
            <td>${paymentMethodText}</td>
            <td>${expense.banks?.name || 'N/A'}</td>
            <td>${expense.details || '-'}</td>
            <td>
                <button class="action-btn edit-btn" data-id="${expense.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                <button class="action-btn delete-btn" data-id="${expense.id}" title="حذف"><i class="fas fa-trash-alt"></i></button>
            </td>
        `;

        // Add event listeners for edit/delete later
        // row.querySelector('.edit-btn').addEventListener('click', () => handleEditExpense(expense));
        // row.querySelector('.delete-btn').addEventListener('click', () => handleDeleteExpense(expense.id));

        expensesTableBody.appendChild(row);
    });
    renderPaginationControls();
};

const renderPaginationControls = () => {
    if (!paginationControls || totalItems <= itemsPerPage) {
        if (paginationControls) paginationControls.innerHTML = '';
        return;
    }

    paginationControls.innerHTML = '';
    const totalPages = Math.ceil(totalItems / itemsPerPage);

    const prevButton = document.createElement('button');
    prevButton.innerHTML = '&laquo; السابق';
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            renderExpensesTablePaginated();
        }
    });
    paginationControls.appendChild(prevButton);

    const pageInfo = document.createElement('span');
    pageInfo.textContent = ` صفحة ${currentPage} من ${totalPages} `;
    pageInfo.style.margin = '0 10px';
    paginationControls.appendChild(pageInfo);

    const nextButton = document.createElement('button');
    nextButton.innerHTML = 'التالي &raquo;';
    nextButton.disabled = currentPage === totalPages;
    nextButton.addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage++;
            renderExpensesTablePaginated();
        }
    });
    paginationControls.appendChild(nextButton);
};

// --- Modal Logic ---
const openExpenseModal = (expenseToEdit = null) => {
    // Ensure selectedMonthId is available globally
    if (!expenseFormSection || !selectedBusId || !selectedMonthId) {
        showMessage(dashboardMessage, 'خطأ: لا يمكن فتح النموذج. الشهر غير محدد أو الحافلة غير محددة.', 'error');
        return;
    }
    console.log("Opening expense modal for month ID:", selectedMonthId);
    resetExpenseForm();

    // Update modal title
    expenseFormTitle.textContent = expenseToEdit ? `تعديل مصروف للحافلة: ${selectedBusIdentifier}` : `إضافة مصروف للحافلة: ${selectedBusIdentifier}`;
    modalBusIdField.value = selectedBusId;
    modalMonthIdField.value = selectedMonthId; // Directly set the month ID

    // Update the label in the details section
    if (modalSelectedMonthLabel && selectedMonthNumber && selectedYearNumber) {
        modalSelectedMonthLabel.textContent = `${getMonthName(selectedMonthNumber)}/${selectedYearNumber}`;
    } else if (modalSelectedMonthLabel) {
        modalSelectedMonthLabel.textContent = 'الشهر المحدد'; // Fallback
    }

    // Set default date for new expense
    if (!expenseToEdit && expenseDateField) {
        expenseDateField.value = formatDate(new Date());
    }

    if (expenseToEdit) {
        // Populate form for editing (implementation needed later)
        console.log("Editing mode - Populate form for:", expenseToEdit.id);
        expenseIdField.value = expenseToEdit.id;
        expenseDateField.value = formatDate(expenseToEdit.expense_date);
        expenseTypeInput.value = expenseToEdit.expense_type;
        amountNumericInput.value = expenseToEdit.amount;
        includeTaxSelect.value = expenseToEdit.include_tax ? 'true' : 'false';
        bankIdSelect.value = expenseToEdit.bank_id || '';
        detailsTextarea.value = expenseToEdit.details || '';
        calculateTax(); // Calculate tax/total based on loaded values
        // Note: Cannot pre-select month/year as that step is removed.
        // Ensure the expenseToEdit.budget_month_id matches selectedMonthId if needed.
    } else {
        // Adding new - Reset ID field
        expenseIdField.value = '';
    }

    // Directly show the details form (Step 2)
    // REMOVED: goToModalStep('month');
    // REMOVED: modalStepMonth.style.display = 'block';
    // REMOVED: modalStepDetails.style.display = 'none';
    // No need to explicitly show modalStepDetails if default display is block in HTML

    expenseFormSection.classList.add('show');
    document.body.style.overflow = 'hidden';
    setTimeout(() => expenseDateField.focus(), 100); // Focus date field
};

const closeExpenseModal = () => {
    if (!expenseFormSection) return;
    expenseFormSection.classList.remove('show');
    document.body.style.overflow = '';
    resetExpenseForm();
};

const resetExpenseForm = () => {
    expenseForm.reset();
    expenseIdField.value = '';
    modalBusIdField.value = '';
    modalMonthIdField.value = ''; // Will be set again when opening
    // REMOVED: modalSelectedYear = null;
    // REMOVED: modalSelectedMonth = null;

    // REMOVED: Reset steps visibility
    // REMOVED: modalStepMonth.style.display = 'block';
    // REMOVED: modalStepDetails.style.display = 'none';

    // REMOVED: Reset dropdowns and buttons for steps
    // REMOVED: modalYearSelect.disabled = false;
    // REMOVED: modalMonthSelect.innerHTML = '<option value="">اختر الشهر...</option>';
    // REMOVED: modalMonthSelect.disabled = true;
    // REMOVED: modalNextToDetailsBtn.disabled = true;

    // Reset calculated fields and payment method display
    if(amountNumericInput) amountNumericInput.value = ''; // Clear amount
    if(taxAmountInput) taxAmountInput.value = '0.00';
    if(totalWithTaxInput) totalWithTaxInput.value = '0.00';
    if(bankIdSelect) bankIdSelect.value = '';
    if(includeTaxSelect) includeTaxSelect.value = 'false'; // Default to no tax
    if(expenseDateField) expenseDateField.value = ''; // Clear date
    if(expenseTypeInput) expenseTypeInput.value = ''; // Clear type
    if(detailsTextarea) detailsTextarea.value = ''; // Clear details


    // Reset messages
    if (expenseFormMessage) expenseFormMessage.style.display = 'none';
    if (expenseFormMessage) expenseFormMessage.className = 'message';
};

// --- Form Calculations ---
const calculateTax = () => { // Renamed from calculateVat
    const amount = parseFloat(amountNumericInput.value) || 0;
    const includeTax = includeTaxSelect.value === 'true'; // Use includeTaxSelect
    let taxAmount = 0;
    let totalWithTax = amount;

    if (includeTax && amount > 0) {
        taxAmount = amount * VAT_RATE; // VAT_RATE is still the percentage
        totalWithTax = amount + taxAmount;
    }

    taxAmountInput.value = formatCurrency(taxAmount);     // Update taxAmountInput
    totalWithTaxInput.value = formatCurrency(totalWithTax); // Update totalWithTaxInput
};

// --- Helper Function to Add Bank Transaction ---
const addBankTransactionForBusExpense = async (expenseData, busIdentifier) => {
    console.log("Attempting to add bank transaction for expense:", expenseData); // Log input data

    // Ensure expenseData and necessary fields exist
    if (!expenseData || !expenseData.id) {
        console.error("Invalid expense data provided to addBankTransactionForBusExpense:", expenseData);
        throw new Error("بيانات المصروف غير صالحة لإضافة معاملة بنكية.");
    }
    if (!expenseData.bank_id) {
        console.warn("Bank ID is missing in expense data, cannot add bank transaction for expense:", expenseData.id);
        throw new Error("لم يتم تحديد البنك للمصروف، لا يمكن إضافة معاملة بنكية.");
    }
    if (expenseData.total_with_tax === undefined || expenseData.total_with_tax === null) {
         console.error("Total amount with tax is missing in expense data:", expenseData);
         throw new Error("المبلغ الإجمالي للمصروف غير متوفر لإضافة معاملة بنكية.");
    }
     if (!expenseData.expense_date) {
         console.error("Expense date is missing in expense data:", expenseData);
         throw new Error("تاريخ المصروف غير متوفر لإضافة معاملة بنكية.");
     }

    const transactionAmount = expenseData.total_with_tax;
    const description = `حافلة ${busIdentifier || 'غير محدد'} - ${expenseData.expense_type || 'مصروف'}${expenseData.details ? ' - ' + expenseData.details.trim() : ''}`;

    const bankTransactionData = {
        bank_id: expenseData.bank_id,
        transaction_date: expenseData.expense_date,
        transaction_type: 'withdrawal',
        amount: transactionAmount,
        description: description,
        reference_table: 'bus_expenses',
        reference_id: expenseData.id
    };

    console.log("Constructed Bank Transaction Data:", bankTransactionData); // Log data before insert

    try {
        const { data: transactionResult, error: bankError } = await _supabase
            .from('bank_transactions')
            .insert([bankTransactionData])
            .select() // Optionally select the inserted transaction for confirmation
            .single(); // Assuming you insert one transaction

        if (bankError) {
            console.error('Supabase bank transaction insert error:', bankError); // Log the specific Supabase error
            // Provide a more specific error message if possible
            let userMessage = `فشل تسجيل الحركة البنكية: ${bankError.message}`;
            if (bankError.code === '23503') { // Example: Foreign key violation
                userMessage = `فشل تسجيل الحركة البنكية: البنك المحدد غير صالح (${bankError.details || bankError.message})`;
            } else if (bankError.code === '23505') { // Example: Unique constraint violation
                 userMessage = `فشل تسجيل الحركة البنكية: هذه المعاملة مسجلة مسبقاً (${bankError.details || bankError.message})`;
            }
            throw new Error(userMessage); // Throw a new error with a potentially clearer message
        } else {
            console.log("Bank transaction added successfully:", transactionResult); // Log the result
            return true;
        }
    } catch (error) {
        // Catch errors from the insert attempt or re-thrown errors
        console.error('Error during bank transaction insertion process:', error);
        // Ensure the error thrown from here is informative
        throw new Error(error.message || "حدث خطأ غير متوقع أثناء تسجيل الحركة البنكية.");
    }
};

// --- Form Submission ---
const handleExpenseSubmit = async (event) => {
    event.preventDefault();
    // Use the global selectedMonthId directly
    if (!selectedMonthId || !selectedBusId) {
        showMessage(expenseFormMessage, 'خطأ: بيانات غير مكتملة (الحافلة/الشهر).', 'error');
        return;
    }

    const submitBtn = expenseForm.querySelector('.submit-btn');
    submitBtn.disabled = true;
    submitBtn.textContent = 'جاري الحفظ...';
    showMessage(expenseFormMessage, 'جاري حفظ المصروف...', 'info', 0);

    calculateTax();

    const paymentMethodValue = "Bank";

    const expenseData = {
        bus_id: selectedBusId,
        budget_month_id: selectedMonthId, // Use global selectedMonthId
        expense_date: expenseDateField.value,
        expense_type: expenseTypeInput.value.trim(),
        amount: parseFloat(amountNumericInput.value) || 0,
        include_tax: includeTaxSelect.value === 'true',
        tax_amount: parseFloat(taxAmountInput.value) || 0,
        payment_method: paymentMethodValue,
        bank_id: bankIdSelect.value || null,
        details: detailsTextarea.value.trim() || null,
    };

    let savedExpense = null;
    let bankTransactionWarning = null;

    try {
        // --- Validation ---
        if (!expenseData.expense_date) throw new Error('تاريخ المصروف مطلوب.');
        if (!expenseData.expense_type) throw new Error('نوع المصروف مطلوب.');
        if (expenseData.amount <= 0) throw new Error('المبلغ الأساسي يجب أن يكون أكبر من صفر.');
        if ((parseFloat(totalWithTaxInput.value) || 0) <= 0) throw new Error('المبلغ الإجمالي (شامل الضريبة) يجب أن يكون أكبر من صفر.');
        if (!expenseData.bank_id) {
            throw new Error('البنك مطلوب.');
        }

        const expenseId = expenseIdField.value;
        console.log("Attempting to save expense data (excluding generated columns):", expenseData);

        if (expenseId) {
            // Update existing expense
            console.log("Updating expense ID:", expenseId);
            // Ensure budget_month_id is included in the update data
            const { data, error } = await _supabase
                .from('bus_expenses')
                .update(expenseData) // expenseData already contains budget_month_id
                .eq('id', expenseId)
                .select()
                .single();
            if (error) {
                 console.error("Supabase update error details:", error);
                 throw error;
            }
            savedExpense = data;
            console.log("Expense updated successfully (with generated values):", savedExpense);
            // TODO: Handle potential bank transaction update/deletion/creation if bank details changed.
            // For now, we assume the bank transaction might need manual adjustment if bank/amount changes significantly.
            bankTransactionWarning = "تم تحديث المصروف. قد تحتاج المعاملة البنكية المرتبطة للمراجعة اليدوية إذا تغير البنك أو المبلغ.";

        } else {
            // Insert new expense
            const { data, error } = await _supabase
                .from('bus_expenses')
                .insert([expenseData])
                .select()
                .single();
            if (error) {
                 console.error("Supabase insert error details:", error);
                 throw error;
            }
            savedExpense = data;
            console.log("Expense saved successfully (with generated values):", savedExpense);

             // --- Add Bank Transaction for NEW expense ---
            if (savedExpense && savedExpense.id) {
                console.log("Proceeding to add bank transaction for saved expense ID:", savedExpense.id);
                try {
                    await addBankTransactionForBusExpense(savedExpense, selectedBusIdentifier);
                    console.log("Bank transaction process completed successfully for expense:", savedExpense.id);
                } catch (bankError) {
                    console.error('Caught bank transaction error in handleExpenseSubmit:', bankError);
                    bankTransactionWarning = bankError?.message || "فشل تسجيل الحركة البنكية لسبب غير معروف.";
                    console.log(`Setting bankTransactionWarning to: "${bankTransactionWarning}"`);
                }
            } else {
                 console.error("Expense data is missing or invalid after save attempt. Cannot add bank transaction. SavedExpense:", savedExpense);
                 bankTransactionWarning = "خطأ حرج: بيانات المصروف غير متوفرة بعد الحفظ، تعذرت إضافة المعاملة البنكية.";
                 console.log(`Setting bankTransactionWarning due to missing savedExpense: "${bankTransactionWarning}"`);
            }
        }

        // --- Success/Warning Message ---
        if (bankTransactionWarning) {
             console.log("Displaying warning message:", bankTransactionWarning);
             showMessage(expenseFormMessage, `تم حفظ المصروف، لكن: ${bankTransactionWarning}`, 'warning', 7000);
        } else {
             console.log("Displaying success message.");
             const successMessage = expenseId ? 'تم تحديث المصروف بنجاح!' : 'تم حفظ المصروف بنجاح!';
             showMessage(expenseFormMessage, successMessage, 'success');
        }

        fetchBusExpenses(searchInput.value.trim()); // Refresh table
        setTimeout(closeExpenseModal, bankTransactionWarning ? 7000 : 1500);

    } catch (error) {
        console.error('Error during expense saving process:', error);
        let displayMessage = `خطأ في حفظ المصروف: ${error.message}`;
        showMessage(expenseFormMessage, displayMessage, 'error', 0);
    } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = expenseIdField.value ? 'تحديث المصروف' : 'حفظ المصروف';
    }
};

// --- Delete Handling ---
const handleDeleteExpense = async (expenseId) => {
    if (!_supabase) return;

    const confirmation = confirm('هل أنت متأكد من حذف هذا المصروف؟ سيتم أيضاً محاولة حذف المعاملة البنكية المرتبطة.');
    if (!confirmation) return;

    showMessage(listMessage, 'جاري حذف المصروف...', 'info', 0);

    try {
        // 1. Delete from bus_expenses
        const { error: deleteError } = await _supabase
            .from('bus_expenses')
            .delete()
            .eq('id', expenseId);

        if (deleteError) throw new Error(`فشل حذف المصروف: ${deleteError.message}`);

        // 2. Attempt to delete corresponding bank transaction
        const { error: bankTxDeleteError } = await _supabase
            .from('bank_transactions')
            .delete()
            .eq('reference_id', expenseId)
            .eq('transaction_source_type', 'bus_expense'); // Be specific

        if (bankTxDeleteError) {
            console.warn("Expense deleted, but failed to delete linked bank transaction:", bankTxDeleteError);
            showMessage(listMessage, `تم حذف المصروف، ولكن فشل حذف المعاملة البنكية المرتبطة: ${bankTxDeleteError.message}. الرجاء المراجعة اليدوية.`, 'warning', 0);
        } else {
            showMessage(listMessage, 'تم حذف المصروف والمعاملة البنكية المرتبطة بنجاح.', 'success');
        }

        fetchExpenses(); // Refresh the table

    } catch (error) {
        console.error('Error deleting expense:', error);
        showMessage(listMessage, `خطأ في عملية الحذف: ${error.message}`, 'error', 0);
    }
};

// --- Event Listeners Setup ---
const setupEventListeners = () => {
    // *** REMOVED: Listener for addExpenseForm (now handled by expenseForm) ***
    // if (addExpenseForm) {
    //     addExpenseForm.addEventListener('submit', handleAddExpense); // OLD, REMOVED
    // }

    // REMOVED: Listener for the old footer back button
    // if (backToDashboardBtn) {
    //     backToDashboardBtn.addEventListener('click', () => {
    //         window.location.href = '../financial_dashboard.html'; // Navigate back to dashboard
    //     });
    // }

    // Add listeners for edit modal if implemented

    // Sidebar listener is set during population

    // Back button (Sidebar)
    if (backToFinanceBtnSidebar) {
        backToFinanceBtnSidebar.addEventListener('click', () => {
            window.location.href = '../financial_dashboard.html'; // Adjust path if needed
        });
    }

    // Open Modal Button
    // *** CORRECTED: Check for openAddExpenseModalBtn ***
    if (openAddExpenseModalBtn) {
        openAddExpenseModalBtn.addEventListener('click', () => openExpenseModal()); // Pass null for adding new
    }

    // REMOVED: Modal Navigation Buttons Listeners
    // if (modalYearSelect) modalYearSelect.addEventListener('change', handleModalYearChange);
    // if (modalMonthSelect) modalMonthSelect.addEventListener('change', handleModalMonthChange);
    // if (modalNextToDetailsBtn) modalNextToDetailsBtn.addEventListener('click', () => goToModalStep('details'));
    // if (modalBackToMonthBtn) modalBackToMonthBtn.addEventListener('click', () => goToModalStep('month'));

    // Modal Close Buttons
    if (closeExpenseFormBtn) closeExpenseFormBtn.addEventListener('click', closeExpenseModal);
    if (expenseFormSection) expenseFormSection.addEventListener('click', (e) => {
        if (e.target === expenseFormSection) closeExpenseModal();
    });

    // Form Submission
    if (expenseForm) {
        expenseForm.addEventListener('submit', handleExpenseSubmit);
    }

    // Form Calculation Listeners
    if (amountNumericInput) amountNumericInput.addEventListener('input', calculateTax);
    if (includeTaxSelect) includeTaxSelect.addEventListener('change', calculateTax);

    // Search/Filter Listeners
    if (filterBtn) filterBtn.addEventListener('click', () => {
        currentPage = 1;
        fetchBusExpenses(searchInput.value.trim());
    });
    if (resetFilterBtn) resetFilterBtn.addEventListener('click', () => {
        if (searchInput) searchInput.value = '';
        currentPage = 1;
        fetchBusExpenses();
    });
    if (searchInput) searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            if (filterBtn) filterBtn.click();
        }
    });

    // Table Action Buttons Listener (Delegation)
    if (expensesTableBody) {
        expensesTableBody.addEventListener('click', (event) => {
            const editButton = event.target.closest('.edit-btn');
            const deleteButton = event.target.closest('.delete-btn');

            if (editButton) {
                const expenseId = editButton.dataset.id;
                const expenseToEdit = currentBusExpenses.find(exp => exp.id == expenseId);
                if (expenseToEdit) {
                    openExpenseModal(expenseToEdit);
                } else {
                    console.error("Expense not found for editing:", expenseId);
                    showMessage(listMessage, 'خطأ: المصروف المحدد للتعديل غير موجود.', 'error');
                }
            } else if (deleteButton) {
                const expenseId = deleteButton.dataset.id;
                handleDeleteExpense(expenseId);
            }
        });
    }


    // Global Escape Listener
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && expenseFormSection?.classList.contains('show')) {
            closeExpenseModal();
        }
    });
};

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log('Bus Expenses page loaded.');

    // Get selected month and year from sessionStorage
    selectedMonthId = sessionStorage.getItem('selectedBudgetMonthId');
    selectedMonthNumber = sessionStorage.getItem('selectedBudgetMonthNumber');
    selectedYearNumber = sessionStorage.getItem('selectedBudgetYearNumber');

    console.log('Retrieved from sessionStorage:', { selectedMonthId, selectedMonthNumber, selectedYearNumber });

    // Update page title
    if (pageMonthYearTitle) {
        if (selectedMonthNumber && selectedYearNumber) {
            pageMonthYearTitle.textContent = `${getMonthName(selectedMonthNumber)} ${selectedYearNumber}`;
        } else {
            pageMonthYearTitle.textContent = 'الشهر غير محدد';
        }
    }

    // Check if Month ID is missing
    if (!selectedMonthId) {
        console.error('CRITICAL: Selected Budget Month ID not found in sessionStorage.');
        showMessage(dashboardMessage, 'خطأ حرج: لم يتم العثور على معرّف الشهر المحدد. الرجاء العودة واختيار شهر أولاً.', 'error', 0);
        // Disable form?
        // REMOVED: if (addExpenseForm) addExpenseForm.style.opacity = '0.5'; // Old form reference
        if (expensesTableBody) expensesTableBody.innerHTML = `<tr><td colspan="10" class="loading-message error">الرجاء العودة واختيار شهر.</td></tr>`; // Updated colspan
        return; // Stop further execution
    }

    // Fetch initial data
    try {
        await Promise.all([
            fetchBuses(),
            fetchBanks()
        ]);

        // *** CORRECTED: Call fetchBusExpenses() instead of fetchExpenses() ***
        // No initial fetch of expenses needed until a bus is selected from the sidebar.
        // await fetchBusExpenses(); // REMOVED - Fetch when bus is selected

        // Display initial message in table
        if (expensesTableBody) expensesTableBody.innerHTML = `<tr><td colspan="10" class="loading-message">الرجاء اختيار حافلة من القائمة الجانبية لعرض المصاريف.</td></tr>`;
        if (openAddExpenseModalBtn) openAddExpenseModalBtn.disabled = true; // Disable add button initially

    } catch (error) {
        console.error("Error during initial data fetch (buses/banks):", error);
        showMessage(dashboardMessage, `حدث خطأ أثناء تحميل بيانات الصفحة الأساسية: ${error.message}`, 'error', 0);
    }

    // Setup event listeners after fetching initial dropdown data
    setupEventListeners();

    // Initial state message
    if (mainHeaderTitle) mainHeaderTitle.innerHTML = `<i class="fas fa-info-circle"></i> اختر حافلة`;
    if (mainHeaderSubtitle) mainHeaderSubtitle.textContent = `الرجاء اختيار حافلة من القائمة الجانبية لعرض وإدارة مصاريفها.`;
    resetBusStats();
});
