<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختيار الشهر المالي</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="../../config.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script defer src="script.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-calendar-alt"></i> اختيار الفترة المالية</h1>
            <p>الرجاء اختيار السنة والشهر لعرض الخيارات المتاحة.</p>
        </header>

        <main>
            <div class="selection-card">
                <div class="form-group">
                    <label for="select-year">السنة المالية:</label>
                    <select id="select-year">
                        <option value="">اختر السنة...</option>
                        <!-- Years loaded by JS -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="select-month">الشهر:</label>
                    <select id="select-month" disabled>
                        <option value="">اختر السنة أولاً...</option>
                        <!-- Months loaded by JS -->
                    </select>
                </div>
                <div id="message-area" class="message" style="display: none;"></div>
            </div>

            <div id="actions-card" class="actions-card" style="display: none;">
                <h2><i class="fas fa-cogs"></i> الإجراءات المتاحة لشهر <span id="selected-month-name"></span> سنة <span id="selected-year-number"></span></h2>
                 <div id="month-status-display" class="month-status"></div>
                <div class="action-buttons">
                    <button id="manage-subscriptions-btn" class="action-btn primary">
                        <i class="fas fa-user-graduate"></i> إدارة الاشتراكات
                    </button>
                    <button id="view-transactions-btn" class="action-btn secondary">
                        <i class="fas fa-exchange-alt"></i> عرض المعاملات
                    </button>
                    <!-- Add more actions here if needed -->
                </div>
            </div>
        </main>

        <footer>
            <p>© 2023 نظام الإدارة المالية</p>
        </footer>
    </div>
</body>
</html>
