<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الطلاب</title>

    <!-- الترتيب مهم جداً حسب الدليل -->
    <!-- Shared Styles -->
    <link rel="stylesheet" href="../shared_styles.css">
    <!-- Page Specific Styles -->
    <link rel="stylesheet" href="style.css">
    <!-- Sidebar Component Styles -->
    <link rel="stylesheet" href="../shared_components/sidebar.css">

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="../config.js"></script>
    <script src="../auth.js"></script>
    <!-- Sidebar Component Script -->
    <script src="../shared_components/sidebar.js"></script>
    <!-- Page Script -->
    <script defer src="script.js"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body class="dashboard-layout" data-load-sidebar="true">
    <!-- Top Navigation Bar -->
    <nav class="top-navbar">
        <div class="navbar-left">
            <button id="sidebar-toggle" class="sidebar-toggle-btn">
                <i class="fas fa-bars"></i>
            </button>
            <div class="navbar-brand">
                <i class="fas fa-graduation-cap"></i>
                <span>نظام إدارة الطلاب</span>
            </div>
        </div>
        <div class="navbar-right">
            <div class="navbar-user">
                <span id="navbar-username"></span>
                <button id="logout-btn" class="logout-btn" style="display: none;">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Sidebar Container (will be populated by sidebar.js) -->
    <div id="sidebar-container"></div>

    <!-- Main Content Area -->
    <main class="main-content">
        <div class="content-wrapper">
            <!-- Dashboard Header -->
            <header class="dashboard-header">
                <div class="header-content">
                    <h1>
                        <i class="fas fa-graduation-cap"></i>
                        نظام إدارة الطلاب
                    </h1>
                    <p>إدارة وتتبع بيانات الطلاب بكل سهولة</p>
                </div>
            </header>

            <!-- Statistics Cards -->
            <section class="stats-section">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-user-graduate"></i></div>
                    <div class="stat-info">
                        <h3 id="total-students">0</h3>
                        <p>إجمالي الطلاب</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                    <div class="stat-info">
                        <h3 id="active-students">0</h3>
                        <p>طلاب نشطون</p>
                    </div>
                </div>
                <div class="stat-card clickable-card" id="schools-card" title="انقر لإدارة المدارس">
                    <div class="stat-icon"><i class="fas fa-school"></i></div>
                    <div class="stat-info">
                        <h3 id="total-schools">0</h3>
                        <p>المدارس المسجلة</p>
                    </div>
                </div>
                <div class="stat-card clickable-card" id="neighborhoods-card" title="انقر لإدارة الأحياء">
                    <div class="stat-icon"><i class="fas fa-map-marker-alt"></i></div>
                    <div class="stat-info">
                        <h3 id="total-neighborhoods">0</h3>
                        <p>الأحياء المسجلة</p>
                    </div>
                </div>
            </section>

            <!-- Removed filters section - will be replaced with simple card view buttons -->

            <!-- Controls Section -->
            <section class="controls-section">
                <div class="control-card">
                    <div class="card-header">
                        <h2>أدوات التحكم</h2>
                    </div>
                    <div class="card-body">
                        <div class="controls-grid">
                            <!-- Add Student Button -->
                            <button id="add-student-btn" class="control-btn add-btn">
                                <i class="fas fa-plus-circle"></i> إضافة طالب
                            </button>
                            <!-- Manage Schools Button will be added dynamically in JavaScript -->
                            <div class="search-container">
                                <input type="text" id="search-input" placeholder="ابحث عن طالب...">
                                <button id="search-btn" class="search-btn"><i class="fas fa-search"></i></button>
                            </div>
                            <button id="print-report-btn" class="control-btn print-btn">
                                <i class="fas fa-print"></i> طباعة تقرير
                            </button>
                            <button id="view-cards-btn" class="control-btn cards-btn">
                                <i class="fas fa-th-large"></i> عرض الكروت
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Students Display Section -->
            <section class="display-section">
                <!-- Table View -->
                <div id="table-view" class="table-card">
                    <div class="card-header">
                        <h2>قائمة الطلاب</h2>
                        <span class="badge" id="students-count">0</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="students-table">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>رقم ولي الأمر</th>
                                        <th>المدرسة</th>
                                        <th>الحي</th>
                                        <th>الحالة</th>
                                        <th>الموقع</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="students-tbody">
                                    <tr><td colspan="7" class="loading-message">جاري تحميل البيانات...</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <div id="list-message" class="message"></div>
                        <div class="pagination" id="pagination-controls">
                            <!-- Pagination will be added by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Cards View -->
                <div id="cards-view" class="cards-container" style="display: none;">
                    <div class="cards-header">
                        <h2 id="cards-title">عرض الكروت</h2>
                        <span class="badge" id="cards-count">0</span>
                    </div>
                    <div id="cards-grid" class="cards-grid">
                        <!-- Cards will be generated by JavaScript -->
                    </div>
                </div>
            </section>

            <!-- Student Form Section - Updated Structure -->
            <section id="add-student-section" class="form-section">
                <div class="form-card">
                    <div class="card-header">
                        <h2 id="form-title">إضافة طالب جديد</h2>
                        <button id="close-form-btn" class="close-btn"><i class="fas fa-times"></i></button>
                    </div>
                    <div class="card-body">
                        <form id="student-form">
                            <input type="hidden" id="student_id" name="student_id">

                            <!-- Personal Information Section -->
                            <fieldset class="form-fieldset">
                                <legend>البيانات الشخصية</legend>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="student_name">اسم الطالب <span class="required">*</span></label>
                                        <input type="text" id="student_name" name="student_name" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="parent_phone">رقم جوال ولي الأمر <span class="required">*</span></label>
                                        <input type="tel" id="parent_phone" name="parent_phone" placeholder="05xxxxxxxx">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="student_phone">رقم جوال الطالب</label>
                                        <input type="tel" id="student_phone" name="student_phone" placeholder="05xxxxxxxx">
                                    </div>
                                    <div class="form-group form-group-checkbox">
                                        <label for="is_active">
                                            <input type="checkbox" id="is_active" name="is_active" value="true" checked>
                                            نشط
                                        </label>
                                    </div>
                                </div>
                            </fieldset>

                            <!-- Study Information Section -->
                            <fieldset class="form-fieldset">
                                <legend>البيانات الدراسية</legend>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="stage">المرحلة الدراسية <span class="required">*</span></label>
                                        <select id="stage" name="stage" required>
                                            <option value="" disabled selected>اختر المرحلة</option>
                                            <!-- Will be populated dynamically -->
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="school_id" id="school-label">المدرسة <span class="required">*</span></label>
                                        <select id="school_id" name="school_id" required>
                                            <option value="" disabled selected>اختر المدرسة</option>
                                            <!-- Will be populated dynamically -->
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="class_id">الصف <span class="required">*</span></label>
                                        <select id="class_id" name="class_id" required>
                                            <option value="" disabled selected>اختر الصف</option>
                                            <!-- Will be populated dynamically -->
                                        </select>
                                    </div>
                                </div>
                            </fieldset>

                            <!-- Address and Location Section -->
                            <fieldset class="form-fieldset">
                                <legend>بيانات العنوان والموقع</legend>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="neighborhood_id">الحي <span class="required">*</span></label>
                                        <select id="neighborhood_id" name="neighborhood_id" required>
                                            <option value="" disabled selected>اختر الحي</option>
                                            <!-- Will be populated dynamically -->
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="location_url">رابط الموقع</label>
                                        <input type="url" id="location_url" name="location_url" placeholder="https://maps.google.com/...">
                                        <small>أدخل رابط الموقع من خرائط Google</small>
                                    </div>
                                </div>
                                <div class="form-row">
                                     <div class="form-group">
                                        <label for="house_photo">صورة باب البيت</label>
                                        <input type="file" id="house_photo" name="house_photo" accept="image/*">
                                        <small>أرفق صورة لباب البيت لتسهيل وصول السائق</small>
                                    </div>
                                </div>
                            </fieldset>

                            <!-- Removed fields: platform, student_status (replaced by is_active checkbox) -->
                            <!-- Removed fields: class_name, school_name, neighborhood_name (replaced by selects) -->

                            <div class="form-actions">
                                <button type="submit" class="submit-btn">حفظ</button>
                                <button type="button" id="cancel-edit-btn" class="cancel-btn">إلغاء</button>
                            </div>
                        </form>
                        <div id="form-message" class="message"></div>
                    </div>
                </div>
            </section>

            <!-- Schools Management Modal -->
            <section id="schools-modal" class="form-section">
                <div class="form-card">
                    <div class="card-header">
                        <h2>إدارة المدارس</h2>
                        <button id="close-schools-modal" class="close-btn"><i class="fas fa-times"></i></button>
                    </div>
                    <div class="card-body">
                        <!-- Add School Form -->
                        <div class="add-form-section">
                            <h3>إضافة مدرسة جديدة</h3>
                            <form id="school-form">
                                <input type="hidden" id="school_edit_id" name="school_edit_id">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="school_name">اسم المدرسة <span class="required">*</span></label>
                                        <input type="text" id="school_name" name="school_name" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="school_stage">المرحلة الدراسية <span class="required">*</span></label>
                                        <select id="school_stage" name="school_stage" required>
                                            <option value="" disabled selected>اختر المرحلة</option>
                                            <option value="ابتدائي">ابتدائي</option>
                                            <option value="متوسط">متوسط</option>
                                            <option value="ثانوي">ثانوي</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="submit-btn">
                                        <i class="fas fa-save"></i> حفظ
                                    </button>
                                    <button type="button" id="cancel-school-edit" class="cancel-btn">
                                        <i class="fas fa-times"></i> إلغاء
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Schools List -->
                        <div class="list-section">
                            <h3>قائمة المدارس</h3>
                            <div class="table-responsive">
                                <table id="schools-table">
                                    <thead>
                                        <tr>
                                            <th>اسم المدرسة</th>
                                            <th>المرحلة الدراسية</th>
                                            <th>إجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="schools-tbody">
                                        <tr><td colspan="3" class="loading-message">جاري تحميل البيانات...</td></tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Cards View Options Modal -->
            <section id="cards-options-modal" class="form-section">
                <div class="form-card">
                    <div class="card-header">
                        <h2><i class="fas fa-th-large"></i> اختر نوع العرض</h2>
                        <button id="close-cards-options-modal" class="close-btn"><i class="fas fa-times"></i></button>
                    </div>
                    <div class="card-body">
                        <div class="cards-options-grid">
                            <div class="card-option schools-option" data-view="cards-school">
                                <div class="option-icon">
                                    <i class="fas fa-school"></i>
                                </div>
                                <h3>كروت المدارس</h3>
                                <p>عرض الطلاب مجمعين حسب المدرسة</p>
                                <div class="option-stats" id="schools-stats">
                                    <span class="stat-number">0</span>
                                    <span class="stat-label">مدرسة</span>
                                </div>
                            </div>

                            <div class="card-option neighborhoods-option" data-view="cards-neighborhood">
                                <div class="option-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <h3>كروت الأحياء</h3>
                                <p>عرض الطلاب مجمعين حسب الحي</p>
                                <div class="option-stats" id="neighborhoods-stats">
                                    <span class="stat-number">0</span>
                                    <span class="stat-label">حي</span>
                                </div>
                            </div>

                            <div class="card-option siblings-option" data-view="cards-siblings">
                                <div class="option-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <h3>كروت الأخوان</h3>
                                <p>عرض الطلاب الأخوان مجمعين</p>
                                <div class="option-stats" id="siblings-stats">
                                    <span class="stat-number">0</span>
                                    <span class="stat-label">مجموعة أخوان</span>
                                </div>
                            </div>

                            <div class="card-option table-option" data-view="table">
                                <div class="option-icon">
                                    <i class="fas fa-table"></i>
                                </div>
                                <h3>عرض الجدول</h3>
                                <p>العرض التقليدي في جدول</p>
                                <div class="option-stats" id="table-stats">
                                    <span class="stat-number">0</span>
                                    <span class="stat-label">طالب</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Neighborhoods Management Modal -->
            <section id="neighborhoods-modal" class="form-section">
                <div class="form-card">
                    <div class="card-header">
                        <h2>إدارة الأحياء</h2>
                        <button id="close-neighborhoods-modal" class="close-btn"><i class="fas fa-times"></i></button>
                    </div>
                    <div class="card-body">
                        <!-- Add Neighborhood Form -->
                        <div class="add-form-section">
                            <h3>إضافة حي جديد</h3>
                            <form id="neighborhood-form">
                                <input type="hidden" id="neighborhood_edit_id" name="neighborhood_edit_id">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="neighborhood_name">اسم الحي <span class="required">*</span></label>
                                        <input type="text" id="neighborhood_name" name="neighborhood_name" required>
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="submit-btn">
                                        <i class="fas fa-save"></i> حفظ
                                    </button>
                                    <button type="button" id="cancel-neighborhood-edit" class="cancel-btn">
                                        <i class="fas fa-times"></i> إلغاء
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Neighborhoods List -->
                        <div class="list-section">
                            <h3>قائمة الأحياء</h3>
                            <div class="table-responsive">
                                <table id="neighborhoods-table">
                                    <thead>
                                        <tr>
                                            <th>اسم الحي</th>
                                            <th>إجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="neighborhoods-tbody">
                                        <tr><td colspan="2" class="loading-message">جاري تحميل البيانات...</td></tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>
</body>
</html>
