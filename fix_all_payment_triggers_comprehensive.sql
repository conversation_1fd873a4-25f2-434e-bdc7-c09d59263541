-- إصلاح شامل لجميع مشاكل التريجرات والدفعات
-- المشاكل المُصلحة:
-- 1. أنواع المعاملات (استخدام القيم الإنجليزية)
-- 2. التعامل مع الحقول المختلفة في كل جدول
-- 3. التأكد من وجود جميع الحقول المطلوبة

-- الخطوة 1: إصلاح دالة generic_payment_trigger_handler
CREATE OR REPLACE FUNCTION public.generic_payment_trigger_handler()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_description text;
    v_source_table_name text := TG_TABLE_NAME;
    v_original_record_id text;
    v_bank_id uuid;
    v_budget_month_id uuid;
    v_transaction_date date;
    v_amount numeric;

    -- إصلاح: استخدام القيم العربية المُعرفة في النوع
    v_income_transaction_type public.transaction_type := 'إيداع';
    v_expense_transaction_type public.transaction_type := 'سحب';
    v_current_transaction_type public.transaction_type;
    v_reversed_transaction_type public.transaction_type;

    v_log_entry_id uuid;
    v_old_log_entry_id uuid;
    v_is_new_transaction boolean := false;
    v_is_old_transaction_reversal boolean := false;

BEGIN
    RAISE NOTICE 'generic_payment_trigger_handler: Processing % operation on table %', TG_OP, v_source_table_name;

    -- تحديد الحقول المشتركة بناءً على الجدول
    IF v_source_table_name = 'student_payments' THEN
        v_current_transaction_type := v_income_transaction_type;
        IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := NEW.id::text;
            v_bank_id := NEW.bank_id;
            v_budget_month_id := NEW.budget_month_id;
            v_transaction_date := NEW.payment_date;
            v_amount := NEW.amount;
            v_description := 'Student payment: ' || NEW.student_id::text;
            v_is_new_transaction := true;
        END IF;
        IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := OLD.id::text;
            v_bank_id := OLD.bank_id;
            v_budget_month_id := OLD.budget_month_id;
            v_transaction_date := OLD.payment_date;
            v_amount := OLD.amount;
            v_description := 'Reversal for student payment: ' || OLD.student_id::text;
            v_is_old_transaction_reversal := true;
        END IF;

    ELSIF v_source_table_name = 'enterprise_group_payments' THEN
        v_current_transaction_type := v_income_transaction_type;
        IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := NEW.id::text;
            v_bank_id := NEW.payment_bank_id;
            -- جلب budget_month_id من enterprise_subscriptions
            SELECT es.budget_month_id INTO v_budget_month_id
            FROM public.enterprise_subscriptions es
            WHERE es.id = NEW.enterprise_subscription_id;
            v_transaction_date := NEW.payment_date;
            v_amount := NEW.payment_amount;
            v_description := 'Enterprise group payment: ' || COALESCE(NEW.enterprise_group_id::text, 'unknown');
            v_is_new_transaction := true;
        END IF;
        IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := OLD.id::text;
            v_bank_id := OLD.payment_bank_id;
            SELECT es.budget_month_id INTO v_budget_month_id
            FROM public.enterprise_subscriptions es
            WHERE es.id = OLD.enterprise_subscription_id;
            v_transaction_date := OLD.payment_date;
            v_amount := OLD.payment_amount;
            v_description := 'Reversal for enterprise group payment: ' || COALESCE(OLD.enterprise_group_id::text, 'unknown');
            v_is_old_transaction_reversal := true;
        END IF;

    ELSIF v_source_table_name = 'enterprise_subscriptions' THEN
        v_current_transaction_type := v_income_transaction_type;
        IF TG_OP = 'INSERT' THEN
            IF NEW.paid_amount > 0 AND NEW.payment_bank_id IS NOT NULL AND NEW.payment_date IS NOT NULL THEN
                v_original_record_id := NEW.id::text;
                v_bank_id := NEW.payment_bank_id;
                v_budget_month_id := NEW.budget_month_id;
                v_transaction_date := NEW.payment_date;
                v_amount := NEW.paid_amount;
                v_description := 'Initial payment for enterprise subscription: ' || NEW.enterprise_id::text;
                v_is_new_transaction := true;
            END IF;
        ELSIF TG_OP = 'UPDATE' THEN
            -- عكس الدفعة القديمة إذا تغيرت
            IF OLD.paid_amount > 0 AND OLD.payment_bank_id IS NOT NULL AND OLD.payment_date IS NOT NULL AND
               (OLD.paid_amount IS DISTINCT FROM NEW.paid_amount OR
                OLD.payment_bank_id IS DISTINCT FROM NEW.payment_bank_id OR
                OLD.payment_date IS DISTINCT FROM NEW.payment_date)
            THEN
                v_original_record_id := OLD.id::text;
                v_bank_id := OLD.payment_bank_id;
                v_budget_month_id := OLD.budget_month_id;
                v_transaction_date := OLD.payment_date;
                v_amount := OLD.paid_amount;
                v_description := 'Reversal for enterprise subscription payment update: ' || OLD.enterprise_id::text;
                v_is_old_transaction_reversal := true;
            END IF;
            -- إضافة الدفعة الجديدة
            IF NEW.paid_amount > 0 AND NEW.payment_bank_id IS NOT NULL AND NEW.payment_date IS NOT NULL THEN
                v_original_record_id := NEW.id::text;
                v_bank_id := NEW.payment_bank_id;
                v_budget_month_id := NEW.budget_month_id;
                v_transaction_date := NEW.payment_date;
                v_amount := NEW.paid_amount;
                v_description := 'Updated payment for enterprise subscription: ' || NEW.enterprise_id::text;
                v_is_new_transaction := true;
            END IF;
        END IF;

    ELSIF v_source_table_name = 'nathriyat_transactions' THEN
        v_current_transaction_type := v_expense_transaction_type;
        IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := NEW.id::text;
            v_bank_id := NEW.bank_id;
            v_budget_month_id := NEW.budget_month_id;
            v_transaction_date := NEW.transaction_date;
            v_amount := NEW.amount;
            v_description := 'Nathriyat transaction: ' || COALESCE(NEW.nathriyat_type_id::text, 'unknown');
            v_is_new_transaction := true;
        END IF;
        IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := OLD.id::text;
            v_bank_id := OLD.bank_id;
            v_budget_month_id := OLD.budget_month_id;
            v_transaction_date := OLD.transaction_date;
            v_amount := OLD.amount;
            v_description := 'Reversal for nathriyat transaction: ' || COALESCE(OLD.nathriyat_type_id::text, 'unknown');
            v_is_old_transaction_reversal := true;
        END IF;

    ELSIF v_source_table_name = 'bus_expenses' THEN
        v_current_transaction_type := v_expense_transaction_type;
        IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := NEW.id::text;
            v_bank_id := NEW.bank_id;
            v_budget_month_id := NEW.budget_month_id;
            v_transaction_date := NEW.expense_date;
            -- استخدام total_with_tax إذا كان موجوداً، وإلا amount
            v_amount := COALESCE(NEW.total_with_tax, NEW.amount, 0);
            v_description := 'Bus expense: ' || COALESCE(NEW.expense_type, 'unknown') ||
                           CASE WHEN NEW.details IS NOT NULL THEN ' - ' || NEW.details ELSE '' END;
            v_is_new_transaction := true;
        END IF;
        IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
            v_original_record_id := OLD.id::text;
            v_bank_id := OLD.bank_id;
            v_budget_month_id := OLD.budget_month_id;
            v_transaction_date := OLD.expense_date;
            v_amount := COALESCE(OLD.total_with_tax, OLD.amount, 0);
            v_description := 'Reversal for bus expense: ' || COALESCE(OLD.expense_type, 'unknown');
            v_is_old_transaction_reversal := true;
        END IF;

    ELSIF v_source_table_name = 'driver_expenses' THEN
        -- فقط إذا كان هناك bank_paid_amount و bank_id
        v_current_transaction_type := v_expense_transaction_type;
        IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
            IF NEW.bank_id IS NOT NULL AND COALESCE(NEW.bank_paid_amount, 0) > 0 THEN
                v_original_record_id := NEW.id::text;
                v_bank_id := NEW.bank_id;
                v_budget_month_id := NEW.budget_month_id;
                v_transaction_date := NEW.expense_date;
                v_amount := NEW.bank_paid_amount;
                v_description := 'Driver expense (bank paid): ' || COALESCE(NEW.expense_type, 'unknown');
                v_is_new_transaction := true;
            END IF;
        END IF;
        IF TG_OP = 'DELETE' OR TG_OP = 'UPDATE' THEN
            IF OLD.bank_id IS NOT NULL AND COALESCE(OLD.bank_paid_amount, 0) > 0 THEN
                v_original_record_id := OLD.id::text;
                v_bank_id := OLD.bank_id;
                v_budget_month_id := OLD.budget_month_id;
                v_transaction_date := OLD.expense_date;
                v_amount := OLD.bank_paid_amount;
                v_description := 'Reversal for driver expense (bank paid): ' || COALESCE(OLD.expense_type, 'unknown');
                v_is_old_transaction_reversal := true;
            END IF;
        END IF;
    ELSE
        -- جدول غير مدعوم
        RAISE NOTICE 'generic_payment_trigger_handler: Table % not supported.', v_source_table_name;
        RETURN COALESCE(NEW, OLD);
    END IF;

    -- التحقق من صحة البيانات
    IF v_is_new_transaction OR v_is_old_transaction_reversal THEN
        IF v_bank_id IS NULL THEN
            RAISE NOTICE 'generic_payment_trigger_handler: No bank_id for table %, record %. Skipping.', v_source_table_name, v_original_record_id;
            RETURN COALESCE(NEW, OLD);
        END IF;
        IF v_budget_month_id IS NULL THEN
            RAISE NOTICE 'generic_payment_trigger_handler: No budget_month_id for table %, record %. Skipping.', v_source_table_name, v_original_record_id;
            RETURN COALESCE(NEW, OLD);
        END IF;
        IF v_amount IS NULL OR v_amount <= 0 THEN
            RAISE NOTICE 'generic_payment_trigger_handler: Invalid amount (%) for table %, record %. Skipping.', v_amount, v_source_table_name, v_original_record_id;
            RETURN COALESCE(NEW, OLD);
        END IF;
    END IF;

    -- تحديد نوع المعاملة العكسية
    IF v_current_transaction_type = v_income_transaction_type THEN
        v_reversed_transaction_type := v_expense_transaction_type;
    ELSE
        v_reversed_transaction_type := v_income_transaction_type;
    END IF;

    -- 1. عكس المعاملة القديمة إذا كانت UPDATE أو DELETE
    IF v_is_old_transaction_reversal AND v_amount > 0 AND v_bank_id IS NOT NULL THEN
        -- البحث عن السجل الأصلي في اللوغ لعكسه
        SELECT id INTO v_old_log_entry_id
        FROM public.financial_transactions_log ftl
        WHERE ftl.source_table = v_source_table_name
          AND ftl.source_record_id = v_original_record_id
          AND ftl.is_reversal = false
        ORDER BY ftl.created_at DESC LIMIT 1;

        IF v_old_log_entry_id IS NOT NULL THEN
            PERFORM public.log_financial_transaction(
                p_transaction_date := v_transaction_date,
                p_amount := v_amount,
                p_transaction_type := v_reversed_transaction_type,
                p_description := v_description,
                p_source_table := v_source_table_name,
                p_source_record_id := v_original_record_id,
                p_bank_id := v_bank_id,
                p_budget_month_id := v_budget_month_id,
                p_is_reversal := true,
                p_original_log_entry_id := v_old_log_entry_id
            );
            RAISE NOTICE 'Created reversal transaction for % record %', v_source_table_name, v_original_record_id;
        ELSE
            RAISE NOTICE 'Could not find original log entry to reverse for table %, record id %', v_source_table_name, v_original_record_id;
        END IF;
    END IF;

    -- 2. إضافة المعاملة الجديدة إذا كانت INSERT أو UPDATE
    IF v_is_new_transaction AND v_amount > 0 AND v_bank_id IS NOT NULL THEN
        -- للتحديثات في enterprise_subscriptions، تحقق من وجود تغيير فعلي
        IF v_source_table_name = 'enterprise_subscriptions' AND TG_OP = 'UPDATE' THEN
            IF OLD.paid_amount IS DISTINCT FROM NEW.paid_amount OR
               OLD.payment_bank_id IS DISTINCT FROM NEW.payment_bank_id OR
               OLD.payment_date IS DISTINCT FROM NEW.payment_date THEN
                PERFORM public.log_financial_transaction(
                    p_transaction_date := v_transaction_date,
                    p_amount := v_amount,
                    p_transaction_type := v_current_transaction_type,
                    p_description := v_description,
                    p_source_table := v_source_table_name,
                    p_source_record_id := v_original_record_id,
                    p_bank_id := v_bank_id,
                    p_budget_month_id := v_budget_month_id
                );
                RAISE NOTICE 'Created updated transaction for % record %', v_source_table_name, v_original_record_id;
            END IF;
        ELSE
            PERFORM public.log_financial_transaction(
                p_transaction_date := v_transaction_date,
                p_amount := v_amount,
                p_transaction_type := v_current_transaction_type,
                p_description := v_description,
                p_source_table := v_source_table_name,
                p_source_record_id := v_original_record_id,
                p_bank_id := v_bank_id,
                p_budget_month_id := v_budget_month_id
            );
            RAISE NOTICE 'Created new transaction for % record %', v_source_table_name, v_original_record_id;
        END IF;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$;

-- الخطوة 2: التأكد من وجود دالة log_financial_transaction
CREATE OR REPLACE FUNCTION public.log_financial_transaction(
    p_transaction_date date,
    p_amount numeric,
    p_transaction_type public.transaction_type,
    p_description text,
    p_source_table text,
    p_source_record_id text,
    p_bank_id uuid,
    p_budget_month_id uuid,
    p_is_reversal boolean DEFAULT false,
    p_original_log_entry_id uuid DEFAULT NULL
)
RETURNS uuid
LANGUAGE plpgsql
AS $$
DECLARE
    v_log_id uuid;
BEGIN
    INSERT INTO public.financial_transactions_log (
        transaction_date, amount, transaction_type, description,
        source_table, source_record_id, bank_id, budget_month_id,
        is_reversal, original_log_entry_id
    )
    VALUES (
        p_transaction_date, p_amount, p_transaction_type, p_description,
        p_source_table, p_source_record_id, p_bank_id, p_budget_month_id,
        p_is_reversal, p_original_log_entry_id
    )
    RETURNING id INTO v_log_id;

    RAISE NOTICE 'Logged financial transaction with ID: %', v_log_id;
    RETURN v_log_id;
END;
$$;

-- الخطوة 3: إعادة إنشاء جميع التريجرات
DROP TRIGGER IF EXISTS enterprise_group_payments_financial_log_trigger ON public.enterprise_group_payments;
DROP TRIGGER IF EXISTS enterprise_subscriptions_financial_log_trigger ON public.enterprise_subscriptions;
DROP TRIGGER IF EXISTS nathriyat_transactions_financial_log_trigger ON public.nathriyat_transactions;
DROP TRIGGER IF EXISTS bus_expenses_financial_log_trigger ON public.bus_expenses;
DROP TRIGGER IF EXISTS driver_expenses_financial_log_trigger ON public.driver_expenses;

-- إعادة إنشاء التريجرات مع التحقق من وجود الجداول
DO $$
BEGIN
    -- enterprise_group_payments
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'enterprise_group_payments') THEN
        CREATE TRIGGER enterprise_group_payments_financial_log_trigger
        AFTER INSERT OR UPDATE OR DELETE ON public.enterprise_group_payments
        FOR EACH ROW EXECUTE FUNCTION public.generic_payment_trigger_handler();
        RAISE NOTICE 'Created trigger for enterprise_group_payments';
    END IF;

    -- enterprise_subscriptions
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'enterprise_subscriptions') THEN
        CREATE TRIGGER enterprise_subscriptions_financial_log_trigger
        AFTER INSERT OR UPDATE OF paid_amount, payment_bank_id, payment_date ON public.enterprise_subscriptions
        FOR EACH ROW EXECUTE FUNCTION public.generic_payment_trigger_handler();
        RAISE NOTICE 'Created trigger for enterprise_subscriptions';
    END IF;

    -- nathriyat_transactions
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'nathriyat_transactions') THEN
        CREATE TRIGGER nathriyat_transactions_financial_log_trigger
        AFTER INSERT OR UPDATE OR DELETE ON public.nathriyat_transactions
        FOR EACH ROW EXECUTE FUNCTION public.generic_payment_trigger_handler();
        RAISE NOTICE 'Created trigger for nathriyat_transactions';
    END IF;

    -- bus_expenses
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'bus_expenses') THEN
        CREATE TRIGGER bus_expenses_financial_log_trigger
        AFTER INSERT OR UPDATE OR DELETE ON public.bus_expenses
        FOR EACH ROW EXECUTE FUNCTION public.generic_payment_trigger_handler();
        RAISE NOTICE 'Created trigger for bus_expenses';
    END IF;

    -- driver_expenses
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'driver_expenses') THEN
        CREATE TRIGGER driver_expenses_financial_log_trigger
        AFTER INSERT OR UPDATE OR DELETE ON public.driver_expenses
        FOR EACH ROW EXECUTE FUNCTION public.generic_payment_trigger_handler();
        RAISE NOTICE 'Created trigger for driver_expenses';
    END IF;
END $$;

-- الخطوة 4: دالة للتحقق من حالة التريجرات
CREATE OR REPLACE FUNCTION public.check_payment_triggers_status()
RETURNS TABLE(
    tbl_name text,
    trigger_exists boolean,
    trigger_name text,
    last_test_result text
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        t.tbl_name::text,
        EXISTS(
            SELECT 1 FROM information_schema.triggers tr
            WHERE tr.event_object_table = t.tbl_name
              AND tr.trigger_name LIKE '%financial_log_trigger'
        ) as trigger_exists,
        COALESCE(
            (SELECT tr.trigger_name FROM information_schema.triggers tr
             WHERE tr.event_object_table = t.tbl_name
               AND tr.trigger_name LIKE '%financial_log_trigger'
             LIMIT 1),
            'لا يوجد'
        )::text as trigger_name,
        CASE
            WHEN EXISTS(SELECT 1 FROM information_schema.tables ist WHERE ist.table_name = t.tbl_name) THEN 'الجدول موجود'
            ELSE 'الجدول غير موجود'
        END::text as last_test_result
    FROM (VALUES
        ('student_payments'),
        ('enterprise_group_payments'),
        ('enterprise_subscriptions'),
        ('nathriyat_transactions'),
        ('bus_expenses'),
        ('driver_expenses')
    ) AS t(tbl_name);
END;
$$;

-- الخطوة 5: عرض النتائج
SELECT 'تم إصلاح جميع التريجرات بنجاح!' as status;

-- عرض حالة التريجرات
SELECT * FROM public.check_payment_triggers_status();

-- تعليمات الاستخدام
SELECT 'للتحقق من حالة التريجرات: SELECT * FROM public.check_payment_triggers_status();' as instruction
UNION ALL
SELECT 'لاختبار تريجر معين: قم بإدراج/تحديث/حذف سجل في الجدول المطلوب' as instruction
UNION ALL
SELECT 'لمراقبة الرسائل: تحقق من NOTICE messages في السجلات' as instruction;
