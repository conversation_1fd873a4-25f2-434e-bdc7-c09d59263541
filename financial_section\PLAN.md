# خطة تطوير قسم النثريات (Nathriyat)

## المرحلة الأولى: الإعداد والهيكل الأساسي

1.  **إنشاء الملفات:**
    *   إنشاء مجلد جديد باسم `nathriyat_section` (يمكن وضعه داخل `financial_section` أو بجواره حسب الهيكل العام للمشروع).
    *   إنشاء ملف `index.html` داخل المجلد ليكون الواجهة الرئيسية للقسم.
    *   إنشاء ملف `nathriyat.css` داخل المجلد لتنسيقات القسم الخاصة.
    *   إنشاء ملف `nathriyat.js` داخل المجلد لمنطق جافاسكربت الخاص بالقسم.
    *   (اختياري) إنشاء مجلد `components` داخل `nathriyat_section` للمكونات مثل `modal.css`.
2.  **تصميم الهيكل (HTML):**
    *   تصميم الهيكل الأساسي لـ `index.html` ليشمل:
        *   منطقة رئيسية لعرض لوحة التحكم أو حركات السير.
        *   عنصر نائب للشريط الجانبي (Sidebar).
        *   عناصر نائبة للنوافذ المنبثقة (Modals) لإضافة نوع نثريات وإضافة سير.
        *   تضمين ملفات CSS و JS.
3.  **التنسيق الأساسي (CSS):**
    *   تطبيق تنسيقات أساسية في `nathriyat.css` لتحديد التخطيط العام (Layout).
    *   البدء في تصميم مبدئي للنوافذ المنبثقة في `modal.css` (أو `nathriyat.css`) مع التركيز على مظهر احترافي (ظلال، انتقالات بسيطة).
4.  **الربط المبدئي (JS):**
    *   ربط ملف `nathriyat.js` بـ `index.html`.
    *   استدعاء دالة `checkAuth()` من `auth.js` في بداية `nathriyat.js` لضمان حماية القسم. (تأكد من صحة المسار النسبي لـ `auth.js` و `login.html`).
    *   إضافة وظائف أساسية لإظهار/إخفاء النوافذ المنبثقة عند النقر على الأزرار المعنية.
5.  **البيانات المبدئية:**
    *   تحديد كيفية تخزين البيانات مؤقتًا (مثل `localStorage` أو متغيرات JavaScript) لأنواع النثريات والحركات.
    *   تحديد قائمة مبدئية للأيقونات المقترحة لأنواع النثريات (مثل: <i class="fas fa-utensils"></i> طعام, <i class="fas fa-gas-pump"></i> وقود, <i class="fas fa-shopping-cart"></i> تسوق, <i class="fas fa-bus"></i> مواصلات, <i class="fas fa-file-invoice-dollar"></i> فواتير, <i class="fas fa-gift"></i> هدايا, <i class="fas fa-ellipsis-h"></i> أخرى).
    *   تحديد قائمة مبدئية للبنوك (يمكن أن تكون ثابتة في البداية، مثل: بنك الراجحي، البنك الأهلي، بنك الرياض).

## المرحلة الثانية: إدارة أنواع النثريات

1.  **تصميم نافذة إضافة نوع نثريات:**
    *   إكمال تصميم HTML و CSS للنافذة المنبثقة الخاصة بإضافة نوع نثريات (الاسم، اختيار الأيقونة).
    *   جعل اختيار الأيقونة تفاعليًا (عرض الأيقونات للاختيار).
2.  **منطق إضافة/حفظ الأنواع (JS):**
    *   تنفيذ وظيفة JavaScript لأخذ البيانات من نافذة الإضافة.
    *   حفظ بيانات النوع الجديد (مع الأيقونة المختارة) في مكان التخزين المحدد (`localStorage` مثلاً).
    *   تحديث الواجهة لعرض النوع الجديد فورًا دون الحاجة لإعادة تحميل الصفحة.
3.  **عرض الأنواع:**
    *   تنفيذ وظيفة JavaScript لقراءة أنواع النثريات المحفوظة.
    *   عرض الأنواع في لوحة التحكم الرئيسية (Dashboard).
    *   عرض الأنواع في الشريط الجانبي (Sidebar).
    *   ربط حدث النقر على كل نوع في الشريط الجانبي لعرض حركات السير الخاصة به (سيتم تنفيذ العرض في المرحلة التالية).

## المرحلة الثالثة: إدارة حركات السير (المعاملات)

1.  **تصميم نافذة إضافة سير:**
    *   إكمال تصميم HTML و CSS للنافذة المنبثقة الخاصة بإضافة سير (المبلغ، التاريخ، اختيار البنك).
    *   استخدام حقل إدخال مناسب للتاريخ (type="date").
    *   ملء قائمة اختيار البنك بالبيانات المحددة مسبقًا.
2.  **منطق إضافة/حفظ السير (JS):**
    *   تنفيذ وظيفة JavaScript لأخذ البيانات من نافذة الإضافة.
    *   ربط السير المضاف بنوع النثرية المحدد حاليًا.
    *   حفظ بيانات السير الجديد في مكان التخزين المحدد.
    *   تحديث واجهة عرض حركات السير فورًا.
3.  **عرض حركات السير:**
    *   تنفيذ وظيفة JavaScript لقراءة حركات السير المرتبطة بنوع النثرية المحدد عند النقر عليه في الشريط الجانبي.
    *   عرض قائمة حركات السير (المبلغ، التاريخ، البنك) في المنطقة الرئيسية.
    *   تنسيق عرض حركات السير بشكل واضح ومنظم.

## المرحلة الرابعة: التحسينات والتكامل النهائي

1.  **لوحة تحكم لكل نوع نثريات:**
    *   تحديد المعلومات التي يجب عرضها في لوحة التحكم الخاصة بكل نوع (مثل إجمالي المصروفات، عدد الحركات، رسم بياني بسيط إن أمكن).
    *   تصميم وتنفيذ هذه الواجهة عند تحديد نوع معين (يمكن أن تكون جزءًا من عرض حركات السير أو واجهة منفصلة).
2.  **تحسينات الواجهة والتجربة (UI/UX):**
    *   مراجعة وتحسين تصميم جميع العناصر (الأزرار، النوافذ المنبثقة، القوائم) لضمان مظهر احترافي وسهولة استخدام.
    *   إضافة انتقالات (transitions/animations) بسيطة لتحسين التجربة.
    *   ضمان استجابة التصميم (Responsiveness) للشاشات المختلفة إن أمكن.
3.  **التكامل مع المصادقة:**
    *   مراجعة نهائية لتكامل `checkAuth()` والتأكد من عمل تسجيل الخروج بشكل صحيح من داخل قسم النثريات.
    *   تحديث مسار تسجيل الخروج في `auth.js` إذا لزم الأمر ليشمل `nathriyat_section`.
4.  **الاختبار وإصلاح الأخطاء:**
    *   اختبار شامل لجميع الوظائف (إضافة/عرض الأنواع، إضافة/عرض السير، التنقل).
    *   إصلاح أي أخطاء تظهر أثناء الاختبار.
    *   التحقق من حفظ واسترجاع البيانات بشكل صحيح.
