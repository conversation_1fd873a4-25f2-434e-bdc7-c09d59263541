// Supabase Initialization
let supabaseUrl, supabaseAnonKey;
if (typeof SUPABASE_URL !== 'undefined') supabaseUrl = SUPABASE_URL;
else console.error('SUPABASE_URL not found.');
if (typeof SUPABASE_ANON_KEY !== 'undefined') supabaseAnonKey = SUPABASE_ANON_KEY;
else console.error('SUPABASE_ANON_KEY not found.');

const { createClient } = supabase;
const _supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('Supabase Initialized for Monthly Budget:', _supabase);

// --- DOM Elements ---
const yearSelect = document.getElementById('year-select');
const monthsTableBody = document.getElementById('months-tbody');
const monthsSection = document.getElementById('months-section');
const selectedYearTitle = document.getElementById('selected-year-title');
const listMessage = document.getElementById('list-message');
const yearMessage = document.getElementById('year-message');
const monthsCountBadge = document.getElementById('months-count');
const addNewYearBtn = document.getElementById('add-new-year-btn');
const addYearSection = document.getElementById('add-year-section');
const yearForm = document.getElementById('year-form');
const addYearMessage = document.getElementById('add-year-message');
const cancelYearBtn = document.getElementById('cancel-year-btn');
const closeYearFormBtn = document.getElementById('close-year-form-btn');
const yearNumberInput = document.getElementById('year_number');

// --- State ---
let availableYears = [];
let currentMonths = [];
let selectedYearId = null;

// --- Functions ---

// Function to display messages
const showMessage = (element, message, type = 'info', duration = 5000) => {
    if (!element) return;
    element.textContent = message;
    element.className = `message ${type} show`;
    if (type === 'success' || duration > 0) {
        setTimeout(() => { element.style.display = 'none'; element.classList.remove('show'); }, duration);
    } else {
        element.style.display = 'block';
    }
};

// Function to format currency (example)
const formatCurrency = (amount) => {
    if (amount === null || amount === undefined) return '0.00';
    // Basic formatting, consider a library for more complex needs
    return parseFloat(amount).toFixed(2);
};

// Function to render months table
const renderMonthsTable = (months) => {
    if (!monthsTableBody) return;
    monthsTableBody.innerHTML = '';
    if (listMessage) listMessage.style.display = 'none';

    if (!months || months.length === 0) {
        monthsTableBody.innerHTML = `<tr><td colspan="8" class="loading-message">لا توجد شهور لهذه السنة. قد تحتاج لإنشائها.</td></tr>`;
        if (monthsCountBadge) monthsCountBadge.textContent = '0';
        return;
    }

    if (monthsCountBadge) monthsCountBadge.textContent = months.length;

    // Sort months by month_number
    months.sort((a, b) => a.month_number - b.month_number);

    months.forEach(month => {
        const row = document.createElement('tr');
        const statusClass = month.is_closed ? 'status-closed' : 'status-open';
        const statusText = month.is_closed ? 'مغلق' : 'مفتوح';

        row.innerHTML = `
            <td>${month.month_name || `شهر ${month.month_number}`}</td>
            <td>${month.month_number}</td>
            <td>${formatCurrency(month.total_income)}</td>
            <td>${formatCurrency(month.total_expense)}</td>
            <td>${formatCurrency(month.opening_balance)}</td>
            <td>${formatCurrency(month.closing_balance)}</td>
            <td><span class="${statusClass}">${statusText}</span></td>
            <td>
                <button class="action-btn close-month-btn" data-id="${month.id}" title="${month.is_closed ? 'الشهر مغلق' : 'إغلاق الشهر'}" ${month.is_closed ? 'disabled' : ''}>
                    <i class="fas ${month.is_closed ? 'fa-lock' : 'fa-lock-open'}"></i>
                </button>
                <!-- Add view details button later if needed -->
                <!-- <button class="action-btn view-details-btn" data-id="${month.id}" title="عرض تفاصيل الشهر"><i class="fas fa-eye"></i></button> -->
            </td>
        `;

        // Add event listener for close button
        const closeBtn = row.querySelector('.close-month-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => handleCloseMonth(month.id, month.month_name || `شهر ${month.month_number}`, month.is_closed));
        }

        monthsTableBody.appendChild(row);
    });
};

// Function to fetch months for a selected year
const fetchMonthsForYear = async (yearId) => {
    if (!yearId) {
        if (monthsSection) monthsSection.style.display = 'none';
        return;
    }
    selectedYearId = yearId; // Store selected year ID
    if (!monthsTableBody || !monthsSection) return;

    monthsTableBody.innerHTML = `<tr><td colspan="8" class="loading-message">جاري تحميل الشهور...</td></tr>`;
    monthsSection.style.display = 'block'; // Show the section
    const selectedYear = availableYears.find(y => y.id == yearId);
    if (selectedYearTitle) selectedYearTitle.textContent = `(${selectedYear?.year_number || ''})`;

    try {
        const { data, error } = await _supabase
            .from('budget_months')
            .select('*')
            .eq('budget_year_id', yearId)
            .order('month_number', { ascending: true });

        if (error) {
            console.error('Supabase Fetch Months Error:', error);
            showMessage(listMessage, `خطأ في جلب الشهور: ${error.message}`, 'error', 0); // Show error indefinitely
            monthsTableBody.innerHTML = `<tr><td colspan="8" class="loading-message">خطأ في تحميل الشهور.</td></tr>`;
        } else {
            console.log('Fetched months:', data);
            currentMonths = data || [];
            renderMonthsTable(currentMonths);
            if (data.length === 0) {
                 showMessage(listMessage, 'لم يتم إنشاء شهور لهذه السنة بعد.', 'info');
            }
        }
    } catch (error) {
        console.error('JavaScript Fetch Months Error:', error);
        showMessage(listMessage, `حدث خطأ غير متوقع: ${error.message}`, 'error', 0);
        monthsTableBody.innerHTML = `<tr><td colspan="8" class="loading-message">خطأ غير متوقع.</td></tr>`;
    }
};

// Function to fetch available years
const fetchYears = async () => {
    if (!yearSelect) return;
    yearSelect.innerHTML = '<option value="">جاري تحميل السنوات...</option>';
    try {
        const { data, error } = await _supabase
            .from('budget_years')
            .select('id, year_number')
            .order('year_number', { ascending: false }); // Show recent years first

        if (error) {
            console.error('Supabase Fetch Years Error:', error);
            showMessage(yearMessage, `خطأ في جلب السنوات: ${error.message}`, 'error', 0);
            yearSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
        } else {
            availableYears = data || [];
            yearSelect.innerHTML = '<option value="">-- اختر سنة --</option>'; // Default option
            if (availableYears.length === 0) {
                yearSelect.innerHTML = '<option value="">لا توجد سنوات، قم بإضافة سنة جديدة</option>';
                 showMessage(yearMessage, 'لا توجد سنوات مالية معرفة. الرجاء إضافة سنة جديدة لبدء العمل.', 'info', 0);
            } else {
                availableYears.forEach(year => {
                    const option = document.createElement('option');
                    option.value = year.id;
                    option.textContent = year.year_number;
                    yearSelect.appendChild(option);
                });
                // Optionally select the most recent year automatically
                // yearSelect.value = availableYears[0].id;
                // fetchMonthsForYear(availableYears[0].id);
            }
        }
    } catch (error) {
        console.error('JavaScript Fetch Years Error:', error);
        showMessage(yearMessage, `حدث خطأ غير متوقع: ${error.message}`, 'error', 0);
        yearSelect.innerHTML = '<option value="">خطأ غير متوقع</option>';
    }
};

// Function to handle closing/reopening a month
const handleCloseMonth = async (monthId, monthName, isCurrentlyClosed) => {
    if (isCurrentlyClosed) {
        // Optional: Add logic to reopen if needed, requires confirmation
        alert(`الشهر "${monthName}" مغلق بالفعل.`);
        return;
    }

    const confirmClose = confirm(`هل أنت متأكد من إغلاق شهر "${monthName}"؟ \nلن تتمكن من تعديل الاشتراكات أو الرواتب أو المصاريف المتعلقة بهذا الشهر بعد الإغلاق.`);

    if (confirmClose) {
        try {
            const { error } = await _supabase
                .from('budget_months')
                .update({ is_closed: true })
                .eq('id', monthId);

            if (error) {
                console.error('Supabase Close Month Error:', error);
                showMessage(listMessage, `خطأ في إغلاق الشهر: ${error.message}`, 'error');
            } else {
                console.log('Month closed:', monthId);
                showMessage(listMessage, `تم إغلاق شهر "${monthName}" بنجاح.`, 'success');
                // Refresh the months table for the currently selected year
                if (selectedYearId) {
                    fetchMonthsForYear(selectedYearId);
                }
            }
        } catch (error) {
            console.error('JavaScript Close Month Error:', error);
            showMessage(listMessage, `حدث خطأ غير متوقع أثناء إغلاق الشهر: ${error.message}`, 'error');
        }
    }
};

// Function to toggle the Add Year modal
function toggleYearModal(show = true) {
    if (!addYearSection || !yearForm) return;
    if (show) {
        yearForm.reset();
        if(addYearMessage) addYearMessage.style.display = 'none';
        addYearSection.classList.add('show');
        setTimeout(() => yearNumberInput?.focus(), 100);
        document.body.style.overflow = 'hidden';
    } else {
        addYearSection.classList.remove('show');
        document.body.style.overflow = '';
    }
}

// Function to handle adding a new year and its months
const handleAddYear = async (event) => {
    event.preventDefault();
    if (!yearForm || !addYearMessage) return;

    const yearNumber = yearNumberInput?.value;
    if (!yearNumber || isNaN(parseInt(yearNumber))) {
        showMessage(addYearMessage, 'الرجاء إدخال رقم سنة صحيح.', 'error');
        return;
    }

    const year = parseInt(yearNumber);

    // Check if year already exists
    if (availableYears.some(y => y.year_number === year)) {
         showMessage(addYearMessage, `السنة ${year} موجودة بالفعل.`, 'warning');
         return;
    }

    showMessage(addYearMessage, `جاري إضافة السنة ${year} والشهور...`, 'info');
    const submitBtn = yearForm.querySelector('.submit-btn');
    if (submitBtn) submitBtn.disabled = true;

    try {
        // 1. Insert the new year
        const { data: yearData, error: yearError } = await _supabase
            .from('budget_years')
            .insert({ year_number: year })
            .select()
            .single(); // Get the newly inserted year record

        if (yearError) throw yearError;
        if (!yearData) throw new Error('Failed to retrieve newly added year.');

        const newYearId = yearData.id;
        console.log('Year added:', yearData);

        // 2. Prepare month data for the new year
        const monthsToAdd = [];
        const monthNames = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"];
        for (let i = 1; i <= 12; i++) {
            monthsToAdd.push({
                budget_year_id: newYearId,
                month_number: i,
                month_name: monthNames[i - 1],
                is_closed: false,
                opening_balance: 0, // Default opening balance
                // Other fields will default to null or 0 in the DB
            });
        }

        // 3. Insert the 12 months for the new year
        const { error: monthError } = await _supabase
            .from('budget_months')
            .insert(monthsToAdd);

        if (monthError) throw monthError;

        console.log(`12 months added for year ${year}`);
        showMessage(addYearMessage, `تم إضافة السنة ${year} وشهورها بنجاح!`, 'success');
        await fetchYears(); // Refresh the year dropdown
        yearSelect.value = newYearId; // Select the newly added year
        fetchMonthsForYear(newYearId); // Fetch and display its months
        setTimeout(() => toggleYearModal(false), 1500);

    } catch (error) {
        console.error('Error adding year/months:', error);
        showMessage(addYearMessage, `خطأ في إضافة السنة: ${error.message}`, 'error');
        // Consider deleting the year if months failed? Or handle partial success.
    } finally {
         if (submitBtn) submitBtn.disabled = false;
    }
};


// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOM loaded, initializing monthly budget page...');

    // Fetch available years on load
    await fetchYears();

    // Setup event listeners
    setupEventListeners();
});


// --- Event Listeners ---
const setupEventListeners = () => {
    if (yearSelect) {
        yearSelect.addEventListener('change', (event) => {
            const selectedId = event.target.value;
            if (selectedId) {
                fetchMonthsForYear(selectedId);
            } else {
                // Clear months table if no year is selected
                if (monthsSection) monthsSection.style.display = 'none';
                if (monthsTableBody) monthsTableBody.innerHTML = `<tr><td colspan="8" class="loading-message">اختر سنة لعرض الشهور...</td></tr>`;
                if (selectedYearTitle) selectedYearTitle.textContent = '';
                if (monthsCountBadge) monthsCountBadge.textContent = '0';
                selectedYearId = null;
            }
        });
    }

    if (addNewYearBtn) {
        addNewYearBtn.addEventListener('click', () => toggleYearModal(true));
    }

    if (yearForm) {
        yearForm.addEventListener('submit', handleAddYear);
    }

    if (cancelYearBtn) {
        cancelYearBtn.addEventListener('click', () => toggleYearModal(false));
    }

    if (closeYearFormBtn) {
        closeYearFormBtn.addEventListener('click', () => toggleYearModal(false));
    }

     // Close modal on background click
    if (addYearSection) {
        addYearSection.addEventListener('click', (event) => {
            if (event.target === addYearSection) {
                toggleYearModal(false);
            }
        });
    }

    // Escape key listener for modal
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && addYearSection && addYearSection.classList.contains('show')) {
            toggleYearModal(false);
        }
    });
};
